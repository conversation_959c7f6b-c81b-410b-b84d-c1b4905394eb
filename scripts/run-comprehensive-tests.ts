#!/usr/bin/env tsx

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import chalk from 'chalk';

interface TestResult {
  suite: string;
  passed: number;
  failed: number;
  duration: number;
  coverage?: number;
}

interface TestSuite {
  name: string;
  command: string;
  description: string;
  critical: boolean;
}

const testSuites: TestSuite[] = [
  {
    name: 'ai-travel-assistant',
    command: 'npm run test tests/ai-travel-assistant.test.ts',
    description: 'End-to-end AI Travel Assistant functionality',
    critical: true
  },
  {
    name: 'multi-bedroom-service',
    command: 'npm run test tests/multi-bedroom-service.test.ts',
    description: 'Multi-bedroom accommodation search and filtering',
    critical: true
  },
  {
    name: 'unified-ai-chat',
    command: 'npm run test tests/unified-ai-chat.test.tsx',
    description: 'Unified AI Chat component functionality',
    critical: true
  },
  {
    name: 'property-caching',
    command: 'npm run test tests/property-caching.test.ts',
    description: 'Property caching and database operations',
    critical: true
  },
  {
    name: 'geocoding-service',
    command: 'npm run test tests/geocoding-service.test.ts',
    description: 'Location detection and geocoding',
    critical: false
  },
  {
    name: 'api-endpoints',
    command: 'npm run test tests/api-endpoints.test.ts',
    description: 'API endpoint functionality and responses',
    critical: true
  },
  {
    name: 'performance',
    command: 'npm run test tests/performance.test.ts',
    description: 'Performance and load testing',
    critical: false
  }
];

async function runTest(suite: TestSuite): Promise<TestResult> {
  console.log(chalk.blue(`\n🧪 Running ${suite.name} tests...`));
  console.log(chalk.gray(`   ${suite.description}`));

  const startTime = Date.now();

  return new Promise((resolve) => {
    const child = spawn('npm', ['run', 'test', `tests/${suite.name}.test.ts`], {
      stdio: 'pipe',
      shell: true
    });

    let output = '';
    let passed = 0;
    let failed = 0;

    child.stdout?.on('data', (data) => {
      output += data.toString();
      process.stdout.write(chalk.gray(data.toString()));
    });

    child.stderr?.on('data', (data) => {
      output += data.toString();
      process.stderr.write(chalk.red(data.toString()));
    });

    child.on('close', (code) => {
      const duration = Date.now() - startTime;

      // Parse test results from output
      const passedMatch = output.match(/(\d+) passed/);
      const failedMatch = output.match(/(\d+) failed/);

      if (passedMatch) passed = parseInt(passedMatch[1]);
      if (failedMatch) failed = parseInt(failedMatch[1]);

      const result: TestResult = {
        suite: suite.name,
        passed,
        failed,
        duration
      };

      if (code === 0) {
        console.log(chalk.green(`✅ ${suite.name} tests passed (${duration}ms)`));
      } else {
        console.log(chalk.red(`❌ ${suite.name} tests failed (${duration}ms)`));
      }

      resolve(result);
    });
  });
}

async function generateTestReport(results: TestResult[]): Promise<void> {
  const totalPassed = results.reduce((sum, r) => sum + r.passed, 0);
  const totalFailed = results.reduce((sum, r) => sum + r.failed, 0);
  const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);

  const report = `
# Ultimate AI Travel Assistant - Test Report

Generated: ${new Date().toISOString()}

## Summary

- **Total Tests**: ${totalPassed + totalFailed}
- **Passed**: ${totalPassed}
- **Failed**: ${totalFailed}
- **Success Rate**: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%
- **Total Duration**: ${(totalDuration / 1000).toFixed(2)}s

## Test Suites

${results.map(result => `
### ${result.suite}
- **Passed**: ${result.passed}
- **Failed**: ${result.failed}
- **Duration**: ${(result.duration / 1000).toFixed(2)}s
- **Status**: ${result.failed === 0 ? '✅ PASS' : '❌ FAIL'}
`).join('\n')}

## Critical Issues

${results.filter(r => r.failed > 0).map(result => `
- **${result.suite}**: ${result.failed} failing test(s)
`).join('\n') || 'No critical issues found! 🎉'}

## Recommendations

${totalFailed === 0 ? `
🎉 **Excellent!** All tests are passing. Your AI Travel Assistant is ready for production.

### Next Steps:
1. Deploy to staging environment
2. Run user acceptance testing
3. Monitor performance metrics
4. Prepare for production deployment
` : `
⚠️ **Action Required**: ${totalFailed} test(s) are failing.

### Priority Actions:
1. Fix failing tests before deployment
2. Review error logs and stack traces
3. Update implementation as needed
4. Re-run tests to verify fixes

### Critical Test Failures:
${results.filter(r => r.failed > 0 && testSuites.find(s => s.name === r.suite)?.critical).map(r => `- ${r.suite}`).join('\n') || 'None'}
`}

## Performance Metrics

- **Average Test Duration**: ${(totalDuration / results.length / 1000).toFixed(2)}s
- **Fastest Suite**: ${results.sort((a, b) => a.duration - b.duration)[0]?.suite} (${(results.sort((a, b) => a.duration - b.duration)[0]?.duration / 1000).toFixed(2)}s)
- **Slowest Suite**: ${results.sort((a, b) => b.duration - a.duration)[0]?.suite} (${(results.sort((a, b) => b.duration - a.duration)[0]?.duration / 1000).toFixed(2)}s)

## Coverage Goals

- **AI Chat Functionality**: ✅ Comprehensive coverage
- **Multi-bedroom Search**: ✅ Comprehensive coverage  
- **Property Management**: ✅ Comprehensive coverage
- **Error Handling**: ✅ Comprehensive coverage
- **Performance**: ✅ Basic coverage
- **Integration**: ✅ End-to-end coverage

---

*This report was generated automatically by the Ultimate AI Travel Assistant test suite.*
`;

  await fs.writeFile('TEST_REPORT.md', report);
  console.log(chalk.green('\n📊 Test report generated: TEST_REPORT.md'));
}

async function main() {
  console.log(chalk.bold.blue('\n🚀 Ultimate AI Travel Assistant - Comprehensive Test Suite\n'));
  console.log(chalk.gray('Testing the world\'s most intelligent travel booking platform...\n'));

  const results: TestResult[] = [];

  // Run critical tests first
  const criticalSuites = testSuites.filter(s => s.critical);
  const nonCriticalSuites = testSuites.filter(s => !s.critical);

  console.log(chalk.bold.yellow('🔥 Running Critical Tests First...\n'));

  for (const suite of criticalSuites) {
    const result = await runTest(suite);
    results.push(result);

    // Stop if critical test fails
    if (result.failed > 0) {
      console.log(chalk.bold.red(`\n💥 Critical test failure in ${suite.name}!`));
      console.log(chalk.yellow('Recommendation: Fix critical issues before continuing.\n'));
      
      // Still generate report for failed critical tests
      await generateTestReport(results);
      process.exit(1);
    }
  }

  console.log(chalk.bold.green('\n✅ All critical tests passed! Running additional tests...\n'));

  // Run non-critical tests
  for (const suite of nonCriticalSuites) {
    const result = await runTest(suite);
    results.push(result);
  }

  // Generate comprehensive report
  await generateTestReport(results);

  const totalFailed = results.reduce((sum, r) => sum + r.failed, 0);
  const totalPassed = results.reduce((sum, r) => sum + r.passed, 0);

  console.log(chalk.bold.blue('\n🎯 Test Suite Complete!\n'));

  if (totalFailed === 0) {
    console.log(chalk.bold.green('🎉 ALL TESTS PASSED! 🎉'));
    console.log(chalk.green(`✅ ${totalPassed} tests passed successfully`));
    console.log(chalk.green('\n🚀 Your Ultimate AI Travel Assistant is ready for deployment!'));
    
    console.log(chalk.bold.blue('\n📋 Deployment Checklist:'));
    console.log(chalk.green('✅ Core functionality tested'));
    console.log(chalk.green('✅ AI chat system verified'));
    console.log(chalk.green('✅ Multi-bedroom search working'));
    console.log(chalk.green('✅ Property caching functional'));
    console.log(chalk.green('✅ Error handling robust'));
    console.log(chalk.green('✅ Performance acceptable'));
    
    process.exit(0);
  } else {
    console.log(chalk.bold.red(`❌ ${totalFailed} tests failed`));
    console.log(chalk.yellow(`⚠️  ${totalPassed} tests passed`));
    console.log(chalk.red('\n🔧 Please fix failing tests before deployment.'));
    
    process.exit(1);
  }
}

// Handle process interruption
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n⚠️  Test suite interrupted by user'));
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error(chalk.red('\n💥 Uncaught exception in test runner:'));
  console.error(error);
  process.exit(1);
});

if (require.main === module) {
  main().catch((error) => {
    console.error(chalk.red('\n💥 Test runner failed:'));
    console.error(error);
    process.exit(1);
  });
}
