const testUrls = ["https://media.travsrv.com/images/3451904/854790155_70.jpg", "https://media.travsrv.com/images/24620/1089108981_70.jpg"]; function getFullSizeImageUrl(imagePath) { if (!imagePath) return imagePath; if (imagePath.includes("_70.jpg")) { return imagePath.replace("_70.jpg", "_0.jpg"); } const fullSizeUrl = imagePath.replace(/_(70|thumb|thumbnail|small|medium)\.jpg$/i, "_0.jpg"); if (fullSizeUrl === imagePath && imagePath.endsWith(".jpg")) { return imagePath.replace(/_.+\.jpg$/, "_0.jpg"); } return fullSizeUrl; } console.log("Image URL Conversion Test:"); testUrls.forEach(url => { const converted = getFullSizeImageUrl(url); console.log(`${url} -> ${converted}`); });
