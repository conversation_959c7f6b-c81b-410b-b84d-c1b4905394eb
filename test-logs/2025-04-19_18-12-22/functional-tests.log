============ Test Output ============
[0m[7m[1m[31m FAIL [39m[22m[27m[0m [2mserver/__tests__/functional/[22m[1mauth-system.test.ts[22m
  [1m● [22mTest suite failed to run

    A jest worker process (pid=8237) was terminated by another process: signal=SIGKILL, exitCode=null. Operating system logs may contain more information on why this occurred.

      [2mat ChildProcessWorker._onExit ([22mnode_modules/jest-worker/build/workers/ChildProcessWorker.js[2m:370:23)[22m

[0m[7m[1m[31m FAIL [39m[22m[27m[0m [2mserver/__tests__/functional/[22m[1mchat-api.test.ts[22m
  [1m● [22mTest suite failed to run

    [1m[31mJest encountered an unexpected token[39m[22m

    Jest failed to parse a file. This happens e.g. when your code or its dependencies use non-standard JavaScript syntax, or when Jest is not configured to support such syntax.

    Out of the box Jest supports Babel, which will be used to transform your files into valid JS based on your Babel configuration.

    By default "node_modules" folder is ignored by transformers.

    Here's what you can do:
     • If you are trying to use ECMAScript Modules, see [4mhttps://jestjs.io/docs/ecmascript-modules[24m for how to enable it.
     • If you are trying to use TypeScript, see [4mhttps://jestjs.io/docs/getting-started#using-typescript[24m
     • To have some of your "node_modules" files transformed, you can specify a custom [1m"transformIgnorePatterns"[22m in your config.
     • If you need a custom transformation specify a [1m"transform"[22m option in your config.
     • If you simply want to mock your non-JS modules (e.g. binary assets) you can stub them out with the [1m"moduleNameMapper"[22m config option.

    You'll find more details and examples of these config options in the docs:
    [36mhttps://jestjs.io/docs/configuration[39m
    For information about custom transformations, see:
    [36mhttps://jestjs.io/docs/code-transformation[39m

    [1m[31mDetails:[39m[22m

    /home/<USER>/workspace/node_modules/node-fetch/src/index.js:9
    import http from 'node:http';
    ^^^^^^

    SyntaxError: Cannot use import statement outside a module

    [0m [90m 10 |[39m
     [90m 11 |[39m [36mimport[39m { describe[33m,[39m test[33m,[39m expect[33m,[39m jest[33m,[39m beforeEach[33m,[39m afterEach[33m,[39m beforeAll } [36mfrom[39m [32m'@jest/globals'[39m[33m;[39m
    [31m[1m>[22m[39m[90m 12 |[39m [36mimport[39m fetch [36mfrom[39m [32m'node-fetch'[39m[33m;[39m
     [90m    |[39m [31m[1m^[22m[39m
     [90m 13 |[39m [36mimport[39m { [33mResponse[39m } [36mfrom[39m [32m'node-fetch'[39m[33m;[39m
     [90m 14 |[39m [36mimport[39m { safeParseSSEData } [36mfrom[39m [32m'../../utils/streamParser'[39m[33m;[39m
     [90m 15 |[39m [36mimport[39m { [33mTextResponse[39m[33m,[39m [33mLocationResponse[39m[33m,[39m [33mPropertiesResponse[39m[33m,[39m [33mTypedChatResponse[39m } [36mfrom[39m [32m'../../services/openai'[39m[33m;[39m[0m

      [2mat Runtime.createScriptFromCode ([22mnode_modules/jest-runtime/build/index.js[2m:1505:14)[22m
      [2mat Object.<anonymous> ([22m[0m[36mserver/__tests__/functional/chat-api.test.ts[39m[0m[2m:12:1)[22m

[0m[7m[1m[31m FAIL [39m[22m[27m[0m [2mserver/__tests__/functional/[22m[1msearch-functionality.test.ts[22m
  [1m● [22mTest suite failed to run

    [96mserver/__tests__/functional/search-functionality.test.ts[0m:[93m57[0m:[93m50[0m - [91merror[0m[90m TS18046: [0m'subValue' is of type 'unknown'.

    [7m57[0m             queryParams.set(`${key}[${subKey}]`, subValue.toString());
    [7m  [0m [91m                                                 ~~~~~~~~[0m
    [96mserver/__tests__/functional/search-functionality.test.ts[0m:[93m80[0m:[93m19[0m - [91merror[0m[90m TS18046: [0m'data' is of type 'unknown'.

    [7m80[0m       properties: data.properties,
    [7m  [0m [91m                  ~~~~[0m
    [96mserver/__tests__/functional/search-functionality.test.ts[0m:[93m81[0m:[93m19[0m - [91merror[0m[90m TS18046: [0m'data' is of type 'unknown'.

    [7m81[0m       totalCount: data.totalCount
    [7m  [0m [91m                  ~~~~[0m

[0m[7m[1m[31m FAIL [39m[22m[27m[0m [2mserver/__tests__/functional/[22m[1mai-property-recommendations.test.ts[22m
  [1m● [22mTest suite failed to run

    [96mserver/__tests__/functional/ai-property-recommendations.test.ts[0m:[93m11[0m:[93m26[0m - [91merror[0m[90m TS2307: [0mCannot find module '../../db/schema' or its corresponding type declarations.

    [7m11[0m import { Property } from '../../db/schema';
    [7m  [0m [91m                         ~~~~~~~~~~~~~~~~~[0m
    [96mserver/__tests__/functional/ai-property-recommendations.test.ts[0m:[93m112[0m:[93m34[0m - [91merror[0m[90m TS2339: [0mProperty 'getReader' does not exist on type 'ReadableStream'.

    [7m112[0m     const reader = response.body.getReader();
    [7m   [0m [91m                                 ~~~~~~~~~[0m
    [96mserver/__tests__/functional/ai-property-recommendations.test.ts[0m:[93m250[0m:[93m11[0m - [91merror[0m[90m TS2554: [0mExpected 1 arguments, but got 2.

    [7m250[0m           `Property at index ${index} is missing fields: ${validation.missingFields.join(', ')}`
    [7m   [0m [91m          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
    [96mserver/__tests__/functional/ai-property-recommendations.test.ts[0m:[93m269[0m:[93m15[0m - [91merror[0m[90m TS7006: [0mParameter 'propAmenity' implicitly has an 'any' type.

    [7m269[0m               propAmenity => propAmenity.toLowerCase().includes(amenity.toLowerCase())
    [7m   [0m [91m              ~~~~~~~~~~~[0m

[0m[7m[1m[31m FAIL [39m[22m[27m[0m [2mserver/__tests__/functional/[22m[1mbooking-process.test.ts[22m
  [1m● [22mTest suite failed to run

    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m48[0m:[93m23[0m - [91merror[0m[90m TS18046: [0m'data' is of type 'unknown'.

    [7m48[0m       throw new Error(data.message || `Registration failed with status ${response.status}`);
    [7m  [0m [91m                      ~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m53[0m:[93m14[0m - [91merror[0m[90m TS18046: [0m'data' is of type 'unknown'.

    [7m53[0m       token: data.token,
    [7m  [0m [91m             ~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m54[0m:[93m15[0m - [91merror[0m[90m TS18046: [0m'data' is of type 'unknown'.

    [7m54[0m       userId: data.user?.id
    [7m  [0m [91m              ~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m87[0m:[93m23[0m - [91merror[0m[90m TS18046: [0m'data' is of type 'unknown'.

    [7m87[0m       throw new Error(data.message || `Login failed with status ${response.status}`);
    [7m  [0m [91m                      ~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m92[0m:[93m14[0m - [91merror[0m[90m TS18046: [0m'data' is of type 'unknown'.

    [7m92[0m       token: data.token,
    [7m  [0m [91m             ~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m93[0m:[93m15[0m - [91merror[0m[90m TS18046: [0m'data' is of type 'unknown'.

    [7m93[0m       userId: data.user.id
    [7m  [0m [91m              ~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m123[0m:[93m10[0m - [91merror[0m[90m TS18046: [0m'searchData' is of type 'unknown'.

    [7m123[0m     if (!searchData.properties || searchData.properties.length === 0) {
    [7m   [0m [91m         ~~~~~~~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m123[0m:[93m35[0m - [91merror[0m[90m TS18046: [0m'searchData' is of type 'unknown'.

    [7m123[0m     if (!searchData.properties || searchData.properties.length === 0) {
    [7m   [0m [91m                                  ~~~~~~~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m137[0m:[93m28[0m - [91merror[0m[90m TS18046: [0m'searchData' is of type 'unknown'.

    [7m137[0m     for (const property of searchData.properties.slice(0, 5)) { // Try first 5 properties
    [7m   [0m [91m                           ~~~~~~~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m157[0m:[93m13[0m - [91merror[0m[90m TS18046: [0m'availabilityData' is of type 'unknown'.

    [7m157[0m         if (availabilityData.ratePlans && Object.keys(availabilityData.ratePlans).length > 0) {
    [7m   [0m [91m            ~~~~~~~~~~~~~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m157[0m:[93m55[0m - [91merror[0m[90m TS18046: [0m'availabilityData' is of type 'unknown'.

    [7m157[0m         if (availabilityData.ratePlans && Object.keys(availabilityData.ratePlans).length > 0) {
    [7m   [0m [91m                                                      ~~~~~~~~~~~~~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m159[0m:[93m38[0m - [91merror[0m[90m TS18046: [0m'availabilityData' is of type 'unknown'.

    [7m159[0m           const rateId = Object.keys(availabilityData.ratePlans)[0];
    [7m   [0m [91m                                     ~~~~~~~~~~~~~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m223[0m:[93m23[0m - [91merror[0m[90m TS18046: [0m'data' is of type 'unknown'.

    [7m223[0m       throw new Error(data.message || `Booking initiation failed with status ${response.status}`);
    [7m   [0m [91m                      ~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m228[0m:[93m18[0m - [91merror[0m[90m TS18046: [0m'data' is of type 'unknown'.

    [7m228[0m       bookingId: data.bookingId
    [7m   [0m [91m                 ~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m268[0m:[93m23[0m - [91merror[0m[90m TS18046: [0m'data' is of type 'unknown'.

    [7m268[0m       throw new Error(data.message || `Payment failed with status ${response.status}`);
    [7m   [0m [91m                      ~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m273[0m:[93m15[0m - [91merror[0m[90m TS18046: [0m'data' is of type 'unknown'.

    [7m273[0m       status: data.status
    [7m   [0m [91m              ~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m306[0m:[93m23[0m - [91merror[0m[90m TS18046: [0m'data' is of type 'unknown'.

    [7m306[0m       throw new Error(data.message || `Booking confirmation failed with status ${response.status}`);
    [7m   [0m [91m                      ~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m311[0m:[93m22[0m - [91merror[0m[90m TS18046: [0m'data' is of type 'unknown'.

    [7m311[0m       reservationId: data.reservationId,
    [7m   [0m [91m                     ~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m312[0m:[93m15[0m - [91merror[0m[90m TS18046: [0m'data' is of type 'unknown'.

    [7m312[0m       status: data.status
    [7m   [0m [91m              ~~~~[0m
    [96mserver/__tests__/functional/booking-process.test.ts[0m:[93m342[0m:[93m23[0m - [91merror[0m[90m TS18046: [0m'data' is of type 'unknown'.

    [7m342[0m       throw new Error(data.message || `Get booking details failed with status ${response.status}`);
    [7m   [0m [91m                      ~~~~[0m

[0m[7m[1m[31m FAIL [39m[22m[27m[0m [2mserver/__tests__/functional/[22m[1mchat-location.test.ts[22m
  [1m● [22mTest suite failed to run

    [96mserver/__tests__/functional/chat-location.test.ts[0m:[93m92[0m:[93m34[0m - [91merror[0m[90m TS2339: [0mProperty 'getReader' does not exist on type 'ReadableStream'.

    [7m92[0m     const reader = response.body.getReader();
    [7m  [0m [91m                                 ~~~~~~~~~[0m
    [96mserver/__tests__/functional/chat-location.test.ts[0m:[93m120[0m:[93m7[0m - [91merror[0m[90m TS2322: [0mType '{ name: string; lat: number; lng: number; placeType?: string | undefined; } | null' is not assignable to type '{ name: string; lat: number; lng: number; placeType?: string | undefined; } | undefined'.
      Type 'null' is not assignable to type '{ name: string; lat: number; lng: number; placeType?: string | undefined; } | undefined'.

    [7m120[0m       location: locationData,
    [7m   [0m [91m      ~~~~~~~~[0m

      [96mserver/__tests__/functional/chat-location.test.ts[0m:[93m68[0m:[93m3[0m
        [7m68[0m   location?: { name: string; lat: number; lng: number; placeType?: string };
        [7m  [0m [96m  ~~~~~~~~[0m
        The expected type comes from property 'location' which is declared here on type '{ success: boolean; location?: { name: string; lat: number; lng: number; placeType?: string | undefined; } | undefined; error?: string | undefined; textResponses?: string[] | undefined; }'

[0m[7m[1m[31m FAIL [39m[22m[27m[0m [2mserver/__tests__/functional/[22m[1mai-location-detection.test.ts[22m
  [1m● [22mTest suite failed to run

    [96mserver/__tests__/functional/ai-location-detection.test.ts[0m:[93m102[0m:[93m34[0m - [91merror[0m[90m TS2339: [0mProperty 'getReader' does not exist on type 'ReadableStream'.

    [7m102[0m     const reader = response.body.getReader();
    [7m   [0m [91m                                 ~~~~~~~~~[0m

[1mTest Suites: [22m[1m[31m7 failed[39m[22m, 7 total
[1mTests:       [22m0 total
[1mSnapshots:   [22m0 total
[1mTime:[22m        68.564 s
[2mRan all test suites[22m[2m.[22m
