============ Test Output ============
[0m[7m[1m[31m FAIL [39m[22m[27m[0m [2mserver/__tests__/unit/[22m[1mchat-api.test.ts[22m
  [1m● [22mTest suite failed to run

    A jest worker process (pid=7920) was terminated by another process: signal=SIGKILL, exitCode=null. Operating system logs may contain more information on why this occurred.

      [2mat ChildProcessWorker._onExit ([22mnode_modules/jest-worker/build/workers/ChildProcessWorker.js[2m:370:23)[22m

[0m[7m[1m[32m PASS [39m[22m[27m[0m [2mserver/__tests__/unit/[22m[1mtest-utils.test.ts[22m ([0m[1m[41m55.668 s[49m[22m[0m)
  Test Utilities
    [32m✓[39m [2massertType should correctly type cast unknown data (36 ms)[22m
    [32m✓[39m [2mparseApiResponse should correctly type cast responses[22m

[33mwarn[39m: JSON parse error {"data":"{\"type\":\"text\",\"data\":Hello world}","error":"Unexpected token 'H', ...\"t\",\"data\":Hello worl\"... is not valid JSON","service":"travel-booking-service","timestamp":"2025-04-19T18:13:21.504Z"}
[33mwarn[39m: JSON parse error {"data":"{\"type\":text,\"data\"\"Hello world\"}","error":"Unexpected token 'e', \"{\"type\":text,\"data\"\"... is not valid JSON","service":"travel-booking-service","timestamp":"2025-04-19T18:13:21.536Z"}
[0m[7m[1m[31m FAIL [39m[22m[27m[0m [2mserver/__tests__/unit/[22m[1mstream-parser.test.ts[22m ([0m[1m[41m55.915 s[49m[22m[0m)
  Stream Parser
    safeParseSSEData
      [32m✓[39m [2mshould parse valid JSON data (7 ms)[22m
      [32m✓[39m [2mshould handle the DONE marker (1 ms)[22m
      [32m✓[39m [2mshould handle invalid JSON (12 ms)[22m
      [32m✓[39m [2mshould handle non-JSON format[22m
      [32m✓[39m [2mshould handle empty data (1 ms)[22m
      [32m✓[39m [2mshould detect unbalanced JSON structure (1 ms)[22m
    processStreamChunk
      [32m✓[39m [2mshould process a valid chunk with a text response (1 ms)[22m
      [32m✓[39m [2mshould process a valid chunk with a location response (1 ms)[22m
      [31m✕[39m [2mshould handle a done marker (8 ms)[22m
      [32m✓[39m [2mshould handle invalid data (1 ms)[22m
      [32m✓[39m [2mshould handle missing required properties (1 ms)[22m

[1m[31m  [1m● [22m[1mStream Parser › processStreamChunk › should handle a done marker[39m[22m

    [2mexpect([22m[31mreceived[39m[2m).[22mtoBe[2m([22m[32mexpected[39m[2m) // Object.is equality[22m

    Expected: [32mtrue[39m
    Received: [31mfalse[39m
[2m[22m
[2m    [0m [90m 91 |[39m       [36mconst[39m result [33m=[39m processStreamChunk(chunk)[33m;[39m[22m
[2m     [90m 92 |[39m       [22m
[2m    [31m[1m>[22m[2m[39m[90m 93 |[39m       expect(result[33m.[39misValid)[33m.[39mtoBe([36mtrue[39m)[33m;[39m[22m
[2m     [90m    |[39m                              [31m[1m^[22m[2m[39m[22m
[2m     [90m 94 |[39m       expect(result[33m.[39mchunk)[33m.[39mtoEqual({[22m
[2m     [90m 95 |[39m         type[33m:[39m [32m'control'[39m[33m,[39m[22m
[2m     [90m 96 |[39m         action[33m:[39m [32m'done'[39m[0m[22m
[2m[22m
[2m      [2mat Object.<anonymous> ([22m[2m[0m[36mserver/__tests__/unit/stream-parser.test.ts[39m[0m[2m:93:30)[22m[2m[22m

[0m[7m[1m[31m FAIL [39m[22m[27m[0m [2mserver/__tests__/unit/[22m[1mstream-parser-enhanced.test.ts[22m ([0m[1m[41m55.954 s[49m[22m[0m)
  Enhanced Stream Parser
    safeParseSSEData with complex payloads
      [32m✓[39m [2mshould parse location data with complex nested structure (7 ms)[22m
      [32m✓[39m [2mshould parse property recommendations with extended data (1 ms)[22m
      [32m✓[39m [2mshould parse action data with complex action structures[22m
      [32m✓[39m [2mshould parse multi-line text response[22m
      [32m✓[39m [2mshould handle JSON with special characters and unicode (1 ms)[22m
      [31m✕[39m [2mshould detect and handle truncated JSON (5 ms)[22m
    processStreamChunk with type validation
      [32m✓[39m [2mshould validate and process location responses (1 ms)[22m
      [32m✓[39m [2mshould validate and process text responses (1 ms)[22m
      [32m✓[39m [2mshould validate and process property responses (2 ms)[22m
      [32m✓[39m [2mshould validate and process action responses (1 ms)[22m
      [32m✓[39m [2mshould identify missing required data fields[22m
      [32m✓[39m [2mshould identify invalid response types (1 ms)[22m
      [31m✕[39m [2mshould handle chunked responses in sequence (1 ms)[22m

[1m[31m  [1m● [22m[1mEnhanced Stream Parser › safeParseSSEData with complex payloads › should detect and handle truncated JSON[39m[22m

    [2mexpect([22m[31mreceived[39m[2m).[22mtoBe[2m([22m[32mexpected[39m[2m) // Object.is equality[22m

    Expected: [32m"Unbalanced JSON structure"[39m
    Received: [31m"Not JSON format: {\"type\":\"text\",\"data..."[39m
[2m[22m
[2m    [0m [90m 115 |[39m       [22m
[2m     [90m 116 |[39m       expect(result[33m.[39mvalid)[33m.[39mtoBe([36mfalse[39m)[33m;[39m[22m
[2m    [31m[1m>[22m[2m[39m[90m 117 |[39m       expect(result[33m.[39minvalidReason)[33m.[39mtoBe([32m'Unbalanced JSON structure'[39m)[33m;[39m[22m
[2m     [90m     |[39m                                    [31m[1m^[22m[2m[39m[22m
[2m     [90m 118 |[39m     })[33m;[39m[22m
[2m     [90m 119 |[39m   })[33m;[39m[22m
[2m     [90m 120 |[39m   [0m[22m
[2m[22m
[2m      [2mat Object.<anonymous> ([22m[2m[0m[36mserver/__tests__/unit/stream-parser-enhanced.test.ts[39m[0m[2m:117:36)[22m[2m[22m

[1m[31m  [1m● [22m[1mEnhanced Stream Parser › processStreamChunk with type validation › should handle chunked responses in sequence[39m[22m

    [2mexpect([22m[31mreceived[39m[2m).[22mtoBe[2m([22m[32mexpected[39m[2m) // Object.is equality[22m

    Expected: [32mtrue[39m
    Received: [31mfalse[39m
[2m[22m
[2m    [0m [90m 252 |[39m       [90m// All chunks should be valid[39m[22m
[2m     [90m 253 |[39m       results[33m.[39mforEach((result[33m,[39m index) [33m=>[39m {[22m
[2m    [31m[1m>[22m[2m[39m[90m 254 |[39m         expect(result[33m.[39misValid)[33m.[39mtoBe([36mtrue[39m)[33m;[39m[22m
[2m     [90m     |[39m                                [31m[1m^[22m[2m[39m[22m
[2m     [90m 255 |[39m         [22m
[2m     [90m 256 |[39m         [36mif[39m (index [33m===[39m chunks[33m.[39mlength [33m-[39m [35m1[39m) {[22m
[2m     [90m 257 |[39m           [90m// Last chunk should be DONE marker[39m[0m[22m
[2m[22m
[2m      [2mat [22m[2m[0m[36mserver/__tests__/unit/stream-parser-enhanced.test.ts[39m[0m[2m:254:32[22m[2m[22m
[2m          at Array.forEach (<anonymous>)[22m
[2m      [2mat Object.<anonymous> ([22m[2m[0m[36mserver/__tests__/unit/stream-parser-enhanced.test.ts[39m[0m[2m:253:15)[22m[2m[22m

[0m[7m[1m[31m FAIL [39m[22m[27m[0m [2mserver/__tests__/unit/[22m[1mopenai.test.ts[22m
  [1m● [22mTest suite failed to run

    [96mserver/__tests__/unit/openai.test.ts[0m:[93m24[0m:[93m43[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ data: { id: string; }[]; }' is not assignable to parameter of type 'never'.

    [7m24[0m         list: jest.fn().mockResolvedValue({ data: [{ id: 'test-model' }] })
    [7m  [0m [91m                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m
    [96mserver/__tests__/unit/openai.test.ts[0m:[93m58[0m:[93m9[0m - [91merror[0m[90m TS2322: [0mType 'string[]' is not assignable to type 'string'.

    [7m58[0m         amenities: ['pool', 'spa', 'restaurant'],
    [7m  [0m [91m        ~~~~~~~~~[0m

      [96mnode_modules/drizzle-orm/utils.d.cts[0m:[93m11[0m:[93m27[0m
        [7m11[0m export type Simplify<T> = {
        [7m  [0m [96m                          ~[0m
        [7m12[0m     [K in keyof T]: T[K];
        [7m  [0m [96m~~~~~~~~~~~~~~~~~~~~~~~~~[0m
        [7m13[0m } & {};
        [7m  [0m [96m~[0m
        The expected type comes from property 'amenities' which is declared here on type '{ id: number; externalId: string | null; name: string; description: string | null; latitude: number; longitude: number; address: string; city: string; state: string | null; country: string; ... 11 more ...; featured: boolean | null; }'
    [96mserver/__tests__/unit/openai.test.ts[0m:[93m59[0m:[93m9[0m - [91merror[0m[90m TS2322: [0mType 'string[]' is not assignable to type 'string'.

    [7m59[0m         images: ['image1.jpg', 'image2.jpg'],
    [7m  [0m [91m        ~~~~~~[0m

      [96mnode_modules/drizzle-orm/utils.d.cts[0m:[93m11[0m:[93m27[0m
        [7m11[0m export type Simplify<T> = {
        [7m  [0m [96m                          ~[0m
        [7m12[0m     [K in keyof T]: T[K];
        [7m  [0m [96m~~~~~~~~~~~~~~~~~~~~~~~~~[0m
        [7m13[0m } & {};
        [7m  [0m [96m~[0m
        The expected type comes from property 'images' which is declared here on type '{ id: number; externalId: string | null; name: string; description: string | null; latitude: number; longitude: number; address: string; city: string; state: string | null; country: string; ... 11 more ...; featured: boolean | null; }'
    [96mserver/__tests__/unit/openai.test.ts[0m:[93m81[0m:[93m9[0m - [91merror[0m[90m TS2322: [0mType 'string[]' is not assignable to type 'string'.

    [7m81[0m         amenities: ['wifi', 'breakfast', 'bar'],
    [7m  [0m [91m        ~~~~~~~~~[0m

      [96mnode_modules/drizzle-orm/utils.d.cts[0m:[93m11[0m:[93m27[0m
        [7m11[0m export type Simplify<T> = {
        [7m  [0m [96m                          ~[0m
        [7m12[0m     [K in keyof T]: T[K];
        [7m  [0m [96m~~~~~~~~~~~~~~~~~~~~~~~~~[0m
        [7m13[0m } & {};
        [7m  [0m [96m~[0m
        The expected type comes from property 'amenities' which is declared here on type '{ id: number; externalId: string | null; name: string; description: string | null; latitude: number; longitude: number; address: string; city: string; state: string | null; country: string; ... 11 more ...; featured: boolean | null; }'
    [96mserver/__tests__/unit/openai.test.ts[0m:[93m82[0m:[93m9[0m - [91merror[0m[90m TS2322: [0mType 'string[]' is not assignable to type 'string'.

    [7m82[0m         images: ['image3.jpg', 'image4.jpg'],
    [7m  [0m [91m        ~~~~~~[0m

      [96mnode_modules/drizzle-orm/utils.d.cts[0m:[93m11[0m:[93m27[0m
        [7m11[0m export type Simplify<T> = {
        [7m  [0m [96m                          ~[0m
        [7m12[0m     [K in keyof T]: T[K];
        [7m  [0m [96m~~~~~~~~~~~~~~~~~~~~~~~~~[0m
        [7m13[0m } & {};
        [7m  [0m [96m~[0m
        The expected type comes from property 'images' which is declared here on type '{ id: number; externalId: string | null; name: string; description: string | null; latitude: number; longitude: number; address: string; city: string; state: string | null; country: string; ... 11 more ...; featured: boolean | null; }'
    [96mserver/__tests__/unit/openai.test.ts[0m:[93m134[0m:[93m87[0m - [91merror[0m[90m TS2345: [0mArgument of type 'string' is not assignable to parameter of type 'string & ChatCompletionContentPartText'.
      Type 'string' is not assignable to type 'ChatCompletionContentPartText'.

    [7m134[0m       if (params.messages[0].role === 'system' && params.messages[0].content.includes('You are RoomLamAI')) {
    [7m   [0m [91m                                                                                      ~~~~~~~~~~~~~~~~~~~[0m
    [96mserver/__tests__/unit/openai.test.ts[0m:[93m146[0m:[93m94[0m - [91merror[0m[90m TS2345: [0mArgument of type 'string' is not assignable to parameter of type 'string & ChatCompletionContentPartText'.
      Type 'string' is not assignable to type 'ChatCompletionContentPartText'.

    [7m146[0m       } else if (params.messages[0].role === 'system' && params.messages[0].content.includes('conversation summarizer')) {
    [7m   [0m [91m                                                                                             ~~~~~~~~~~~~~~~~~~~~~~~~~[0m
    [96mserver/__tests__/unit/openai.test.ts[0m:[93m307[0m:[93m51[0m - [91merror[0m[90m TS2345: [0mArgument of type '{ role: string; content: string; }' is not assignable to parameter of type 'Omit<ChatMessage, "timestamp">'.
      Types of property 'role' are incompatible.
        Type 'string' is not assignable to type '"user" | "assistant"'.

    [7m307[0m       addMessageToConversation('test-session-id', newMessage);
    [7m   [0m [91m                                                  ~~~~~~~~~~[0m

[1mTest Suites: [22m[1m[31m4 failed[39m[22m, [1m[32m1 passed[39m[22m, 5 total
[1mTests:       [22m[1m[31m3 failed[39m[22m, [1m[32m23 passed[39m[22m, 26 total
[1mSnapshots:   [22m0 total
[1mTime:[22m        56.551 s
[2mRan all test suites[22m[2m.[22m
