============ Test Output ============
[31mError: [31m[1m[1m● [22m[1mValidation Error[22m:[39m[31m[39m
[31m[31m[39m[31m[39m
[31m[31m  Test environment [1mjest-environment-jsdom[22m cannot be found. Make sure the [1mtestEnvironment[22m configuration option points to an existing node module.[39m[31m[39m
[31m[31m[39m[31m[39m
[31m[31m  [1mConfiguration Documentation:[22m[39m[31m[39m
[31m[31m  https://jestjs.io/docs/configuration[39m[31m[39m
[31m[31m[39m[31m[39m
[31m[39m
[31mAs of Jest 28 "jest-environment-jsdom" is no longer shipped by default, make sure to install it separately.[39m
