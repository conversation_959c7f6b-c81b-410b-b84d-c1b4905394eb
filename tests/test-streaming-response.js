#!/usr/bin/env node

import http from 'http';

async function testStreamingChat() {
  return new Promise((resolve, reject) => {
    const testMessage = "Surprise me with amazing destinations";
    const postData = JSON.stringify({ 
      message: testMessage,
      sessionId: `test-${Date.now()}`,
      extractLocation: true
    });
    
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/chat',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    const req = http.request(options, (res) => {
      console.log('Response Status:', res.statusCode);
      console.log('Response Headers:', res.headers);
      
      let fullResponse = '';
      let chunkCount = 0;
      
      res.on('data', (chunk) => {
        chunkCount++;
        const chunkStr = chunk.toString();
        fullResponse += chunkStr;
        
        console.log(`\n--- Chunk #${chunkCount} ---`);
        console.log(chunkStr);
      });
      
      res.on('end', () => {
        console.log('\n=== STREAM COMPLETE ===');
        console.log('Total chunks received:', chunkCount);
        console.log('Full response length:', fullResponse.length);
        
        // Parse SSE data
        const lines = fullResponse.split('\n');
        const dataLines = lines.filter(line => line.startsWith('data: '));
        
        console.log('\n📊 SSE Data Analysis:');
        console.log('Total data lines:', dataLines.length);
        
        let textContent = '';
        let actionCount = 0;
        
        dataLines.forEach((line, i) => {
          const data = line.slice(5).trim();
          if (!data || data === '[DONE]' || data === 'DONE') return;
          
          try {
            const parsed = JSON.parse(data);
            console.log(`Data line ${i + 1}:`, parsed.type || 'unknown type');
            
            if (parsed.type === 'text') {
              textContent += parsed.data;
            }
          } catch (e) {
            console.log(`Data line ${i + 1}: Parse error -`, data.substring(0, 50) + '...');
          }
        });
        
        // Check for ACTION tokens in the accumulated text
        const actionRegex = /\[ACTION:(LOCATION|PROPERTY|SEARCH|FILTER)\|([^|]+)\|(\{[^}]+\})\]/g;
        const actionMatches = textContent.match(actionRegex) || [];
        
        console.log('\n🎯 ACTION Token Analysis:');
        console.log('Full text content length:', textContent.length);
        console.log('ACTION tokens found:', actionMatches.length);
        actionMatches.forEach((action, i) => {
          console.log(`  ${i + 1}. ${action}`);
        });
        
        console.log('\n📄 Text Content Preview (first 300 chars):');
        console.log('"' + textContent.substring(0, 300) + '"');
        
        resolve({
          statusCode: res.statusCode,
          chunkCount,
          textContent,
          actionTokens: actionMatches
        });
      });
    });
    
    req.on('error', (err) => {
      console.error('Request error:', err);
      reject(err);
    });
    
    req.write(postData);
    req.end();
  });
}

async function runStreamingTest() {
  console.log('🧪 Testing Streaming Chat API...\n');
  
  try {
    const result = await testStreamingChat();
    
    console.log('\n✅ Test Results Summary:');
    console.log('Status Code:', result.statusCode);
    console.log('Chunks Received:', result.chunkCount);
    console.log('Text Content Length:', result.textContent.length);
    console.log('ACTION Tokens Found:', result.actionTokens.length);
    
    if (result.statusCode === 200 && result.textContent.length > 0) {
      console.log('🎉 Streaming API working correctly!');
      
      if (result.actionTokens.length > 0) {
        console.log('🎯 ACTION tokens detected - frontend should parse these into interactive buttons!');
      } else {
        console.log('⚠️  No ACTION tokens found - AI may not be generating them consistently.');
      }
      
      return true;
    } else {
      console.log('❌ Issues detected with streaming API');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Streaming test failed:', error.message);
    return false;
  }
}

runStreamingTest()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(err => {
    console.error('Test execution failed:', err);
    process.exit(1);
  }); 