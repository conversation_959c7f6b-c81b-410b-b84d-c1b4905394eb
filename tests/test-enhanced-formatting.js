#!/usr/bin/env node

import http from 'http';

// Test the enhanced formatting and action parsing
async function testEnhancedFormatting() {
  return new Promise((resolve, reject) => {
    const testMessage = "Surprise me with amazing destinations";
    const postData = JSON.stringify({ message: testMessage });
    
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/test-chat',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve(result);
        } catch (e) {
          reject(new Error(`Parse error: ${e.message}, Data: ${data}`));
        }
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.write(postData);
    req.end();
  });
}

// Function to test the action parsing
function testActionParsing(text) {
  const actions = [];
  
  // Same regex as in our frontend
  const actionRegex = /\[ACTION:(LOCATION|PROPERTY|SEARCH|FILTER)\|([^|]+)\|(\{[^}]+\})\]/g;
  let match;
  
  while ((match = actionRegex.exec(text)) !== null) {
    try {
      const [fullMatch, type, label, dataStr] = match;
      const data = JSON.parse(dataStr);
      actions.push({ type, label, data });
    } catch (e) {
      console.error('Failed to parse action:', match, e);
    }
  }
  
  // Remove ACTION tokens from text
  const cleanedText = text
    .replace(actionRegex, '')
    .replace(/\*\*Get Ready for a World of Adventure!\*\*/g, '')
    .replace(/\*\s+/g, '• ')
    .replace(/\n{3,}/g, '\n\n')
    .trim();
  
  return { content: cleanedText, actions };
}

async function runEnhancedTests() {
  console.log('🧪 Running Enhanced AI Chat Formatting Tests...\n');
  
  try {
    const result = await testEnhancedFormatting();
    
    console.log('📊 Test Results:');
    console.log('✅ Status:', result.success ? 'SUCCESS' : 'FAILED');
    console.log('✅ Has helpful content:', result.hasHelpfulContent);
    console.log('✅ Not a dead end:', !result.isDeadEnd);
    
    console.log('\n📄 Response Analysis:');
    console.log('Response length:', result.response.length, 'characters');
    
    // Test our action parsing
    const { content, actions } = testActionParsing(result.response);
    
    console.log('\n🎯 Action Parsing Test:');
    console.log('Raw ACTION tokens found:', actions.length);
    actions.forEach((action, i) => {
      console.log(`  ${i + 1}. ${action.type}: ${action.label} (lat: ${action.data.lat}, lng: ${action.data.lng})`);
    });
    
    // Check for structured content
    const hasBeachSection = result.response.includes('🏖️ Beach Destinations');
    const hasCitySection = result.response.includes('🏙️ City Adventures');
    const hasMountainSection = result.response.includes('🏔️ Mountain Escapes');
    const hasActionSection = result.response.includes('✨ Or tell me');
    
    console.log('\n✅ Structured sections:');
    console.log('  - Beach destinations:', hasBeachSection ? '✅' : '❌');
    console.log('  - City adventures:', hasCitySection ? '✅' : '❌');
    console.log('  - Mountain escapes:', hasMountainSection ? '✅' : '❌');
    console.log('  - Action prompts:', hasActionSection ? '✅' : '❌');
    
    // Check for ACTION tokens (these should be parsed on frontend)
    const hasActionTokens = result.response.includes('[ACTION:');
    console.log('  - Raw ACTION tokens:', hasActionTokens ? `✅ Found ${actions.length}` : '❌ None found');
    
    // Check for list formatting
    const hasBulletPoints = result.response.includes('•');
    console.log('  - Bullet points:', hasBulletPoints ? '✅' : '❌');
    
    console.log('\n🎨 Content Preview (first 200 chars):');
    console.log('"' + result.response.substring(0, 200) + '..."');
    
    console.log('\n🧹 Cleaned Content Preview (first 200 chars):');
    console.log('"' + content.substring(0, 200) + '..."');
    
    console.log('\n🎉 Enhanced formatting test completed!');
    
    if (hasBeachSection && hasCitySection && hasMountainSection && hasActionSection && actions.length > 0) {
      console.log('✅ All formatting improvements detected! Frontend should parse actions correctly.');
      return true;
    } else {
      console.log('⚠️  Some formatting improvements may need attention.');
      console.log('Debug info:');
      console.log('  - hasBeachSection:', hasBeachSection);
      console.log('  - hasCitySection:', hasCitySection);
      console.log('  - hasMountainSection:', hasMountainSection);
      console.log('  - hasActionSection:', hasActionSection);
      console.log('  - actions found:', actions.length);
      return false;
    }
    
  } catch (error) {
    console.error('❌ Enhanced formatting test failed:', error.message);
    return false;
  }
}

runEnhancedTests()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(err => {
    console.error('Test execution failed:', err);
    process.exit(1);
  }); 