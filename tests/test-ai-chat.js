#!/usr/bin/env node

import http from 'http';

const testQueries = [
  {
    name: "Vague inspiration query",
    message: "Surprise me with amazing destinations",
    expectHelpful: true
  },
  {
    name: "No location query", 
    message: "Where should I go?",
    expectHelpful: true
  },
  {
    name: "City query",
    message: "Show me hotels in Paris",
    expectLocation: true
  }
];

async function testChat(message) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({ message });
    
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/test-chat',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve(result);
        } catch (e) {
          reject(new Error(`Parse error: ${e.message}, Data: ${data}`));
        }
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.write(postData);
    req.end();
  });
}

async function runTests() {
  console.log('🧪 Running AI Chat Validation Tests...\n');
  
  let passed = 0;
  let total = testQueries.length;
  
  for (const test of testQueries) {
    try {
      const result = await testChat(test.message);
      
      let success = true;
      let feedback = [];
      
      if (test.expectHelpful && !result.hasHelpfulContent) {
        success = false;
        feedback.push('Expected helpful content but got dead end');
      }
      
      if (test.expectLocation && !result.hasLocation) {
        success = false;
        feedback.push('Expected location detection');
      }
      
      if (result.isDeadEnd) {
        success = false;
        feedback.push('Response is a dead end');
      }
      
      if (success) {
        console.log(`✅ ${test.name}: PASSED`);
        passed++;
      } else {
        console.log(`❌ ${test.name}: FAILED - ${feedback.join(', ')}`);
        console.log(`   Response: ${result.response.substring(0, 100)}...`);
      }
      
    } catch (error) {
      console.log(`❌ ${test.name}: ERROR - ${error.message}`);
    }
  }
  
  console.log(`\n📊 Results: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! AI chat is working as expected.');
    process.exit(0);
  } else {
    console.log('❌ Some tests failed. Please check the issues above.');
    process.exit(1);
  }
}

runTests().catch(console.error); 