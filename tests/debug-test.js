import http from 'http';

async function testChat() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({ message: 'Surprise me with amazing destinations' });
    
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/test-chat',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    const req = http.request(options, (res) => {
      console.log('Status Code:', res.statusCode);
      console.log('Headers:', res.headers);
      
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log('Raw Response:', data);
        try {
          const result = JSON.parse(data);
          console.log('Parsed:', JSON.stringify(result, null, 2));
          resolve(result);
        } catch (e) {
          console.log('Parse error:', e.message);
          reject(new Error(`Parse error: ${e.message}, Data: ${data}`));
        }
      });
    });
    
    req.on('error', (err) => {
      console.log('Request error:', err);
      reject(err);
    });
    
    req.write(postData);
    req.end();
  });
}

testChat().then(() => process.exit(0)).catch((err) => { console.error(err); process.exit(1); }); 