import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import UnifiedAIChat from '../client/src/components/UnifiedAIChat';

// Mock the hooks and utilities
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}));

vi.mock('wouter', () => ({
  useLocation: () => ['/test', vi.fn()]
}));

vi.mock('@/lib/session', () => ({
  getSessionId: () => 'test-session-id'
}));

// Mock fetch for API calls
global.fetch = vi.fn();

const mockFetch = global.fetch as any;

describe('UnifiedAIChat Component', () => {
  const defaultProps = {
    context: {
      location: 'Miami Beach',
      checkIn: '2024-06-01',
      checkOut: '2024-06-03',
      guests: '4',
      bedrooms: '2',
      groupType: 'family' as const
    },
    variant: 'modal' as const,
    onClose: vi.fn(),
    onNavigate: vi.fn(),
    onPropertySelect: vi.fn(),
    onSearchUpdate: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockFetch.mockClear();
  });

  const renderComponent = (props = {}) => {
    return render(
      <BrowserRouter>
        <UnifiedAIChat {...defaultProps} {...props} />
      </BrowserRouter>
    );
  };

  describe('Initial Rendering', () => {
    it('should render with contextual welcome message', () => {
      renderComponent();
      
      expect(screen.getByText('AI Travel Companion')).toBeInTheDocument();
      expect(screen.getByText(/Miami Beach/)).toBeInTheDocument();
      expect(screen.getByText(/June 1, 2024/)).toBeInTheDocument();
    });

    it('should show context-aware quick actions', () => {
      renderComponent();
      
      expect(screen.getByText('Explore Miami Beach')).toBeInTheDocument();
      expect(screen.getByText('2-Bedroom Options')).toBeInTheDocument();
      expect(screen.getByText('Family-Friendly')).toBeInTheDocument();
    });

    it('should render without welcome message when showWelcome is false', () => {
      renderComponent({ showWelcome: false });
      
      expect(screen.getByText('AI Travel Companion')).toBeInTheDocument();
      expect(screen.queryByText(/Miami Beach/)).not.toBeInTheDocument();
    });
  });

  describe('Message Handling', () => {
    it('should send user messages to AI service', async () => {
      const mockResponse = {
        ok: true,
        body: {
          getReader: () => ({
            read: vi.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode('data: {"type": "text", "data": "Hello! I can help you find accommodations."}\n\n')
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined
              })
          })
        }
      };

      mockFetch.mockResolvedValue(mockResponse);

      renderComponent();
      
      const input = screen.getByPlaceholderText('Ask me anything about travel...');
      const sendButton = screen.getByRole('button', { name: /send/i });

      fireEvent.change(input, { target: { value: 'Find me a family resort' } });
      fireEvent.click(sendButton);

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: expect.stringContaining('Find me a family resort')
        });
      });
    });

    it('should handle property recommendations', async () => {
      const mockProperties = [
        {
          id: 1,
          name: 'Beach Resort',
          type: 'resort',
          bedrooms: 2,
          price: 300,
          currency: 'USD',
          rating: 4.5,
          image: 'resort.jpg',
          highlights: ['Pool', 'Beach Access'],
          reasonForRecommendation: 'Perfect for families'
        }
      ];

      const mockResponse = {
        ok: true,
        body: {
          getReader: () => ({
            read: vi.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(`data: {"type": "properties", "data": ${JSON.stringify(mockProperties)}}\n\n`)
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined
              })
          })
        }
      };

      mockFetch.mockResolvedValue(mockResponse);

      renderComponent();
      
      const input = screen.getByPlaceholderText('Ask me anything about travel...');
      fireEvent.change(input, { target: { value: 'Show me resorts' } });
      fireEvent.click(screen.getByRole('button', { name: /send/i }));

      await waitFor(() => {
        expect(screen.getByText('Beach Resort')).toBeInTheDocument();
        expect(screen.getByText('Perfect for families')).toBeInTheDocument();
        expect(screen.getByText('$300/USD')).toBeInTheDocument();
      });
    });

    it('should handle experience recommendations', async () => {
      const mockExperiences = [
        {
          id: 'exp1',
          name: 'Snorkeling Tour',
          type: 'activity' as const,
          description: 'Explore coral reefs',
          location: 'Miami Beach',
          duration: '3 hours',
          priceRange: '$50-80'
        }
      ];

      const mockResponse = {
        ok: true,
        body: {
          getReader: () => ({
            read: vi.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(`data: {"type": "experience", "data": ${JSON.stringify(mockExperiences)}}\n\n`)
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined
              })
          })
        }
      };

      mockFetch.mockResolvedValue(mockResponse);

      renderComponent();
      
      const input = screen.getByPlaceholderText('Ask me anything about travel...');
      fireEvent.change(input, { target: { value: 'What activities are available?' } });
      fireEvent.click(screen.getByRole('button', { name: /send/i }));

      await waitFor(() => {
        expect(screen.getByText('Snorkeling Tour')).toBeInTheDocument();
        expect(screen.getByText('Explore coral reefs')).toBeInTheDocument();
        expect(screen.getByText('3 hours')).toBeInTheDocument();
        expect(screen.getByText('$50-80')).toBeInTheDocument();
      });
    });

    it('should handle travel insights', async () => {
      const mockInsights = [
        {
          type: 'weather' as const,
          title: 'Perfect Beach Weather',
          content: 'Sunny skies and 80°F temperatures expected',
          importance: 'medium' as const,
          actionable: true
        }
      ];

      const mockResponse = {
        ok: true,
        body: {
          getReader: () => ({
            read: vi.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(`data: {"type": "insight", "data": ${JSON.stringify(mockInsights)}}\n\n`)
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined
              })
          })
        }
      };

      mockFetch.mockResolvedValue(mockResponse);

      renderComponent();
      
      const input = screen.getByPlaceholderText('Ask me anything about travel...');
      fireEvent.change(input, { target: { value: 'What\'s the weather like?' } });
      fireEvent.click(screen.getByRole('button', { name: /send/i }));

      await waitFor(() => {
        expect(screen.getByText('Perfect Beach Weather')).toBeInTheDocument();
        expect(screen.getByText('Sunny skies and 80°F temperatures expected')).toBeInTheDocument();
        expect(screen.getByText('Actionable Tip')).toBeInTheDocument();
      });
    });
  });

  describe('Quick Actions', () => {
    it('should trigger quick actions when clicked', () => {
      renderComponent();
      
      const exploreAction = screen.getByText('Explore Miami Beach');
      fireEvent.click(exploreAction);

      const input = screen.getByPlaceholderText('Ask me anything about travel...');
      expect(input).toHaveValue('Tell me about the best accommodations and experiences in Miami Beach');
    });

    it('should show bedroom-specific quick actions', () => {
      renderComponent({
        context: {
          ...defaultProps.context,
          bedrooms: '4'
        }
      });
      
      expect(screen.getByText('4-Bedroom Options')).toBeInTheDocument();
    });

    it('should show group-type specific quick actions', () => {
      renderComponent({
        context: {
          ...defaultProps.context,
          groupType: 'corporate'
        }
      });
      
      expect(screen.getByText('Business Travel')).toBeInTheDocument();
    });
  });

  describe('Property Interactions', () => {
    it('should call onPropertySelect when property is clicked', async () => {
      const mockProperties = [
        {
          id: 1,
          name: 'Test Resort',
          type: 'resort',
          bedrooms: 2,
          price: 300,
          currency: 'USD',
          rating: 4.5,
          image: 'resort.jpg',
          highlights: ['Pool'],
          reasonForRecommendation: 'Great choice'
        }
      ];

      const mockResponse = {
        ok: true,
        body: {
          getReader: () => ({
            read: vi.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode(`data: {"type": "properties", "data": ${JSON.stringify(mockProperties)}}\n\n`)
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined
              })
          })
        }
      };

      mockFetch.mockResolvedValue(mockResponse);

      renderComponent();
      
      const input = screen.getByPlaceholderText('Ask me anything about travel...');
      fireEvent.change(input, { target: { value: 'Show properties' } });
      fireEvent.click(screen.getByRole('button', { name: /send/i }));

      await waitFor(() => {
        const propertyCard = screen.getByText('Test Resort').closest('div');
        fireEvent.click(propertyCard!);
        
        expect(defaultProps.onPropertySelect).toHaveBeenCalledWith(mockProperties[0]);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'));

      renderComponent();
      
      const input = screen.getByPlaceholderText('Ask me anything about travel...');
      fireEvent.change(input, { target: { value: 'Test message' } });
      fireEvent.click(screen.getByRole('button', { name: /send/i }));

      await waitFor(() => {
        expect(screen.getByText(/encountered an error/)).toBeInTheDocument();
      });
    });

    it('should handle malformed streaming responses', async () => {
      const mockResponse = {
        ok: true,
        body: {
          getReader: () => ({
            read: vi.fn()
              .mockResolvedValueOnce({
                done: false,
                value: new TextEncoder().encode('data: invalid json\n\n')
              })
              .mockResolvedValueOnce({
                done: true,
                value: undefined
              })
          })
        }
      };

      mockFetch.mockResolvedValue(mockResponse);

      renderComponent();
      
      const input = screen.getByPlaceholderText('Ask me anything about travel...');
      fireEvent.change(input, { target: { value: 'Test message' } });
      fireEvent.click(screen.getByRole('button', { name: /send/i }));

      // Should not crash and should handle gracefully
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Ask me anything about travel...')).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('should be keyboard navigable', () => {
      renderComponent();
      
      const input = screen.getByPlaceholderText('Ask me anything about travel...');
      input.focus();
      
      expect(document.activeElement).toBe(input);
      
      fireEvent.keyDown(input, { key: 'Tab' });
      expect(document.activeElement).toBe(screen.getByRole('button', { name: /send/i }));
    });

    it('should have proper ARIA labels', () => {
      renderComponent();
      
      expect(screen.getByRole('textbox')).toHaveAttribute('placeholder', 'Ask me anything about travel...');
      expect(screen.getByRole('button', { name: /send/i })).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    it('should render correctly in different variants', () => {
      const { rerender } = renderComponent({ variant: 'floating' });
      expect(screen.getByText('AI Travel Companion')).toBeInTheDocument();
      
      rerender(
        <BrowserRouter>
          <UnifiedAIChat {...defaultProps} variant="embedded" />
        </BrowserRouter>
      );
      expect(screen.getByText('AI Travel Companion')).toBeInTheDocument();
      
      rerender(
        <BrowserRouter>
          <UnifiedAIChat {...defaultProps} variant="sidebar" />
        </BrowserRouter>
      );
      expect(screen.getByText('AI Travel Companion')).toBeInTheDocument();
    });
  });
});
