import { describe, it, expect, beforeEach, vi } from 'vitest';
import { searchMultiBedroomAccommodations, MultiBedroomSearchParams } from '../server/services/multiBedroomService.js';
import * as travsrvService from '../server/services/travsrv.js';
import * as geocodingService from '../server/services/geocodingService.js';

// Mock external services
vi.mock('../server/services/travsrv.js');
vi.mock('../server/services/geocodingService.js');

describe('Multi-Bedroom Accommodation Service', () => {
  const mockLocationData = {
    name: 'Miami Beach',
    lat: 25.7617,
    lng: -80.1918,
    placeType: 'locality'
  };

  const mockProperties = [
    {
      id: 1,
      externalId: 'villa-001',
      name: 'Luxury 4-Bedroom Beach Villa',
      description: 'Stunning 4-bedroom, 3-bathroom villa with ocean views, full kitchen, private pool, and game room. Perfect for families and groups up to 12 guests.',
      latitude: 25.7617,
      longitude: -80.1918,
      address: '123 Ocean Drive',
      city: 'Miami Beach',
      state: 'FL',
      country: 'USA',
      rating: 4.8,
      reviewCount: 156,
      basePrice: 450,
      currency: 'USD',
      propertyType: 'villa',
      amenities: ['pool', 'kitchen', 'wifi', 'parking', 'bbq', 'game_room', 'family_friendly', 'laundry'],
      images: ['villa1.jpg', 'villa2.jpg', 'villa3.jpg']
    },
    {
      id: 2,
      externalId: 'resort-002',
      name: 'Family Resort with 2-Bedroom Suites',
      description: 'All-inclusive family resort featuring 2-bedroom, 2-bathroom suites with kitchenette, kids club, and multiple pools.',
      latitude: 25.7907,
      longitude: -80.1300,
      address: '456 Resort Boulevard',
      city: 'Miami Beach',
      state: 'FL',
      country: 'USA',
      rating: 4.6,
      reviewCount: 289,
      basePrice: 320,
      currency: 'USD',
      propertyType: 'resort',
      amenities: ['pool', 'kids_club', 'restaurant', 'wifi', 'spa', 'family_friendly'],
      images: ['resort1.jpg', 'resort2.jpg']
    }
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(geocodingService.geocodeLocation).mockResolvedValue(mockLocationData);
    vi.mocked(travsrvService.searchTravSrvProperties).mockResolvedValue(mockProperties);
    vi.mocked(travsrvService.getPropertyAvailability).mockResolvedValue({
      ratePlans: {
        'standard': {
          code: 'standard',
          description: 'Standard Rate',
          commissionStatus: 'active',
          rooms: {
            'room1': {
              rate: 450,
              currency: 'USD',
              description: 'Master bedroom with king bed',
              maxOccupancy: 2
            }
          }
        }
      }
    });
  });

  describe('Basic Multi-Bedroom Search', () => {
    it('should search for multi-bedroom accommodations successfully', async () => {
      const searchParams: MultiBedroomSearchParams = {
        location: 'Miami Beach',
        checkIn: '2024-06-01',
        checkOut: '2024-06-03',
        adults: 6,
        children: 2,
        bedrooms: 3,
        groupType: 'family'
      };

      const results = await searchMultiBedroomAccommodations(searchParams);

      expect(results).toBeDefined();
      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBeGreaterThan(0);

      expect(geocodingService.geocodeLocation).toHaveBeenCalledWith('Miami Beach');
      expect(travsrvService.searchTravSrvProperties).toHaveBeenCalledWith({
        latitude: 25.7617,
        longitude: -80.1918,
        inDate: '2024-06-01',
        outDate: '2024-06-03',
        adults: 6,
        children: 2,
        rooms: 3,
        radius: 25
      });
    });

    it('should filter properties by minimum bedroom requirements', async () => {
      const searchParams: MultiBedroomSearchParams = {
        location: 'Miami Beach',
        checkIn: '2024-06-01',
        checkOut: '2024-06-03',
        adults: 8,
        bedrooms: 3,
        minBedrooms: 3
      };

      const results = await searchMultiBedroomAccommodations(searchParams);

      results.forEach(property => {
        expect(property.accommodations.bedrooms).toBeGreaterThanOrEqual(3);
      });
    });
  });

  describe('Group Type Optimization', () => {
    it('should optimize results for family groups', async () => {
      const searchParams: MultiBedroomSearchParams = {
        location: 'Miami Beach',
        checkIn: '2024-06-01',
        checkOut: '2024-06-03',
        adults: 6,
        children: 3,
        bedrooms: 3,
        groupType: 'family'
      };

      const results = await searchMultiBedroomAccommodations(searchParams);

      const familyResort = results.find(p => p.name.includes('Family Resort'));
      if (familyResort) {
        expect(familyResort.suitabilityScore).toBeGreaterThan(70);
        expect(familyResort.recommendations.bestFor).toContain('Family vacations with children');
      }
    });

    it('should optimize results for corporate groups', async () => {
      const searchParams: MultiBedroomSearchParams = {
        location: 'Miami Beach',
        checkIn: '2024-06-01',
        checkOut: '2024-06-03',
        adults: 8,
        bedrooms: 4,
        groupType: 'corporate'
      };

      const results = await searchMultiBedroomAccommodations(searchParams);

      results.forEach(property => {
        if (property.amenities.wifi) {
          expect(property.suitabilityScore).toBeGreaterThan(60);
        }
      });
    });
  });

  describe('Property Enhancement', () => {
    it('should correctly analyze bedroom configurations', async () => {
      const searchParams: MultiBedroomSearchParams = {
        location: 'Miami Beach',
        checkIn: '2024-06-01',
        checkOut: '2024-06-03',
        adults: 8,
        bedrooms: 4
      };

      const results = await searchMultiBedroomAccommodations(searchParams);

      const villa = results.find(p => p.name.includes('4-Bedroom'));
      if (villa) {
        expect(villa.accommodations.bedrooms).toBe(4);
        expect(villa.accommodations.bathrooms).toBe(3);
        expect(villa.accommodations.maxOccupancy).toBeGreaterThanOrEqual(8);
        expect(villa.accommodations.bedConfiguration).toContain('King Bed');
        expect(villa.accommodations.livingSpaces).toContain('Full Kitchen');
      }
    });

    it('should analyze amenities correctly', async () => {
      const searchParams: MultiBedroomSearchParams = {
        location: 'Miami Beach',
        checkIn: '2024-06-01',
        checkOut: '2024-06-03',
        adults: 6,
        bedrooms: 3
      };

      const results = await searchMultiBedroomAccommodations(searchParams);

      const villa = results.find(p => p.name.includes('Villa'));
      if (villa) {
        expect(villa.amenities.kitchen).toBe(true);
        expect(villa.amenities.pool).toBe(true);
        expect(villa.amenities.wifi).toBe(true);
        expect(villa.amenities.parking).toBe(true);
        expect(villa.amenities.bbq).toBe(true);
        expect(villa.amenities.gameRoom).toBe(true);
        expect(villa.amenities.laundry).toBe(true);
      }
    });

    it('should calculate pricing correctly', async () => {
      const searchParams: MultiBedroomSearchParams = {
        location: 'Miami Beach',
        checkIn: '2024-06-01',
        checkOut: '2024-06-03',
        adults: 6,
        bedrooms: 3
      };

      const results = await searchMultiBedroomAccommodations(searchParams);

      const villa = results.find(p => p.name.includes('Villa'));
      if (villa) {
        expect(villa.pricing.basePrice).toBe(450);
        expect(villa.pricing.currency).toBe('USD');
        expect(villa.pricing.pricePerNight).toBe(450);
        
        const expectedBaseTotal = 450 * 2;
        expect(villa.pricing.totalPrice).toBeGreaterThan(expectedBaseTotal);
        
        expect(villa.pricing.fees.length).toBeGreaterThan(0);
        expect(villa.pricing.fees.some(fee => fee.type === 'cleaning')).toBe(true);
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle geocoding failures gracefully', async () => {
      vi.mocked(geocodingService.geocodeLocation).mockResolvedValue(null);

      const searchParams: MultiBedroomSearchParams = {
        location: 'Unknown Location',
        checkIn: '2024-06-01',
        checkOut: '2024-06-03',
        adults: 4,
        bedrooms: 2
      };

      await expect(searchMultiBedroomAccommodations(searchParams))
        .rejects.toThrow('Unable to find location: Unknown Location');
    });

    it('should handle empty search results', async () => {
      vi.mocked(travsrvService.searchTravSrvProperties).mockResolvedValue([]);

      const searchParams: MultiBedroomSearchParams = {
        location: 'Miami Beach',
        checkIn: '2024-06-01',
        checkOut: '2024-06-03',
        adults: 4,
        bedrooms: 2
      };

      const results = await searchMultiBedroomAccommodations(searchParams);
      expect(results).toEqual([]);
    });
  });

  describe('Performance', () => {
    it('should complete searches within reasonable time', async () => {
      const startTime = Date.now();

      const searchParams: MultiBedroomSearchParams = {
        location: 'Miami Beach',
        checkIn: '2024-06-01',
        checkOut: '2024-06-03',
        adults: 6,
        bedrooms: 3
      };

      await searchMultiBedroomAccommodations(searchParams);

      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(5000);
    });
  });
});
