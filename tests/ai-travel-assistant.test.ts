import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import { app } from '../server/index.js';
import { db } from '../db/index.js';
import { properties, users, reservations } from '../db/schema.js';
import { eq } from 'drizzle-orm';

describe('Ultimate AI Travel Assistant - End-to-End Tests', () => {
  let testUser: any;
  let testProperties: any[];
  let authCookie: string;

  beforeEach(async () => {
    // Create test user
    const userResult = await db.insert(users).values({
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      password: 'hashedpassword'
    }).returning();
    testUser = userResult[0];

    // Create test properties with multi-bedroom options
    const propertyData = [
      {
        externalId: 'test-villa-001',
        name: 'Luxury 4-Bedroom Beach Villa',
        description: 'Stunning 4-bedroom villa with ocean views, full kitchen, private pool, and game room. Perfect for families and groups.',
        latitude: 25.7617,
        longitude: -80.1918,
        address: '123 Ocean Drive',
        city: 'Miami Beach',
        state: 'FL',
        country: 'USA',
        rating: 4.8,
        reviewCount: 156,
        basePrice: 450,
        currency: 'USD',
        propertyType: 'villa',
        amenities: ['pool', 'kitchen', 'wifi', 'parking', 'bbq', 'game_room', 'family_friendly'],
        images: ['villa1.jpg', 'villa2.jpg', 'villa3.jpg']
      },
      {
        externalId: 'test-resort-002',
        name: 'Family Resort with 2-Bedroom Suites',
        description: 'All-inclusive family resort featuring 2-bedroom suites with kitchenette, kids club, and multiple pools.',
        latitude: 25.7907,
        longitude: -80.1300,
        address: '456 Resort Boulevard',
        city: 'Miami Beach',
        state: 'FL',
        country: 'USA',
        rating: 4.6,
        reviewCount: 289,
        basePrice: 320,
        currency: 'USD',
        propertyType: 'resort',
        amenities: ['pool', 'kids_club', 'restaurant', 'wifi', 'spa', 'family_friendly'],
        images: ['resort1.jpg', 'resort2.jpg']
      },
      {
        externalId: 'test-apartment-003',
        name: 'Downtown 3-Bedroom Corporate Apartment',
        description: 'Modern 3-bedroom apartment in business district with high-speed wifi, meeting space, and concierge.',
        latitude: 25.7753,
        longitude: -80.1937,
        address: '789 Business Center',
        city: 'Miami',
        state: 'FL',
        country: 'USA',
        rating: 4.4,
        reviewCount: 94,
        basePrice: 280,
        currency: 'USD',
        propertyType: 'apartment',
        amenities: ['wifi', 'business_center', 'concierge', 'parking', 'kitchen'],
        images: ['apt1.jpg', 'apt2.jpg']
      }
    ];

    const insertedProperties = await db.insert(properties).values(propertyData).returning();
    testProperties = insertedProperties;

    // Simulate authentication
    authCookie = 'session=test-session-id';
  });

  afterEach(async () => {
    // Cleanup
    await db.delete(reservations).where(eq(reservations.userId, testUser.id));
    await db.delete(properties).where(eq(properties.externalId, 'test-villa-001'));
    await db.delete(properties).where(eq(properties.externalId, 'test-resort-002'));
    await db.delete(properties).where(eq(properties.externalId, 'test-apartment-003'));
    await db.delete(users).where(eq(users.id, testUser.id));
  });

  describe('AI Chat Intelligence', () => {
    it('should understand multi-bedroom accommodation requests', async () => {
      const response = await request(app)
        .post('/api/chat')
        .set('Cookie', authCookie)
        .send({
          message: 'I need a 4-bedroom villa in Miami Beach for my family reunion with 12 people',
          context: {
            conversation: { summary: '', messages: [] },
            extractLocation: true,
            enhancedMode: true
          },
          sessionId: 'test-session'
        });

      expect(response.status).toBe(200);
      
      // Should detect location
      expect(response.body).toContain('Miami Beach');
      
      // Should understand group size and bedroom requirements
      expect(response.body.toLowerCase()).toContain('4-bedroom');
      expect(response.body.toLowerCase()).toContain('family');
      
      // Should provide relevant recommendations
      expect(response.body.toLowerCase()).toContain('villa');
    });

    it('should provide contextual recommendations based on group type', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({
          message: 'Planning a corporate retreat for 20 people in Miami',
          context: {
            conversation: { summary: '', messages: [] },
            groupType: 'corporate',
            enhancedMode: true
          },
          sessionId: 'test-session'
        });

      expect(response.status).toBe(200);
      
      // Should understand corporate context
      expect(response.body.toLowerCase()).toContain('business');
      expect(response.body.toLowerCase()).toContain('meeting');
      
      // Should suggest appropriate amenities
      expect(response.body.toLowerCase()).toContain('wifi');
    });

    it('should handle streaming responses with action tokens', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({
          message: 'Show me beachfront properties in Miami',
          context: {
            conversation: { summary: '', messages: [] },
            enhancedMode: true
          },
          sessionId: 'test-session'
        });

      expect(response.status).toBe(200);
      expect(response.headers['content-type']).toContain('text/plain');
      
      // Should contain streaming data
      expect(response.text).toContain('data:');
    });
  });

  describe('Multi-Bedroom Search Integration', () => {
    it('should search for multi-bedroom accommodations', async () => {
      const response = await request(app)
        .get('/api/properties/search')
        .query({
          lat: 25.7617,
          lng: -80.1918,
          checkIn: '2024-06-01',
          checkOut: '2024-06-03',
          guests: '8',
          rooms: '4',
          bedrooms: '4',
          groupType: 'family'
        });

      expect(response.status).toBe(200);
      expect(response.body.properties).toBeDefined();
      expect(response.body.properties.length).toBeGreaterThan(0);
      
      // Should include the 4-bedroom villa
      const villa = response.body.properties.find((p: any) => 
        p.name.includes('4-Bedroom Beach Villa')
      );
      expect(villa).toBeDefined();
    });

    it('should filter properties by bedroom count', async () => {
      const response = await request(app)
        .get('/api/properties/search')
        .query({
          lat: 25.7617,
          lng: -80.1918,
          checkIn: '2024-06-01',
          checkOut: '2024-06-03',
          guests: '6',
          bedrooms: '3',
          minBedrooms: '3'
        });

      expect(response.status).toBe(200);
      
      // Should only return properties with 3+ bedrooms
      const properties = response.body.properties;
      properties.forEach((property: any) => {
        const bedroomCount = extractBedroomCount(property.description || property.name);
        expect(bedroomCount).toBeGreaterThanOrEqual(3);
      });
    });

    it('should provide suitability scores for group types', async () => {
      const response = await request(app)
        .get('/api/properties/search')
        .query({
          lat: 25.7617,
          lng: -80.1918,
          checkIn: '2024-06-01',
          checkOut: '2024-06-03',
          guests: '4',
          bedrooms: '2',
          groupType: 'family'
        });

      expect(response.status).toBe(200);
      
      // Family resort should score higher for family groups
      const familyResort = response.body.properties.find((p: any) => 
        p.name.includes('Family Resort')
      );
      
      if (familyResort) {
        expect(familyResort.suitabilityScore).toBeGreaterThan(70);
      }
    });
  });

  describe('Enhanced Property Details', () => {
    it('should provide detailed property information', async () => {
      const property = testProperties[0]; // Villa
      
      const response = await request(app)
        .get(`/api/properties/${property.id}`);

      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        id: property.id,
        name: property.name,
        description: property.description,
        amenities: expect.arrayContaining(['pool', 'kitchen', 'wifi'])
      });
    });

    it('should include bedroom configuration analysis', async () => {
      const response = await request(app)
        .get(`/api/properties/${testProperties[0].id}/details`);

      expect(response.status).toBe(200);
      
      // Should analyze bedroom configuration
      expect(response.body.accommodations).toBeDefined();
      expect(response.body.accommodations.bedrooms).toBe(4);
      expect(response.body.accommodations.maxOccupancy).toBeGreaterThan(8);
    });
  });

  describe('AI Response Quality', () => {
    it('should provide personalized recommendations', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({
          message: 'I have elderly grandparents and young kids traveling together',
          context: {
            conversation: { 
              summary: 'User planning multi-generational family trip',
              messages: []
            },
            groupType: 'multi_generational',
            enhancedMode: true
          },
          sessionId: 'test-session'
        });

      expect(response.status).toBe(200);
      
      // Should understand accessibility needs
      expect(response.body.toLowerCase()).toContain('accessible');
      
      // Should suggest family-friendly amenities
      expect(response.body.toLowerCase()).toContain('family');
    });

    it('should provide local insights and experiences', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({
          message: 'What should we do in Miami Beach with kids?',
          context: {
            conversation: { summary: '', messages: [] },
            location: 'Miami Beach',
            enhancedMode: true
          },
          sessionId: 'test-session'
        });

      expect(response.status).toBe(200);
      
      // Should provide local recommendations
      expect(response.body.toLowerCase()).toContain('beach');
      expect(response.body.toLowerCase()).toContain('family');
    });

    it('should handle complex travel scenarios', async () => {
      const response = await request(app)
        .post('/api/chat')
        .send({
          message: 'Planning a wedding party trip for 30 people, need multiple accommodations near each other',
          context: {
            conversation: { summary: '', messages: [] },
            groupType: 'wedding',
            enhancedMode: true
          },
          sessionId: 'test-session'
        });

      expect(response.status).toBe(200);
      
      // Should understand large group logistics
      expect(response.body.toLowerCase()).toContain('multiple');
      expect(response.body.toLowerCase()).toContain('wedding');
      
      // Should suggest coordination strategies
      expect(response.body.toLowerCase()).toContain('near');
    });
  });

  describe('Performance and Reliability', () => {
    it('should handle concurrent AI requests', async () => {
      const requests = Array(5).fill(null).map(() =>
        request(app)
          .post('/api/chat')
          .send({
            message: 'Find me a vacation rental',
            context: {
              conversation: { summary: '', messages: [] },
              enhancedMode: true
            },
            sessionId: `test-session-${Math.random()}`
          })
      );

      const responses = await Promise.all(requests);
      
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
    });

    it('should maintain conversation context', async () => {
      const sessionId = 'test-context-session';
      
      // First message
      const response1 = await request(app)
        .post('/api/chat')
        .send({
          message: 'I want to visit Miami Beach',
          context: {
            conversation: { summary: '', messages: [] },
            enhancedMode: true
          },
          sessionId
        });

      expect(response1.status).toBe(200);

      // Follow-up message should remember context
      const response2 = await request(app)
        .post('/api/chat')
        .send({
          message: 'Show me 3-bedroom options',
          context: {
            conversation: { 
              summary: 'User wants to visit Miami Beach',
              messages: [
                { role: 'user', content: 'I want to visit Miami Beach' },
                { role: 'assistant', content: response1.body }
              ]
            },
            location: 'Miami Beach',
            enhancedMode: true
          },
          sessionId
        });

      expect(response2.status).toBe(200);
      
      // Should remember Miami Beach context
      expect(response2.body.toLowerCase()).toContain('miami');
      expect(response2.body.toLowerCase()).toContain('3-bedroom');
    });
  });
});

// Helper function to extract bedroom count from text
function extractBedroomCount(text: string): number {
  const match = text.toLowerCase().match(/(\d+)[-\s]*bedroom/);
  return match ? parseInt(match[1]) : 1;
}
