const fetch = require('node-fetch');

async function testAPI() {
  try {
    const response = await fetch('http://localhost:5000/api/search?lat=40.7128&lng=-74.0060&checkIn=2024-07-15&checkOut=2024-07-16&guests=2&rooms=1&pageSize=1');
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    console.log('Response status:', response.status);
    console.log('Total properties:', data.properties?.length || 0);
    
    if (data.properties && data.properties.length > 0) {
      const firstProperty = data.properties[0];
      console.log('\nFirst property:');
      console.log('- ID:', firstProperty.id);
      console.log('- Name:', firstProperty.name);
      console.log('- Images type:', typeof firstProperty.images);
      console.log('- Images array:', Array.isArray(firstProperty.images));
      console.log('- Images length:', firstProperty.images ? firstProperty.images.length : 'N/A');
      console.log('- First image:', firstProperty.images?.[0] || 'None');
      
      if (firstProperty.images && firstProperty.images.length > 0) {
        console.log('\nAll images:');
        firstProperty.images.forEach((img, i) => {
          console.log(`  ${i + 1}:`, typeof img === 'string' ? img : img.url || img);
        });
      }
    }
    
    console.log('\nServer logs check complete');
  } catch (error) {
    console.error('Error:', error.message);
  }
}

testAPI(); 