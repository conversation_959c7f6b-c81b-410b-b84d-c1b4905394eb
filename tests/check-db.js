const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');

const sql = postgres(process.env.DATABASE_URL || 'postgresql://user:password@localhost:5432/replit');
const db = drizzle(sql);

async function checkDatabase() {
  try {
    console.log('Checking database for property images...\n');
    
    // Get a sample of properties
    const result = await sql`
      SELECT id, name, images, amenities, updated_at 
      FROM properties 
      ORDER BY id 
      LIMIT 5
    `;
    
    console.log(`Found ${result.length} properties in database:`);
    
    result.forEach((property, index) => {
      console.log(`\n${index + 1}. Property ID: ${property.id}`);
      console.log(`   Name: ${property.name}`);
      console.log(`   Images type: ${typeof property.images}`);
      console.log(`   Images: ${JSON.stringify(property.images)}`);
      console.log(`   Amenities: ${JSON.stringify(property.amenities)}`);
      console.log(`   Updated: ${property.updated_at}`);
      
      // Check if images need refresh
      const hasEmptyImages = !property.images || (Array.isArray(property.images) && property.images.length === 0);
      const hasEmptyAmenities = !property.amenities || (Array.isArray(property.amenities) && property.amenities.length === 0);
      console.log(`   Needs refresh: ${hasEmptyImages || hasEmptyAmenities ? 'YES' : 'NO'}`);
    });
    
    sql.end();
  } catch (error) {
    console.error('Database error:', error);
    process.exit(1);
  }
}

checkDatabase(); 