<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Real User Experience Test - AI Travel Interface</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .test-scenario {
            margin-bottom: 40px;
            padding: 25px;
            border: 2px solid #e1e5e9;
            border-radius: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        .scenario-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        .scenario-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            margin: 8px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        .test-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        .result-area {
            margin-top: 20px;
            padding: 20px;
            border-radius: 12px;
            background: white;
            border-left: 5px solid #667eea;
            min-height: 100px;
        }
        .location-result {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 15px 0;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
        .loading { border-left-color: #ffc107; }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .chat-simulation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        .message {
            margin: 10px 0;
            padding: 12px 18px;
            border-radius: 18px;
            max-width: 80%;
        }
        .user-message {
            background: #667eea;
            color: white;
            margin-left: auto;
        }
        .ai-message {
            background: white;
            border: 1px solid #e1e5e9;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #333; margin-bottom: 40px;">
            🧪 Real User Experience Testing Suite
        </h1>

        <!-- Scenario 1: Family Vacation Planning -->
        <div class="test-scenario">
            <div class="scenario-header">
                <div class="scenario-icon">👨‍👩‍👧‍👦</div>
                <div>
                    <h3>Family Vacation Planning</h3>
                    <p>Testing: Multi-generational family trip with specific needs</p>
                </div>
            </div>
            <button class="test-button" onclick="testScenario('family', 'We need a 4-bedroom vacation rental in Orlando, Florida for our family reunion with grandparents and kids')">
                🏖️ Test Family Orlando Trip
            </button>
            <button class="test-button" onclick="testScenario('family', 'Find us a place to stay in San Diego with multiple bedrooms near the beach')">
                🌊 Test San Diego Beach Trip
            </button>
            <div id="family-results" class="result-area">
                <p>👆 Click buttons above to test family vacation scenarios</p>
            </div>
        </div>

        <!-- Scenario 2: Business Travel -->
        <div class="test-scenario">
            <div class="scenario-header">
                <div class="scenario-icon">💼</div>
                <div>
                    <h3>Business Travel</h3>
                    <p>Testing: Corporate accommodations and meeting facilities</p>
                </div>
            </div>
            <button class="test-button" onclick="testScenario('business', 'I need business hotels in downtown Chicago for a corporate retreat')">
                🏢 Test Chicago Business
            </button>
            <button class="test-button" onclick="testScenario('business', 'Find conference facilities and accommodations in Austin, Texas')">
                🎯 Test Austin Conference
            </button>
            <div id="business-results" class="result-area">
                <p>👆 Click buttons above to test business travel scenarios</p>
            </div>
        </div>

        <!-- Scenario 3: Adventure Travel -->
        <div class="test-scenario">
            <div class="scenario-header">
                <div class="scenario-icon">🏔️</div>
                <div>
                    <h3>Adventure & Unique Stays</h3>
                    <p>Testing: Unique accommodations and adventure destinations</p>
                </div>
            </div>
            <button class="test-button" onclick="testScenario('adventure', 'Show me unique glamping and treehouse accommodations in Colorado')">
                🏕️ Test Colorado Glamping
            </button>
            <button class="test-button" onclick="testScenario('adventure', 'I want to stay in a castle or historic property in Scotland')">
                🏰 Test Scotland Castles
            </button>
            <div id="adventure-results" class="result-area">
                <p>👆 Click buttons above to test adventure travel scenarios</p>
            </div>
        </div>

        <!-- Scenario 4: International Travel -->
        <div class="test-scenario">
            <div class="scenario-header">
                <div class="scenario-icon">🌍</div>
                <div>
                    <h3>International Destinations</h3>
                    <p>Testing: Global destinations with cultural context</p>
                </div>
            </div>
            <button class="test-button" onclick="testScenario('international', 'Plan my honeymoon in Santorini, Greece with romantic accommodations')">
                💕 Test Santorini Honeymoon
            </button>
            <button class="test-button" onclick="testScenario('international', 'Find luxury ryokans and traditional stays in Kyoto, Japan')">
                🏯 Test Kyoto Traditional
            </button>
            <div id="international-results" class="result-area">
                <p>👆 Click buttons above to test international travel scenarios</p>
            </div>
        </div>

        <!-- Real-time Metrics -->
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-value" id="total-tests">0</div>
                <div>Total Tests</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="success-rate">0%</div>
                <div>Success Rate</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="avg-response-time">0ms</div>
                <div>Avg Response Time</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="locations-found">0</div>
                <div>Locations Found</div>
            </div>
        </div>
    </div>

    <script>
        let testMetrics = {
            totalTests: 0,
            successfulTests: 0,
            totalResponseTime: 0,
            locationsFound: 0
        };

        async function testScenario(category, query) {
            const startTime = Date.now();
            testMetrics.totalTests++;
            
            const resultArea = document.getElementById(`${category}-results`);
            
            // Show loading state
            resultArea.innerHTML = `
                <div class="loading">
                    <h4>🔄 Testing: "${query}"</h4>
                    <p>Analyzing AI response quality, location detection, and user experience...</p>
                    <div class="chat-simulation">
                        <div class="message user-message">${query}</div>
                        <div class="message ai-message">🤔 Thinking...</div>
                    </div>
                </div>
            `;

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: query,
                        context: {
                            groupType: category === 'family' ? 'family' : category === 'business' ? 'corporate' : undefined,
                            bedrooms: category === 'family' ? '4' : '1'
                        },
                        sessionId: `test-${category}`,
                        extractLocation: true,
                        enhancedMode: true
                    }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let foundLocation = false;
                let locationData = null;
                let aiResponse = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || '';

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data === '[DONE]') continue;

                            try {
                                const parsed = JSON.parse(data);
                                
                                if (parsed.type === 'text') {
                                    aiResponse += parsed.data;
                                } else if (parsed.type === 'action' && parsed.data.type === 'location') {
                                    foundLocation = true;
                                    locationData = parsed.data.data;
                                    testMetrics.locationsFound++;
                                } else if (parsed.type === 'location') {
                                    foundLocation = true;
                                    locationData = parsed.data;
                                    testMetrics.locationsFound++;
                                }
                            } catch (error) {
                                // Continue processing
                            }
                        }
                    }
                }

                const responseTime = Date.now() - startTime;
                testMetrics.totalResponseTime += responseTime;

                // Evaluate response quality
                const hasLocationContext = foundLocation && locationData;
                const hasRelevantContent = aiResponse.length > 50;
                const isContextAware = aiResponse.toLowerCase().includes(category) || 
                                     (category === 'family' && aiResponse.toLowerCase().includes('bedroom')) ||
                                     (category === 'business' && aiResponse.toLowerCase().includes('business'));

                if (hasLocationContext && hasRelevantContent) {
                    testMetrics.successfulTests++;
                    
                    resultArea.innerHTML = `
                        <div class="success">
                            <h4>✅ EXCELLENT USER EXPERIENCE</h4>
                            <div class="location-result">
                                <h5>📍 ${locationData.name}</h5>
                                <p>🗺️ Coordinates: ${locationData.lat}, ${locationData.lng}</p>
                                ${locationData.description ? `<p>💫 ${locationData.description}</p>` : ''}
                                <p><strong>⚡ Response Time: ${responseTime}ms</strong></p>
                            </div>
                            <div class="chat-simulation">
                                <div class="message user-message">${query}</div>
                                <div class="message ai-message">${aiResponse.substring(0, 200)}...</div>
                            </div>
                            <p><strong>🎯 Quality Metrics:</strong></p>
                            <ul>
                                <li>✅ Location Detection: Perfect</li>
                                <li>✅ Context Awareness: ${isContextAware ? 'Excellent' : 'Good'}</li>
                                <li>✅ Response Quality: High</li>
                                <li>✅ Geography Integration: Active</li>
                            </ul>
                        </div>
                    `;
                } else {
                    resultArea.innerHTML = `
                        <div class="error">
                            <h4>❌ NEEDS IMPROVEMENT</h4>
                            <p><strong>Issues Found:</strong></p>
                            <ul>
                                <li>${hasLocationContext ? '✅' : '❌'} Location Detection</li>
                                <li>${hasRelevantContent ? '✅' : '❌'} Content Quality</li>
                                <li>${isContextAware ? '✅' : '❌'} Context Awareness</li>
                            </ul>
                            <div class="chat-simulation">
                                <div class="message user-message">${query}</div>
                                <div class="message ai-message">${aiResponse || 'No response received'}</div>
                            </div>
                        </div>
                    `;
                }

            } catch (error) {
                resultArea.innerHTML = `
                    <div class="error">
                        <h4>❌ SYSTEM ERROR</h4>
                        <p>Failed to test scenario: ${error.message}</p>
                        <p><strong>This indicates a critical issue that needs immediate attention!</strong></p>
                    </div>
                `;
            }

            updateMetrics();
        }

        function updateMetrics() {
            document.getElementById('total-tests').textContent = testMetrics.totalTests;
            document.getElementById('success-rate').textContent = 
                testMetrics.totalTests > 0 ? 
                Math.round((testMetrics.successfulTests / testMetrics.totalTests) * 100) + '%' : '0%';
            document.getElementById('avg-response-time').textContent = 
                testMetrics.totalTests > 0 ? 
                Math.round(testMetrics.totalResponseTime / testMetrics.totalTests) + 'ms' : '0ms';
            document.getElementById('locations-found').textContent = testMetrics.locationsFound;
        }
    </script>
</body>
</html>
