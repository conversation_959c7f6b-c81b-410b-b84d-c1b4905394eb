{"repl_description": "An advanced AI-powered accommodation booking platform that revolutionizes travel planning through intelligent technology and comprehensive service integration.\n\nCore Technologies:\n- React NextJS with TypeScript for frontend development\n- Google Maps API with Places Autocomplete for intelligent geospatial search\n- OpenAI API for intelligent travel recommendations\n- Stripe payment gateway for secure transactions\n- Multi-supplier API integration (including TravsrvAPI) for global accommodation options\n- Advanced AI recommendation engine for personalized travel experiences", "repl_description_state": "DESCRIPTION_APPROVED", "repl_stack": "FULLSTACK_JS", "stack_rules": {"name": "fullstack_js", "display_name": "Full-stack JavaScript Website", "tarball": "https://storage.googleapis.com/replit-dev-public/fullstack_js/bbcda9580d937093ca88c81f54e4db21a72e75e6.tar.gz", "directory_structure": "├── client\n│   ├── index.html\n│   └── src\n│       ├── App.tsx\n│       ├── components\n│       │   └── ui\n│       │       ├── accordion.tsx\n│       │       ├── alert-dialog.tsx\n│       │       ├── alert.tsx\n│       │       ├── aspect-ratio.tsx\n│       │       ├── avatar.tsx\n│       │       ├── badge.tsx\n│       │       ├── breadcrumb.tsx\n│       │       ├── button.tsx\n│       │       ├── calendar.tsx\n│       │       ├── card.tsx\n│       │       ├── carousel.tsx\n│       │       ├── chart.tsx\n│       │       ├── checkbox.tsx\n│       │       ├── collapsible.tsx\n│       │       ├── command.tsx\n│       │       ├── context-menu.tsx\n│       │       ├── dialog.tsx\n│       │       ├── drawer.tsx\n│       │       ├── dropdown-menu.tsx\n│       │       ├── form.tsx\n│       │       ├── hover-card.tsx\n│       │       ├── input-otp.tsx\n│       │       ├── input.tsx\n│       │       ├── label.tsx\n│       │       ├── menubar.tsx\n│       │       ├── navigation-menu.tsx\n│       │       ├── pagination.tsx\n│       │       ├── popover.tsx\n│       │       ├── progress.tsx\n│       │       ├── radio-group.tsx\n│       │       ├── resizable.tsx\n│       │       ├── scroll-area.tsx\n│       │       ├── select.tsx\n│       │       ├── separator.tsx\n│       │       ├── sheet.tsx\n│       │       ├── sidebar.tsx\n│       │       ├── skeleton.tsx\n│       │       ├── slider.tsx\n│       │       ├── switch.tsx\n│       │       ├── table.tsx\n│       │       ├── tabs.tsx\n│       │       ├── textarea.tsx\n│       │       ├── toast.tsx\n│       │       ├── toaster.tsx\n│       │       ├── toggle-group.tsx\n│       │       ├── toggle.tsx\n│       │       └── tooltip.tsx\n│       ├── hooks\n│       │   ├── use-mobile.tsx\n│       │   └── use-toast.ts\n│       ├── index.css\n│       ├── lib\n│       │   ├── queryClient.ts\n│       │   └── utils.ts\n│       ├── main.tsx\n│       └── pages\n├── db\n│   ├── index.ts\n│   └── schema.ts\n├── drizzle.config.ts\n├── package-lock.json\n├── package.json\n├── postcss.config.js\n├── server\n│   ├── index.ts\n│   ├── routes.ts\n│   └── vite.ts\n├── tailwind.config.ts\n├── theme.json\n├── tsconfig.json\n└── vite.config.ts\n", "description": "Always follow these guidelines when building a full-stack JavaScript application:\n\nArchitecture\n- Follow modern web application patterns and best-practices.\n- Put as much of the app in the frontend as possible. The backend should only be responsible for data persistence and making API calls.\n- If the app is complex and requires functionality that can't be done in a single request, it is okay to stub out the backend and implement the frontend first.\n- Always think through and generate the schema/data first to ensure consistency between frontend and backend. Keep the data model as simple as possible.\n\nFrontend\n- Use `wouter` for routing on the frontend.\n  - Minimize the number of routes and pages in the app. If a page can be a modal or a dialog, make it so.\n  - If you need to add a new page, add them to the `client/src/pages` directory and register them in `client/src/App.tsx`.\n  - If there are multiple pages, create either a navbar or a sidebar for navigation. Use the `Link` component or the `useLocation` hook from `wouter` instead of modifying the window directly.\n- For forms, always use shadcn's `useForm` hook and `Form` component from `@/components/ui/form` which wraps `react-hook-form`.\n  - Remember that the form component is controlled, ensure you pass default values to the `useForm` hook.\n- Always use `@tanstack/react-query` when fetching data.\n  - Show a loading or skeleton state while queries or mutations are being made.\n  - For hierarchical or variable query keys use an array for cache segments so invalidation works properly. That is, do queryKey: ['/api/recipes', id] instead of queryKey: [`/api/recipes/${id}`].\n  - Remember to invalidate queries when it makes sense to.\n- Common pitfalls to avoid:\n  - The `useToast` hook is exported from `@/hooks/use-toast`.\n  - You do not need to explicitly import React as Vite has a JSX transformer that does it automatically.\n  - If a form is failing to submit, try logging out `form.formState.errors` to see if there are form validation errors for fields that might not have associated form fields.\n  - Use `import.meta.env.<ENV_VAR>` to access environment variables on the frontend instead of `process.env.<ENV_VAR>`. Note that variables must be prefixed with `VITE_` in order for the env vars to be available on the frontend.\n\nStyling and Theming\n- Overwrite `theme.json` to match the specified design instead of specifying colors in `index.css`\n  - `theme.json` has a schema of the type `{ primary: string, variant: 'professional' | 'tint' | 'vibrant', appearance: 'light' | 'dark' | 'system', radius: number }`\n  - `primary` should be the primary color to use throughout the app.\n- Use the existing shadcn + Tailwind CSS setup wherever possible instead of writing custom components.\n  - Use the `@`-prefixed paths to import shadcn components and hooks.\n- Create a beautiful, functional, and production-ready design.\n  - Create a responsive grid-based design that looks good on mobile, tablet, and desktop.\n  - Use icons from `lucide-react` to signify actions and provide visual cues. Use `react-icons/si` for company logos.\n  - Never use stock images as background images for large page sections like hero areas or full-width divs as they create accessibility and readability issues. Instead, stock images should only be used as foreground content in smaller, contained elements like cards, thumbnails, or article previews where the image directly relates to and enhances the specific content being presented.\n\nBackend\n- When writing backend API routes, add them to the `server/routes.ts` file.\n- Common pitfalls to avoid:\n  - If you add a WebSocket server, make sure that your custom upgrade handler ignores requests with `sec-websocket-protocol` as `vite-hmr`.\n\nDatabase and auth (if applicable)\n- For applications that require a database, use Drizzle as the database ORM with Postgres as the database.\n  - Put all Drizzle models and relations in `db/schema.ts`. Import from the alias `@db/schema`.\n    - Don't forget to explicitly model relations via the `relations` operator from `drizzle-orm`.\n  - Design your data models and relationships to not require user auth unless explicitly requested.\n  - ALWAYS remember to push the schema changes after editing the data model with `npm run db:push`. NEVER manually write SQL migrations.\n  - When writing API routes that require database access, use `drizzle-orm`. For example, `import { db } from \"db\";` and `import { users } from \"@db/schema\";`.\n    - Remember that filter and order operators like `eq`, `and`, `or`, `asc`, `desc` etc. are available in the Drizzle query API as top-level functions rather than methods on the query object. For example, `db.query.notes.findMany({ orderBy: desc(notes.updatedAt) })` or `db.query.notes.findMany({ where: eq(user.id, 1) })`.\n\nRunning the project\n- The workflow named 'Start application' is already setup and runs `npm run dev` which starts an Express server for the backend and a Vite server for the frontend.\n- After making edits, the workflow will automatically be restarted for you.\n\nForbidden changes\n- NEVER modify the existing Vite setup (`server/vite.ts` and `vite.config.ts`)\n  - It is already configured to serve the frontend and backend on the same port and handles all the necessary setup for you. Don't add a proxy to the Vite server.\n  - All the aliases are already set up for you to import, don't modify them.\n- NEVER edit `package.json`:\n  - If you find yourself stuck and need to modify the scripts, ask the user before doing so.\n  - If you need to install packages, use the packager_install_tool.\n", "pre_match_rules": null, "files": [{"file_name": "postcss.config.js", "content": "export default {\n  plugins: {\n    tailwindcss: {},\n    autoprefixer: {},\n  },\n}\n"}, {"file_name": "vite.config.ts", "content": "import { defineConfig } from \"vite\";\nimport react from \"@vitejs/plugin-react\";\nimport themePlugin from \"@replit/vite-plugin-shadcn-theme-json\";\nimport path, { dirname } from \"path\";\nimport runtimeErrorOverlay from \"@replit/vite-plugin-runtime-error-modal\";\nimport { fileURLToPath } from \"url\";\n\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = dirname(__filename);\nexport default defineConfig({\n  plugins: [react(), runtimeErrorOverlay(), themePlugin()],\n  resolve: {\n    alias: {\n      \"@db\": path.resolve(__dirname, \"db\"),\n      \"@\": path.resolve(__dirname, \"client\", \"src\"),\n    },\n  },\n  root: path.resolve(__dirname, \"client\"),\n  build: {\n    outDir: path.resolve(__dirname, \"dist/public\"),\n    emptyOutDir: true,\n  },\n});\n"}, {"file_name": "theme.json", "content": "{\n  \"variant\": \"professional\",\n  \"primary\": \"hsl(222.2 47.4% 11.2%)\",\n  \"appearance\": \"light\",\n  \"radius\": 0.5\n}\n"}, {"file_name": "tailwind.config.ts", "content": "import type { Config } from \"tailwindcss\";\n\nexport default {\n  darkMode: [\"class\"],\n  content: [\"./client/index.html\", \"./client/src/**/*.{js,jsx,ts,tsx}\"],\n  theme: {\n    extend: {\n      borderRadius: {\n        lg: \"var(--radius)\",\n        md: \"calc(var(--radius) - 2px)\",\n        sm: \"calc(var(--radius) - 4px)\",\n      },\n      colors: {\n        background: \"hsl(var(--background))\",\n        foreground: \"hsl(var(--foreground))\",\n        card: {\n          DEFAULT: \"hsl(var(--card))\",\n          foreground: \"hsl(var(--card-foreground))\",\n        },\n        popover: {\n          DEFAULT: \"hsl(var(--popover))\",\n          foreground: \"hsl(var(--popover-foreground))\",\n        },\n        primary: {\n          DEFAULT: \"hsl(var(--primary))\",\n          foreground: \"hsl(var(--primary-foreground))\",\n        },\n        secondary: {\n          DEFAULT: \"hsl(var(--secondary))\",\n          foreground: \"hsl(var(--secondary-foreground))\",\n        },\n        muted: {\n          DEFAULT: \"hsl(var(--muted))\",\n          foreground: \"hsl(var(--muted-foreground))\",\n        },\n        accent: {\n          DEFAULT: \"hsl(var(--accent))\",\n          foreground: \"hsl(var(--accent-foreground))\",\n        },\n        destructive: {\n          DEFAULT: \"hsl(var(--destructive))\",\n          foreground: \"hsl(var(--destructive-foreground))\",\n        },\n        border: \"hsl(var(--border))\",\n        input: \"hsl(var(--input))\",\n        ring: \"hsl(var(--ring))\",\n        chart: {\n          \"1\": \"hsl(var(--chart-1))\",\n          \"2\": \"hsl(var(--chart-2))\",\n          \"3\": \"hsl(var(--chart-3))\",\n          \"4\": \"hsl(var(--chart-4))\",\n          \"5\": \"hsl(var(--chart-5))\",\n        },\n        sidebar: {\n          DEFAULT: \"hsl(var(--sidebar-background))\",\n          foreground: \"hsl(var(--sidebar-foreground))\",\n          primary: \"hsl(var(--sidebar-primary))\",\n          \"primary-foreground\": \"hsl(var(--sidebar-primary-foreground))\",\n          accent: \"hsl(var(--sidebar-accent))\",\n          \"accent-foreground\": \"hsl(var(--sidebar-accent-foreground))\",\n          border: \"hsl(var(--sidebar-border))\",\n          ring: \"hsl(var(--sidebar-ring))\",\n        },\n      },\n      keyframes: {\n        \"accordion-down\": {\n          from: {\n            height: \"0\",\n          },\n          to: {\n            height: \"var(--radix-accordion-content-height)\",\n          },\n        },\n        \"accordion-up\": {\n          from: {\n            height: \"var(--radix-accordion-content-height)\",\n          },\n          to: {\n            height: \"0\",\n          },\n        },\n      },\n      animation: {\n        \"accordion-down\": \"accordion-down 0.2s ease-out\",\n        \"accordion-up\": \"accordion-up 0.2s ease-out\",\n      },\n    },\n  },\n  plugins: [require(\"tailwindcss-animate\"), require(\"@tailwindcss/typography\")],\n} satisfies Config;\n"}, {"file_name": "drizzle.config.ts", "content": "import { defineConfig } from \"drizzle-kit\";\n\nif (!process.env.DATABASE_URL) {\n  throw new Error(\"DATABASE_URL, ensure the database is provisioned\");\n}\n\nexport default defineConfig({\n  out: \"./migrations\",\n  schema: \"./db/schema.ts\",\n  dialect: \"postgresql\",\n  dbCredentials: {\n    url: process.env.DATABASE_URL,\n  },\n});\n"}, {"file_name": "tsconfig.json", "content": "{\n  \"include\": [\"client/src/**/*\", \"db/**/*\", \"server/**/*\"],\n  \"exclude\": [\"node_modules\", \"build\", \"dist\", \"**/*.test.ts\"],\n  \"compilerOptions\": {\n    \"incremental\": true,\n    \"tsBuildInfoFile\": \"./node_modules/typescript/tsbuildinfo\",\n    \"noEmit\": true,\n    \"module\": \"ESNext\",\n    \"strict\": true,\n    \"lib\": [\"esnext\", \"dom\", \"dom.iterable\"],\n    \"jsx\": \"preserve\",\n    \"esModuleInterop\": true,\n    \"skipLibCheck\": true,\n    \"allowImportingTsExtensions\": true,\n    \"moduleResolution\": \"bundler\",\n    \"baseUrl\": \".\",\n    \"types\": [\"node\", \"vite/client\"],\n    \"paths\": {\n      \"@db\": [\"./db/index.ts\"],\n      \"@db/*\": [\"./db/*\"],\n      \"@/*\": [\"./client/src/*\"]\n    }\n  }\n}\n"}, {"file_name": "package.json", "content": "{\n  \"name\": \"rest-express\",\n  \"version\": \"1.0.0\",\n  \"type\": \"module\",\n  \"license\": \"MIT\",\n  \"scripts\": {\n    \"dev\": \"tsx server/index.ts\",\n    \"build\": \"vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist\",\n    \"start\": \"NODE_ENV=production node dist/index.js\",\n    \"check\": \"tsc\",\n    \"db:push\": \"drizzle-kit push\"\n  },\n  \"dependencies\": {\n    \"@hookform/resolvers\": \"^3.9.1\",\n    \"@jridgewell/trace-mapping\": \"^0.3.25\",\n    \"@radix-ui/react-accordion\": \"^1.2.1\",\n    \"@radix-ui/react-alert-dialog\": \"^1.1.2\",\n    \"@radix-ui/react-aspect-ratio\": \"^1.1.0\",\n    \"@radix-ui/react-avatar\": \"^1.1.1\",\n    \"@radix-ui/react-checkbox\": \"^1.1.2\",\n    \"@radix-ui/react-collapsible\": \"^1.1.1\",\n    \"@radix-ui/react-context-menu\": \"^2.2.2\",\n    \"@radix-ui/react-dialog\": \"^1.1.2\",\n    \"@radix-ui/react-dropdown-menu\": \"^2.1.2\",\n    \"@radix-ui/react-hover-card\": \"^1.1.2\",\n    \"@radix-ui/react-label\": \"^2.1.0\",\n    \"@radix-ui/react-menubar\": \"^1.1.2\",\n    \"@radix-ui/react-navigation-menu\": \"^1.2.1\",\n    \"@radix-ui/react-popover\": \"^1.1.2\",\n    \"@radix-ui/react-progress\": \"^1.1.0\",\n    \"@radix-ui/react-radio-group\": \"^1.2.1\",\n    \"@radix-ui/react-scroll-area\": \"^1.2.0\",\n    \"@radix-ui/react-select\": \"^2.1.2\",\n    \"@radix-ui/react-separator\": \"^1.1.0\",\n    \"@radix-ui/react-slider\": \"^1.2.1\",\n    \"@radix-ui/react-slot\": \"^1.1.0\",\n    \"@radix-ui/react-switch\": \"^1.1.1\",\n    \"@radix-ui/react-tabs\": \"^1.1.1\",\n    \"@radix-ui/react-toast\": \"^1.2.2\",\n    \"@radix-ui/react-toggle\": \"^1.1.0\",\n    \"@radix-ui/react-toggle-group\": \"^1.1.0\",\n    \"@radix-ui/react-tooltip\": \"^1.1.3\",\n    \"@replit/vite-plugin-shadcn-theme-json\": \"^0.0.4\",\n    \"@tanstack/react-query\": \"^5.60.5\",\n    \"class-variance-authority\": \"^0.7.0\",\n    \"clsx\": \"^2.1.1\",\n    \"cmdk\": \"^1.0.0\",\n    \"date-fns\": \"^3.6.0\",\n    \"drizzle-orm\": \"^0.38.2\",\n    \"drizzle-zod\": \"^0.6.0\",\n    \"embla-carousel-react\": \"^8.3.0\",\n    \"express\": \"^4.21.2\",\n    \"express-session\": \"^1.18.1\",\n    \"framer-motion\": \"^11.13.1\",\n    \"input-otp\": \"^1.2.4\",\n    \"lucide-react\": \"^0.453.0\",\n    \"memorystore\": \"^1.6.7\",\n    \"passport\": \"^0.7.0\",\n    \"passport-local\": \"^1.0.0\",\n    \"react\": \"^18.3.1\",\n    \"react-day-picker\": \"^8.10.1\",\n    \"react-dom\": \"^18.3.1\",\n    \"react-hook-form\": \"^7.53.1\",\n    \"react-icons\": \"^5.4.0\",\n    \"react-resizable-panels\": \"^2.1.4\",\n    \"recharts\": \"^2.13.0\",\n    \"tailwind-merge\": \"^2.5.4\",\n    \"tailwindcss-animate\": \"^1.0.7\",\n    \"vaul\": \"^1.1.0\",\n    \"wouter\": \"^3.3.5\",\n    \"ws\": \"^8.18.0\",\n    \"zod\": \"^3.23.8\"\n  },\n  \"devDependencies\": {\n    \"@replit/vite-plugin-runtime-error-modal\": \"^0.0.3\",\n    \"@tailwindcss/typography\": \"^0.5.15\",\n    \"@types/express\": \"4.17.21\",\n    \"@types/express-session\": \"^1.18.0\",\n    \"@types/node\": \"20.16.11\",\n    \"@types/passport\": \"^1.0.16\",\n    \"@types/passport-local\": \"^1.0.38\",\n    \"@types/react\": \"^18.3.11\",\n    \"@types/react-dom\": \"^18.3.1\",\n    \"@types/ws\": \"^8.5.13\",\n    \"@vitejs/plugin-react\": \"^4.3.2\",\n    \"autoprefixer\": \"^10.4.20\",\n    \"drizzle-kit\": \"^0.27.1\",\n    \"esbuild\": \"^0.24.0\",\n    \"postcss\": \"^8.4.47\",\n    \"tailwindcss\": \"^3.4.14\",\n    \"tsx\": \"^4.19.1\",\n    \"typescript\": \"5.6.3\",\n    \"vite\": \"^5.4.9\"\n  },\n  \"optionalDependencies\": {\n    \"bufferutil\": \"^4.0.8\"\n  }\n}\n"}, {"file_name": "server/routes.ts", "content": "import type { Express } from \"express\";\nimport { createServer, type Server } from \"http\";\n\nexport function registerRoutes(app: Express): Server {\n  // put application routes here\n  // prefix all routes with /api\n\n  const httpServer = createServer(app);\n\n  return httpServer;\n}\n"}, {"file_name": "server/vite.ts", "content": "import express, { type Express } from \"express\";\nimport fs from \"fs\";\nimport path, { dirname } from \"path\";\nimport { fileURLToPath } from \"url\";\nimport { createServer as createViteServer, createLogger } from \"vite\";\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = dirname(__filename);\nimport { type Server } from \"http\";\nimport viteConfig from \"../vite.config\";\n\nconst viteLogger = createLogger();\n\nexport function log(message: string, source = \"express\") {\n  const formattedTime = new Date().toLocaleTimeString(\"en-US\", {\n    hour: \"numeric\",\n    minute: \"2-digit\",\n    second: \"2-digit\",\n    hour12: true,\n  });\n\n  console.log(`${formattedTime} [${source}] ${message}`);\n}\n\nexport async function setupVite(app: Express, server: Server) {\n  const vite = await createViteServer({\n    ...viteConfig,\n    configFile: false,\n    customLogger: {\n      ...viteLogger,\n      error: (msg, options) => {\n        if (\n          msg.includes(\"[TypeScript] Found 0 errors. Watching for file changes\")\n        ) {\n          log(\"no errors found\", \"tsc\");\n          return;\n        }\n\n        if (msg.includes(\"[TypeScript] \")) {\n          const [errors, summary] = msg.split(\"[TypeScript] \", 2);\n          log(`${summary} ${errors}\\u001b[0m`, \"tsc\");\n          return;\n        } else {\n          viteLogger.error(msg, options);\n          process.exit(1);\n        }\n      },\n    },\n    server: {\n      middlewareMode: true,\n      hmr: { server },\n    },\n    appType: \"custom\",\n  });\n\n  app.use(vite.middlewares);\n  app.use(\"*\", async (req, res, next) => {\n    const url = req.originalUrl;\n\n    try {\n      const clientTemplate = path.resolve(\n        __dirname,\n        \"..\",\n        \"client\",\n        \"index.html\",\n      );\n\n      // always reload the index.html file from disk incase it changes\n      const template = await fs.promises.readFile(clientTemplate, \"utf-8\");\n      const page = await vite.transformIndexHtml(url, template);\n      res.status(200).set({ \"Content-Type\": \"text/html\" }).end(page);\n    } catch (e) {\n      vite.ssrFixStacktrace(e as Error);\n      next(e);\n    }\n  });\n}\n\nexport function serveStatic(app: Express) {\n  const distPath = path.resolve(__dirname, \"public\");\n\n  if (!fs.existsSync(distPath)) {\n    throw new Error(\n      `Could not find the build directory: ${distPath}, make sure to build the client first`,\n    );\n  }\n\n  app.use(express.static(distPath));\n\n  // fall through to index.html if the file doesn't exist\n  app.use(\"*\", (_req, res) => {\n    res.sendFile(path.resolve(distPath, \"index.html\"));\n  });\n}\n"}, {"file_name": "server/index.ts", "content": "import express, { type Request, Response, NextFunction } from \"express\";\nimport { registerRoutes } from \"./routes\";\nimport { setupVite, serveStatic, log } from \"./vite\";\n\nconst app = express();\napp.use(express.json());\napp.use(express.urlencoded({ extended: false }));\n\napp.use((req, res, next) => {\n  const start = Date.now();\n  const path = req.path;\n  let capturedJsonResponse: Record<string, any> | undefined = undefined;\n\n  const originalResJson = res.json;\n  res.json = function (bodyJson, ...args) {\n    capturedJsonResponse = bodyJson;\n    return originalResJson.apply(res, [bodyJson, ...args]);\n  };\n\n  res.on(\"finish\", () => {\n    const duration = Date.now() - start;\n    if (path.startsWith(\"/api\")) {\n      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;\n      if (capturedJsonResponse) {\n        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;\n      }\n\n      if (logLine.length > 80) {\n        logLine = logLine.slice(0, 79) + \"…\";\n      }\n\n      log(logLine);\n    }\n  });\n\n  next();\n});\n\n(async () => {\n  const server = registerRoutes(app);\n\n  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {\n    const status = err.status || err.statusCode || 500;\n    const message = err.message || \"Internal Server Error\";\n\n    res.status(status).json({ message });\n    throw err;\n  });\n\n  // importantly only setup vite in development and after\n  // setting up all the other routes so the catch-all route\n  // doesn't interfere with the other routes\n  if (app.get(\"env\") === \"development\") {\n    await setupVite(app, server);\n  } else {\n    serveStatic(app);\n  }\n\n  // ALWAYS serve the app on port 5000\n  // this serves both the API and the client\n  const PORT = 5000;\n  server.listen(PORT, \"0.0.0.0\", () => {\n    log(`serving on port ${PORT}`);\n  });\n})();\n"}, {"file_name": "db/schema.ts", "content": "import { pgTable, text, serial, integer, boolean } from \"drizzle-orm/pg-core\";\nimport { createInsertSchema, createSelectSchema } from \"drizzle-zod\";\n\nexport const users = pgTable(\"users\", {\n  id: serial(\"id\").primaryKey(),\n  username: text(\"username\").unique().notNull(),\n  password: text(\"password\").notNull(),\n});\n\nexport const insertUserSchema = createInsertSchema(users);\nexport const selectUserSchema = createSelectSchema(users);\nexport type InsertUser = typeof users.$inferInsert;\nexport type SelectUser = typeof users.$inferSelect;\n"}, {"file_name": "db/index.ts", "content": "import { drizzle } from \"drizzle-orm/neon-serverless\";\nimport ws from \"ws\";\nimport * as schema from \"@db/schema\";\n\nif (!process.env.DATABASE_URL) {\n  throw new Error(\n    \"DATABASE_URL must be set. Did you forget to provision a database?\",\n  );\n}\n\nexport const db = drizzle({\n  connection: process.env.DATABASE_URL,\n  schema,\n  ws: ws,\n});\n"}, {"file_name": "client/src/App.tsx", "content": "import { Switch, Route } from \"wouter\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { AlertCircle } from \"lucide-react\";\n\nfunction App() {\n  return (\n    <Switch>\n      {/* Add pages below */}\n      {/* <Route path=\"/\" component={Home}/> */}\n      <Route component={NotFound} />\n    </Switch>\n  );\n}\n\n// fallback 404 not found page\nfunction NotFound() {\n  return (\n    <div className=\"min-h-screen w-full flex items-center justify-center bg-gray-50\">\n      <Card className=\"w-full max-w-md mx-4\">\n        <CardContent className=\"pt-6\">\n          <div className=\"flex mb-4 gap-2\">\n            <AlertCircle className=\"h-8 w-8 text-red-500\" />\n            <h1 className=\"text-2xl font-bold text-gray-900\">404 Page Not Found</h1>\n          </div>\n\n          <p className=\"mt-4 text-sm text-gray-600\">\n            Did you forget to add the page to the router?\n          </p>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n\nexport default App;\n"}, {"file_name": "client/src/index.css", "content": "@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n@layer base {\n  * {\n    @apply border-border;\n  }\n\n  body {\n    @apply font-sans antialiased bg-background text-foreground;\n  }\n}"}, {"file_name": "client/src/main.tsx", "content": "import { StrictMode } from \"react\";\nimport { createRoot } from \"react-dom/client\";\nimport { QueryClientProvider } from \"@tanstack/react-query\";\nimport { queryClient } from \"./lib/queryClient\";\nimport { Toaster } from \"@/components/ui/toaster\";\nimport App from './App';\nimport \"./index.css\";\n\ncreateRoot(document.getElementById(\"root\")!).render(\n  <StrictMode>\n    <QueryClientProvider client={queryClient}>\n      <App />\n      <Toaster />\n    </QueryClientProvider>\n  </StrictMode>,\n);\n"}, {"file_name": "client/src/lib/queryClient.ts", "content": "import { QueryClient } from \"@tanstack/react-query\";\n\nexport const queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      queryFn: async ({ queryKey }) => {\n        const res = await fetch(queryKey[0] as string, {\n          credentials: \"include\",\n        });\n\n        if (!res.ok) {\n          if (res.status >= 500) {\n            throw new Error(`${res.status}: ${res.statusText}`);\n          }\n\n          throw new Error(`${res.status}: ${await res.text()}`);\n        }\n\n        return res.json();\n      },\n      refetchInterval: false,\n      refetchOnWindowFocus: false,\n      staleTime: Infinity,\n      retry: false,\n    },\n    mutations: {\n      retry: false,\n    }\n  },\n});\n"}], "setup_steps": [{"description": "Install the project dependencies", "tool": "packager_install_tool", "args": {"programming_language": "nodejs", "dependency_list": []}}, {"description": "Set the run configuration for the project", "tool": "workflows_set_run_config_tool", "args": {"name": "Start application", "command": "npm run dev", "wait_for_port": 5000}}, {"description": "Setup the database, if needed", "tool": "execute_command", "args": {"command": "[[ -z \"$DATABASE_URL\" ]] || npm run db:push\n"}}], "compatible_selected_stacks": ["FULLSTACK_JS"], "questions": [], "secrets": [], "category": "stack", "flag": null}}