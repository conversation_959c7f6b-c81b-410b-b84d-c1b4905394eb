@import './styles/animations.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

/* Google Places Autocomplete Styles */
.pac-container {
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  margin-top: 0.5rem;
  padding: 0.5rem;
  z-index: 9999;
}

.pac-item {
  padding: 0.5rem;
  cursor: pointer;
  border: none;
  color: hsl(var(--foreground));
  font-family: var(--font-sans);
}

.pac-item:hover {
  background-color: hsl(var(--accent));
}

.pac-item-query {
  color: hsl(var(--foreground));
  font-size: 0.875rem;
}

.pac-matched {
  color: hsl(var(--primary));
  font-weight: 500;
}

.pac-icon {
  display: none;
}

/* Text wrapping utilities for chat content */
@layer utilities {
  .overflow-wrap-anywhere {
    overflow-wrap: anywhere;
    word-break: break-word;
    hyphens: auto;
  }

  .break-words {
    word-break: break-word;
    overflow-wrap: break-word;
  }

  /* Ensure modal content doesn't overflow */
  .modal-content {
    max-width: 100%;
    overflow: hidden;
  }

  .modal-content * {
    max-width: 100%;
    box-sizing: border-box;
  }
}