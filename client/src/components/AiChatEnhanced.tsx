import { useState, useRef, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useMutation } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import { 
  Send, 
  MapPin, 
  Calendar,
  Users,
  DollarSign,
  Star,
  Hotel,
  Sparkles,
  Info,
  TrendingUp,
  AlertCircle,
  ChevronRight,
  Loader2,
  Heart,
  Compass,
  Sun,
  Cloud,
  Plane,
  Car,
  Coffee,
  Utensils,
  ShoppingBag,
  Music,
  Palette,
  Trees,
  Waves,
  Mountain,
  Building,
  Globe,
  Clock,
  Shield,
  Zap,
  Search
} from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { getSessionId } from "@/lib/session";

interface Message {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
  timestamp: Date;
  suggestions?: Suggestion[];
  locations?: LocationSuggestion[];
  properties?: PropertyCard[];
  insights?: TravelInsight[];
  quickActions?: QuickAction[];
}

interface Suggestion {
  id: string;
  text: string;
  icon: React.ReactNode;
  action: () => void;
}

interface LocationSuggestion {
  id: string;
  name: string;
  description: string;
  image: string;
  coordinates: { lat: number; lng: number };
  highlights: string[];
  bestTime: string;
  avgPrice: string;
}

interface PropertyCard {
  id: string;
  name: string;
  image: string;
  rating: number;
  price: number;
  location: string;
  amenities: string[];
  specialOffer?: string;
}

interface TravelInsight {
  type: "tip" | "warning" | "recommendation" | "deal";
  title: string;
  description: string;
  icon: React.ReactNode;
  action?: () => void;
}

interface QuickAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  color: string;
  action: () => void;
}

interface AiChatEnhancedProps {
  context?: {
    location?: string | null;
    checkIn?: string | null;
    checkOut?: string | null;
    guests?: string | null;
    searchHistory?: any[];
  };
  onClose?: () => void;
}

// Travel destinations data
const popularDestinations = [
  {
    id: "1",
    name: "Miami Beach",
    description: "Sun, sand, and vibrant nightlife",
    image: "/images/miami-beach.jpg",
    coordinates: { lat: 25.7907, lng: -80.1300 },
    highlights: ["Beautiful beaches", "Art Deco architecture", "World-class dining"],
    bestTime: "Dec - Apr",
    avgPrice: "$250-400/night"
  },
  {
    id: "2",
    name: "New York City",
    description: "The city that never sleeps",
    image: "/images/nyc.jpg",
    coordinates: { lat: 40.7128, lng: -74.0060 },
    highlights: ["Broadway shows", "Central Park", "Museums & galleries"],
    bestTime: "Apr - Jun, Sep - Nov",
    avgPrice: "$300-500/night"
  },
  {
    id: "3",
    name: "San Francisco",
    description: "Tech hub meets natural beauty",
    image: "/images/sf.jpg",
    coordinates: { lat: 37.7749, lng: -122.4194 },
    highlights: ["Golden Gate Bridge", "Alcatraz", "Cable cars"],
    bestTime: "Sep - Nov",
    avgPrice: "$280-450/night"
  }
];

export default function AiChatEnhanced({ context, onClose }: AiChatEnhancedProps) {
  const { toast } = useToast();
  const [_, navigate] = useLocation();
  const [input, setInput] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [sessionId] = useState(() => getSessionId());
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [isMaximized, setIsMaximized] = useState(false);

  // Initialize with a welcoming message
  useEffect(() => {
    const welcomeMessage: Message = {
      id: "welcome",
      role: "system",
      content: "Welcome to your AI Travel Companion! 🌍✨",
      timestamp: new Date(),
      quickActions: [
        {
          id: "explore",
          label: "Explore Destinations",
          icon: <Compass className="w-4 h-4" />,
          color: "bg-blue-500",
          action: () => handleQuickAction("Show me popular travel destinations")
        },
        {
          id: "plan",
          label: "Plan My Trip",
          icon: <Calendar className="w-4 h-4" />,
          color: "bg-purple-500",
          action: () => handleQuickAction("Help me plan my next vacation")
        },
        {
          id: "deals",
          label: "Find Deals",
          icon: <TrendingUp className="w-4 h-4" />,
          color: "bg-green-500",
          action: () => handleQuickAction("Show me the best travel deals")
        },
        {
          id: "inspire",
          label: "Inspire Me",
          icon: <Sparkles className="w-4 h-4" />,
          color: "bg-yellow-500",
          action: () => handleQuickAction("Surprise me with amazing destinations")
        }
      ],
      insights: [
        {
          type: "tip",
          title: "Travel Smarter",
          description: "I can help you find hidden gems, compare prices, and plan the perfect itinerary!",
          icon: <Zap className="w-5 h-5 text-yellow-500" />
        }
      ]
    };
    // Always just show the welcome message on mount
    setMessages([welcomeMessage]);
  }, [context]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Force re-render when message content changes during streaming
  useEffect(() => {
    // This ensures formatAIResponse is called as content streams in
    const lastMessage = messages[messages.length - 1];
    if (lastMessage?.role === 'assistant' && lastMessage.content) {
      // Trigger a re-render to ensure ACTION tokens are parsed
      // No state change needed, just force React to re-evaluate the render
    }
  }, [messages]);

  const handleQuickAction = (text: string) => {
    // Add user message
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: "user",
      content: text,
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInput("");
    setIsTyping(true);
    
    // Send to API
    sendMessage.mutate(text);
  };

  const sendMessage = useMutation({
    mutationFn: async (message: string) => {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message,
          sessionId,
          context: context || {},
          extractLocation: true
        })
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No response stream');

      const decoder = new TextDecoder();
      let assistantContent = '';
      let locationData = null;
      let propertiesData = [];

      // Create initial assistant message for streaming
      const assistantMessageId = `assistant-${Date.now()}`;
      const initialAssistantMessage: Message = {
        id: assistantMessageId,
        role: "assistant",
        content: "",
        timestamp: new Date(),
        suggestions: [
          {
            id: "1",
            text: "Show me more options",
            icon: <Hotel className="w-4 h-4" />,
            action: () => handleQuickAction("Show me more accommodation options")
          },
          {
            id: "2",
            text: "Change dates",
            icon: <Calendar className="w-4 h-4" />,
            action: () => handleQuickAction("I want to change my travel dates")
          },
          {
            id: "3",
            text: "Filter by price",
            icon: <DollarSign className="w-4 h-4" />,
            action: () => handleQuickAction("Show me budget-friendly options")
          }
        ]
      };

      // Add initial message to start streaming
      setMessages(prev => [...prev, initialAssistantMessage]);

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(5).trim();
            // Skip empty data, DONE markers, or whitespace-only content
            if (!data || data === '[DONE]' || data === 'DONE' || data === '{}') continue;
            try {
              const parsed = JSON.parse(data);
              
              if (parsed.type === 'text') {
                assistantContent += parsed.data;
                // Update message in real-time as content streams in
                setMessages(prev => prev.map(msg => 
                  msg.id === assistantMessageId 
                    ? { ...msg, content: assistantContent }
                    : msg
                ));
              } else if (parsed.type === 'location') {
                locationData = parsed.data;
              } else if (parsed.type === 'properties') {
                propertiesData = parsed.data.properties;
              }
            } catch (e) {
              console.error('Parse error for data:', data, e);
              // Continue processing other lines instead of breaking
            }
          }
        }
      }

      return { assistantContent, locationData, propertiesData };
    },
    onSuccess: (data) => {
      const { assistantContent, locationData, propertiesData } = data;
      
      // Find the streaming message and update it with final enhancements
      setMessages(prev => prev.map(msg => {
        if (msg.role === 'assistant' && msg.content === assistantContent) {
          const updatedMessage = { ...msg };

          // Add location suggestions if we detected a location
          if (locationData) {
            const matchingDestinations = popularDestinations.filter(d => 
              d.name.toLowerCase().includes(locationData.name.toLowerCase()) ||
              locationData.name.toLowerCase().includes(d.name.toLowerCase())
            );
            
            if (matchingDestinations.length > 0) {
              updatedMessage.locations = matchingDestinations;
            }
            
            // Add travel insights
            updatedMessage.insights = [
              {
                type: "recommendation",
                title: "Best Time to Visit",
                description: `${locationData.name} is beautiful year-round, but the best weather is typically in spring and fall.`,
                icon: <Sun className="w-5 h-5 text-yellow-500" />
              },
              {
                type: "tip",
                title: "Local Tip",
                description: "Book accommodations at least 2-3 weeks in advance for better rates!",
                icon: <Info className="w-5 h-5 text-blue-500" />
              }
            ];
          }

          // Add property cards if available
          if (propertiesData && propertiesData.length > 0) {
            updatedMessage.properties = propertiesData.slice(0, 3).map((p: any) => ({
              id: p.id,
              name: p.name,
              image: p.images?.[0] || '/images/property-placeholder.jpg',
              rating: p.rating || 4.5,
              price: p.price || 150,
              location: p.location || locationData?.name || 'Great Location',
              amenities: p.amenities || ['WiFi', 'Pool', 'Parking'],
              specialOffer: Math.random() > 0.5 ? '20% off for 3+ nights' : undefined
            }));
          }

          return updatedMessage;
        }
        return msg;
      }));

      setIsTyping(false);
    },
    onError: (error) => {
      console.error('Chat error:', error);
      toast({
        title: "Connection Error",
        description: "Failed to get response. Please try again.",
        variant: "destructive"
      });
      setIsTyping(false);
    }
  });

  const handleSendMessage = (messageText?: string) => {
    const message = messageText || input.trim();
    if (!message || isTyping) return;

    // Add user message
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: "user",
      content: message,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput("");
    setIsTyping(true);

    // Send to API
    sendMessage.mutate(message);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSendMessage();
  };

  // Enhanced helper function to parse and format AI text responses with actions
  const parseActionsAndContent = (text: string): { content: string; actions: Array<{ type: string; label: string; data: any }> } => {
    const actions: Array<{ type: string; label: string; data: any }> = [];
    
    // Parse ACTION tokens and extract them
    const actionRegex = /\[ACTION:(LOCATION|PROPERTY|SEARCH|FILTER)\|([^|]+)\|(\{[^}]+\})\]/g;
    let match;
    
    while ((match = actionRegex.exec(text)) !== null) {
      try {
        const [fullMatch, type, label, dataStr] = match;
        const data = JSON.parse(dataStr);
        actions.push({ type, label, data });
      } catch (e) {
        console.error('Failed to parse action:', match, e);
      }
    }
    
    // Remove ACTION tokens from text and clean up
    const cleanedText = text
      .replace(actionRegex, '')
      .replace(/\*\*Get Ready for a World of Adventure!\*\*/g, '')
      .replace(/\*\s+/g, '• ')
      .replace(/\n{3,}/g, '\n\n')
      .trim();
    
    return { content: cleanedText, actions };
  };

  // Enhanced action button component
  const ActionButton = ({ action, onAction }: { action: any; onAction: (action: any) => void }) => {
    const getButtonConfig = (type: string) => {
      switch (type) {
        case 'LOCATION':
          return {
            icon: <MapPin className="w-4 h-4" />,
            variant: 'secondary' as const,
            className: 'bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200'
          };
        case 'PROPERTY':
          return {
            icon: <Hotel className="w-4 h-4" />,
            variant: 'secondary' as const,
            className: 'bg-green-50 hover:bg-green-100 text-green-700 border-green-200'
          };
        case 'SEARCH':
          return {
            icon: <Search className="w-4 h-4" />,
            variant: 'outline' as const,
            className: 'bg-purple-50 hover:bg-purple-100 text-purple-700 border-purple-200'
          };
        default:
          return {
            icon: <ChevronRight className="w-4 h-4" />,
            variant: 'outline' as const,
            className: 'bg-gray-50 hover:bg-gray-100 text-gray-700 border-gray-200'
          };
      }
    };

    const config = getButtonConfig(action.type);
    
    return (
      <Button
        onClick={() => onAction(action)}
        variant={config.variant}
        size="sm"
        className={cn("gap-2 rounded-lg transition-all duration-200", config.className)}
      >
        {config.icon}
        <span className="text-sm font-medium">{action.label}</span>
      </Button>
    );
  };

  // Helper function to format AI text responses with proper sections
  const formatAIResponse = (text: string): React.ReactNode => {
    const { content, actions } = parseActionsAndContent(text);
    
    // Split content into sections based on patterns
    const sections = content.split(/(?=🏖️|🏙️|🏔️|✨)/g).filter(Boolean);
    
    return (
      <div className="space-y-4">
        {sections.map((section, index) => {
          // Determine section type
          const isBeachSection = section.includes('🏖️');
          const isCitySection = section.includes('🏙️');
          const isMountainSection = section.includes('🏔️');
          const isActionSection = section.includes('✨');
          
          if (isBeachSection || isCitySection || isMountainSection) {
            // Destination category sections
            const lines = section.split('\n').filter(Boolean);
            const title = lines[0];
            const destinations = lines.slice(1).filter(line => line.startsWith('•'));
            
            return (
              <div key={index} className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg p-4 border border-blue-100 dark:border-blue-800">
                <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2 flex items-center gap-2">
                  {isBeachSection && <span>🏖️</span>}
                  {isCitySection && <span>🏙️</span>}
                  {isMountainSection && <span>🏔️</span>}
                  {title.replace(/[🏖️🏙️🏔️]/g, '').trim()}
                </h3>
                <ul className="space-y-1">
                  {destinations.map((dest, i) => (
                    <li key={i} className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                      {dest.replace('•', '').trim()}
                    </li>
                  ))}
                </ul>
              </div>
            );
          } else if (isActionSection) {
            // Action/question sections
            const lines = section.split('\n').filter(Boolean);
            const title = lines[0];
            const questions = lines.slice(1).filter(line => line.startsWith('•'));
            
            return (
              <div key={index} className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-4 border border-purple-100 dark:border-purple-800">
                <h3 className="font-semibold text-purple-900 dark:text-purple-100 mb-2 flex items-center gap-2">
                  ✨ {title.replace(/[✨]/g, '').trim()}
                </h3>
                <ul className="space-y-1">
                  {questions.map((q, i) => (
                    <li key={i} className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                      {q.replace('•', '').trim()}
                    </li>
                  ))}
                </ul>
              </div>
            );
          } else {
            // Regular text sections
            return (
              <div key={index}>
                <ReactMarkdown
                  children={section}
                  remarkPlugins={[remarkGfm]}
                  components={{
                    p: (props: any) => <p className="text-base mb-2 leading-relaxed text-gray-800 dark:text-gray-200" {...props} />,
                    ul: (props: any) => <ul className="list-disc list-inside space-y-1 my-2" {...props} />,
                    ol: (props: any) => <ol className="list-decimal list-inside space-y-1 my-2" {...props} />,
                    li: (props: any) => <li className="text-base text-gray-700 dark:text-gray-300" {...props} />,
                    strong: (props: any) => <strong className="font-semibold text-gray-900 dark:text-gray-100" {...props} />,
                    em: (props: any) => <em className="italic text-gray-700 dark:text-gray-300" {...props} />,
                    a: (props: any) => <a className="text-blue-600 dark:text-blue-400 underline hover:text-blue-800 dark:hover:text-blue-300" target="_blank" rel="noopener noreferrer" {...props} />,
                    h1: (props: any) => <h1 className="text-xl font-bold mt-4 mb-2 text-gray-900 dark:text-gray-100" {...props} />,
                    h2: (props: any) => <h2 className="text-lg font-bold mt-3 mb-1 text-gray-900 dark:text-gray-100" {...props} />,
                    h3: (props: any) => <h3 className="text-base font-semibold mt-2 mb-1 text-gray-900 dark:text-gray-100" {...props} />,
                    blockquote: (props: any) => <blockquote className="border-l-4 border-blue-200 pl-4 italic text-gray-600 dark:text-gray-400 my-2 bg-blue-50 dark:bg-blue-900/20 py-2 rounded-r" {...props} />,
                    code: (props: any) => <code className="bg-gray-100 dark:bg-gray-800 rounded px-1 py-0.5 text-sm text-gray-800 dark:text-gray-200" {...props} />,
                  }}
                />
              </div>
            );
          }
        })}
        
        {/* Render action buttons */}
        {actions.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-3">Quick Actions:</p>
            <div className="flex flex-wrap gap-2">
              {actions.map((action, index) => (
                <ActionButton
                  key={index}
                  action={action}
                  onAction={(selectedAction) => {
                    if (selectedAction.type === 'LOCATION') {
                      // Navigate to results page for location actions
                      const defaultCheckIn = context?.checkIn || new Date().toISOString().split('T')[0];
                      const defaultCheckOut = context?.checkOut || new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // 3 days from now
                      const guests = context?.guests || '2';
                      
                      navigate(`/results?locationName=${encodeURIComponent(selectedAction.label)}&lat=${selectedAction.data.lat}&lng=${selectedAction.data.lng}&checkIn=${defaultCheckIn}&checkOut=${defaultCheckOut}&guests=${guests}&rooms=1`);
                    } else if (selectedAction.type === 'PROPERTY') {
                      handleQuickAction(`Show me details for ${selectedAction.label}`);
                    } else {
                      handleQuickAction(`Search for ${selectedAction.label}`);
                    }
                  }}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderMessage = (message: Message) => {
    const isUser = message.role === "user";
    const isSystem = message.role === "system";

    return (
      <div
        key={message.id}
        className={cn(
          "flex gap-3 mb-6",
          isUser && "flex-row-reverse",
          isSystem && "justify-center"
        )}
      >
        {!isSystem && (
          <Avatar className={cn("h-8 w-8", isUser && "order-2")}>
            {isUser ? (
              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                U
              </AvatarFallback>
            ) : (
              <AvatarFallback className="bg-gradient-to-br from-emerald-500 to-teal-600 text-white">
                AI
              </AvatarFallback>
            )}
          </Avatar>
        )}

        <div className={cn(
          "flex-1 space-y-3",
          isUser && "flex flex-col items-end",
          isSystem && "max-w-2xl"
        )}>
          {/* Message content */}
          {message.content && (
            <div className={cn(
              "rounded-2xl px-4 py-3 max-w-[80%]",
              isUser && "bg-gradient-to-br from-blue-500 to-purple-600 text-white",
              !isUser && !isSystem && "bg-gray-100 dark:bg-gray-800",
              isSystem && "bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 w-full text-center py-4"
            )}>
              {isUser || isSystem ? (
                <p className={cn(
                  "text-sm",
                  isSystem && "text-lg font-semibold"
                )}>
                  {message.content}
                </p>
              ) : (
                <div className="space-y-1">
                  {formatAIResponse(message.content)}
                </div>
              )}
            </div>
          )}

          {/* Quick Actions */}
          {message.quickActions && (
            <div className="grid grid-cols-2 gap-3 max-w-lg">
              {message.quickActions.map((action) => (
                <Button
                  key={action.id}
                  onClick={action.action}
                  className={cn(
                    "justify-start gap-2 text-white",
                    action.color
                  )}
                >
                  {action.icon}
                  <span className="text-sm">{action.label}</span>
                </Button>
              ))}
            </div>
          )}

          {/* Travel Insights */}
          {message.insights && (
            <div className="space-y-3 max-w-lg">
              {message.insights.map((insight, idx) => (
                <Card key={idx} className={cn(
                  "border-l-4",
                  insight.type === "tip" && "border-l-blue-500",
                  insight.type === "warning" && "border-l-yellow-500",
                  insight.type === "recommendation" && "border-l-green-500",
                  insight.type === "deal" && "border-l-purple-500"
                )}>
                  <CardContent className="flex items-start gap-3 p-4">
                    {insight.icon}
                    <div className="flex-1">
                      <h4 className="font-semibold text-sm">{insight.title}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {insight.description}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Location Suggestions */}
          {message.locations && (
            <div className="grid gap-3 max-w-2xl">
              {message.locations.map((location) => (
                <Card 
                  key={location.id} 
                  className="overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
                  onClick={() => {
                    // Generate default dates if not provided in context
                    const defaultCheckIn = context?.checkIn || new Date().toISOString().split('T')[0];
                    const defaultCheckOut = context?.checkOut || new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // 3 days from now
                    const guests = context?.guests || '2';
                    
                    navigate(`/results?locationName=${encodeURIComponent(location.name)}&lat=${location.coordinates.lat}&lng=${location.coordinates.lng}&checkIn=${defaultCheckIn}&checkOut=${defaultCheckOut}&guests=${guests}&rooms=1`);
                  }}
                >
                  <div className="flex">
                    <div className="w-32 h-32 bg-gray-200 flex-shrink-0">
                      <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                        <MapPin className="w-8 h-8 text-white" />
                      </div>
                    </div>
                    <CardContent className="flex-1 p-4">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="font-semibold text-lg">{location.name}</h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {location.description}
                          </p>
                        </div>
                        <ChevronRight className="w-5 h-5 text-gray-400" />
                      </div>
                      <div className="flex flex-wrap gap-2 mt-3">
                        {location.highlights.slice(0, 2).map((highlight, idx) => (
                          <Badge key={idx} variant="secondary" className="text-xs">
                            {highlight}
                          </Badge>
                        ))}
                      </div>
                      <div className="flex items-center gap-4 mt-3 text-xs text-gray-500">
                        <span className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          Best: {location.bestTime}
                        </span>
                        <span className="flex items-center gap-1">
                          <DollarSign className="w-3 h-3" />
                          {location.avgPrice}
                        </span>
                      </div>
                    </CardContent>
                  </div>
                </Card>
              ))}
            </div>
          )}

          {/* Property Cards */}
          {message.properties && (
            <div className="grid gap-3 max-w-2xl">
              {message.properties.map((property) => (
                <Card 
                  key={property.id} 
                  className="overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
                  onClick={() => navigate(`/property/${property.id}`)}
                >
                  <div className="flex">
                    <div className="w-32 h-32 bg-gray-200 flex-shrink-0">
                      <div className="w-full h-full bg-gradient-to-br from-green-400 to-blue-500 flex items-center justify-center">
                        <Hotel className="w-8 h-8 text-white" />
                      </div>
                    </div>
                    <CardContent className="flex-1 p-4">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="font-semibold">{property.name}</h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400 flex items-center gap-1 mt-1">
                            <MapPin className="w-3 h-3" />
                            {property.location}
                          </p>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center gap-1">
                            <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                            <span className="font-semibold">{property.rating}</span>
                          </div>
                          <p className="text-lg font-bold text-green-600">
                            ${property.price}<span className="text-sm font-normal">/night</span>
                          </p>
                        </div>
                      </div>
                      {property.specialOffer && (
                        <Badge className="mt-2 bg-red-500 text-white">
                          {property.specialOffer}
                        </Badge>
                      )}
                      <div className="flex gap-2 mt-2">
                        {property.amenities.slice(0, 3).map((amenity, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {amenity}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </div>
                </Card>
              ))}
            </div>
          )}

          {/* Suggestions */}
          {message.suggestions && (
            <div className="flex flex-wrap gap-2 max-w-lg">
              {message.suggestions.map((suggestion) => (
                <Button
                  key={suggestion.id}
                  variant="outline"
                  size="sm"
                  onClick={suggestion.action}
                  className="gap-2"
                >
                  {suggestion.icon}
                  {suggestion.text}
                </Button>
              ))}
            </div>
          )}

          {/* Timestamp */}
          <p className={cn(
            "text-xs text-gray-500",
            isUser && "text-right"
          )}>
            {format(message.timestamp, "h:mm a")}
          </p>
        </div>
      </div>
    );
  };

  return (
    <Card
      className={cn(
        "w-full max-w-4xl mx-auto flex flex-col shadow-2xl transition-all duration-300",
        isMaximized ? "fixed inset-0 z-[9999] h-full max-w-none rounded-none" : "h-[600px]",
        "bg-white dark:bg-gray-900",
        "sm:rounded-2xl",
        "mobile:fixed mobile:inset-0 mobile:z-[9999] mobile:h-full mobile:max-w-none mobile:rounded-none"
      )}
      style={isMaximized ? {height: '100vh'} : {}}
    >
      <CardHeader className="border-b bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center">
              <Globe className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold">AI Travel Companion</h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Your intelligent travel planning assistant
              </p>
            </div>
          </div>
          <div className="flex gap-2 items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMaximized(m => !m)}
              aria-label={isMaximized ? "Restore" : "Maximize"}
              className="text-xl"
            >
              {isMaximized ? (
                <svg width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><rect x="6" y="6" width="12" height="12" rx="2" /></svg>
              ) : (
                <svg width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M4 8V6a2 2 0 0 1 2-2h2M20 16v2a2 2 0 0 1-2 2h-2M16 4h2a2 2 0 0 1 2 2v2M8 20H6a2 2 0 0 1-2-2v-2" /></svg>
              )}
            </Button>
            {onClose && (
              <Button variant="ghost" size="icon" onClick={onClose} aria-label="Close">
                ×
              </Button>
            )}
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden p-0">
        <ScrollArea className="h-full p-6">
          {messages.map(renderMessage)}
          
          {isTyping && (
            <div className="flex gap-3 mb-6">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-gradient-to-br from-emerald-500 to-teal-600 text-white">
                  AI
                </AvatarFallback>
              </Avatar>
              <div className="bg-gray-100 dark:bg-gray-800 rounded-2xl px-4 py-3">
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm">Thinking...</span>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </ScrollArea>
      </CardContent>

      <CardFooter className="border-t p-4 bg-gray-50 dark:bg-gray-900/50">
        <form onSubmit={handleSubmit} className="flex gap-3 w-full">
          <Input
            ref={inputRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask me anything about travel..."
            className="flex-1"
            disabled={isTyping}
          />
          <Button type="submit" disabled={!input.trim() || isTyping}>
            <Send className="w-4 h-4" />
          </Button>
        </form>
      </CardFooter>
    </Card>
  );
} 