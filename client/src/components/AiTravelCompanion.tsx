import { useState, useRef, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useMutation } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import { 
  Send, 
  MapPin, 
  Calendar,
  Users,
  DollarSign,
  Star,
  Hotel,
  Sparkles,
  Info,
  TrendingUp,
  AlertCircle,
  ChevronRight,
  Loader2,
  Heart,
  Compass,
  Sun,
  Cloud,
  Plane,
  Car,
  Coffee,
  Utensils,
  ShoppingBag,
  Music,
  Palette,
  Trees,
  Waves,
  Mountain,
  Building,
  Globe,
  Clock,
  Shield,
  Zap,
  Search,
  X,
  Maximize2,
  Minimize2
} from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { getSessionId } from "@/lib/session";

interface Message {
  id: string;
  role: "user" | "assistant" | "system";
  content: string;
  timestamp: Date;
  suggestions?: Suggestion[];
  locations?: LocationSuggestion[];
  properties?: PropertyCard[];
  insights?: TravelInsight[];
  quickActions?: QuickAction[];
}

interface Suggestion {
  id: string;
  text: string;
  icon: React.ReactNode;
  action: () => void;
}

interface LocationSuggestion {
  id: string;
  name: string;
  country: string;
  description: string;
  averagePrice: string;
  bestTime: string;
  highlights: string[];
  action: () => void;
}

interface PropertyCard {
  id: string;
  name: string;
  rating: number;
  price: string;
  location: string;
  image?: string;
  amenities: string[];
  action: () => void;
}

interface QuickAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  color: string;
  action: () => void;
}

interface TravelInsight {
  type: "tip" | "warning" | "recommendation" | "deal";
  title: string;
  description: string;
  icon: React.ReactNode;
  action?: () => void;
}

interface AiTravelCompanionProps {
  context?: {
    location?: string | null;
    checkIn?: string | null;
    checkOut?: string | null;
    guests?: string | null;
    searchHistory?: any[];
  };
  onClose?: () => void;
  variant?: 'modal' | 'embedded' | 'floating';
}

// Popular destinations data
const popularDestinations: LocationSuggestion[] = [
  {
    id: "paris",
    name: "Paris",
    country: "France",
    description: "The City of Light with iconic landmarks, world-class museums, and charming cafés",
    averagePrice: "$150-400/night",
    bestTime: "April-June, September-October",
    highlights: ["Eiffel Tower", "Louvre Museum", "Champs-Élysées", "Seine River Cruises"],
    action: () => {}
  },
  {
    id: "tokyo",
    name: "Tokyo",
    country: "Japan",
    description: "Vibrant metropolis blending ancient traditions with cutting-edge technology",
    averagePrice: "$100-350/night",
    bestTime: "March-May, September-November",
    highlights: ["Shibuya Crossing", "Tokyo Tower", "Senso-ji Temple", "Harajuku District"],
    action: () => {}
  },
  {
    id: "bali",
    name: "Bali",
    country: "Indonesia",
    description: "Tropical paradise with stunning beaches, rice terraces, and spiritual culture",
    averagePrice: "$50-250/night",
    bestTime: "April-October",
    highlights: ["Ubud Rice Terraces", "Tanah Lot Temple", "Seminyak Beach", "Mount Batur"],
    action: () => {}
  }
];

export default function AiTravelCompanion({ context, onClose, variant = 'modal' }: AiTravelCompanionProps) {
  const { toast } = useToast();
  const [_, navigate] = useLocation();
  const [input, setInput] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [sessionId] = useState(() => getSessionId());
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [isMaximized, setIsMaximized] = useState(false);

  // Initialize with a comprehensive welcome experience
  useEffect(() => {
    const welcomeMessage: Message = {
      id: "welcome",
      role: "system",
      content: "Welcome to your AI Travel Companion! 🌍✨",
      timestamp: new Date(),
      quickActions: [
        {
          id: "explore",
          label: "Explore Destinations",
          icon: <Compass className="w-4 h-4" />,
          color: "bg-blue-500",
          action: () => handleQuickAction("Show me popular travel destinations")
        },
        {
          id: "plan",
          label: "Plan My Trip",
          icon: <Calendar className="w-4 h-4" />,
          color: "bg-purple-500",
          action: () => handleQuickAction("Help me plan my next vacation")
        },
        {
          id: "deals",
          label: "Find Deals",
          icon: <TrendingUp className="w-4 h-4" />,
          color: "bg-green-500",
          action: () => handleQuickAction("Show me the best travel deals")
        },
        {
          id: "inspire",
          label: "Inspire Me",
          icon: <Sparkles className="w-4 h-4" />,
          color: "bg-yellow-500",
          action: () => handleQuickAction("Surprise me with amazing destinations")
        }
      ],
      insights: [
        {
          type: "tip",
          title: "Smart Travel Planning",
          description: "I can help you find hidden gems, compare prices, and create the perfect itinerary with interactive destination buttons!",
          icon: <Zap className="w-5 h-5 text-yellow-500" />
        }
      ]
    };
    
    setMessages([welcomeMessage]);
  }, [context]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleQuickAction = (text: string) => {
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: "user",
      content: text,
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInput("");
    setIsTyping(true);
    
    sendMessage.mutate(text);
  };

  // Enhanced ACTION button component
  const ActionButton = ({ type, label, data }: { type: string; label: string; data: any }) => {
    const handleClick = () => {
      if (type === 'location' && data.lat && data.lng) {
        const searchParams = new URLSearchParams({
          lat: data.lat.toString(),
          lng: data.lng.toString(),
          locationName: data.name || label,
          checkIn: context?.checkIn || '',
          checkOut: context?.checkOut || '',
          guests: context?.guests || '2',
          rooms: '1'
        });
        navigate(`/results?${searchParams.toString()}`);
        onClose?.();
      }
    };

    const getButtonStyle = () => {
      switch (type) {
        case 'location':
          return 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white';
        case 'property':
          return 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white';
        case 'search':
          return 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white';
        default:
          return 'bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white';
      }
    };

    const getIcon = () => {
      switch (type) {
        case 'location':
          return <MapPin className="w-4 h-4" />;
        case 'property':
          return <Hotel className="w-4 h-4" />;
        case 'search':
          return <Search className="w-4 h-4" />;
        default:
          return <Sparkles className="w-4 h-4" />;
      }
    };

    return (
      <Button
        onClick={handleClick}
        className={cn(
          "rounded-full px-4 py-2 text-sm font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105",
          getButtonStyle()
        )}
      >
        {getIcon()}
        <span className="ml-2">{label}</span>
      </Button>
    );
  };

  // Enhanced response formatting with ACTION button parsing
  const parseActionsAndContent = (text: string): { content: string; actions: Array<{ type: string; label: string; data: any }> } => {
    const actions: Array<{ type: string; label: string; data: any }> = [];
    
    const actionRegex = /\[ACTION:(LOCATION|PROPERTY|SEARCH|FILTER)\|([^|]+)\|(\{[^}]+\})\]/g;
    let match;
    
    while ((match = actionRegex.exec(text)) !== null) {
      try {
        const [fullMatch, type, label, dataStr] = match;
        const data = JSON.parse(dataStr);
        actions.push({ type: type.toLowerCase(), label, data });
      } catch (e) {
        console.error('Failed to parse action:', match, e);
      }
    }
    
    const cleanedText = text
      .replace(actionRegex, '')
      .replace(/\*\*Get Ready for a World of Adventure!\*\*/g, '')
      .replace(/\*\s+/g, '• ')
      .replace(/\n{3,}/g, '\n\n')
      .trim();
    
    return { content: cleanedText, actions };
  };

  const formatAIResponse = (text: string) => {
    const { content, actions } = parseActionsAndContent(text);
    
    const sections = content.split(/(?=\*\*(?:🏖️|🏙️|🏔️|✨))/);
    
    return (
      <div className="space-y-4">
        {sections.map((section, index) => {
          if (!section.trim()) return null;
          
          const lines = section.split('\n').filter(line => line.trim());
          const hasEmoji = /^.*(?:🏖️|🏙️|🏔️|✨)/.test(lines[0]);
          
          if (hasEmoji) {
            const [titleLine, ...contentLines] = lines;
            const title = titleLine.replace(/\*\*/g, '').trim();
            
            return (
              <div key={index} className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                <h3 className="font-semibold text-lg mb-2 text-blue-900 dark:text-blue-100">{title}</h3>
                <div className="space-y-2">
                  {contentLines.map((line, i) => (
                    <div key={i} className="text-gray-700 dark:text-gray-300 prose dark:prose-invert prose-sm max-w-none">
                      <ReactMarkdown remarkPlugins={[remarkGfm]}>
                        {line}
                      </ReactMarkdown>
                    </div>
                  ))}
                </div>
              </div>
            );
          }
          
          return (
            <div key={index} className="space-y-2">
              {lines.map((line, i) => (
                <div key={i} className="text-gray-700 dark:text-gray-300 prose dark:prose-invert prose-sm max-w-none">
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>
                    {line}
                  </ReactMarkdown>
                </div>
              ))}
            </div>
          );
        })}
        
        {actions.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-4">
            {actions.map((action, index) => (
              <ActionButton
                key={index}
                type={action.type}
                label={action.label}
                data={action.data}
              />
            ))}
          </div>
        )}
      </div>
    );
  };

  const sendMessage = useMutation({
    mutationFn: async (message: string) => {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message,
          sessionId,
          context: context || {},
          extractLocation: true
        })
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No response stream');

      const decoder = new TextDecoder();
      let assistantContent = '';

      const assistantMessageId = `assistant-${Date.now()}`;
      const initialAssistantMessage: Message = {
        id: assistantMessageId,
        role: "assistant",
        content: "",
        timestamp: new Date(),
        suggestions: [
          {
            id: "1",
            text: "Show me more options",
            icon: <Hotel className="w-4 h-4" />,
            action: () => handleQuickAction("Show me more accommodation options")
          },
          {
            id: "2",
            text: "Change dates",
            icon: <Calendar className="w-4 h-4" />,
            action: () => handleQuickAction("I want to change my travel dates")
          },
          {
            id: "3",
            text: "Filter by price",
            icon: <DollarSign className="w-4 h-4" />,
            action: () => handleQuickAction("Show me budget-friendly options")
          }
        ]
      };

      setMessages(prev => [...prev, initialAssistantMessage]);

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(5).trim();
              if (data === '[DONE]' || data === 'DONE') continue;

              try {
                const parsed = JSON.parse(data);
                if (parsed.type === 'text') {
                  assistantContent += parsed.data;
                  
                  setMessages(prev => prev.map(msg => 
                    msg.id === assistantMessageId 
                      ? { ...msg, content: assistantContent }
                      : msg
                  ));
                }
              } catch (e) {
                console.error('Failed to parse SSE data:', e);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

      return { assistantContent };
    },
    onSuccess: () => {
      setIsTyping(false);
    },
    onError: (error) => {
      console.error('Chat error:', error);
      setIsTyping(false);
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isTyping) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: "user",
      content: input,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const messageText = input;
    setInput("");
    setIsTyping(true);

    sendMessage.mutate(messageText);
  };

  const renderMessage = (message: Message) => {
    const isUser = message.role === "user";
    const isSystem = message.role === "system";

    return (
      <div
        key={message.id}
        className={cn(
          "flex gap-3 mb-6",
          isUser && "flex-row-reverse"
        )}
      >
        <Avatar className="w-8 h-8 flex-shrink-0">
          {isUser ? (
            <AvatarFallback className="bg-blue-500 text-white">You</AvatarFallback>
          ) : (
            <AvatarFallback className="bg-gradient-to-br from-purple-500 to-blue-600 text-white">
              <Globe className="w-4 h-4" />
            </AvatarFallback>
          )}
        </Avatar>

        <div className={cn(
          "flex-1 space-y-3",
          isUser && "flex flex-col items-end",
          isSystem && "max-w-2xl"
        )}>
          {message.content && (
            <div className={cn(
              "rounded-2xl px-4 py-3 max-w-[80%]",
              isUser && "bg-gradient-to-br from-blue-500 to-purple-600 text-white",
              !isUser && !isSystem && "bg-gray-100 dark:bg-gray-800",
              isSystem && "bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 w-full text-center py-4"
            )}>
              {isUser || isSystem ? (
                <p className={cn(
                  "text-sm",
                  isSystem && "text-lg font-semibold"
                )}>
                  {message.content}
                </p>
              ) : (
                <div className="space-y-1">
                  {formatAIResponse(message.content)}
                </div>
              )}
            </div>
          )}

          {message.quickActions && (
            <div className="grid grid-cols-2 gap-3 max-w-lg">
              {message.quickActions.map((action) => (
                <Button
                  key={action.id}
                  onClick={action.action}
                  className={cn(
                    "justify-start gap-2 text-white",
                    action.color
                  )}
                >
                  {action.icon}
                  <span className="text-sm">{action.label}</span>
                </Button>
              ))}
            </div>
          )}

          {message.insights && (
            <div className="space-y-2">
              {message.insights.map((insight, index) => (
                <div
                  key={index}
                  className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-800"
                >
                  <div className="flex items-start gap-2">
                    {insight.icon}
                    <div className="flex-1">
                      <p className="font-medium text-sm text-blue-900 dark:text-blue-100">
                        {insight.title}
                      </p>
                      <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                        {insight.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {message.suggestions && (
            <div className="flex flex-wrap gap-2">
              {message.suggestions.map((suggestion) => (
                <Button
                  key={suggestion.id}
                  variant="outline"
                  size="sm"
                  onClick={suggestion.action}
                  className="rounded-full text-xs"
                >
                  {suggestion.icon}
                  <span className="ml-1">{suggestion.text}</span>
                </Button>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  const containerClasses = cn(
    "w-full mx-auto flex flex-col shadow-2xl transition-all duration-300 bg-white dark:bg-gray-900",
    variant === 'modal' && (isMaximized 
      ? "fixed inset-0 z-[9999] h-full max-w-none rounded-none" 
      : "max-w-4xl h-[600px] rounded-2xl"),
    variant === 'embedded' && "max-w-4xl h-[600px] rounded-2xl",
    variant === 'floating' && "fixed bottom-4 right-4 w-96 h-[500px] rounded-2xl z-50"
  );

  return (
    <Card className={containerClasses}>
      <CardHeader className="border-b bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center">
              <Globe className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold">AI Travel Companion</h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Your intelligent travel planning assistant
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {variant === 'modal' && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsMaximized(!isMaximized)}
                className="text-gray-500 hover:text-gray-700"
              >
                {isMaximized ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
              </Button>
            )}
            {onClose && (
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>
        </CardTitle>
      </CardHeader>

      <ScrollArea className="flex-1 p-6">
        <div className="space-y-4">
          {messages.map(renderMessage)}
          {isTyping && (
            <div className="flex gap-3">
              <Avatar className="w-8 h-8">
                <AvatarFallback className="bg-gradient-to-br from-purple-500 to-blue-600 text-white">
                  <Globe className="w-4 h-4" />
                </AvatarFallback>
              </Avatar>
              <div className="bg-gray-100 dark:bg-gray-800 rounded-2xl px-4 py-3">
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Thinking...
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
        <div ref={messagesEndRef} />
      </ScrollArea>

      <CardFooter className="border-t p-4 bg-gray-50 dark:bg-gray-900/50">
        <form onSubmit={handleSubmit} className="flex gap-3 w-full">
          <Input
            ref={inputRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask me anything about travel..."
            className="flex-1"
            disabled={isTyping}
          />
          <Button type="submit" disabled={!input.trim() || isTyping}>
            <Send className="w-4 h-4" />
          </Button>
        </form>
      </CardFooter>
    </Card>
  );
} 