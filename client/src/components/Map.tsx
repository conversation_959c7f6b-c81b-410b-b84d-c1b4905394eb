import React, { useEffect, useRef, useState, CSSProperties } from 'react';
import { Map as MapG<PERSON>, Marker, Popup, NavigationControl } from 'react-map-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { Button } from './ui/button.jsx';
import { PropertyWithRates, PropertyType } from '@/types/schema.js';
import PropertyMarkerPopup from './PropertyMarkerPopup.jsx';
import MapLoadingState from './MapLoadingState.jsx';
import { useTheme } from '@/components/theme-provider.jsx';
import Supercluster from 'supercluster';
import type { BBox, Feature, Point } from 'geojson';
import MapFilterControl from './MapFilterControl.jsx';
import { cn } from '../lib/utils.js';
import { AlertCircle, MapPin } from 'lucide-react';
import { Card, CardContent } from './ui/card.jsx';

// Add this CSS at the top of the file, after imports
const mapControlStyles: CSSProperties = {
  zIndex: 20,
  position: 'absolute',
  bottom: '20px',
  right: '20px',
  background: 'white',
  boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
  borderRadius: '4px',
};

// Get Mapbox token with fallback
const getMapboxToken = (): string => {
  const token = import.meta.env.VITE_MAPBOX_TOKEN;
  
  // Check if we're in Replit environment
  const isReplit = window.location.hostname.includes('replit') || 
                   window.location.hostname.includes('repl.it') ||
                   process.env.REPL_ID !== undefined;
  
  if (!token || token === 'undefined' || token === '') {
    // Use a more reliable public token for development
    // Note: Replace this with your own token for production
    console.warn('VITE_MAPBOX_TOKEN not set, using fallback token');
    return 'pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw';
  }
  
  if (isReplit) {
    console.log('Detected Replit environment, using modified map configuration');
  }
  
  return token;
};

// Safe map style URLs
const getMapStyle = (theme: string): string => {
  if (theme === 'dark') {
    return 'mapbox://styles/mapbox/dark-v11';
  }
  // Use a reliable light style
  return 'mapbox://styles/mapbox/streets-v12';
};

export interface MapProps {
  properties: PropertyWithRates[];
  center?: { lat: number; lng: number };
  zoom?: number;
  onMapMoved?: (center: { lat: number; lng: number }, radius: number) => void;
  onSearchThisArea?: (center: { lat: number; lng: number }, radius: number) => void;
  propertyRates?: Record<number, number>;
  onPropertySelect?: (propertyId: number) => void;
  onFiltersChange?: (filters: {
    priceRange: [number, number];
    propertyTypes: PropertyType[];
  }) => void;
  className?: string;
  isFullScreen?: boolean;
  onToggleFullScreen?: () => void;
}

const PROPERTY_TYPE_ICONS: Record<PropertyType, string> = {
  hotel: '🏨',
  resort: '🌴',
  apartment: '🏢',
  villa: '🏡',
  guesthouse: '🏠'
};

interface PointFeature {
  type: 'Feature';
  geometry: {
    type: 'Point';
    coordinates: [number, number];
  };
  properties: {
    id: number;
    property: PropertyWithRates;
  };
}

const Map: React.FC<MapProps> = ({
  properties,
  center,
  zoom,
  onMapMoved,
  onSearchThisArea,
  propertyRates,
  onPropertySelect,
  onFiltersChange,
  className,
  isFullScreen = false,
  onToggleFullScreen,
}) => {
  const mapRef = useRef<any>(null);
  const [viewport, setViewport] = useState({
    latitude: center?.lat || 0,
    longitude: center?.lng || 0,
    zoom: zoom || 12
  });
  const [selectedProperty, setSelectedProperty] = useState<PropertyWithRates | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [mapError, setMapError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [mapInitialized, setMapInitialized] = useState(false);
  const { theme } = useTheme();
  const [clusters, setClusters] = useState<any[]>([]);
  const superclusterRef = useRef<Supercluster>();
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);
  const [selectedTypes, setSelectedTypes] = useState<PropertyType[]>([]);
  const [mapMoved, setMapMoved] = useState(false);

  useEffect(() => {
    if (center && !viewport.latitude && !viewport.longitude) {
      // Only set initial center if viewport is not already set
      setViewport(prev => ({
        ...prev,
        latitude: center.lat,
        longitude: center.lng
      }));
    } else if (!viewport.latitude && !viewport.longitude && properties.length > 0) {
      // Center on first property only if no center provided and viewport not set
      setViewport(prev => ({
        ...prev,
        latitude: Number(properties[0].latitude),
        longitude: Number(properties[0].longitude)
      }));
    }
  }, [center, properties, viewport.latitude, viewport.longitude]);

  // Add timeout for map loading
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    
    if (isLoading && !mapInitialized && !mapError) {
      timeoutId = setTimeout(() => {
        if (isLoading && !mapInitialized) {
          console.warn('Map loading timeout');
          setMapError('Map loading timed out. The map service may be temporarily unavailable.');
          setIsLoading(false);
        }
      }, 15000); // 15 second timeout
    }
    
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [isLoading, mapInitialized, mapError]);

  // Add connection check
  useEffect(() => {
    const checkMapboxConnection = async () => {
      try {
        // Check if we're in Replit environment - if so, skip complex connection checks
        const isReplit = window.location.hostname.includes('replit') || 
                         window.location.hostname.includes('repl.it') ||
                         typeof process !== 'undefined' && process.env.REPL_ID !== undefined;
        
        if (isReplit) {
          console.log('Replit environment detected - using simplified map configuration');
          // In Replit, we'll let the map component handle errors gracefully
          return;
        }
        
        const response = await fetch('https://api.mapbox.com/styles/v1/mapbox/streets-v12?access_token=' + getMapboxToken(), {
          method: 'HEAD',
          signal: AbortSignal.timeout(5000) // 5 second timeout
        });
        
        if (!response.ok) {
          console.warn('Mapbox connection check failed:', response.status);
          if (response.status === 401) {
            setMapError('Invalid Mapbox token. Please check your VITE_MAPBOX_TOKEN environment variable.');
            setIsLoading(false);
            return;
          }
          if (response.status === 403) {
            setMapError('Access denied. Your Mapbox token may not have the required permissions.');
            setIsLoading(false);
            return;
          }
        }
      } catch (error) {
        console.warn('Mapbox connection check failed:', error);
        // Check if it's a hostname error (common in Replit)
        if (error instanceof Error && error.message.includes('hostname')) {
          console.log('Hostname resolution failed - likely in restricted environment like Replit');
          // Don't set error here, let the map component try to load with fallback
        }
      }
    };
    
    checkMapboxConnection();
  }, []);

  const handleMapMove = () => {
    if (!mapRef.current) return;
    
    const map = mapRef.current.getMap();
    const bounds = map.getBounds();
    const center = bounds.getCenter();
    
    // Calculate approximate radius in meters
    const ne = bounds.getNorthEast();
    const sw = bounds.getSouthWest();
    const radius = getDistanceFromLatLonInM(
      center.lat,
      center.lng,
      ne.lat,
      ne.lng
    );

    onMapMoved?.({
      lat: center.lat,
      lng: center.lng
    }, radius);
    setMapMoved(true);
  };

  // Calculate distance between two points in meters
  const getDistanceFromLatLonInM = (lat1: number, lon1: number, lat2: number, lon2: number) => {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI/180;
    const φ2 = lat2 * Math.PI/180;
    const Δφ = (lat2-lat1) * Math.PI/180;
    const Δλ = (lon2-lon1) * Math.PI/180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  };

  const updateClusters = () => {
    if (!mapRef.current || !properties.length) return;
    
    const map = mapRef.current.getMap();
    const bounds = map.getBounds();
    
    // Initialize supercluster if not already done
    if (!superclusterRef.current) {
      superclusterRef.current = new Supercluster({
        radius: 40,
        maxZoom: 16,
      });
      
      // Load property points
      const points: PointFeature[] = properties.map(property => ({
        type: 'Feature',
        properties: { 
          id: property.id,
          property 
        },
        geometry: {
          type: 'Point',
          coordinates: [Number(property.longitude), Number(property.latitude)]
        }
      }));
      
      superclusterRef.current.load(points);
    }
    
    const clusters = superclusterRef.current.getClusters(
      [bounds.getWest(), bounds.getSouth(), bounds.getEast(), bounds.getNorth()],
      Math.floor(map.getZoom())
    );
    
    setClusters(clusters);
  };

  useEffect(() => {
    if (properties.length > 0) {
      updateClusters();
    }
  }, [properties, viewport.zoom]);

  const handlePriceRangeChange = (range: [number, number]) => {
    setPriceRange(range);
    onFiltersChange?.({
      priceRange: range,
      propertyTypes: selectedTypes
    });
  };

  const handleTypeToggle = (type: PropertyType) => {
    setSelectedTypes(prev => {
      const newTypes = prev.includes(type)
        ? prev.filter(t => t !== type)
        : [...prev, type];
      
      onFiltersChange?.({
        priceRange,
        propertyTypes: newTypes
      });
      
      return newTypes;
    });
  };

  const handleResetFilters = () => {
    setPriceRange([0, 1000]);
    setSelectedTypes([]);
    onFiltersChange?.({
      priceRange: [0, 1000],
      propertyTypes: []
    });
  };

  const handleZoomIn = () => {
    setViewport(prev => ({
      ...prev,
      zoom: Math.min((prev.zoom || 0) + 1, 20) // Max zoom level is 20
    }));
  };

  const handleZoomOut = () => {
    setViewport(prev => ({
      ...prev,
      zoom: Math.max((prev.zoom || 0) - 1, 1) // Min zoom level is 1
    }));
  };

  const renderPropertyMarker = (property: PropertyWithRates) => {
    const rates = property.rates || [];
    const hasRates = rates.length > 0;
    const price = hasRates ? Math.round(rates[0].totalAmount) : null;
    const hasDiscount = hasRates && rates[0].discountPercent > 0;
    
    return (
      <div className="relative group">
        <button
          className={cn(
            "bg-white text-foreground px-3 py-1.5 rounded-full text-sm font-medium shadow-lg transform transition-all hover:scale-105 flex items-center gap-1.5",
            hasDiscount ? "ring-2 ring-red-500" : "hover:bg-primary hover:text-primary-foreground",
            !hasRates && "bg-muted hover:bg-muted/80"
          )}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setSelectedProperty(property);
            if (!hasRates) {
              onPropertySelect?.(property.id);
            }
          }}
        >
          <span className="text-base">{PROPERTY_TYPE_ICONS[property.type as PropertyType] || '🏢'}</span>
          {price ? (
            <>
              ${price}
              {hasDiscount && (
                <span className="text-xs text-red-500 font-bold">
                  -{rates[0].discountPercent}%
                </span>
              )}
            </>
          ) : (
            <span className="text-xs font-medium">View rates</span>
          )}
        </button>
        {property.name && (
          <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <div className="bg-white/90 backdrop-blur-sm text-xs px-2 py-1 rounded shadow-lg whitespace-nowrap">
              {property.name}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderCluster = (cluster: any) => {
    const [longitude, latitude] = cluster.geometry.coordinates;
    const { cluster: isCluster, point_count: pointCount } = cluster.properties;

    if (!isCluster) {
      return (
        <Marker
          key={cluster.properties.id}
          longitude={longitude}
          latitude={latitude}
        >
          {renderPropertyMarker(cluster.properties.property)}
        </Marker>
      );
    }

    // Calculate cluster size class based on point count
    const size = pointCount < 10 ? 'small' : pointCount < 50 ? 'medium' : 'large';
    
    return (
      <Marker
        key={cluster.id}
        longitude={longitude}
        latitude={latitude}
      >
        <button
          className={cn(
            "flex items-center justify-center rounded-full bg-primary text-primary-foreground font-bold shadow-lg transform transition-transform hover:scale-105",
            size === 'small' ? 'w-10 h-10 text-sm' : 
            size === 'medium' ? 'w-12 h-12 text-base' : 
            'w-14 h-14 text-lg'
          )}
          onClick={() => {
            const expansionZoom = Math.min(
              superclusterRef.current?.getClusterExpansionZoom(cluster.id) ?? 0,
              20
            );
            
            setViewport({
              ...viewport,
              latitude,
              longitude,
              zoom: expansionZoom
            });
          }}
        >
          {pointCount}
        </button>
      </Marker>
    );
  };

  // Error fallback component
  const MapErrorFallback = () => (
    <div className="w-full h-full bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
      <Card className="max-w-md w-full mx-4">
        <CardContent className="p-6 text-center">
          <AlertCircle className="h-12 w-12 text-orange-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Map Unavailable</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {mapError || "The map service is currently unavailable. Property results are still available in the list view."}
          </p>
          {retryCount < 3 && (
            <Button 
              onClick={handleRetry}
              className="mb-4"
              variant="outline"
            >
              Retry Loading Map
            </Button>
          )}
          <div className="flex items-center justify-center text-sm text-gray-500">
            <MapPin className="h-4 w-4 mr-2" />
            <span>
              {properties.length} {properties.length === 1 ? 'property' : 'properties'} found
              {center && ` near ${center.lat.toFixed(4)}, ${center.lng.toFixed(4)}`}
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    setMapError(null);
    setIsLoading(true);
    setMapInitialized(false);
  };

  // Simple static map fallback
  const StaticMapFallback = () => (
    <div className="w-full h-full bg-gray-100 dark:bg-gray-800 relative overflow-hidden">
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center">
          <MapPin className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Interactive Map Unavailable</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4 max-w-xs">
            Showing property locations in list view instead
          </p>
          <div className="text-sm text-gray-500">
            {properties.length} properties found
            {center && (
              <div className="mt-2">
                📍 {center.lat.toFixed(4)}, {center.lng.toFixed(4)}
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Simple property location indicators */}
      {properties.slice(0, 10).map((property, index) => (
        <div
          key={property.id}
          className="absolute w-3 h-3 bg-blue-500 rounded-full transform -translate-x-1/2 -translate-y-1/2 cursor-pointer hover:scale-150 transition-transform"
          style={{
            left: `${20 + (index % 5) * 15}%`,
            top: `${30 + Math.floor(index / 5) * 20}%`,
          }}
          onClick={() => onPropertySelect?.(property.id)}
          title={property.name}
        />
      ))}
    </div>
  );

  // Show error fallback if map failed to load
  if (mapError) {
    // If we've exhausted retries, show the static map fallback
    if (retryCount >= 3) {
      return <StaticMapFallback />;
    }
    return <MapErrorFallback />;
  }

  return (
    <div className="relative w-full h-full">
      <MapGL
        ref={mapRef}
        mapboxAccessToken={getMapboxToken()}
        {...viewport}
        onMove={evt => {
          setViewport(evt.viewState);
          handleMapMove();
        }}
        onLoad={() => {
          console.log('Map loaded successfully');
          setIsLoading(false);
          setMapInitialized(true);
          setMapError(null);
          updateClusters();
        }}
        onZoom={() => updateClusters()}
        onError={(error) => {
          console.error('Map loading error:', error);
          
          // Handle specific network/hostname errors
          let errorMessage = 'Failed to load map. Please check your internet connection.';
          
          if (error instanceof Error) {
            errorMessage = error.message;
            
            // Check for hostname resolution errors (common in Replit)
            if (error.message.includes('hostname') || error.message.includes('ENOTFOUND')) {
              errorMessage = 'Unable to connect to Mapbox servers. This may be due to:\n' +
                           '• Network connectivity issues in Replit environment\n' +
                           '• Firewall blocking requests to events.mapbox.com\n' +
                           '• DNS resolution problems\n' +
                           'Using fallback map display.';
            } else if (error.message.includes('401') || error.message.includes('Unauthorized')) {
              errorMessage = 'Invalid Mapbox token. Please check your VITE_MAPBOX_TOKEN environment variable.';
            } else if (error.message.includes('403') || error.message.includes('Forbidden')) {
              errorMessage = 'Access denied. Your Mapbox token may not have the required permissions or URL restrictions.';
            }
          }
          
          setMapError(errorMessage);
          setIsLoading(false);
          setMapInitialized(false);
        }}
        mapStyle={getMapStyle(theme)}
        style={{ width: '100%', height: '100%' }}
        reuseMaps={true}
        preserveDrawingBuffer={true}
        // Configure for Replit environment
        transformRequest={(url, resourceType) => {
          // Block analytics/events requests that cause hostname errors in Replit
          if (url.includes('events.mapbox.com')) {
            return { url: '', headers: {} }; // Return empty request instead of null
          }
          
          return {
            url,
            headers: {
              'Cache-Control': 'no-cache'
            }
          };
        }}
        maxTileCacheSize={50}
        collectResourceTiming={false}
      >
        {/* Search This Area Button */}
        <div className="absolute top-20 left-1/2 -translate-x-1/2 z-20">
          {mapMoved && (
            <Button
              variant="default"
              size="sm"
              onClick={() => {
                if (!mapRef.current) return;
                const map = mapRef.current.getMap();
                const center = map.getCenter();
                const bounds = map.getBounds();
                const ne = bounds.getNorthEast();
                const radius = getDistanceFromLatLonInM(
                  center.lat,
                  center.lng,
                  ne.lat,
                  ne.lng
                );
                onSearchThisArea?.({
                  lat: center.lat,
                  lng: center.lng
                }, radius);
                setMapMoved(false); // Reset after search
              }}
              className="shadow-lg bg-white hover:bg-gray-100 text-foreground px-6 py-2 font-medium rounded-full border border-border/50 backdrop-blur-sm"
            >
              Search this area
            </Button>
          )}
        </div>

        {/* Property Markers and Clusters */}
        {clusters.map(renderCluster)}

        {/* Selected Property Popup */}
        {selectedProperty && (
          <Popup
            latitude={Number(selectedProperty.latitude)}
            longitude={Number(selectedProperty.longitude)}
            closeButton={true}
            closeOnClick={false}
            onClose={() => setSelectedProperty(null)}
            anchor="bottom"
            offset={20}
          >
            <PropertyMarkerPopup 
              property={selectedProperty}
              onViewDetails={() => onPropertySelect?.(selectedProperty.id)}
            />
          </Popup>
        )}

        {/* Map Controls */}
        <div style={{
          position: 'absolute',
          bottom: '20px',
          right: '20px',
          zIndex: 20,
          background: 'white',
          boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
          borderRadius: '4px',
        }}>
          <NavigationControl visualizePitch={true} showZoom={true} showCompass={true} />
        </div>

        {/* Filter Controls */}
        <div className="absolute top-4 left-4 z-20">
          <MapFilterControl
            priceRange={priceRange}
            onPriceRangeChange={handlePriceRangeChange}
            selectedTypes={selectedTypes}
            onTypeToggle={handleTypeToggle}
            onReset={handleResetFilters}
            className="bg-white shadow-lg rounded-lg"
          />
        </div>

        {/* Full Screen Toggle */}
        <Button
          variant="secondary"
          size="sm"
          className="absolute top-4 right-4 z-10 shadow-lg"
          onClick={onToggleFullScreen}
        >
          {isFullScreen ? 'Exit Full Screen' : 'Full Screen'}
        </Button>
      </MapGL>
      
      {isLoading && <MapLoadingState />}
    </div>
  );
};

export default Map;