import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { getSessionId } from "@/lib/session";

interface Insight {
  title: string;
  description: string;
}

interface Props {
  locationName: string;
  lat: number;
  lng: number;
  dateRange?: { checkIn: string; checkOut: string };
}

export default function InsightsPanel({ locationName, lat, lng, dateRange }: Props) {
  const [insights, setInsights] = useState<Insight[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!locationName) return;
    const controller = new AbortController();
    (async () => {
      try {
        setLoading(true);
        const resp = await fetch("/api/insights", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            sessionId: getSessionId(),
            location: { name: locationName, lat, lng },
            dateRange,
          }),
          signal: controller.signal,
        });
        if (!resp.ok) throw new Error("Failed to fetch insights");
        const data = await resp.json();
        setInsights(data.insights || []);
      } catch (err: any) {
        if (err.name !== "AbortError") {
          setError(err.message || "Unknown error");
        }
      } finally {
        setLoading(false);
      }
    })();
    return () => controller.abort();
  }, [locationName, lat, lng, dateRange?.checkIn, dateRange?.checkOut]);

  if (loading) return <p className="text-sm text-muted-foreground">Fetching smart suggestions…</p>;
  if (error) return <p className="text-sm text-destructive">{error}</p>;
  if (insights.length === 0) return null;

  return (
    <div className="space-y-3">
      {insights.map((i, idx) => (
        <Card key={idx} className="bg-accent/20">
          <CardHeader>
            <CardTitle>{i.title}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm leading-relaxed">{i.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
} 