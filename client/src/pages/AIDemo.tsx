import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import AiTravelCompanion from "@/components/AiTravelCompanion";
import { 
  Sparkles, 
  Globe, 
  MessageCircle, 
  Zap, 
  Eye, 
  Palette,
  TrendingUp,
  Shield,
  ChevronRight,
  CheckCircle
} from "lucide-react";

export default function AIDemo() {
  const [showChat, setShowChat] = useState(false);
  const [demoContext, setDemoContext] = useState<any>(null);

  const features = [
    {
      icon: <Sparkles className="w-6 h-6 text-purple-500" />,
      title: "Proactive Intelligence",
      description: "Get smart suggestions, travel tips, and personalized recommendations"
    },
    {
      icon: <Eye className="w-6 h-6 text-blue-500" />,
      title: "Visual Experience",
      description: "Beautiful cards for destinations, properties, and insights"
    },
    {
      icon: <Palette className="w-6 h-6 text-green-500" />,
      title: "Intuitive Design",
      description: "Quick actions, easy navigation, and conversational flow"
    },
    {
      icon: <TrendingUp className="w-6 h-6 text-orange-500" />,
      title: "Real-time Updates",
      description: "Streaming responses with instant feedback"
    }
  ];

  const demos = [
    {
      title: "Basic Chat",
      description: "Start with a blank slate",
      context: null,
      color: "bg-blue-500"
    },
    {
      title: "Miami Vacation",
      description: "Pre-filled with Miami context",
      context: { location: "Miami Beach" },
      color: "bg-teal-500"
    },
    {
      title: "Business Trip",
      description: "New York, next week",
      context: { 
        location: "New York", 
        checkIn: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        guests: "1"
      },
      color: "bg-purple-500"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-4">
            <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center">
              <Globe className="w-10 h-10 text-white" />
            </div>
          </div>
          <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
            AI Travel Companion Demo
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Experience the future of travel planning with our enhanced AI assistant
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {features.map((feature, idx) => (
            <Card key={idx} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="mb-4">{feature.icon}</div>
                <h3 className="font-semibold mb-2">{feature.title}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Demo Scenarios */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle>Try Different Scenarios</CardTitle>
            <CardDescription>
              Launch the AI chat with different contexts to see how it adapts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4">
              {demos.map((demo, idx) => (
                <Card 
                  key={idx} 
                  className="cursor-pointer hover:shadow-md transition-all"
                  onClick={() => {
                    setDemoContext(demo.context);
                    setShowChat(true);
                  }}
                >
                  <CardContent className="p-6">
                    <div className={`w-12 h-12 ${demo.color} rounded-lg flex items-center justify-center mb-4`}>
                      <MessageCircle className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="font-semibold mb-1">{demo.title}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      {demo.description}
                    </p>
                    <div className="flex items-center text-sm text-blue-600 dark:text-blue-400">
                      <span>Launch Demo</span>
                      <ChevronRight className="w-4 h-4 ml-1" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* What's New Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="w-6 h-6 text-yellow-500" />
              What's New in This Version
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-3 flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-500" />
                  User Experience
                </h4>
                <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <li>• Beautiful welcome screen with quick actions</li>
                  <li>• Visual cards for destinations and properties</li>
                  <li>• Contextual follow-up suggestions</li>
                  <li>• Real-time typing indicators</li>
                  <li>• Clickable elements for easy navigation</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-3 flex items-center gap-2">
                  <Shield className="w-5 h-5 text-blue-500" />
                  Technical Improvements
                </h4>
                <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <li>• Fixed multiple initialization issues</li>
                  <li>• Improved session management</li>
                  <li>• Better error handling</li>
                  <li>• Comprehensive testing suite</li>
                  <li>• Optimized API streaming</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Testing Instructions */}
        <div className="mt-12 text-center">
          <h3 className="text-xl font-semibold mb-4">Test in Browser Console</h3>
          <Card className="max-w-2xl mx-auto">
            <CardContent className="p-6">
              <pre className="bg-gray-100 dark:bg-gray-900 p-4 rounded-lg text-left text-sm">
                <code>{`// Run complete test suite
testEnhancedAI.full()

// Test individual features
testEnhancedAI.planWithAI()
testEnhancedAI.interaction()
testEnhancedAI.quickActions()
testEnhancedAI.api()`}</code>
              </pre>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Chat Modal */}
      {showChat && (
        <>
          <div 
            className="fixed inset-0 bg-black/30 backdrop-blur-sm z-[999]"
            onClick={() => setShowChat(false)}
          />
          <div className="fixed inset-0 z-[1000] flex items-center justify-center p-4">
            <AiTravelCompanion
              context={demoContext}
              onClose={() => {
                setShowChat(false);
                setDemoContext(null);
              }}
            />
          </div>
        </>
      )}
    </div>
  );
} 