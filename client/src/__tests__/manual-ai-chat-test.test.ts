import { EnhancedAIChatTester } from './test-enhanced-ai-chat';

/**
 * Manual AI Chat Test Suite
 *
 * Simulates user journeys and validates AI/UX behavior for RoomLamAI's travel companion.
 * Run this manually to validate before shipping changes.
 */

async function runManualAIChatTestSuite() {
  const tester = EnhancedAIChatTester.getInstance();
  const results = [];

  // Test 1: Vague query - "Surprise me with amazing destinations"
  results.push(await tester.testUserQuery({
    input: 'Surprise me with amazing destinations',
    expect: [
      'suggestion', // Should offer suggestions, not just an error
      'popular destinations',
      'quick actions'
    ],
    description: 'Vague query should yield helpful suggestions, not a dead end.'
  }));

  // Test 2: No location - "Where should I go?"
  results.push(await tester.testUserQuery({
    input: 'Where should I go?',
    expect: [
      'suggestion',
      'popular destinations',
      'quick actions'
    ],
    description: 'No location should yield inspiration, not an error.'
  }));

  // Test 3: Valid city - "Show me hotels in Paris"
  results.push(await tester.testUserQuery({
    input: 'Show me hotels in Paris',
    expect: ['Paris', 'hotel', 'property card'],
    description: 'City query should yield property suggestions.'
  }));

  // Test 4: Quick action - "Explore Destinations"
  results.push(await tester.testQuickAction({
    actionLabel: 'Explore Destinations',
    expect: ['popular destinations', 'suggestion'],
    description: 'Quick action should yield destination suggestions.'
  }));

  // Test 5: Modal controls
  results.push(await tester.testModalControls());

  // Output results
  let allPassed = true;
  for (const result of results) {
    if (!result.passed) allPassed = false;
    console.log(result.passed ? '✅' : '❌', result.message, result.details || '');
  }
  if (allPassed) {
    console.log('🎉 All manual AI chat tests passed!');
  } else {
    console.error('❌ Some manual AI chat tests failed. See above for details.');
  }
}

// Expose for manual invocation
(window as any).runManualAIChatTestSuite = runManualAIChatTestSuite;

export default runManualAIChatTestSuite; 