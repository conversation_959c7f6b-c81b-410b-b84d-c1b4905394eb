/**
 * Location Detection Test Script
 * 
 * This script tests the implementation of the extractLocation flag
 * in the chat API to ensure it properly detects and returns location data.
 */

/**
 * Test the location detection feature with a sample query
 */
async function testLocationDetection() {
  console.log("=== Testing Location Detection Feature ===");
  const testQuery = "I'm looking for hotels in Miami Beach";
  const sessionId = `test-${Date.now()}`;
  
  console.log(`Query: "${testQuery}"`);
  
  try {
    // Call the API with extractLocation flag enabled
    const response = await fetch("/api/chat", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        message: testQuery,
        sessionId,
        extractLocation: true
      })
    });
    
    if (!response.ok) {
      throw new Error(`API request failed: ${response.status}`);
    }
    
    // Process the response
    const text = await response.text();
    const chunks = text
      .split('\n\n')
      .filter(chunk => chunk.startsWith('data:'))
      .map(chunk => chunk.replace(/^data: /, ''));
    
    console.log(`Received ${chunks.length} chunks`);
    
    // Look for location data
    let locationFound = false;
    let locationData = null;
    
    for (const chunk of chunks) {
      if (chunk === '[DONE]') continue;
      
      try {
        const parsed = JSON.parse(chunk);
        console.log(`Chunk type: ${parsed.type}`);
        
        if (parsed.type === 'location') {
          locationFound = true;
          locationData = parsed.data;
          console.log("✅ Location data found:", locationData);
          break;
        }
      } catch (error) {
        console.error("Error parsing chunk:", error);
      }
    }
    
    if (!locationFound) {
      console.log("❌ No location data found in response");
    }
    
    return { success: locationFound, location: locationData };
    
  } catch (error) {
    console.error("Test failed:", error);
    return { success: false, error: error.message };
  }
}

/**
 * Run multiple location tests with different queries
 */
export async function runLocationTests() {
  const testQueries = [
    "I'm looking for hotels in Miami Beach",
    "Find me a place to stay in New York",
    "Are there any good hotels in Chicago?",
    "I need accommodation in San Francisco",
    "Planning a trip to Las Vegas",
    "Traveling to Orlando next month"
  ];
  
  const results = [];
  
  for (const query of testQueries) {
    console.log(`\nTesting query: "${query}"`);
    const result = await testLocationDetection(query);
    results.push({ query, result });
  }
  
  console.log("\n=== Test Results Summary ===");
  let passCount = 0;
  
  for (const { query, result } of results) {
    if (result.success) {
      passCount++;
      console.log(`✅ "${query}" - Found location: ${result.location.name}`);
    } else {
      console.log(`❌ "${query}" - No location found`);
    }
  }
  
  console.log(`\nPassed: ${passCount}/${results.length} tests (${Math.round(passCount/results.length*100)}%)`);
  
  return results;
}

// Export the test functions
export function testLocationHandling() {
  console.log("Testing location handling in the chat interface");
  return testLocationDetection();
}

// Helper function to process location response
function processLocationResponse(response) {
  if (response && response.type === 'location' && response.data) {
    return {
      found: true,
      name: response.data.name,
      coordinates: {
        lat: response.data.lat,
        lng: response.data.lng
      },
      placeType: response.data.placeType
    };
  }
  return { found: false };
}

// Helper function to process location action
function processLocationAction(response) {
  if (response && 
      response.type === 'action' && 
      response.data && 
      response.data.type === 'location') {
    return {
      found: true,
      name: response.data.label,
      coordinates: response.data.data,
      action: true
    };
  }
  return { found: false };
}