import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function Debug() {
  const [apiResponse, setApiResponse] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testSearchAPI = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/properties/search?lat=25.7617&lng=-80.1918&locationName=Miami&checkIn=2024-01-15&checkOut=2024-01-17&guests=2&rooms=1&page=1&pageSize=3');
      
      if (!response.ok) {
        throw new Error(`API returned ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      setApiResponse(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <CardHeader>
          <CardTitle>API Debug Tool</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={testSearchAPI} disabled={loading}>
            {loading ? 'Testing...' : 'Test Search API'}
          </Button>
          
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded">
              <h3 className="font-semibold text-red-800">Error:</h3>
              <p className="text-red-700">{error}</p>
            </div>
          )}
          
          {apiResponse && (
            <div className="space-y-4">
              <h3 className="font-semibold">API Response:</h3>
              <div className="p-4 bg-gray-50 border rounded">
                <pre className="text-sm overflow-auto max-h-96">
                  {JSON.stringify(apiResponse, null, 2)}
                </pre>
              </div>
              
              {apiResponse.properties && apiResponse.properties.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-semibold">First Property Images:</h4>
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded">
                    <p><strong>Property ID:</strong> {apiResponse.properties[0].id}</p>
                    <p><strong>Property Name:</strong> {apiResponse.properties[0].name}</p>
                    <p><strong>Images Type:</strong> {typeof apiResponse.properties[0].images}</p>
                    <p><strong>Images Array:</strong> {Array.isArray(apiResponse.properties[0].images) ? 'Yes' : 'No'}</p>
                    <p><strong>Images Length:</strong> {Array.isArray(apiResponse.properties[0].images) ? apiResponse.properties[0].images.length : 'N/A'}</p>
                    <p><strong>First Image:</strong> {apiResponse.properties[0].images?.[0] || 'None'}</p>
                    
                    {apiResponse.properties[0].images?.[0] && (
                      <div className="mt-4">
                        <p><strong>Image Preview:</strong></p>
                        <img 
                          src={apiResponse.properties[0].images[0]} 
                          alt="Property" 
                          className="w-32 h-32 object-cover border rounded"
                          onError={(e) => {
                            console.error('Image failed to load:', apiResponse.properties[0].images[0]);
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 