import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectGroup, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { Loader2, AlertCircle, FileBadge, Calendar, BarChart3 } from 'lucide-react';

interface TestResult {
  id: number;
  sessionId: string;
  userId: number | null;
  testType: string;
  query: string;
  success: boolean;
  duration: number;
  timestamp: string;
  metrics: {
    totalTests?: number;
    passedTests?: number;
    failedTests?: number;
    skippedTests?: number;
    [key: string]: any;
  };
  context: {
    [key: string]: any;
  };
  results: {
    logs?: string;
    exitCode?: number;
    [key: string]: any;
  };
  error: string | null;
}

interface TestMetrics {
  totalTests: number;
  successRate: number;
  averageDuration: number;
  testsByType: {
    [key: string]: number;
  };
  testsByStatus: {
    passed: number;
    failed: number;
  };
  recentTests: TestResult[];
}

export default function TestResultsVisualizer() {
  const { toast } = useToast();
  const [testType, setTestType] = useState<string>('all');
  const [dateRange, setDateRange] = useState<string>('7d');
  const [selectedTestId, setSelectedTestId] = useState<number | null>(null);

  // Query to get test metrics
  const { data: metrics, isLoading: isLoadingMetrics, error: metricsError } = useQuery<TestMetrics>({
    queryKey: ['/api/tests/metrics', testType, dateRange],
    queryFn: async () => {
      const res = await fetch(`/api/tests/metrics?testType=${testType}&range=${dateRange}`);
      if (!res.ok) {
        throw new Error('Failed to fetch test metrics');
      }
      return res.json();
    },
  });

  // Query to get test results
  const { data: results, isLoading: isLoadingResults, error: resultsError } = useQuery<TestResult[]>({
    queryKey: ['/api/tests/results', testType, dateRange],
    queryFn: async () => {
      const res = await fetch(`/api/tests/results?testType=${testType}&range=${dateRange}`);
      if (!res.ok) {
        throw new Error('Failed to fetch test results');
      }
      const data = await res.json();
      return data.results;
    },
  });

  // Query to get a specific test result
  const { data: selectedTest, isLoading: isLoadingSelectedTest } = useQuery<TestResult>({
    queryKey: ['/api/tests/results', selectedTestId],
    queryFn: async () => {
      if (!selectedTestId) throw new Error('No test ID selected');
      const res = await fetch(`/api/tests/results/${selectedTestId}`);
      if (!res.ok) {
        throw new Error('Failed to fetch test details');
      }
      return res.json();
    },
    enabled: !!selectedTestId,
  });

  // Handle error cases
  if (metricsError || resultsError) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2 text-destructive" />
            Error Loading Test Results
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p>{metricsError instanceof Error ? metricsError.message : 'Failed to load metrics'}</p>
          <p>{resultsError instanceof Error ? resultsError.message : 'Failed to load results'}</p>
          <Button 
            variant="outline" 
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Prepare data for charts
  const prepareStatusData = () => {
    if (!metrics) return [];
    return [
      { name: 'Passed', value: metrics.testsByStatus.passed },
      { name: 'Failed', value: metrics.testsByStatus.failed },
    ];
  };

  const prepareTypeData = () => {
    if (!metrics) return [];
    return Object.entries(metrics.testsByType).map(([key, value]) => ({
      name: key,
      value,
    }));
  };

  // Colors for the charts
  const COLORS = ['#4ade80', '#f87171', '#facc15', '#60a5fa'];

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="w-full sm:w-auto">
            <Label htmlFor="test-type-filter">Test Type</Label>
            <Select
              value={testType}
              onValueChange={setTestType}
            >
              <SelectTrigger id="test-type-filter" className="w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="unit">Unit Tests</SelectItem>
                  <SelectItem value="functional">Functional Tests</SelectItem>
                  <SelectItem value="react">React Tests</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
          <div className="w-full sm:w-auto">
            <Label htmlFor="date-range-filter">Date Range</Label>
            <Select
              value={dateRange}
              onValueChange={setDateRange}
            >
              <SelectTrigger id="date-range-filter" className="w-[180px]">
                <SelectValue placeholder="Select range" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value="1d">Last 24 Hours</SelectItem>
                  <SelectItem value="7d">Last 7 Days</SelectItem>
                  <SelectItem value="30d">Last 30 Days</SelectItem>
                  <SelectItem value="all">All Time</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {(isLoadingMetrics || isLoadingResults) ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading test data...</span>
        </div>
      ) : (
        <Tabs defaultValue="dashboard">
          <TabsList className="mb-4">
            <TabsTrigger value="dashboard">
              <BarChart3 className="h-4 w-4 mr-2" />
              Dashboard
            </TabsTrigger>
            <TabsTrigger value="results">
              <FileBadge className="h-4 w-4 mr-2" />
              Test Results
            </TabsTrigger>
            <TabsTrigger value="detail" disabled={!selectedTestId}>
              <Calendar className="h-4 w-4 mr-2" />
              Test Detail
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="space-y-4">
            {metrics && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <MetricsCard 
                    title="Total Tests" 
                    value={metrics.totalTests.toString()} 
                  />
                  <MetricsCard 
                    title="Success Rate" 
                    value={`${(metrics.successRate * 100).toFixed(1)}%`}
                    color={metrics.successRate > 0.9 ? 'text-green-500' : metrics.successRate > 0.7 ? 'text-amber-500' : 'text-red-500'}
                  />
                  <MetricsCard 
                    title="Average Duration" 
                    value={`${(metrics.averageDuration / 1000).toFixed(2)}s`} 
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Tests by Status</CardTitle>
                    </CardHeader>
                    <CardContent className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={prepareStatusData()}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {prepareStatusData().map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Tests by Type</CardTitle>
                    </CardHeader>
                    <CardContent className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={prepareTypeData()}
                          margin={{
                            top: 5,
                            right: 30,
                            left: 20,
                            bottom: 5,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip />
                          <Bar dataKey="value" fill="#8884d8" />
                        </BarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>
              </>
            )}
          </TabsContent>

          <TabsContent value="results">
            <Card>
              <CardHeader>
                <CardTitle>Test Results</CardTitle>
              </CardHeader>
              <CardContent>
                {results && results.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="bg-muted">
                          <th className="p-2 text-left">ID</th>
                          <th className="p-2 text-left">Type</th>
                          <th className="p-2 text-left">Query</th>
                          <th className="p-2 text-left">Status</th>
                          <th className="p-2 text-left">Duration</th>
                          <th className="p-2 text-left">Timestamp</th>
                          <th className="p-2 text-left">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {results.map((result) => (
                          <tr key={result.id} className="border-b border-muted-foreground/20">
                            <td className="p-2">{result.id}</td>
                            <td className="p-2">{result.testType}</td>
                            <td className="p-2 max-w-xs truncate">{result.query}</td>
                            <td className="p-2">
                              <span className={`px-2 py-1 rounded-full text-xs ${result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                {result.success ? 'Passed' : 'Failed'}
                              </span>
                            </td>
                            <td className="p-2">{(result.duration / 1000).toFixed(2)}s</td>
                            <td className="p-2">{new Date(result.timestamp).toLocaleString()}</td>
                            <td className="p-2">
                              <Button 
                                variant="outline" 
                                size="sm" 
                                onClick={() => setSelectedTestId(result.id)}
                              >
                                View
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center p-4">
                    <p className="text-muted-foreground">No test results found for the selected criteria.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="detail">
            {isLoadingSelectedTest ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2">Loading test details...</span>
              </div>
            ) : selectedTest ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Test Detail #{selectedTest.id}</span>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => setSelectedTestId(null)}
                    >
                      Back to Results
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Test Type</Label>
                      <div className="bg-muted p-2 rounded">{selectedTest.testType}</div>
                    </div>
                    <div className="space-y-2">
                      <Label>Query</Label>
                      <div className="bg-muted p-2 rounded break-words">{selectedTest.query}</div>
                    </div>
                    <div className="space-y-2">
                      <Label>Status</Label>
                      <div className={`p-2 rounded ${selectedTest.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                        {selectedTest.success ? 'Passed' : 'Failed'}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>Duration</Label>
                      <div className="bg-muted p-2 rounded">{(selectedTest.duration / 1000).toFixed(2)}s</div>
                    </div>
                    <div className="space-y-2">
                      <Label>Timestamp</Label>
                      <div className="bg-muted p-2 rounded">{new Date(selectedTest.timestamp).toLocaleString()}</div>
                    </div>
                    <div className="space-y-2">
                      <Label>Session ID</Label>
                      <div className="bg-muted p-2 rounded">{selectedTest.sessionId}</div>
                    </div>
                  </div>

                  {selectedTest.metrics && Object.keys(selectedTest.metrics).length > 0 && (
                    <div className="space-y-2">
                      <Label>Metrics</Label>
                      <div className="bg-muted p-2 rounded overflow-x-auto">
                        <pre className="text-sm">
                          {JSON.stringify(selectedTest.metrics, null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}

                  {selectedTest.results && selectedTest.results.logs && (
                    <div className="space-y-2">
                      <Label>Logs</Label>
                      <div className="bg-black text-white p-4 rounded h-80 overflow-auto font-mono text-sm whitespace-pre-wrap">
                        {selectedTest.results.logs}
                      </div>
                    </div>
                  )}

                  {selectedTest.error && (
                    <div className="space-y-2">
                      <Label>Error</Label>
                      <div className="bg-red-100 text-red-800 p-2 rounded">
                        {selectedTest.error}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-4 text-center">
                  <p className="text-muted-foreground">Select a test to view details</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}

function MetricsCard({ title, value, color = 'text-foreground' }: { title: string; value: string; color?: string }) {
  return (
    <Card>
      <CardContent className="pt-6">
        <div className="text-center">
          <h3 className="text-lg font-medium text-muted-foreground">{title}</h3>
          <p className={`text-3xl font-bold mt-2 ${color}`}>{value}</p>
        </div>
      </CardContent>
    </Card>
  );
}