/**
 * Quick Chat Test for <PERSON><PERSON><PERSON> Console
 * 
 * Simple functions to test the "Plan with AI" functionality
 * directly in the browser console.
 */

// Make functions available globally for easy testing
declare global {
  interface Window {
    testPlanWithAI: () => void;
    checkChatState: () => void;
    clearChatData: () => void;
    simulatePlanWithAI: () => void;
  }
}

// Test function to simulate the "Plan with AI" button click
window.testPlanWithAI = () => {
  console.log('🧪 Testing Plan with AI functionality...');
  
  // Clear existing data
  localStorage.removeItem('chatHistory');
  localStorage.removeItem('conversationState');
  localStorage.removeItem('ai_chat_trigger');
  console.log('🧹 Cleared existing data');
  
  // Create test message
  const messageId = `test_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  const testMessage = {
    role: "user",
    content: "Hi! I'm looking for a place to stay in New York for 2 people from June 15-20. Can you help me find the perfect place?",
    id: messageId
  };
  
  // Store in localStorage
  localStorage.setItem('chatHistory', JSON.stringify([testMessage]));
  localStorage.setItem('ai_chat_trigger', 'true');
  
  console.log('💬 Test message stored:', testMessage);
  console.log('🚀 Trigger flag set. Now manually open the AI chat to see if it processes automatically.');
  
  // Test the API directly to see if it's working
  console.log('🧪 Testing API directly...');
  fetch('/api/chat', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
      message: testMessage.content,
      sessionId: 'test-session-' + Date.now()
    })
  }).then(async response => {
    console.log('📡 API Response Status:', response.status);
    if (response.ok) {
      console.log('✅ API is working - response received');
      // Log first few chunks
      const reader = response.body?.getReader();
      if (reader) {
        for (let i = 0; i < 3; i++) {
          const {value, done} = await reader.read();
          if (done) break;
          console.log(`📦 Chunk ${i+1}:`, new TextDecoder().decode(value).slice(0, 100) + '...');
        }
      }
    } else {
      console.error('❌ API Error:', response.status);
    }
  }).catch(error => {
    console.error('❌ API Request Failed:', error);
  });
};

// Function to check current chat state
window.checkChatState = () => {
  console.log('📊 Current Chat State:');
  console.log('Chat History:', localStorage.getItem('chatHistory'));
  console.log('Trigger Flag:', localStorage.getItem('ai_chat_trigger'));
  console.log('Conversation State:', localStorage.getItem('conversationState'));
  console.log('Session ID:', localStorage.getItem('booking_session_id'));
};

// Function to clear all chat data
window.clearChatData = () => {
  localStorage.removeItem('chatHistory');
  localStorage.removeItem('conversationState');
  localStorage.removeItem('ai_chat_trigger');
  localStorage.removeItem('booking_session_id');
  console.log('🧹 All chat data cleared');
};

// Simulate the exact same flow as the Plan with AI button
window.simulatePlanWithAI = () => {
  console.log('🎯 Simulating Plan with AI button click...');
  
  // Clear any existing chat history and conversation state for a fresh start
  localStorage.removeItem('chatHistory');
  localStorage.removeItem('conversationState');
  localStorage.removeItem('ai_chat_trigger');
  
  console.log('🧹 Cleared existing localStorage data');
  
  // Generate a unique ID for the welcome message
  const messageId = `user_init_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  
  // Create the same message that would be generated by getInitialMessage()
  const initialMessage = {
    role: "user",
    content: "Hi! I'm looking for a place to stay. Can you help me find the perfect place?",
    id: messageId
  };
  
  console.log('💬 Storing initial message:', initialMessage);
  localStorage.setItem('chatHistory', JSON.stringify([initialMessage]));
  
  // Set a trigger flag to ensure the message gets processed
  localStorage.setItem('ai_chat_trigger', 'true');
  console.log('🚀 Set trigger flag to true');
  
  console.log('✨ Simulation complete. Now open the AI chat modal to test.');
};

console.log('🎯 Chat Testing Functions Available:');
console.log('  - testPlanWithAI() - Basic test');
console.log('  - simulatePlanWithAI() - Full simulation');
console.log('  - checkChatState() - Check current state');
console.log('  - clearChatData() - Clear all data');

export {}; 