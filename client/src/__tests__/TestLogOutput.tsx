import React, { useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useTestRunner, TestRun } from '@/hooks/use-test-runner';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Download, Clipboard, RefreshCw } from 'lucide-react';

export function TestLogOutput() {
  const { testRuns, getRunLogs, clearRunLogs } = useTestRunner();
  const [selectedRunId, setSelectedRunId] = React.useState<string | null>(null);
  const logContainerRef = useRef<HTMLDivElement>(null);

  // Auto-select the most recent run
  useEffect(() => {
    if (testRuns.length > 0 && (!selectedRunId || !testRuns.find(run => run.runId === selectedRunId))) {
      // Sort by start time descending
      const sortedRuns = [...testRuns].sort((a, b) => b.startTime - a.startTime);
      setSelectedRunId(sortedRuns[0].runId);
    }
  }, [testRuns, selectedRunId]);

  // Auto-scroll to bottom when logs update
  useEffect(() => {
    if (selectedRunId && logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [selectedRunId, getRunLogs(selectedRunId || '')]);

  const handleTabChange = (runId: string) => {
    setSelectedRunId(runId);
  };

  const selectedRun = testRuns.find(run => run.runId === selectedRunId);
  const logs = selectedRunId ? getRunLogs(selectedRunId) : [];

  const handleCopyLogs = () => {
    if (logs.length > 0) {
      navigator.clipboard.writeText(logs.join('\n'));
    }
  };

  const handleDownloadLogs = () => {
    if (logs.length > 0 && selectedRun) {
      const blob = new Blob([logs.join('\n')], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `test-run-${selectedRun.testType}-${selectedRunId?.substring(0, 8)}.log`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const handleClearLogs = () => {
    if (selectedRunId) {
      clearRunLogs(selectedRunId);
    }
  };

  if (testRuns.length === 0) {
    return (
      <Card className="w-full h-[400px] bg-card">
        <CardHeader>
          <CardTitle>Test Logs</CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-[300px]">
          <p className="text-muted-foreground">No test runs available. Start a test run to see logs.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full bg-card">
      <CardHeader className="pb-0">
        <div className="flex justify-between items-center">
          <CardTitle>Test Logs</CardTitle>
          <div className="space-x-2">
            <Button 
              size="sm" 
              variant="outline" 
              onClick={handleCopyLogs}
              disabled={logs.length === 0}
            >
              <Clipboard className="h-4 w-4 mr-1" />
              Copy
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              onClick={handleDownloadLogs}
              disabled={logs.length === 0}
            >
              <Download className="h-4 w-4 mr-1" />
              Download
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={handleClearLogs}
              disabled={logs.length === 0}
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Clear
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <Tabs
          value={selectedRunId || undefined}
          onValueChange={handleTabChange}
          className="w-full"
        >
          <TabsList className="mt-2">
            {testRuns.map(run => (
              <TabsTrigger key={run.runId} value={run.runId} className="text-xs">
                {run.testType} ({run.runId.substring(0, 8)})
                <span 
                  className={`ml-2 w-2 h-2 rounded-full ${
                    run.status === 'running' ? 'bg-blue-500' : 
                    run.status === 'completed' ? 'bg-green-500' : 
                    'bg-red-500'
                  }`}
                />
              </TabsTrigger>
            ))}
          </TabsList>

          {testRuns.map(run => (
            <TabsContent key={run.runId} value={run.runId} className="mt-0 p-0">
              <ScrollArea 
                ref={logContainerRef} 
                className="h-[400px] w-full border rounded-md bg-black text-white p-2 font-mono text-sm whitespace-pre-wrap overflow-auto"
              >
                {getRunLogs(run.runId).map((log, index) => (
                  <div key={index} className="log-line">
                    {log}
                  </div>
                ))}
                {getRunLogs(run.runId).length === 0 && (
                  <div className="flex items-center justify-center h-full text-gray-400">
                    Waiting for test output...
                  </div>
                )}
              </ScrollArea>
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  );
}