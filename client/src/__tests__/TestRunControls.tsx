import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useTestRunner, TestRun } from '@/hooks/use-test-runner';
import { Loader2, Play, StopCircle, CheckCircle2, AlertCircle } from 'lucide-react';

export function TestRunControls() {
  const [testType, setTestType] = useState<'unit' | 'functional' | 'react' | 'all'>('all');
  const [testFile, setTestFile] = useState<string>('');
  const { 
    testRuns, 
    startTestRun, 
    stopTestRun, 
    isStarting, 
    isStopping 
  } = useTestRunner();

  const handleStartTest = () => {
    const options = testFile ? { testFile } : undefined;
    startTestRun({ testType, options });
  };

  return (
    <Card className="w-full bg-card">
      <CardHeader>
        <CardTitle>Test Runner</CardTitle>
        <CardDescription>
          Run tests to verify system functionality
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="test-type">Test Type</Label>
              <Select
                value={testType}
                onValueChange={(value) => setTestType(value as any)}
              >
                <SelectTrigger id="test-type">
                  <SelectValue placeholder="Select test type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Test Types</SelectLabel>
                    <SelectItem value="unit">Unit Tests</SelectItem>
                    <SelectItem value="functional">Functional Tests</SelectItem>
                    <SelectItem value="react">React Component Tests</SelectItem>
                    <SelectItem value="all">All Tests</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="test-file">Test File (Optional)</Label>
              <Input
                id="test-file"
                placeholder="Specific test file path"
                value={testFile}
                onChange={(e) => setTestFile(e.target.value)}
              />
            </div>
          </div>
          
          <div className="pt-2">
            <Button
              onClick={handleStartTest}
              disabled={isStarting}
              className="mr-2"
            >
              {isStarting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Play className="mr-2 h-4 w-4" />
              )}
              Run Tests
            </Button>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex-col items-start border-t p-4">
        <h3 className="text-sm font-medium mb-2">Active Test Runs</h3>
        {testRuns.length > 0 ? (
          <div className="w-full space-y-2">
            {testRuns.map((run) => (
              <TestRunItem 
                key={run.runId} 
                run={run} 
                onStop={() => stopTestRun(run.runId)} 
                isStopping={isStopping} 
              />
            ))}
          </div>
        ) : (
          <p className="text-sm text-muted-foreground">No active test runs</p>
        )}
      </CardFooter>
    </Card>
  );
}

function TestRunItem({ run, onStop, isStopping }: { 
  run: TestRun; 
  onStop: () => void; 
  isStopping: boolean;
}) {
  const statusIcon = {
    running: <Loader2 className="h-4 w-4 animate-spin text-blue-500" />,
    completed: <CheckCircle2 className="h-4 w-4 text-green-500" />,
    failed: <AlertCircle className="h-4 w-4 text-destructive" />,
  }[run.status];

  const duration = run.duration ? 
    `${(run.duration / 1000).toFixed(1)}s` : 
    ((Date.now() - run.startTime) / 1000).toFixed(1) + 's';

  return (
    <div className="flex items-center justify-between bg-accent/30 p-2 rounded">
      <div className="flex items-center space-x-2">
        {statusIcon}
        <div>
          <p className="text-sm font-medium">
            {run.testType} 
            <span className="text-muted-foreground ml-2 text-xs">
              {run.runId.substring(0, 8)}
            </span>
          </p>
          <div className="text-xs flex items-center space-x-2">
            <span>
              {run.status}
            </span>
            <span>•</span>
            <span>
              {duration}
            </span>
            {run.summary && (
              <>
                <span>•</span>
                <span className="text-green-500">{run.summary.passed} ✓</span>
                {run.summary.failed > 0 && (
                  <span className="text-destructive">{run.summary.failed} ✗</span>
                )}
              </>
            )}
          </div>
        </div>
      </div>
      {run.status === 'running' && (
        <Button
          size="sm"
          variant="outline"
          onClick={onStop}
          disabled={isStopping}
        >
          <StopCircle className="h-4 w-4 mr-1" />
          Stop
        </Button>
      )}
    </div>
  );
}