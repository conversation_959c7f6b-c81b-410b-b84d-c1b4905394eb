/**
 * Chat Interaction Test Utility
 * 
 * This utility provides comprehensive testing for the full AI chat interaction flow:
 * 1. Location detection
 * 2. Property recommendations
 * 3. Contextual follow-ups
 * 4. Link generation for property details
 * 
 * It ensures the entire user journey is seamless from initial query to booking.
 */

import { validateLocationResponse } from './location-test-utility';
import { Property, PropertyWithRates } from '@/types/schema';

interface ChatTestOptions {
  extractLocation?: boolean;
  includeHistory?: boolean;
  sessionId?: string;
}

interface LocationData {
  name: string;
  lat: number;
  lng: number;
  placeType?: string;
}

interface PropertyReference {
  id: number;
  name: string;
}

interface LinkAction {
  type: 'link';
  target: string;
  label: string;
}

interface ChatInteractionResult {
  success: boolean;
  location?: LocationData;
  properties?: PropertyReference[];
  conversations?: {
    query: string;
    response: string[];
  }[];
  actions?: {
    links?: LinkAction[];
    bookingAttempt?: boolean;
    searchInitiated?: boolean;
  };
  error?: string;
  duration: number;
}

/**
 * Test the complete chat interaction flow
 * @param initialQuery The initial user query
 * @param followupQueries Any followup queries to send after the initial one
 * @param options Test configuration options
 */
export async function testChatInteraction(
  initialQuery: string,
  followupQueries: string[] = [],
  options: ChatTestOptions = {}
): Promise<ChatInteractionResult> {
  const startTime = performance.now();
  const sessionId = options.sessionId || `test-${Date.now()}`;
  const extractLocation = options.extractLocation !== false; // Default to true
  const includeHistory = options.includeHistory !== false; // Default to true
  
  const result: ChatInteractionResult = {
    success: false,
    conversations: [],
    duration: 0
  };
  
  try {
    // Step 1: Initial query to test location detection
    console.log(`Testing initial query: "${initialQuery}"`);
    const initialResponse = await sendChatMessage(initialQuery, sessionId, { extractLocation });
    
    // Process initial response
    const locationData = extractLocationFromResponse(initialResponse);
    if (locationData) {
      result.location = locationData;
      result.success = true;
      console.log(`✅ Location detected: ${locationData.name}`);
    }
    
    // Extract text responses
    const textResponses = extractTextResponses(initialResponse);
    result.conversations = [{ query: initialQuery, response: textResponses }];
    
    // Extract property recommendations
    const properties = extractPropertyReferences(initialResponse);
    if (properties && properties.length > 0) {
      result.properties = properties;
      console.log(`✅ Properties recommended: ${properties.length}`);
    }
    
    // Extract action links
    const links = extractActionLinks(initialResponse);
    if (links && links.length > 0) {
      result.actions = { links };
      console.log(`✅ Action links detected: ${links.length}`);
    }
    
    // Step 2: Process follow-up queries if any
    for (const followupQuery of followupQueries) {
      console.log(`Testing followup query: "${followupQuery}"`);
      const followupResponse = await sendChatMessage(followupQuery, sessionId, { includeHistory });
      
      // Extract text responses for this followup
      const followupTextResponses = extractTextResponses(followupResponse);
      result.conversations.push({ query: followupQuery, response: followupTextResponses });
      
      // Check for additional properties
      const additionalProperties = extractPropertyReferences(followupResponse);
      if (additionalProperties && additionalProperties.length > 0) {
        result.properties = [...(result.properties || []), ...additionalProperties];
        console.log(`✅ Additional properties recommended: ${additionalProperties.length}`);
      }
      
      // Check for additional action links
      const additionalLinks = extractActionLinks(followupResponse);
      if (additionalLinks && additionalLinks.length > 0) {
        result.actions = result.actions || {};
        result.actions.links = [...(result.actions.links || []), ...additionalLinks];
        console.log(`✅ Additional action links detected: ${additionalLinks.length}`);
      }
      
      // Check if search was initiated
      if (followupResponse.includes('search_initiated')) {
        result.actions = result.actions || {};
        result.actions.searchInitiated = true;
        console.log('✅ Search initiated');
      }
      
      // Check if booking attempt was detected
      if (followupResponse.includes('booking_attempt')) {
        result.actions = result.actions || {};
        result.actions.bookingAttempt = true;
        console.log('✅ Booking attempt detected');
      }
    }
    
    // Final success determination
    result.success = !!result.location || !!(result.properties && result.properties.length > 0);
    
  } catch (error) {
    result.success = false;
    result.error = error instanceof Error ? error.message : String(error);
    console.error('Chat interaction test failed:', result.error);
  } finally {
    const endTime = performance.now();
    result.duration = Math.round(endTime - startTime);
    console.log(`Test completed in ${result.duration}ms`);
  }
  
  return result;
}

/**
 * Send a chat message and return the raw response
 */
async function sendChatMessage(
  message: string,
  sessionId: string,
  options: { extractLocation?: boolean; includeHistory?: boolean } = {}
): Promise<string> {
  const extractLocation = options.extractLocation !== false;
  const includeHistory = options.includeHistory !== false;
  
  const response = await fetch('/api/chat', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      message,
      sessionId,
      extractLocation,
      includeHistory
    })
  });
  
  if (!response.ok) {
    throw new Error(`API request failed: ${response.status}`);
  }
  
  // Process the streaming response
  const reader = response.body?.getReader();
  if (!reader) {
    throw new Error('Cannot read response stream');
  }
  
  let responseText = '';
  
  // Read all chunks from the stream
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    
    // Convert bytes to text
    const chunk = new TextDecoder().decode(value);
    responseText += chunk;
  }
  
  return responseText;
}

/**
 * Extract location data from a chat response
 */
function extractLocationFromResponse(responseText: string): LocationData | undefined {
  try {
    // Parse SSE format
    const chunks = responseText
      .split('\n\n')
      .filter(chunk => chunk.startsWith('data:'))
      .map(chunk => chunk.replace(/^data: /, ''));
    
    for (const chunk of chunks) {
      if (chunk === '[DONE]') continue;
      
      try {
        const parsed = JSON.parse(chunk);
        const validationResult = validateLocationResponse(parsed);
        
        if (validationResult.isValid && validationResult.locationData) {
          return validationResult.locationData;
        }
      } catch (error) {
        console.warn('Error parsing chunk:', error);
      }
    }
  } catch (error) {
    console.error('Error extracting location:', error);
  }
  
  return undefined;
}

/**
 * Extract text responses from a chat response
 */
function extractTextResponses(responseText: string): string[] {
  try {
    const textResponses: string[] = [];
    
    // Parse SSE format
    const chunks = responseText
      .split('\n\n')
      .filter(chunk => chunk.startsWith('data:'))
      .map(chunk => chunk.replace(/^data: /, ''));
    
    for (const chunk of chunks) {
      if (chunk === '[DONE]') continue;
      
      try {
        const parsed = JSON.parse(chunk);
        if (parsed.type === 'text' && parsed.data) {
          textResponses.push(parsed.data);
        }
      } catch (error) {
        console.warn('Error parsing text chunk:', error);
      }
    }
    
    return textResponses;
  } catch (error) {
    console.error('Error extracting text responses:', error);
    return [];
  }
}

/**
 * Extract property references from a chat response
 */
function extractPropertyReferences(responseText: string): PropertyReference[] | undefined {
  try {
    // Parse SSE format
    const chunks = responseText
      .split('\n\n')
      .filter(chunk => chunk.startsWith('data:'))
      .map(chunk => chunk.replace(/^data: /, ''));
    
    for (const chunk of chunks) {
      if (chunk === '[DONE]') continue;
      
      try {
        const parsed = JSON.parse(chunk);
        if (parsed.type === 'properties' && Array.isArray(parsed.data)) {
          return parsed.data.map((property: any) => ({
            id: property.id,
            name: property.name
          }));
        }
      } catch (error) {
        console.warn('Error parsing properties chunk:', error);
      }
    }
  } catch (error) {
    console.error('Error extracting properties:', error);
  }
  
  return undefined;
}

/**
 * Extract action links from a chat response
 */
function extractActionLinks(responseText: string): LinkAction[] | undefined {
  try {
    const links: LinkAction[] = [];
    
    // Parse SSE format
    const chunks = responseText
      .split('\n\n')
      .filter(chunk => chunk.startsWith('data:'))
      .map(chunk => chunk.replace(/^data: /, ''));
    
    for (const chunk of chunks) {
      if (chunk === '[DONE]') continue;
      
      try {
        const parsed = JSON.parse(chunk);
        if (parsed.type === 'action' && 
            parsed.data && 
            (parsed.data.type === 'link' || parsed.data.type === 'property')) {
          
          links.push({
            type: 'link',
            target: parsed.data.data?.url || `/property/${parsed.data.data?.id}`,
            label: parsed.data.label || 'View Details'
          });
        }
      } catch (error) {
        console.warn('Error parsing action chunk:', error);
      }
    }
    
    return links.length > 0 ? links : undefined;
  } catch (error) {
    console.error('Error extracting action links:', error);
    return undefined;
  }
}

/**
 * Run a set of predefined tests to verify chat functionality
 */
export async function runChatIntegrationTests(): Promise<{
  results: ChatInteractionResult[];
  overallSuccess: boolean;
}> {
  const testScenarios = [
    {
      initialQuery: "I'm looking for hotels in Miami Beach",
      followupQueries: ["Can you show me more details about the first hotel?"]
    },
    {
      initialQuery: "Find me a place to stay in San Francisco",
      followupQueries: ["Which one has the best reviews?", "Does it have a pool?"]
    },
    {
      initialQuery: "Planning a trip to New York next month",
      followupQueries: ["What areas are best to stay in?", "Show me hotels in Midtown"]
    },
    {
      initialQuery: "I need a beach resort in Hawaii",
      followupQueries: ["What activities are available there?"]
    }
  ];
  
  const results: ChatInteractionResult[] = [];
  
  for (const scenario of testScenarios) {
    console.log(`\n=== Testing Scenario ===`);
    console.log(`Initial: "${scenario.initialQuery}"`);
    console.log(`Followups: ${scenario.followupQueries.join(', ')}`);
    
    const result = await testChatInteraction(
      scenario.initialQuery,
      scenario.followupQueries
    );
    
    results.push(result);
  }
  
  // Calculate overall success rate
  const successCount = results.filter(result => result.success).length;
  const overallSuccess = successCount > results.length / 2;
  
  console.log(`\n=== Test Summary ===`);
  console.log(`Passed: ${successCount}/${results.length} scenarios`);
  console.log(`Overall: ${overallSuccess ? 'SUCCESS' : 'FAILED'}`);
  
  return {
    results,
    overallSuccess
  };
}

// Allow this to be run from the browser console for testing
if (typeof window !== 'undefined') {
  // @ts-ignore - For debugging in browser console
  window.testChatInteraction = testChatInteraction;
  // @ts-ignore - For debugging in browser console
  window.runChatIntegrationTests = runChatIntegrationTests;
}