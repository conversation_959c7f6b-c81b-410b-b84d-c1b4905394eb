/**
 * End-to-End AI Chat Test for Replit Environment
 * 
 * This comprehensive test simulates the actual user experience
 * of the "Plan with AI" functionality in the Replit environment.
 */

interface TestStep {
  name: string;
  test: () => Promise<boolean>;
  description: string;
  critical: boolean;
}

interface E2ETestResult {
  success: boolean;
  steps: Array<{
    name: string;
    success: boolean;
    error?: string;
    details?: any;
  }>;
  summary: string;
}

export class E2EChatTester {
  private static instance: E2EChatTester;
  
  static getInstance(): E2EChatTester {
    if (!E2EChatTester.instance) {
      E2EChatTester.instance = new E2EChatTester();
    }
    return E2EChatTester.instance;
  }

  /**
   * Test the complete "Plan with AI" user flow
   */
  async testCompleteUserFlow(): Promise<E2ETestResult> {
    console.log('🚀 Starting Complete E2E Test for "Plan with AI" in Replit Environment...');
    
    const steps: TestStep[] = [
      {
        name: 'Server Health Check',
        description: 'Verify server is running and accessible',
        critical: true,
        test: async () => {
          try {
            // In Replit, we need to check the actual external URL
            const response = await fetch('/api/config', {
              method: 'GET',
              headers: { 'Accept': 'application/json' }
            });
            return response.ok;
          } catch (error) {
            console.error('Server health check failed:', error);
            return false;
          }
        }
      },
      
      {
        name: 'Chat API Availability',
        description: 'Test if the chat API endpoint is accessible',
        critical: true,
        test: async () => {
          try {
            const response = await fetch('/api/chat', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                message: 'Test connectivity',
                sessionId: `e2e-test-${Date.now()}`
              })
            });
            return response.ok;
          } catch (error) {
            console.error('Chat API test failed:', error);
            return false;
          }
        }
      },

      {
        name: 'LocalStorage Functionality',
        description: 'Test localStorage operations for chat state',
        critical: true,
        test: async () => {
          try {
            // Test writing and reading from localStorage
            const testData = { test: 'e2e-test', timestamp: Date.now() };
            localStorage.setItem('e2e-test', JSON.stringify(testData));
            const retrieved = localStorage.getItem('e2e-test');
            localStorage.removeItem('e2e-test');
            return retrieved !== null && JSON.parse(retrieved).test === 'e2e-test';
          } catch (error) {
            console.error('LocalStorage test failed:', error);
            return false;
          }
        }
      },

      {
        name: 'Plan with AI Initialization',
        description: 'Simulate the "Plan with AI" button click flow',
        critical: true,
        test: async () => {
          try {
            // Clear any existing data
            localStorage.removeItem('chatHistory');
            localStorage.removeItem('ai_chat_trigger');
            localStorage.removeItem('conversationState');
            
            // Simulate the user flow
            const initialMessage = {
              role: "user",
              content: "Hi! I'm looking for a place to stay in New York for 2 people from June 15-20. Can you help me find the perfect place?",
              id: `e2e_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
            };
            
            localStorage.setItem('chatHistory', JSON.stringify([initialMessage]));
            localStorage.setItem('ai_chat_trigger', 'true');
            
            // Verify data was stored correctly
            const storedHistory = localStorage.getItem('chatHistory');
            const storedTrigger = localStorage.getItem('ai_chat_trigger');
            
            return storedHistory !== null && storedTrigger === 'true';
          } catch (error) {
            console.error('Plan with AI initialization test failed:', error);
            return false;
          }
        }
      },

      {
        name: 'AI Response Generation',
        description: 'Test actual AI response to travel query',
        critical: true,
        test: async () => {
          try {
            const testMessage = "I need a hotel in Paris for 3 nights, budget around $200 per night";
            const sessionId = `e2e-test-${Date.now()}`;
            
            const response = await fetch('/api/chat', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                message: testMessage,
                sessionId,
                context: {
                  extractLocation: true,
                  location: { name: 'Paris' },
                  preferences: { budget: 200 }
                }
              })
            });

            if (!response.ok) {
              console.error('AI response failed with status:', response.status);
              return false;
            }

            // Test streaming response
            const reader = response.body?.getReader();
            if (!reader) {
              console.error('No readable stream in response');
              return false;
            }

            let hasContent = false;
            let chunkCount = 0;
            const maxChunks = 5; // Limit reading to avoid hanging

            try {
              while (chunkCount < maxChunks) {
                const { done, value } = await reader.read();
                if (done) break;
                
                if (value && value.length > 0) {
                  hasContent = true;
                  const text = new TextDecoder().decode(value);
                  // Look for data chunks (streaming format)
                  if (text.includes('data:') || text.includes('text')) {
                    console.log('✅ Received AI response chunk');
                    break;
                  }
                }
                chunkCount++;
              }
            } finally {
              reader.releaseLock();
            }

            return hasContent;
          } catch (error) {
            console.error('AI response test failed:', error);
            return false;
          }
        }
      },

      {
        name: 'Session Management',
        description: 'Test session ID generation and persistence',
        critical: false,
        test: async () => {
          try {
            localStorage.removeItem('booking_session_id');
            
            // Simulate session ID generation like in AiChat component
            const newSessionId = `session-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
            localStorage.setItem('booking_session_id', newSessionId);
            
            const storedSessionId = localStorage.getItem('booking_session_id');
            return storedSessionId === newSessionId && storedSessionId.startsWith('session-');
          } catch (error) {
            console.error('Session management test failed:', error);
            return false;
          }
        }
      },

      {
        name: 'Error Handling',
        description: 'Test graceful error handling for malformed requests',
        critical: false,
        test: async () => {
          try {
            // Test with malformed request
            const response = await fetch('/api/chat', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                // Missing required message field
                sessionId: 'error-test'
              })
            });
            
            // Should return 400 for bad request
            return response.status === 400;
          } catch (error) {
            // Network errors are expected for malformed requests
            return true;
          }
        }
      }
    ];

    const results: E2ETestResult = {
      success: true,
      steps: [],
      summary: ''
    };

    // Run all test steps
    for (const step of steps) {
      console.log(`🧪 Testing: ${step.name}...`);
      
      let stepSuccess = false;
      let error: string | undefined;
      let details: any;

      try {
        stepSuccess = await step.test();
        if (stepSuccess) {
          console.log(`✅ ${step.name}: PASSED`);
        } else {
          console.log(`❌ ${step.name}: FAILED`);
          if (step.critical) {
            results.success = false;
          }
        }
      } catch (err) {
        stepSuccess = false;
        error = err instanceof Error ? err.message : 'Unknown error';
        console.log(`❌ ${step.name}: ERROR - ${error}`);
        if (step.critical) {
          results.success = false;
        }
      }

      results.steps.push({
        name: step.name,
        success: stepSuccess,
        error,
        details
      });
    }

    // Generate summary
    const criticalSteps = results.steps.filter((_, index) => steps[index].critical);
    const criticalPassed = criticalSteps.filter(step => step.success).length;
    const totalSteps = results.steps.length;
    const totalPassed = results.steps.filter(step => step.success).length;

    results.summary = `${totalPassed}/${totalSteps} tests passed (${criticalPassed}/${criticalSteps.length} critical)`;

    if (results.success) {
      console.log('🎉 All critical tests passed! AI Chat functionality is working.');
    } else {
      console.log('⚠️ Some critical tests failed. Please check the issues above.');
    }

    return results;
  }

  /**
   * Quick health check for development
   */
  async quickHealthCheck(): Promise<boolean> {
    console.log('🔍 Running quick health check...');
    
    try {
      // Test server availability
      const configResponse = await fetch('/api/config');
      if (!configResponse.ok) {
        console.log('❌ Server not responding');
        return false;
      }

      // Test chat API
      const chatResponse = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: 'Quick health check',
          sessionId: `health-${Date.now()}`
        })
      });

      if (!chatResponse.ok) {
        console.log('❌ Chat API not responding');
        return false;
      }

      console.log('✅ Health check passed');
      return true;
    } catch (error) {
      console.log('❌ Health check failed:', error);
      return false;
    }
  }

  /**
   * Test real user scenario
   */
  async testRealUserScenario(): Promise<boolean> {
    console.log('👤 Testing real user scenario...');
    
    try {
      // Simulate user searching for a vacation rental
      const userMessage = "I'm planning a family vacation to Orlando for 5 people, staying 7 nights in July. We need a place with a pool and near Disney World. Budget is around $300 per night.";
      
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: userMessage,
          sessionId: `user-scenario-${Date.now()}`,
          context: {
            extractLocation: true,
            guests: 5,
            preferences: {
              amenities: ['pool'],
              propertyTypes: ['vacation-rental'],
              priceRange: [250, 350]
            }
          }
        })
      });

      if (!response.ok) {
        console.log('❌ User scenario test failed - API error');
        return false;
      }

      // Check if we get a streaming response
      const reader = response.body?.getReader();
      if (!reader) {
        console.log('❌ No streaming response');
        return false;
      }

      let hasRelevantContent = false;
      let chunkCount = 0;

      try {
        while (chunkCount < 10) {
          const { done, value } = await reader.read();
          if (done) break;
          
          if (value) {
            const text = new TextDecoder().decode(value);
            // Look for travel-related keywords in response
            if (text.toLowerCase().includes('orlando') || 
                text.toLowerCase().includes('disney') || 
                text.toLowerCase().includes('pool') ||
                text.toLowerCase().includes('family')) {
              hasRelevantContent = true;
              console.log('✅ AI provided relevant travel response');
              break;
            }
          }
          chunkCount++;
        }
      } finally {
        reader.releaseLock();
      }

      return hasRelevantContent;
    } catch (error) {
      console.log('❌ User scenario test error:', error);
      return false;
    }
  }

  /**
   * Clean up all test data
   */
  cleanup(): void {
    try {
      localStorage.removeItem('chatHistory');
      localStorage.removeItem('ai_chat_trigger');
      localStorage.removeItem('conversationState');
      localStorage.removeItem('booking_session_id');
      localStorage.removeItem('e2e-test');
      console.log('🧹 E2E test cleanup completed');
    } catch (error) {
      console.log('⚠️ Cleanup error:', error);
    }
  }
}

// Export singleton instance
export const e2eChatTester = E2EChatTester.getInstance();

// Make available in browser console for Replit environment
if (typeof window !== 'undefined') {
  (window as any).e2eChatTester = e2eChatTester;
  
  // Add convenient test commands
  (window as any).testAI = {
    full: () => e2eChatTester.testCompleteUserFlow(),
    quick: () => e2eChatTester.quickHealthCheck(),
    user: () => e2eChatTester.testRealUserScenario(),
    cleanup: () => e2eChatTester.cleanup()
  };
  
  console.log('🎯 E2E Chat Tester loaded! Use window.testAI for quick testing:');
  console.log('  - testAI.full() - Complete end-to-end test');
  console.log('  - testAI.quick() - Quick health check');
  console.log('  - testAI.user() - Real user scenario test');
  console.log('  - testAI.cleanup() - Clean up test data');
} 