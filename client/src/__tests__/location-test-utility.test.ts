/**
 * Location Testing Utility
 * 
 * This utility helps test location detection and handling in the chat interface
 * by providing functions to validate and process location data.
 */

interface LocationData {
  name: string;
  lat: number;
  lng: number;
  placeType?: string;
}

interface LocationResponse {
  type: 'location';
  data: LocationData;
}

interface TextResponse {
  type: 'text';
  data: string;
}

interface LocationActionResponse {
  type: 'action';
  data: {
    type: 'location';
    label: string;
    data: LocationData;
  };
}

interface ErrorResponse {
  type: 'error';
  data: {
    message: string;
    details?: string;
  };
}

type ChatResponse = LocationResponse | TextResponse | LocationActionResponse | ErrorResponse;

/**
 * Validates a location response from the server
 * @param response The response to validate
 * @returns True if the response is a valid location response
 */
export function validateLocationResponse(response: any): { 
  isValid: boolean; 
  error?: string;
  locationData?: LocationData;
} {
  if (!response) {
    return { isValid: false, error: 'Response is empty or null' };
  }

  // Check for direct location response
  if (response.type === 'location' && response.data) {
    try {
      const { name, lat, lng } = response.data;
      
      if (!name || typeof name !== 'string') {
        return { isValid: false, error: 'Location name is missing or invalid' };
      }
      
      if (typeof lat !== 'number' || isNaN(lat)) {
        return { isValid: false, error: 'Latitude is not a valid number' };
      }
      
      if (typeof lng !== 'number' || isNaN(lng)) {
        return { isValid: false, error: 'Longitude is not a valid number' };
      }
      
      return { 
        isValid: true,
        locationData: {
          name,
          lat,
          lng,
          placeType: response.data.placeType
        }
      };
    } catch (e) {
      return { isValid: false, error: `Error parsing location data: ${e instanceof Error ? e.message : String(e)}` };
    }
  }
  
  // Check for location action response
  if (response.type === 'action' && 
      response.data && 
      response.data.type === 'location' && 
      response.data.data) {
    try {
      const { name, lat, lng } = response.data.data;
      
      if (!name || typeof name !== 'string') {
        return { isValid: false, error: 'Location action name is missing or invalid' };
      }
      
      if (typeof lat !== 'number' || isNaN(lat)) {
        return { isValid: false, error: 'Location action latitude is not a valid number' };
      }
      
      if (typeof lng !== 'number' || isNaN(lng)) {
        return { isValid: false, error: 'Location action longitude is not a valid number' };
      }
      
      return { 
        isValid: true,
        locationData: {
          name,
          lat,
          lng,
          placeType: response.data.data.placeType
        }
      };
    } catch (e) {
      return { isValid: false, error: `Error parsing location action data: ${e instanceof Error ? e.message : String(e)}` };
    }
  }
  
  return { isValid: false, error: 'Response does not contain location data' };
}

/**
 * Tests a location query against the API
 * @param query The location query to test
 * @returns The test result
 */
export async function testLocationQuery(query: string): Promise<{
  success: boolean;
  message: string;
  location?: LocationData;
  textResponse?: string[];
  error?: string;
  rawResponse?: any;
}> {
  try {
    const response = await fetch('/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: query,
        extractLocation: true, // Important flag to ensure location extraction
      }),
    });

    if (!response.ok) {
      throw new Error(`API responded with status ${response.status}: ${response.statusText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('Cannot read response stream');
    }

    let responseText = '';
    let locationFound = false;
    let extractedLocation: LocationData | undefined;
    const textResponses: string[] = [];

    // Process the stream chunks
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      // Convert bytes to text
      const chunk = new TextDecoder().decode(value);
      responseText += chunk;

      // Process each line (SSE format)
      const lines = chunk.split('\n');
      for (const line of lines) {
        if (!line.trim() || !line.startsWith('data:')) continue;
        
        try {
          // Extract the JSON data
          const jsonStr = line.substring(5).trim();
          if (jsonStr === '[DONE]') continue;
          
          const data = JSON.parse(jsonStr);
          
          // Check if it's a location response
          const validationResult = validateLocationResponse(data);
          if (validationResult.isValid && validationResult.locationData) {
            locationFound = true;
            extractedLocation = validationResult.locationData;
          } else if (data.type === 'text') {
            textResponses.push(data.data);
          }
        } catch (e) {
          console.error('Error parsing SSE data:', e);
        }
      }
    }

    if (locationFound && extractedLocation) {
      return {
        success: true,
        message: `Location detected: ${extractedLocation.name}`,
        location: extractedLocation,
        textResponse: textResponses,
        rawResponse: responseText
      };
    } else {
      return {
        success: false,
        message: 'No location detected in response',
        textResponse: textResponses,
        rawResponse: responseText
      };
    }
  } catch (error) {
    return {
      success: false,
      message: 'Error testing location detection',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Runs a series of location tests on common queries
 * @returns Test results for each query
 */
export async function runLocationTests(): Promise<{
  results: Array<{
    query: string;
    success: boolean;
    location?: LocationData;
  }>;
  overallSuccess: boolean;
}> {
  const testQueries = [
    "Find hotels in Miami Beach",
    "I want to travel to New York City",
    "Show me accommodations in San Francisco",
    "I'm planning a trip to Las Vegas",
    "Tell me about hotels in Tokyo"
  ];
  
  const results = [];
  let successCount = 0;
  
  for (const query of testQueries) {
    const result = await testLocationQuery(query);
    results.push({
      query,
      success: result.success,
      location: result.location
    });
    
    if (result.success) {
      successCount++;
    }
  }
  
  return {
    results,
    overallSuccess: successCount > testQueries.length / 2
  };
}

/**
 * Gets a location from the test endpoint
 * @param locationName The location name to get
 * @returns The location data if successful
 */
export async function getTestLocation(locationName: string): Promise<LocationData | null> {
  try {
    const response = await fetch(`/api/test/location?name=${encodeURIComponent(locationName)}`);
    
    if (!response.ok) {
      throw new Error(`Failed to get test location: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    if (data && data.location) {
      return data.location;
    }
    
    return null;
  } catch (error) {
    console.error('Error getting test location:', error);
    return null;
  }
}