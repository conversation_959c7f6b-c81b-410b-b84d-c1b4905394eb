export function getSessionId(): string {
  // Universal session generator – persists across tabs & reloads
  if (typeof window === 'undefined') {
    // SSR / tests fallback
    return `session-${Date.now()}-${Math.random().toString(36).slice(2, 9)}`;
  }

  try {
    const key = 'travel_session_id';
    let id = window.localStorage.getItem(key);
    if (!id) {
      id = `session-${Date.now()}-${Math.random().toString(36).slice(2, 9)}`;
      window.localStorage.setItem(key, id);
    }
    return id;
  } catch {
    // localStorage might be blocked
    return `session-${Date.now()}-${Math.random().toString(36).slice(2, 9)}`;
  }
} 