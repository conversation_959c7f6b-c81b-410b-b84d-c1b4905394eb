import { PropertyImage, RoomImage } from '../types/schema';

/**
 * Transform URL to use full-sized version using regex to handle all cases
 * Converts _70.jpg, _thumbnail.jpg, etc. to _0.jpg for full resolution
 */
export function getFullSizeImageUrl(imagePath: string): string {
  if (!imagePath) return imagePath;
  
  // Handle the travsrv.com image format: convert _70.jpg to _0.jpg
  if (imagePath.includes('_70.jpg')) {
    return imagePath.replace('_70.jpg', '_0.jpg');
  }
  
  // Handle other thumbnail formats with regex
  const fullSizeUrl = imagePath.replace(/_(70|thumb|thumbnail|small|medium)\.jpg$/i, '_0.jpg');
  
  // If no changes were made and it ends with .jpg, try the general pattern
  if (fullSizeUrl === imagePath && imagePath.endsWith('.jpg')) {
    return imagePath.replace(/_.+\.jpg$/, '_0.jpg');
  }
  
  return fullSizeUrl;
}

/**
 * Get the image source URL with proper type handling and fallback
 * @param image The image which can be a string URL or PropertyImage object
 * @param fallbackImage Optional fallback image URL
 * @returns The resolved image URL
 */
export function getImageSource(image: string | PropertyImage | undefined | null, fallbackImage: string = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg=='): string {
  if (!image) {
    return fallbackImage;
  }

  if (typeof image === 'string') {
    return image;
  }

  if (typeof image === 'object' && 'url' in image && image.url) {
    return image.url;
  }

  return fallbackImage;
}

/**
 * Sort images by their display order
 * @param images Array of images to sort
 * @returns Sorted array of images
 */
export function sortByDisplayOrder<T extends PropertyImage>(images: T[]): T[] {
  return [...images].sort((a, b) => (a.displayOrder || 0) - (b.displayOrder || 0));
}

/**
 * Filter property images to get room-specific images
 * @param images Array of property images
 * @param roomTypeCode The room type code to filter by
 * @returns Array of images specific to the room type
 */
export function getRoomImages(images: (PropertyImage | RoomImage)[], roomTypeCode: string): PropertyImage[] {
  return sortByDisplayOrder(
    images.filter((img): img is RoomImage => 
      'roomTypeCode' in img && img.roomTypeCode === roomTypeCode
    )
  );
}

/**
 * Get all property-level images (excluding room-specific images)
 * @param images Array of property images
 * @returns Array of property-level images
 */
export function getPropertyImages(images: (PropertyImage | RoomImage)[]): PropertyImage[] {
  return sortByDisplayOrder(
    images.filter(img => 
      !('roomTypeCode' in img) || img.category === 'property'
    )
  );
}

// TODO: Add your custom image filtering logic here
// This is where you can add additional functions to filter images based on your specific needs
// For example:
// - Filter by image category
// - Get images for multiple room types
// - Combine property and room images
// - Filter by custom metadata 