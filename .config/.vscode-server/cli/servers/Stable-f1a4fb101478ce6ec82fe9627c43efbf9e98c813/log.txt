*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[17:59:58] 




[17:59:58] Extension host agent started.
[17:59:58] Started initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
[17:59:58] Completed initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
[17:59:59] [<unknown>][0362e62b][ManagementConnection] New connection established.
[17:59:59] [<unknown>][6614c1c8][ExtensionHostConnection] New connection established.
[17:59:59] [<unknown>][6614c1c8][ExtensionHostConnection] <1675> Launched Extension Host Process.
[17:59:59] [network] #1: https://az764295.vo.msecnd.net/extensions/marketplace.json - error GET getaddrinfo ENOTFOUND az764295.vo.msecnd.net
rejected promise not handled within 1 second: CodeExpectedError: Could not find pty 4 on pty host
stack trace: CodeExpectedError: Could not find pty 4 on pty host
    at I.W (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:48:12233)
    at I.updateTitle (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:48:7722)
    at N.s.<computed> (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:48:2962)
    at Object.call (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:31:4203)
    at Jc.s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:78968)
    at Jc.q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:78491)
    at hs.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:77893)
    at C.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:746)
    at C.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:964)
    at process.w (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:24:29137)
    at process.emit (node:events:519:28)
    at emit (node:internal/child_process:951:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21)
[18:00:02] Error [CodeExpectedError]: Could not find pty 4 on pty host
    at I.W (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:48:12233)
    at I.updateTitle (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:48:7722)
    at N.s.<computed> (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:48:2962)
    at Object.call (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:31:4203)
    at Jc.s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:78968)
    at Jc.q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:78491)
    at hs.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:77893)
    at C.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:746)
    at C.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:964)
    at process.w (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:24:29137)
    at process.emit (node:events:519:28)
    at emit (node:internal/child_process:951:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21)
rejected promise not handled within 1 second: CodeExpectedError: Could not find pty 4 on pty host
stack trace: CodeExpectedError: Could not find pty 4 on pty host
    at I.W (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:48:12233)
    at I.updateIcon (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:48:7772)
    at N.s.<computed> (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:48:2962)
    at Object.call (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:31:4203)
    at Jc.s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:78968)
    at Jc.q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:78491)
    at hs.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:77893)
    at C.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:746)
    at C.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:964)
    at process.w (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:24:29137)
    at process.emit (node:events:519:28)
    at emit (node:internal/child_process:951:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21)
[18:00:02] Error [CodeExpectedError]: Could not find pty 4 on pty host
    at I.W (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:48:12233)
    at I.updateIcon (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:48:7772)
    at N.s.<computed> (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:48:2962)
    at Object.call (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:31:4203)
    at Jc.s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:78968)
    at Jc.q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:78491)
    at hs.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:77893)
    at C.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:746)
    at C.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:964)
    at process.w (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:24:29137)
    at process.emit (node:events:519:28)
    at emit (node:internal/child_process:951:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21)
New EH opened, aborting shutdown
[18:04:58] New EH opened, aborting shutdown
[18:44:16] [<unknown>][0362e62b][ManagementConnection] The client has reconnected.
[18:44:49] [<unknown>][6614c1c8][ExtensionHostConnection] The client has reconnected.
