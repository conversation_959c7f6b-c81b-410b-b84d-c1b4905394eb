<?xml version='1.0' standalone='no' ?>
<svg xmlns='http://www.w3.org/2000/svg' version='1.1' width='10px' height='10px'>
	<style>
    circle {
      animation: ball 0.6s linear infinite;
    }

    circle:nth-child(2) { animation-delay: 0.075s; }
    circle:nth-child(3) { animation-delay: 0.15s; }
    circle:nth-child(4) { animation-delay: 0.225s; }
    circle:nth-child(5) { animation-delay: 0.3s; }
    circle:nth-child(6) { animation-delay: 0.375s; }
    circle:nth-child(7) { animation-delay: 0.45s; }
    circle:nth-child(8) { animation-delay: 0.525s; }

    @keyframes ball {
      from { opacity: 1; }
      to { opacity: 0.3; }
    }
	</style>
	<g style="fill:grey;">
		<circle cx='5' cy='1' r='1' style='opacity:0.3;' />
		<circle cx='7.8284' cy='2.1716' r='1' style='opacity:0.3;' />
		<circle cx='9' cy='5' r='1' style='opacity:0.3;' />
		<circle cx='7.8284' cy='7.8284' r='1' style='opacity:0.3;' />
		<circle cx='5' cy='9' r='1' style='opacity:0.3;' />
		<circle cx='2.1716' cy='7.8284' r='1' style='opacity:0.3;' />
		<circle cx='1' cy='5' r='1' style='opacity:0.3;' />
		<circle cx='2.1716' cy='2.1716' r='1' style='opacity:0.3;' />
	</g>
</svg>
