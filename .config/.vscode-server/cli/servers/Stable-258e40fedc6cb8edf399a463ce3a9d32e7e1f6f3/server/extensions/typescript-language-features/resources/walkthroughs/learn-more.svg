<svg width="482" height="332" viewBox="0 0 482 332" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g filter="url(#filter0_d_1720_12523)">
    <g clip-path="url(#clip0_1720_12523)">
      <rect x="16" y="14" width="450" height="300" rx="2" fill="var(--vscode-quickInput-background, #1E1E1E)" />
      <g>
        <rect width="450" height="35" transform="translate(16 14)" fill="var(--vscode-editorGroupHeader-tabsBackground, #292929)" />
        <g clip-path="url(#clip1_1720_12523)">
          <g>
            <rect width="103" height="35" transform="translate(16 14)" fill="var(--vscode-tab-activeBackground, #1E1E1E)" />
            <rect x="28" y="28" width="79" height="7" rx="3.5" fill="var(--vscode-tab-inactiveForeground, #FFFFFF)" fill-opacity="0.12" />
          </g>
        </g>
      </g>
      <g>
        <g>
          <g>
            <g>
              <rect x="43" y="144" width="320" height="8" rx="4" fill="var(--vscode-list-deemphasizedForeground, #FFFFFF)" fill-opacity="0.16" />
              <rect x="43" y="159" width="224.192" height="8" rx="4" fill="var(--vscode-list-deemphasizedForeground, #FFFFFF)" fill-opacity="0.16" />
              <rect x="278.688" y="159" width="61.3174" height="8" rx="4" fill="var(--vscode-textLink-foreground, #007ACC)" />
            </g>
            <g>
              <rect x="43" y="199" width="105.389" height="8" rx="4" fill="var(--vscode-list-deemphasizedForeground, #FFFFFF)" fill-opacity="0.16" />
              <rect x="43" y="214" width="318.084" height="8" rx="4" fill="var(--vscode-list-deemphasizedForeground, #FFFFFF)" fill-opacity="0.16" />
              <rect x="43" y="229" width="318.38" height="8" rx="4" fill="var(--vscode-list-deemphasizedForeground, #FFFFFF)" fill-opacity="0.16" />
              <rect x="43.8101" y="244" width="223.595" height="8" rx="4" fill="var(--vscode-list-deemphasizedForeground, #FFFFFF)" fill-opacity="0.16" />
            </g>
          </g>
          <g>
            <g filter="url(#filter1_d_1720_12523)">
              <g>
                <mask style="mask-type:alpha" maskUnits="userSpaceOnUse" x="44" y="66" width="14" height="14">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M53.9277 79.9044C54.1482 79.9903 54.3996 79.9848 54.6221 79.8777L57.5045 78.4908C57.8074 78.3451 58 78.0385 58 77.7022V68.2979C58 67.9616 57.8074 67.6551 57.5045 67.5093L54.6221 66.1224C54.3301 65.9818 53.9882 66.0162 53.7319 66.2026C53.6953 66.2292 53.6604 66.259 53.6277 66.2917L48.1097 71.3258L45.7062 69.5014C45.4825 69.3315 45.1695 69.3455 44.9617 69.5345L44.1908 70.2357C43.9366 70.4669 43.9364 70.8668 44.1902 71.0984L46.2746 73L44.1902 74.9017C43.9364 75.1333 43.9366 75.5332 44.1908 75.7644L44.9617 76.4656C45.1695 76.6546 45.4825 76.6685 45.7062 76.4987L48.1097 74.6743L53.6277 79.7084C53.7149 79.7957 53.8174 79.8615 53.9277 79.9044ZM54.5021 69.8219L50.3153 73L54.5021 76.1782V69.8219Z" fill="#FFFFFF" />
                </mask>
                <g mask="url(#mask0_1720_12523)">
                  <path d="M57.5048 67.5114L54.6202 66.1225C54.2863 65.9617 53.8872 66.0295 53.6252 66.2916L44.182 74.9016C43.928 75.1331 43.9283 75.533 44.1826 75.7642L44.954 76.4655C45.1619 76.6545 45.4751 76.6684 45.6989 76.4986L57.0707 67.8717C57.4522 67.5823 58.0002 67.8544 58.0002 68.3332V68.2998C58.0002 67.9636 57.8077 67.6572 57.5048 67.5114Z" fill="#0065A9" />
                  <g filter="url(#filter2_d_1720_12523)">
                    <path d="M57.5048 78.4886L54.6202 79.8775C54.2863 80.0383 53.8872 79.9705 53.6252 79.7084L44.182 71.0984C43.928 70.8668 43.9283 70.467 44.1826 70.2357L44.954 69.5345C45.1619 69.3455 45.4751 69.3316 45.6989 69.5014L57.0707 78.1283C57.4522 78.4177 58.0002 78.1456 58.0002 77.6667V77.7002C58.0002 78.0364 57.8077 78.3428 57.5048 78.4886Z" fill="#007ACC" />
                  </g>
                  <g filter="url(#filter3_d_1720_12523)">
                    <path d="M54.6201 79.8777C54.2861 80.0384 53.8871 79.9704 53.625 79.7084C53.9479 80.0313 54.5 79.8026 54.5 79.3459V66.6541C54.5 66.1975 53.9479 65.9688 53.625 66.2917C53.8871 66.0296 54.2861 65.9617 54.6201 66.1224L57.5042 67.5093C57.8073 67.6551 58 67.9616 58 68.2979V77.7022C58 78.0385 57.8073 78.3451 57.5042 78.4908L54.6201 79.8777Z" fill="#1F9CF0" />
                  </g>
                  <g style="mix-blend-mode:overlay" opacity="0.25">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M53.9194 79.9044C54.1399 79.9903 54.3913 79.9848 54.6138 79.8777L57.4962 78.4908C57.7991 78.3451 57.9917 78.0385 57.9917 77.7022V68.2979C57.9917 67.9616 57.7991 67.6551 57.4962 67.5093L54.6138 66.1224C54.3218 65.9818 53.9799 66.0162 53.7236 66.2026C53.687 66.2292 53.6521 66.259 53.6194 66.2917L48.1014 71.3258L45.6979 69.5014C45.4742 69.3315 45.1612 69.3455 44.9534 69.5345L44.1825 70.2357C43.9283 70.4669 43.9281 70.8668 44.1819 71.0984L46.2663 73L44.1819 74.9017C43.9281 75.1333 43.9283 75.5332 44.1825 75.7644L44.9534 76.4656C45.1612 76.6546 45.4742 76.6685 45.6979 76.4987L48.1014 74.6742L53.6194 79.7084C53.7066 79.7957 53.8091 79.8615 53.9194 79.9044ZM54.4938 69.8219L50.307 73L54.4938 76.1782V69.8219Z" fill="url(#paint0_linear_1720_12523)" />
                  </g>
                </g>
              </g>
            </g>
          </g>
          <g>
            <rect x="73" y="69" width="41" height="7" rx="3.5" fill="var(--vscode-list-deemphasizedForeground, #FFFFFF)" fill-opacity="0.32" />
            <rect x="123" y="69" width="41" height="7" rx="3.5" fill="var(--vscode-list-deemphasizedForeground, #FFFFFF)" fill-opacity="0.16" />
            <rect x="173" y="69" width="55" height="7" rx="3.5" fill="var(--vscode-list-deemphasizedForeground, #FFFFFF)" fill-opacity="0.16" />
            <rect x="237" y="69" width="33" height="7" rx="3.5" fill="var(--vscode-list-deemphasizedForeground, #FFFFFF)" fill-opacity="0.16" />
          </g>
          <path d="M51.7803 113.517H51.9033L54.6279 123H56.4824L59.9102 110.317H57.8623L55.542 120.179H55.4189L52.7559 110.317H50.9277L48.2822 120.179H48.1592L45.8301 110.317H43.7822L47.2012 123H49.0645L51.7803 113.517ZM65.166 123.185C67.9609 123.185 69.6748 121.312 69.6748 118.228C69.6748 115.143 67.9521 113.271 65.166 113.271C62.3711 113.271 60.6484 115.151 60.6484 118.228C60.6484 121.312 62.3623 123.185 65.166 123.185ZM65.166 121.576C63.5225 121.576 62.5996 120.354 62.5996 118.228C62.5996 116.101 63.5225 114.87 65.166 114.87C66.8008 114.87 67.7324 116.101 67.7324 118.228C67.7324 120.346 66.8008 121.576 65.166 121.576ZM71.9512 123H73.8408V117.296C73.8408 115.951 74.8516 115.028 76.2842 115.028C76.6182 115.028 77.1807 115.09 77.3389 115.134V113.358C77.1367 113.314 76.7764 113.288 76.4951 113.288C75.2471 113.288 74.1836 113.965 73.9111 114.905H73.7705V113.455H71.9512V123ZM81.2852 117.48H81.1357V109.711H79.2461V123H81.1357V119.528L81.9707 118.729L85.2578 123H87.5781L83.377 117.542L87.3145 113.455H85.082L81.2852 117.48ZM90.2939 111.75C90.9531 111.75 91.4893 111.214 91.4893 110.563C91.4893 109.904 90.9531 109.368 90.2939 109.368C89.6348 109.368 89.0986 109.904 89.0986 110.563C89.0986 111.214 89.6348 111.75 90.2939 111.75ZM89.3535 123H91.2344V113.455H89.3535V123ZM94.0117 123H95.9014V117.401C95.9014 115.863 96.7891 114.914 98.1865 114.914C99.584 114.914 100.252 115.688 100.252 117.27V123H102.142V116.821C102.142 114.545 100.964 113.271 98.8281 113.271C97.3867 113.271 96.4375 113.912 95.9717 114.967H95.8311V113.455H94.0117V123ZM108.839 126.647C111.546 126.647 113.26 125.268 113.26 123.097V113.455H111.449V114.976H111.3C110.764 113.938 109.612 113.288 108.294 113.288C105.851 113.288 104.321 115.213 104.321 118.104C104.321 120.97 105.833 122.859 108.276 122.859C109.595 122.859 110.649 122.288 111.229 121.251H111.37V123.088C111.37 124.38 110.438 125.153 108.865 125.153C107.591 125.153 106.791 124.696 106.633 123.976H104.699C104.901 125.584 106.448 126.647 108.839 126.647ZM108.812 121.295C107.187 121.295 106.272 120.056 106.272 118.104C106.272 116.153 107.187 114.905 108.812 114.905C110.421 114.905 111.405 116.153 111.405 118.104C111.405 120.056 110.43 121.295 108.812 121.295ZM133.387 113.455H131.497L129.792 120.82H129.643L127.674 113.455H125.863L123.895 120.82H123.754L122.04 113.455H120.124L122.761 123H124.703L126.672 115.881H126.821L128.799 123H130.759L133.387 113.455ZM136.313 111.75C136.973 111.75 137.509 111.214 137.509 110.563C137.509 109.904 136.973 109.368 136.313 109.368C135.654 109.368 135.118 109.904 135.118 110.563C135.118 111.214 135.654 111.75 136.313 111.75ZM135.373 123H137.254V113.455H135.373V123ZM140.699 111.047V113.481H139.179V114.993H140.699V120.486C140.699 122.323 141.534 123.062 143.635 123.062C144.004 123.062 144.355 123.018 144.663 122.965V121.462C144.399 121.488 144.232 121.506 143.942 121.506C143.002 121.506 142.589 121.058 142.589 120.029V114.993H144.663V113.481H142.589V111.047H140.699ZM146.975 123H148.864V117.41C148.864 115.916 149.726 114.923 151.272 114.923C152.608 114.923 153.32 115.705 153.32 117.278V123H155.21V116.83C155.21 114.571 153.953 113.279 151.914 113.279C150.473 113.279 149.462 113.912 148.996 114.976H148.847V109.711H146.975V123ZM162.145 119.783C162.145 121.91 163.718 123.308 166.012 123.308C168.455 123.308 169.932 121.857 169.932 119.335V110.317H167.963V119.326C167.963 120.776 167.251 121.55 165.994 121.55C164.869 121.55 164.104 120.838 164.078 119.783H162.145ZM175.478 123.158C176.734 123.158 177.78 122.613 178.352 121.646H178.501V123H180.32V116.47C180.32 114.466 178.967 113.271 176.567 113.271C174.396 113.271 172.85 114.316 172.656 115.925H174.484C174.695 115.23 175.425 114.835 176.479 114.835C177.771 114.835 178.439 115.424 178.439 116.47V117.305L175.847 117.463C173.57 117.604 172.287 118.597 172.287 120.311C172.287 122.051 173.632 123.158 175.478 123.158ZM175.961 121.638C174.933 121.638 174.186 121.119 174.186 120.231C174.186 119.361 174.783 118.896 176.102 118.808L178.439 118.649V119.476C178.439 120.706 177.385 121.638 175.961 121.638ZM190.797 113.455H188.793L186.42 121.066H186.271L183.889 113.455H181.867L185.321 123H187.352L190.797 113.455ZM195.024 123.158C196.281 123.158 197.327 122.613 197.898 121.646H198.048V123H199.867V116.47C199.867 114.466 198.514 113.271 196.114 113.271C193.943 113.271 192.396 114.316 192.203 115.925H194.031C194.242 115.23 194.972 114.835 196.026 114.835C197.318 114.835 197.986 115.424 197.986 116.47V117.305L195.394 117.463C193.117 117.604 191.834 118.597 191.834 120.311C191.834 122.051 193.179 123.158 195.024 123.158ZM195.508 121.638C194.479 121.638 193.732 121.119 193.732 120.231C193.732 119.361 194.33 118.896 195.648 118.808L197.986 118.649V119.476C197.986 120.706 196.932 121.638 195.508 121.638ZM202.179 119.616C202.319 121.866 204.209 123.308 207.021 123.308C210.027 123.308 211.908 121.796 211.908 119.396C211.908 117.498 210.836 116.452 208.234 115.828L206.837 115.485C205.185 115.081 204.517 114.554 204.517 113.631C204.517 112.444 205.554 111.68 207.109 111.68C208.586 111.68 209.605 112.418 209.79 113.622H211.706C211.592 111.504 209.711 110.01 207.136 110.01C204.367 110.01 202.521 111.504 202.521 113.736C202.521 115.582 203.567 116.672 205.861 117.226L207.496 117.621C209.175 118.025 209.913 118.641 209.913 119.634C209.913 120.794 208.753 121.629 207.171 121.629C205.475 121.629 204.297 120.847 204.13 119.616H202.179ZM222.385 116.522C222.139 114.668 220.688 113.271 218.289 113.271C215.503 113.271 213.807 115.16 213.807 118.192C213.807 121.286 215.512 123.185 218.298 123.185C220.662 123.185 222.139 121.857 222.385 119.977H220.548C220.302 121.014 219.484 121.576 218.289 121.576C216.716 121.576 215.74 120.302 215.74 118.192C215.74 116.127 216.707 114.879 218.289 114.879C219.555 114.879 220.328 115.591 220.548 116.522H222.385ZM224.547 123H226.437V117.296C226.437 115.951 227.447 115.028 228.88 115.028C229.214 115.028 229.776 115.09 229.935 115.134V113.358C229.732 113.314 229.372 113.288 229.091 113.288C227.843 113.288 226.779 113.965 226.507 114.905H226.366V113.455H224.547V123ZM232.729 111.75C233.389 111.75 233.925 111.214 233.925 110.563C233.925 109.904 233.389 109.368 232.729 109.368C232.07 109.368 231.534 109.904 231.534 110.563C231.534 111.214 232.07 111.75 232.729 111.75ZM231.789 123H233.67V113.455H231.789V123ZM241.413 113.288C240.112 113.288 238.987 113.947 238.407 115.037H238.267V113.455H236.447V126.182H238.337V121.559H238.486C238.987 122.569 240.068 123.158 241.431 123.158C243.848 123.158 245.386 121.242 245.386 118.228C245.386 115.195 243.848 113.288 241.413 113.288ZM240.877 121.541C239.295 121.541 238.302 120.267 238.302 118.228C238.302 116.18 239.295 114.905 240.886 114.905C242.485 114.905 243.443 116.153 243.443 118.228C243.443 120.302 242.485 121.541 240.877 121.541ZM248.33 111.047V113.481H246.81V114.993H248.33V120.486C248.33 122.323 249.165 123.062 251.266 123.062C251.635 123.062 251.986 123.018 252.294 122.965V121.462C252.03 121.488 251.863 121.506 251.573 121.506C250.633 121.506 250.22 121.058 250.22 120.029V114.993H252.294V113.481H250.22V111.047H248.33Z" fill="var(--vscode-settings-headerForeground, #FFFFFF)" />
        </g>
      </g>
    </g>
  </g>
  <defs>
    <filter id="filter0_d_1720_12523" x="0" y="0" width="482" height="332" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
      <feOffset dy="2" />
      <feGaussianBlur stdDeviation="8" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.36 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1720_12523" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1720_12523" result="shape" />
    </filter>
    <filter id="filter1_d_1720_12523" x="42.1284" y="64.7523" width="17.7432" height="17.7432" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
      <feOffset dy="0.623874" />
      <feGaussianBlur stdDeviation="0.935811" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1720_12523" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1720_12523" result="shape" />
    </filter>
    <filter id="filter2_d_1720_12523" x="39.6246" y="65.0157" width="22.7425" height="19.3153" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="2.18356" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
      <feBlend mode="overlay" in2="BackgroundImageFix" result="effect1_dropShadow_1720_12523" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1720_12523" result="shape" />
    </filter>
    <filter id="filter3_d_1720_12523" x="49.2579" y="61.669" width="13.1092" height="22.662" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="2.18356" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
      <feBlend mode="overlay" in2="BackgroundImageFix" result="effect1_dropShadow_1720_12523" />
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1720_12523" result="shape" />
    </filter>
    <linearGradient id="paint0_linear_1720_12523" x1="50.9917" y1="66.0361" x2="50.9917" y2="79.964" gradientUnits="userSpaceOnUse">
      <stop stop-color="white" />
      <stop offset="1" stop-color="white" stop-opacity="0" />
    </linearGradient>
    <clipPath id="clip0_1720_12523">
      <rect x="16" y="14" width="450" height="300" rx="2" fill="#FFFFFF" />
    </clipPath>
    <clipPath id="clip1_1720_12523">
      <rect width="103" height="35" fill="#FFFFFF" transform="translate(16 14)" />
    </clipPath>
  </defs>
</svg>
