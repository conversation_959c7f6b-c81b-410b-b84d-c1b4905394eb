{"name": "npm", "publisher": "vscode", "displayName": "%displayName%", "description": "%description%", "version": "1.0.1", "private": true, "license": "MIT", "engines": {"vscode": "0.10.x"}, "icon": "images/npm_icon.png", "categories": ["Other"], "enabledApiProposals": ["terminalQuickFixProvider"], "main": "./dist/npmMain", "browser": "./dist/browser/npmBrowserMain", "activationEvents": ["onTaskType:npm", "onLanguage:json", "workspaceContains:package.json"], "capabilities": {"virtualWorkspaces": {"supported": "limited", "description": "%virtualWorkspaces%"}, "untrustedWorkspaces": {"supported": "limited", "description": "%workspaceTrust%"}}, "contributes": {"languages": [{"id": "ignore", "extensions": [".n<PERSON><PERSON><PERSON>"]}, {"id": "properties", "extensions": [".npmrc"]}], "views": {"explorer": [{"id": "npm", "name": "%view.name%", "when": "npm:showScriptExplorer", "icon": "$(json)", "visibility": "hidden", "contextualTitle": "%view.name%"}]}, "commands": [{"command": "npm.runScript", "title": "%command.run%", "icon": "$(run)"}, {"command": "npm.debugScript", "title": "%command.debug%", "icon": "$(debug)"}, {"command": "npm.openScript", "title": "%command.openScript%"}, {"command": "npm.runInstall", "title": "%command.runInstall%"}, {"command": "npm.refresh", "title": "%command.refresh%", "icon": "$(refresh)"}, {"command": "npm.runSelectedScript", "title": "%command.runSelectedScript%"}, {"command": "npm.runScriptFromFolder", "title": "%command.runScriptFromFolder%"}, {"command": "npm.packageManager", "title": "%command.packageManager%"}], "menus": {"commandPalette": [{"command": "npm.refresh", "when": "false"}, {"command": "npm.runScript", "when": "false"}, {"command": "npm.debugScript", "when": "false"}, {"command": "npm.openScript", "when": "false"}, {"command": "npm.runInstall", "when": "false"}, {"command": "npm.runSelectedScript", "when": "false"}, {"command": "npm.runScriptFromFolder", "when": "false"}, {"command": "npm.packageManager", "when": "false"}], "editor/context": [{"command": "npm.runSelectedScript", "when": "resourceFilename == 'package.json' && resourceScheme == file", "group": "navigation@+1"}], "view/title": [{"command": "npm.refresh", "when": "view == npm", "group": "navigation"}], "view/item/context": [{"command": "npm.openScript", "when": "view == npm && viewItem == packageJSON", "group": "navigation@1"}, {"command": "npm.runInstall", "when": "view == npm && viewItem == packageJSON", "group": "navigation@2"}, {"command": "npm.openScript", "when": "view == npm && viewItem == script", "group": "navigation@1"}, {"command": "npm.runScript", "when": "view == npm && viewItem == script", "group": "navigation@2"}, {"command": "npm.runScript", "when": "view == npm && viewItem == script", "group": "inline"}, {"command": "npm.debugScript", "when": "view == npm && viewItem == script", "group": "inline"}, {"command": "npm.debugScript", "when": "view == npm && viewItem == script", "group": "navigation@3"}], "explorer/context": [{"when": "config.npm.enableRunFromFolder && explorerViewletVisible && explorerResourceIsFolder && resourceScheme == file", "command": "npm.runScriptFromFolder", "group": "2_workspace"}]}, "configuration": {"id": "npm", "type": "object", "title": "Npm", "properties": {"npm.autoDetect": {"type": "string", "enum": ["off", "on"], "default": "on", "scope": "resource", "description": "%config.npm.autoDetect%"}, "npm.runSilent": {"type": "boolean", "default": false, "scope": "resource", "markdownDescription": "%config.npm.runSilent%"}, "npm.packageManager": {"scope": "resource", "type": "string", "enum": ["auto", "npm", "yarn", "pnpm", "bun"], "enumDescriptions": ["%config.npm.packageManager.auto%", "%config.npm.packageManager.npm%", "%config.npm.packageManager.yarn%", "%config.npm.packageManager.pnpm%", "%config.npm.packageManager.bun%"], "default": "auto", "description": "%config.npm.packageManager%"}, "npm.scriptRunner": {"scope": "resource", "type": "string", "enum": ["auto", "npm", "yarn", "pnpm", "bun", "node"], "enumDescriptions": ["%config.npm.scriptRunner.auto%", "%config.npm.scriptRunner.npm%", "%config.npm.scriptRunner.yarn%", "%config.npm.scriptRunner.pnpm%", "%config.npm.scriptRunner.bun%", "%config.npm.scriptRunner.node%"], "default": "auto", "description": "%config.npm.scriptRunner%"}, "npm.exclude": {"type": ["string", "array"], "items": {"type": "string"}, "description": "%config.npm.exclude%", "scope": "resource"}, "npm.enableScriptExplorer": {"type": "boolean", "default": false, "scope": "resource", "deprecationMessage": "The NPM Script Explorer is now available in 'Views' menu in the Explorer in all folders.", "description": "%config.npm.enableScriptExplorer%"}, "npm.enableRunFromFolder": {"type": "boolean", "default": false, "scope": "resource", "description": "%config.npm.enableRunFromFolder%"}, "npm.scriptExplorerAction": {"type": "string", "enum": ["open", "run"], "markdownDescription": "%config.npm.scriptExplorerAction%", "scope": "window", "default": "open"}, "npm.scriptExplorerExclude": {"type": "array", "items": {"type": "string"}, "markdownDescription": "%config.npm.scriptExplorerExclude%", "scope": "resource", "default": []}, "npm.fetchOnlinePackageInfo": {"type": "boolean", "description": "%config.npm.fetchOnlinePackageInfo%", "default": true, "scope": "window", "tags": ["usesOnlineServices"]}, "npm.scriptHover": {"type": "boolean", "description": "%config.npm.scriptHover%", "default": true, "scope": "window"}}}, "jsonValidation": [{"fileMatch": "package.json", "url": "https://json.schemastore.org/package"}, {"fileMatch": "bower.json", "url": "https://json.schemastore.org/bower"}], "taskDefinitions": [{"type": "npm", "required": ["script"], "properties": {"script": {"type": "string", "description": "%taskdef.script%"}, "path": {"type": "string", "description": "%taskdef.path%"}}, "when": "shellExecutionSupported"}], "terminalQuickFixes": [{"id": "ms-vscode.npm-command", "commandLineMatcher": "npm", "commandExitResult": "error", "outputMatcher": {"anchor": "bottom", "length": 8, "lineMatcher": "Did you mean (?:this|one of these)\\?((?:\\n.+?npm .+ #.+)+)", "offset": 2}}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}