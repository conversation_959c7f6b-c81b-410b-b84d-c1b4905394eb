/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/var he=function(e,t){return he=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(r[s]=n[s])},he(e,t)};export function __extends(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");he(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}export var __assign=function(){return __assign=Object.assign||function(t){for(var r,n=1,s=arguments.length;n<s;n++){r=arguments[n];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t},__assign.apply(this,arguments)};export function __rest(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(e);s<n.length;s++)t.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(e,n[s])&&(r[n[s]]=e[n[s]]);return r}export function __decorate(e,t,r,n){var s=arguments.length,i=s<3?t:n===null?n=Object.getOwnPropertyDescriptor(t,r):n,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(e,t,r,n);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(i=(s<3?o(i):s>3?o(t,r,i):o(t,r))||i);return s>3&&i&&Object.defineProperty(t,r,i),i}export function __param(e,t){return function(r,n){t(r,n,e)}}export function __esDecorate(e,t,r,n,s,i){function o(y){if(y!==void 0&&typeof y!="function")throw new TypeError("Function expected");return y}for(var a=n.kind,c=a==="getter"?"get":a==="setter"?"set":"value",l=!t&&e?n.static?e:e.prototype:null,f=t||(l?Object.getOwnPropertyDescriptor(l,n.name):{}),u,h=!1,g=r.length-1;g>=0;g--){var m={};for(var O in n)m[O]=O==="access"?{}:n[O];for(var O in n.access)m.access[O]=n.access[O];m.addInitializer=function(y){if(h)throw new TypeError("Cannot add initializers after decoration has completed");i.push(o(y||null))};var d=(0,r[g])(a==="accessor"?{get:f.get,set:f.set}:f[c],m);if(a==="accessor"){if(d===void 0)continue;if(d===null||typeof d!="object")throw new TypeError("Object expected");(u=o(d.get))&&(f.get=u),(u=o(d.set))&&(f.set=u),(u=o(d.init))&&s.unshift(u)}else(u=o(d))&&(a==="field"?s.unshift(u):f[c]=u)}l&&Object.defineProperty(l,n.name,f),h=!0}export function __runInitializers(e,t,r){for(var n=arguments.length>2,s=0;s<t.length;s++)r=n?t[s].call(e,r):t[s].call(e);return n?r:void 0}export function __propKey(e){return typeof e=="symbol"?e:"".concat(e)}export function __setFunctionName(e,t,r){return typeof t=="symbol"&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:r?"".concat(r," ",t):t})}export function __metadata(e,t){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(e,t)}export function __awaiter(e,t,r,n){function s(i){return i instanceof r?i:new r(function(o){o(i)})}return new(r||(r=Promise))(function(i,o){function a(f){try{l(n.next(f))}catch(u){o(u)}}function c(f){try{l(n.throw(f))}catch(u){o(u)}}function l(f){f.done?i(f.value):s(f.value).then(a,c)}l((n=n.apply(e,t||[])).next())})}export function __generator(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,s,i,o;return o={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function a(l){return function(f){return c([l,f])}}function c(l){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,l[0]&&(r=0)),r;)try{if(n=1,s&&(i=l[0]&2?s.return:l[0]?s.throw||((i=s.return)&&i.call(s),0):s.next)&&!(i=i.call(s,l[1])).done)return i;switch(s=0,i&&(l=[l[0]&2,i.value]),l[0]){case 0:case 1:i=l;break;case 4:return r.label++,{value:l[1],done:!1};case 5:r.label++,s=l[1],l=[0];continue;case 7:l=r.ops.pop(),r.trys.pop();continue;default:if(i=r.trys,!(i=i.length>0&&i[i.length-1])&&(l[0]===6||l[0]===2)){r=0;continue}if(l[0]===3&&(!i||l[1]>i[0]&&l[1]<i[3])){r.label=l[1];break}if(l[0]===6&&r.label<i[1]){r.label=i[1],i=l;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(l);break}i[2]&&r.ops.pop(),r.trys.pop();continue}l=t.call(e,r)}catch(f){l=[6,f],s=0}finally{n=i=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}export var __createBinding=Object.create?function(e,t,r,n){n===void 0&&(n=r);var s=Object.getOwnPropertyDescriptor(t,r);(!s||("get"in s?!t.__esModule:s.writable||s.configurable))&&(s={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,s)}:function(e,t,r,n){n===void 0&&(n=r),e[n]=t[r]};export function __exportStar(e,t){for(var r in e)r!=="default"&&!Object.prototype.hasOwnProperty.call(t,r)&&__createBinding(t,e,r)}export function __values(e){var t=typeof Symbol=="function"&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}export function __read(e,t){var r=typeof Symbol=="function"&&e[Symbol.iterator];if(!r)return e;var n=r.call(e),s,i=[],o;try{for(;(t===void 0||t-- >0)&&!(s=n.next()).done;)i.push(s.value)}catch(a){o={error:a}}finally{try{s&&!s.done&&(r=n.return)&&r.call(n)}finally{if(o)throw o.error}}return i}export function __spread(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(__read(arguments[t]));return e}export function __spreadArrays(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;for(var n=Array(e),s=0,t=0;t<r;t++)for(var i=arguments[t],o=0,a=i.length;o<a;o++,s++)n[s]=i[o];return n}export function __spreadArray(e,t,r){if(r||arguments.length===2)for(var n=0,s=t.length,i;n<s;n++)(i||!(n in t))&&(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))}export function __await(e){return this instanceof __await?(this.v=e,this):new __await(e)}export function __asyncGenerator(e,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=r.apply(e,t||[]),s,i=[];return s={},a("next"),a("throw"),a("return",o),s[Symbol.asyncIterator]=function(){return this},s;function o(g){return function(m){return Promise.resolve(m).then(g,u)}}function a(g,m){n[g]&&(s[g]=function(O){return new Promise(function(d,y){i.push([g,O,d,y])>1||c(g,O)})},m&&(s[g]=m(s[g])))}function c(g,m){try{l(n[g](m))}catch(O){h(i[0][3],O)}}function l(g){g.value instanceof __await?Promise.resolve(g.value.v).then(f,u):h(i[0][2],g)}function f(g){c("next",g)}function u(g){c("throw",g)}function h(g,m){g(m),i.shift(),i.length&&c(i[0][0],i[0][1])}}export function __asyncDelegator(e){var t,r;return t={},n("next"),n("throw",function(s){throw s}),n("return"),t[Symbol.iterator]=function(){return this},t;function n(s,i){t[s]=e[s]?function(o){return(r=!r)?{value:__await(e[s](o)),done:!1}:i?i(o):o}:i}}export function __asyncValues(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],r;return t?t.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),r={},n("next"),n("throw"),n("return"),r[Symbol.asyncIterator]=function(){return this},r);function n(i){r[i]=e[i]&&function(o){return new Promise(function(a,c){o=e[i](o),s(a,c,o.done,o.value)})}}function s(i,o,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},o)}}export function __makeTemplateObject(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var Mn=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t};export function __importStar(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)r!=="default"&&Object.prototype.hasOwnProperty.call(e,r)&&__createBinding(t,e,r);return Mn(t,e),t}export function __importDefault(e){return e&&e.__esModule?e:{default:e}}export function __classPrivateFieldGet(e,t,r,n){if(r==="a"&&!n)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return r==="m"?n:r==="a"?n.call(e):n?n.value:t.get(e)}export function __classPrivateFieldSet(e,t,r,n,s){if(n==="m")throw new TypeError("Private method is not writable");if(n==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?e!==t||!s:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return n==="a"?s.call(e,r):s?s.value=r:t.set(e,r),r}export function __classPrivateFieldIn(e,t){if(t===null||typeof t!="object"&&typeof t!="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof e=="function"?t===e:e.has(t)}export function __addDisposableResource(e,t,r){if(t!=null){if(typeof t!="object"&&typeof t!="function")throw new TypeError("Object expected.");var n,s;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");n=t[Symbol.asyncDispose]}if(n===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");n=t[Symbol.dispose],r&&(s=n)}if(typeof n!="function")throw new TypeError("Object not disposable.");s&&(n=function(){try{s.call(this)}catch(i){return Promise.reject(i)}}),e.stack.push({value:t,dispose:n,async:r})}else r&&e.stack.push({async:!0});return t}var Fn=typeof SuppressedError=="function"?SuppressedError:function(e,t,r){var n=new Error(r);return n.name="SuppressedError",n.error=e,n.suppressed=t,n};export function __disposeResources(e){function t(n){e.error=e.hasError?new Fn(n,e.error,"An error was suppressed during disposal."):n,e.hasError=!0}function r(){for(;e.stack.length;){var n=e.stack.pop();try{var s=n.dispose&&n.dispose.call(n.value);if(n.async)return Promise.resolve(s).then(r,function(i){return t(i),r()})}catch(i){t(i)}}if(e.hasError)throw e.error}return r()}export default{__extends,__assign,__rest,__decorate,__param,__metadata,__awaiter,__generator,__createBinding,__exportStar,__values,__read,__spread,__spreadArrays,__spreadArray,__await,__asyncGenerator,__asyncDelegator,__asyncValues,__makeTemplateObject,__importStar,__importDefault,__classPrivateFieldGet,__classPrivateFieldSet,__classPrivateFieldIn,__addDisposableResource,__disposeResources};var Un=Object.create,de=Object.defineProperty,Vn=Object.getOwnPropertyDescriptor,pe=Object.getOwnPropertyNames,qn=Object.getPrototypeOf,zn=Object.prototype.hasOwnProperty,k=(e,t)=>function(){return e&&(t=(0,e[pe(e)[0]])(e=0)),t},Wn=(e,t)=>function(){return t||(0,e[pe(e)[0]])((t={exports:{}}).exports,t),t.exports},Hn=(e,t)=>{for(var r in t)de(e,r,{get:t[r],enumerable:!0})},Jn=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of pe(t))!zn.call(e,s)&&s!==r&&de(e,s,{get:()=>t[s],enumerable:!(n=Vn(t,s))||n.enumerable});return e},Kn=(e,t,r)=>(r=e!=null?Un(qn(e)):{},Jn(t||!e||!e.__esModule?de(r,"default",{value:e,enumerable:!0}):r,e));function Gn(){return globalThis._VSCODE_NLS_MESSAGES}function tt(){return globalThis._VSCODE_NLS_LANGUAGE}var rt=k({"out-build/vs/nls.messages.js"(){"use strict"}});function nt(e,t){let r;return t.length===0?r=e:r=e.replace(/\{(\d+)\}/g,(n,s)=>{const i=s[0],o=t[i];let a=n;return typeof o=="string"?a=o:(typeof o=="number"||typeof o=="boolean"||o===void 0||o===null)&&(a=String(o)),a}),it&&(r="\uFF3B"+r.replace(/[aouei]/g,"$&$&")+"\uFF3D"),r}function $(e,t,...r){return nt(typeof e=="number"?Bn(e,t):t,r)}function Bn(e,t){const r=Gn()?.[e];if(typeof r!="string"){if(typeof t=="string")return t;throw new Error(`!!! NLS MISSING: ${e} !!!`)}return r}var it,st=k({"out-build/vs/nls.js"(){"use strict";rt(),rt(),it=tt()==="pseudo"||typeof document<"u"&&document.location&&typeof document.location.hash=="string"&&document.location.hash.indexOf("pseudo=true")>=0}}),m1,S1,x1,C1,ot,ge,H1,at,lt,ct,ut,P1,L1,me,ft,n1,i1,B,ve,ht,dt,J1,s1,pt,gt,mt,vt,yt,bt,e1,c1,wt,_t,Ct,Et,At,Zn,Qn,Yn,Xn,h1=k({"out-build/vs/base/common/platform.js"(){"use strict";if(st(),m1="en",S1=!1,x1=!1,C1=!1,ot=!1,ge=!1,H1=!1,at=!1,lt=!1,ct=!1,ut=!1,P1=void 0,L1=m1,me=m1,ft=void 0,n1=void 0,i1=globalThis,B=void 0,typeof i1.vscode<"u"&&typeof i1.vscode.process<"u"?B=i1.vscode.process:typeof process<"u"&&typeof process?.versions?.node=="string"&&(B=process),ve=typeof B?.versions?.electron=="string",ht=ve&&B?.type==="renderer",typeof B=="object"){S1=B.platform==="win32",x1=B.platform==="darwin",C1=B.platform==="linux",ot=C1&&!!B.env.SNAP&&!!B.env.SNAP_REVISION,at=ve,ct=!!B.env.CI||!!B.env.BUILD_ARTIFACTSTAGINGDIRECTORY,P1=m1,L1=m1;const e=B.env.VSCODE_NLS_CONFIG;if(e)try{const t=JSON.parse(e);P1=t.userLocale,me=t.osLocale,L1=t.resolvedLanguage||m1,ft=t.languagePack?.translationsConfigFile}catch{}ge=!0}else typeof navigator=="object"&&!ht?(n1=navigator.userAgent,S1=n1.indexOf("Windows")>=0,x1=n1.indexOf("Macintosh")>=0,lt=(n1.indexOf("Macintosh")>=0||n1.indexOf("iPad")>=0||n1.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,C1=n1.indexOf("Linux")>=0,ut=n1?.indexOf("Mobi")>=0,H1=!0,L1=tt()||m1,P1=navigator.language.toLowerCase(),me=P1):console.error("Unable to resolve platform.");(function(e){e[e.Web=0]="Web",e[e.Mac=1]="Mac",e[e.Linux=2]="Linux",e[e.Windows=3]="Windows"})(dt||(dt={})),J1=0,x1?J1=1:S1?J1=3:C1&&(J1=2),s1=S1,pt=x1,gt=C1,mt=ge,vt=H1,yt=H1&&typeof i1.importScripts=="function",bt=yt?i1.origin:void 0,e1=n1,c1=L1,function(e){function t(){return c1}e.value=t;function r(){return c1.length===2?c1==="en":c1.length>=3?c1[0]==="e"&&c1[1]==="n"&&c1[2]==="-":!1}e.isDefaultVariant=r;function n(){return c1==="en"}e.isDefault=n}(wt||(wt={})),_t=typeof i1.postMessage=="function"&&!i1.importScripts,Ct=(()=>{if(_t){const e=[];i1.addEventListener("message",r=>{if(r.data&&r.data.vscodeScheduleAsyncWork)for(let n=0,s=e.length;n<s;n++){const i=e[n];if(i.id===r.data.vscodeScheduleAsyncWork){e.splice(n,1),i.callback();return}}});let t=0;return r=>{const n=++t;e.push({id:n,callback:r}),i1.postMessage({vscodeScheduleAsyncWork:n},"*")}}return e=>setTimeout(e)})(),function(e){e[e.Windows=1]="Windows",e[e.Macintosh=2]="Macintosh",e[e.Linux=3]="Linux"}(Et||(Et={})),At=!!(e1&&e1.indexOf("Chrome")>=0),Zn=!!(e1&&e1.indexOf("Firefox")>=0),Qn=!!(!At&&e1&&e1.indexOf("Safari")>=0),Yn=!!(e1&&e1.indexOf("Edg/")>=0),Xn=!!(e1&&e1.indexOf("Android")>=0)}}),v1,K1,y1,$t,Ot,ei,St=k({"out-build/vs/base/common/process.js"(){"use strict";if(h1(),K1=globalThis.vscode,typeof K1<"u"&&typeof K1.process<"u"){const e=K1.process;v1={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd(){return e.cwd()}}}else typeof process<"u"&&typeof process?.versions?.node=="string"?v1={get platform(){return process.platform},get arch(){return process.arch},get env(){return process.env},cwd(){return process.env.VSCODE_CWD||process.cwd()}}:v1={get platform(){return s1?"win32":pt?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};y1=v1.cwd,$t=v1.env,Ot=v1.platform,ei=v1.arch}});function ti(e,t){if(e===null||typeof e!="object")throw new be(t,"Object",e)}function V(e,t){if(typeof e!="string")throw new be(t,"string",e)}function P(e){return e===z||e===K}function ye(e){return e===z}function u1(e){return e>=Pt&&e<=Dt||e>=Lt&&e<=kt}function G1(e,t,r,n){let s="",i=0,o=-1,a=0,c=0;for(let l=0;l<=e.length;++l){if(l<e.length)c=e.charCodeAt(l);else{if(n(c))break;c=z}if(n(c)){if(!(o===l-1||a===1))if(a===2){if(s.length<2||i!==2||s.charCodeAt(s.length-1)!==d1||s.charCodeAt(s.length-2)!==d1){if(s.length>2){const f=s.lastIndexOf(r);f===-1?(s="",i=0):(s=s.slice(0,f),i=s.length-1-s.lastIndexOf(r)),o=l,a=0;continue}else if(s.length!==0){s="",i=0,o=l,a=0;continue}}t&&(s+=s.length>0?`${r}..`:"..",i=2)}else s.length>0?s+=`${r}${e.slice(o+1,l)}`:s=e.slice(o+1,l),i=l-o-1;o=l,a=0}else c===d1&&a!==-1?++a:a=-1}return s}function ri(e){return e?`${e[0]==="."?"":"."}${e}`:""}function xt(e,t){ti(t,"pathObject");const r=t.dir||t.root,n=t.base||`${t.name||""}${ri(t.ext)}`;return r?r===t.root?`${r}${n}`:`${r}${e}${n}`:n}var Pt,Lt,Dt,kt,d1,z,K,o1,Nt,be,G,q,Tt,j,jt,ni,B1,we,Rt,Z1,ii,It,si,oi,ai,D1,li,k1=k({"out-build/vs/base/common/path.js"(){"use strict";St(),Pt=65,Lt=97,Dt=90,kt=122,d1=46,z=47,K=92,o1=58,Nt=63,be=class extends Error{constructor(e,t,r){let n;typeof t=="string"&&t.indexOf("not ")===0?(n="must not be",t=t.replace(/^not /,"")):n="must be";const s=e.indexOf(".")!==-1?"property":"argument";let i=`The "${e}" ${s} ${n} of type ${t}`;i+=`. Received type ${typeof r}`,super(i),this.code="ERR_INVALID_ARG_TYPE"}},G=Ot==="win32",q={resolve(...e){let t="",r="",n=!1;for(let s=e.length-1;s>=-1;s--){let i;if(s>=0){if(i=e[s],V(i,`paths[${s}]`),i.length===0)continue}else t.length===0?i=y1():(i=$t[`=${t}`]||y1(),(i===void 0||i.slice(0,2).toLowerCase()!==t.toLowerCase()&&i.charCodeAt(2)===K)&&(i=`${t}\\`));const o=i.length;let a=0,c="",l=!1;const f=i.charCodeAt(0);if(o===1)P(f)&&(a=1,l=!0);else if(P(f))if(l=!0,P(i.charCodeAt(1))){let u=2,h=u;for(;u<o&&!P(i.charCodeAt(u));)u++;if(u<o&&u!==h){const g=i.slice(h,u);for(h=u;u<o&&P(i.charCodeAt(u));)u++;if(u<o&&u!==h){for(h=u;u<o&&!P(i.charCodeAt(u));)u++;(u===o||u!==h)&&(c=`\\\\${g}\\${i.slice(h,u)}`,a=u)}}}else a=1;else u1(f)&&i.charCodeAt(1)===o1&&(c=i.slice(0,2),a=2,o>2&&P(i.charCodeAt(2))&&(l=!0,a=3));if(c.length>0)if(t.length>0){if(c.toLowerCase()!==t.toLowerCase())continue}else t=c;if(n){if(t.length>0)break}else if(r=`${i.slice(a)}\\${r}`,n=l,l&&t.length>0)break}return r=G1(r,!n,"\\",P),n?`${t}\\${r}`:`${t}${r}`||"."},normalize(e){V(e,"path");const t=e.length;if(t===0)return".";let r=0,n,s=!1;const i=e.charCodeAt(0);if(t===1)return ye(i)?"\\":e;if(P(i))if(s=!0,P(e.charCodeAt(1))){let a=2,c=a;for(;a<t&&!P(e.charCodeAt(a));)a++;if(a<t&&a!==c){const l=e.slice(c,a);for(c=a;a<t&&P(e.charCodeAt(a));)a++;if(a<t&&a!==c){for(c=a;a<t&&!P(e.charCodeAt(a));)a++;if(a===t)return`\\\\${l}\\${e.slice(c)}\\`;a!==c&&(n=`\\\\${l}\\${e.slice(c,a)}`,r=a)}}}else r=1;else u1(i)&&e.charCodeAt(1)===o1&&(n=e.slice(0,2),r=2,t>2&&P(e.charCodeAt(2))&&(s=!0,r=3));let o=r<t?G1(e.slice(r),!s,"\\",P):"";if(o.length===0&&!s&&(o="."),o.length>0&&P(e.charCodeAt(t-1))&&(o+="\\"),!s&&n===void 0&&e.includes(":")){if(o.length>=2&&u1(o.charCodeAt(0))&&o.charCodeAt(1)===o1)return`.\\${o}`;let a=e.indexOf(":");do if(a===t-1||P(e.charCodeAt(a+1)))return`.\\${o}`;while((a=e.indexOf(":",a+1))!==-1)}return n===void 0?s?`\\${o}`:o:s?`${n}\\${o}`:`${n}${o}`},isAbsolute(e){V(e,"path");const t=e.length;if(t===0)return!1;const r=e.charCodeAt(0);return P(r)||t>2&&u1(r)&&e.charCodeAt(1)===o1&&P(e.charCodeAt(2))},join(...e){if(e.length===0)return".";let t,r;for(let i=0;i<e.length;++i){const o=e[i];V(o,"path"),o.length>0&&(t===void 0?t=r=o:t+=`\\${o}`)}if(t===void 0)return".";let n=!0,s=0;if(typeof r=="string"&&P(r.charCodeAt(0))){++s;const i=r.length;i>1&&P(r.charCodeAt(1))&&(++s,i>2&&(P(r.charCodeAt(2))?++s:n=!1))}if(n){for(;s<t.length&&P(t.charCodeAt(s));)s++;s>=2&&(t=`\\${t.slice(s)}`)}return q.normalize(t)},relative(e,t){if(V(e,"from"),V(t,"to"),e===t)return"";const r=q.resolve(e),n=q.resolve(t);if(r===n||(e=r.toLowerCase(),t=n.toLowerCase(),e===t))return"";if(r.length!==e.length||n.length!==t.length){const m=r.split("\\"),O=n.split("\\");m[m.length-1]===""&&m.pop(),O[O.length-1]===""&&O.pop();const d=m.length,y=O.length,A=d<y?d:y;let S;for(S=0;S<A&&m[S].toLowerCase()===O[S].toLowerCase();S++);return S===0?n:S===A?y>A?O.slice(S).join("\\"):d>A?"..\\".repeat(d-1-S)+"..":"":"..\\".repeat(d-S)+O.slice(S).join("\\")}let s=0;for(;s<e.length&&e.charCodeAt(s)===K;)s++;let i=e.length;for(;i-1>s&&e.charCodeAt(i-1)===K;)i--;const o=i-s;let a=0;for(;a<t.length&&t.charCodeAt(a)===K;)a++;let c=t.length;for(;c-1>a&&t.charCodeAt(c-1)===K;)c--;const l=c-a,f=o<l?o:l;let u=-1,h=0;for(;h<f;h++){const m=e.charCodeAt(s+h);if(m!==t.charCodeAt(a+h))break;m===K&&(u=h)}if(h!==f){if(u===-1)return n}else{if(l>f){if(t.charCodeAt(a+h)===K)return n.slice(a+h+1);if(h===2)return n.slice(a+h)}o>f&&(e.charCodeAt(s+h)===K?u=h:h===2&&(u=3)),u===-1&&(u=0)}let g="";for(h=s+u+1;h<=i;++h)(h===i||e.charCodeAt(h)===K)&&(g+=g.length===0?"..":"\\..");return a+=u,g.length>0?`${g}${n.slice(a,c)}`:(n.charCodeAt(a)===K&&++a,n.slice(a,c))},toNamespacedPath(e){if(typeof e!="string"||e.length===0)return e;const t=q.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===K){if(t.charCodeAt(1)===K){const r=t.charCodeAt(2);if(r!==Nt&&r!==d1)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(u1(t.charCodeAt(0))&&t.charCodeAt(1)===o1&&t.charCodeAt(2)===K)return`\\\\?\\${t}`;return t},dirname(e){V(e,"path");const t=e.length;if(t===0)return".";let r=-1,n=0;const s=e.charCodeAt(0);if(t===1)return P(s)?e:".";if(P(s)){if(r=n=1,P(e.charCodeAt(1))){let a=2,c=a;for(;a<t&&!P(e.charCodeAt(a));)a++;if(a<t&&a!==c){for(c=a;a<t&&P(e.charCodeAt(a));)a++;if(a<t&&a!==c){for(c=a;a<t&&!P(e.charCodeAt(a));)a++;if(a===t)return e;a!==c&&(r=n=a+1)}}}}else u1(s)&&e.charCodeAt(1)===o1&&(r=t>2&&P(e.charCodeAt(2))?3:2,n=r);let i=-1,o=!0;for(let a=t-1;a>=n;--a)if(P(e.charCodeAt(a))){if(!o){i=a;break}}else o=!1;if(i===-1){if(r===-1)return".";i=r}return e.slice(0,i)},basename(e,t){t!==void 0&&V(t,"suffix"),V(e,"path");let r=0,n=-1,s=!0,i;if(e.length>=2&&u1(e.charCodeAt(0))&&e.charCodeAt(1)===o1&&(r=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,a=-1;for(i=e.length-1;i>=r;--i){const c=e.charCodeAt(i);if(P(c)){if(!s){r=i+1;break}}else a===-1&&(s=!1,a=i+1),o>=0&&(c===t.charCodeAt(o)?--o===-1&&(n=i):(o=-1,n=a))}return r===n?n=a:n===-1&&(n=e.length),e.slice(r,n)}for(i=e.length-1;i>=r;--i)if(P(e.charCodeAt(i))){if(!s){r=i+1;break}}else n===-1&&(s=!1,n=i+1);return n===-1?"":e.slice(r,n)},extname(e){V(e,"path");let t=0,r=-1,n=0,s=-1,i=!0,o=0;e.length>=2&&e.charCodeAt(1)===o1&&u1(e.charCodeAt(0))&&(t=n=2);for(let a=e.length-1;a>=t;--a){const c=e.charCodeAt(a);if(P(c)){if(!i){n=a+1;break}continue}s===-1&&(i=!1,s=a+1),c===d1?r===-1?r=a:o!==1&&(o=1):r!==-1&&(o=-1)}return r===-1||s===-1||o===0||o===1&&r===s-1&&r===n+1?"":e.slice(r,s)},format:xt.bind(null,"\\"),parse(e){V(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const r=e.length;let n=0,s=e.charCodeAt(0);if(r===1)return P(s)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(P(s)){if(n=1,P(e.charCodeAt(1))){let u=2,h=u;for(;u<r&&!P(e.charCodeAt(u));)u++;if(u<r&&u!==h){for(h=u;u<r&&P(e.charCodeAt(u));)u++;if(u<r&&u!==h){for(h=u;u<r&&!P(e.charCodeAt(u));)u++;u===r?n=u:u!==h&&(n=u+1)}}}}else if(u1(s)&&e.charCodeAt(1)===o1){if(r<=2)return t.root=t.dir=e,t;if(n=2,P(e.charCodeAt(2))){if(r===3)return t.root=t.dir=e,t;n=3}}n>0&&(t.root=e.slice(0,n));let i=-1,o=n,a=-1,c=!0,l=e.length-1,f=0;for(;l>=n;--l){if(s=e.charCodeAt(l),P(s)){if(!c){o=l+1;break}continue}a===-1&&(c=!1,a=l+1),s===d1?i===-1?i=l:f!==1&&(f=1):i!==-1&&(f=-1)}return a!==-1&&(i===-1||f===0||f===1&&i===a-1&&i===o+1?t.base=t.name=e.slice(o,a):(t.name=e.slice(o,i),t.base=e.slice(o,a),t.ext=e.slice(i,a))),o>0&&o!==n?t.dir=e.slice(0,o-1):t.dir=t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},Tt=(()=>{if(G){const e=/\\/g;return()=>{const t=y1().replace(e,"/");return t.slice(t.indexOf("/"))}}return()=>y1()})(),j={resolve(...e){let t="",r=!1;for(let n=e.length-1;n>=0&&!r;n--){const s=e[n];V(s,`paths[${n}]`),s.length!==0&&(t=`${s}/${t}`,r=s.charCodeAt(0)===z)}if(!r){const n=Tt();t=`${n}/${t}`,r=n.charCodeAt(0)===z}return t=G1(t,!r,"/",ye),r?`/${t}`:t.length>0?t:"."},normalize(e){if(V(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===z,r=e.charCodeAt(e.length-1)===z;return e=G1(e,!t,"/",ye),e.length===0?t?"/":r?"./":".":(r&&(e+="/"),t?`/${e}`:e)},isAbsolute(e){return V(e,"path"),e.length>0&&e.charCodeAt(0)===z},join(...e){if(e.length===0)return".";const t=[];for(let r=0;r<e.length;++r){const n=e[r];V(n,"path"),n.length>0&&t.push(n)}return t.length===0?".":j.normalize(t.join("/"))},relative(e,t){if(V(e,"from"),V(t,"to"),e===t||(e=j.resolve(e),t=j.resolve(t),e===t))return"";const r=1,n=e.length,s=n-r,i=1,o=t.length-i,a=s<o?s:o;let c=-1,l=0;for(;l<a;l++){const u=e.charCodeAt(r+l);if(u!==t.charCodeAt(i+l))break;u===z&&(c=l)}if(l===a)if(o>a){if(t.charCodeAt(i+l)===z)return t.slice(i+l+1);if(l===0)return t.slice(i+l)}else s>a&&(e.charCodeAt(r+l)===z?c=l:l===0&&(c=0));let f="";for(l=r+c+1;l<=n;++l)(l===n||e.charCodeAt(l)===z)&&(f+=f.length===0?"..":"/..");return`${f}${t.slice(i+c)}`},toNamespacedPath(e){return e},dirname(e){if(V(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===z;let r=-1,n=!0;for(let s=e.length-1;s>=1;--s)if(e.charCodeAt(s)===z){if(!n){r=s;break}}else n=!1;return r===-1?t?"/":".":t&&r===1?"//":e.slice(0,r)},basename(e,t){t!==void 0&&V(t,"suffix"),V(e,"path");let r=0,n=-1,s=!0,i;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,a=-1;for(i=e.length-1;i>=0;--i){const c=e.charCodeAt(i);if(c===z){if(!s){r=i+1;break}}else a===-1&&(s=!1,a=i+1),o>=0&&(c===t.charCodeAt(o)?--o===-1&&(n=i):(o=-1,n=a))}return r===n?n=a:n===-1&&(n=e.length),e.slice(r,n)}for(i=e.length-1;i>=0;--i)if(e.charCodeAt(i)===z){if(!s){r=i+1;break}}else n===-1&&(s=!1,n=i+1);return n===-1?"":e.slice(r,n)},extname(e){V(e,"path");let t=-1,r=0,n=-1,s=!0,i=0;for(let o=e.length-1;o>=0;--o){const a=e[o];if(a==="/"){if(!s){r=o+1;break}continue}n===-1&&(s=!1,n=o+1),a==="."?t===-1?t=o:i!==1&&(i=1):t!==-1&&(i=-1)}return t===-1||n===-1||i===0||i===1&&t===n-1&&t===r+1?"":e.slice(t,n)},format:xt.bind(null,"/"),parse(e){V(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const r=e.charCodeAt(0)===z;let n;r?(t.root="/",n=1):n=0;let s=-1,i=0,o=-1,a=!0,c=e.length-1,l=0;for(;c>=n;--c){const f=e.charCodeAt(c);if(f===z){if(!a){i=c+1;break}continue}o===-1&&(a=!1,o=c+1),f===d1?s===-1?s=c:l!==1&&(l=1):s!==-1&&(l=-1)}if(o!==-1){const f=i===0&&r?1:i;s===-1||l===0||l===1&&s===o-1&&s===i+1?t.base=t.name=e.slice(f,o):(t.name=e.slice(f,s),t.base=e.slice(f,o),t.ext=e.slice(s,o))}return i>0?t.dir=e.slice(0,i-1):r&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null},j.win32=q.win32=q,j.posix=q.posix=j,jt=G?q.normalize:j.normalize,ni=G?q.isAbsolute:j.isAbsolute,B1=G?q.join:j.join,we=G?q.resolve:j.resolve,Rt=G?q.relative:j.relative,Z1=G?q.dirname:j.dirname,ii=G?q.basename:j.basename,It=G?q.extname:j.extname,si=G?q.format:j.format,oi=G?q.parse:j.parse,ai=G?q.toNamespacedPath:j.toNamespacedPath,D1=G?q.sep:j.sep,li=G?q.delimiter:j.delimiter}}),ci=Wn({"node_modules/minimist/index.js"(e,t){"use strict";function r(i,o){var a=i;o.slice(0,-1).forEach(function(l){a=a[l]||{}});var c=o[o.length-1];return c in a}function n(i){return typeof i=="number"||/^0x[0-9a-f]+$/i.test(i)?!0:/^[-+]?(?:\d+(?:\.\d*)?|\.\d+)(e[-+]?\d+)?$/.test(i)}function s(i,o){return o==="constructor"&&typeof i[o]=="function"||o==="__proto__"}t.exports=function(i,o){o||(o={});var a={bools:{},strings:{},unknownFn:null};typeof o.unknown=="function"&&(a.unknownFn=o.unknown),typeof o.boolean=="boolean"&&o.boolean?a.allBools=!0:[].concat(o.boolean).filter(Boolean).forEach(function(x){a.bools[x]=!0});var c={};function l(x){return c[x].some(function(F){return a.bools[F]})}Object.keys(o.alias||{}).forEach(function(x){c[x]=[].concat(o.alias[x]),c[x].forEach(function(F){c[F]=[x].concat(c[x].filter(function(X){return F!==X}))})}),[].concat(o.string).filter(Boolean).forEach(function(x){a.strings[x]=!0,c[x]&&[].concat(c[x]).forEach(function(F){a.strings[F]=!0})});var f=o.default||{},u={_:[]};function h(x,F){return a.allBools&&/^--[^=]+$/.test(F)||a.strings[x]||a.bools[x]||c[x]}function g(x,F,X){for(var R=x,g1=0;g1<F.length-1;g1++){var v=F[g1];if(s(R,v))return;R[v]===void 0&&(R[v]={}),(R[v]===Object.prototype||R[v]===Number.prototype||R[v]===String.prototype)&&(R[v]={}),R[v]===Array.prototype&&(R[v]=[]),R=R[v]}var p=F[F.length-1];s(R,p)||((R===Object.prototype||R===Number.prototype||R===String.prototype)&&(R={}),R===Array.prototype&&(R=[]),R[p]===void 0||a.bools[p]||typeof R[p]=="boolean"?R[p]=X:Array.isArray(R[p])?R[p].push(X):R[p]=[R[p],X])}function m(x,F,X){if(!(X&&a.unknownFn&&!h(x,X)&&a.unknownFn(X)===!1)){var R=!a.strings[x]&&n(F)?Number(F):F;g(u,x.split("."),R),(c[x]||[]).forEach(function(g1){g(u,g1.split("."),R)})}}Object.keys(a.bools).forEach(function(x){m(x,f[x]===void 0?!1:f[x])});var O=[];i.indexOf("--")!==-1&&(O=i.slice(i.indexOf("--")+1),i=i.slice(0,i.indexOf("--")));for(var d=0;d<i.length;d++){var y=i[d],A,S;if(/^--.+=/.test(y)){var U=y.match(/^--([^=]+)=([\s\S]*)$/);A=U[1];var b=U[2];a.bools[A]&&(b=b!=="false"),m(A,b,y)}else if(/^--no-.+/.test(y))A=y.match(/^--no-(.+)/)[1],m(A,!1,y);else if(/^--.+/.test(y))A=y.match(/^--(.+)/)[1],S=i[d+1],S!==void 0&&!/^(-|--)[^-]/.test(S)&&!a.bools[A]&&!a.allBools&&(!c[A]||!l(A))?(m(A,S,y),d+=1):/^(true|false)$/.test(S)?(m(A,S==="true",y),d+=1):m(A,a.strings[A]?"":!0,y);else if(/^-[^-]+/.test(y)){for(var C=y.slice(1,-1).split(""),D=!1,T=0;T<C.length;T++){if(S=y.slice(T+2),S==="-"){m(C[T],S,y);continue}if(/[A-Za-z]/.test(C[T])&&S[0]==="="){m(C[T],S.slice(1),y),D=!0;break}if(/[A-Za-z]/.test(C[T])&&/-?\d+(\.\d*)?(e-?\d+)?$/.test(S)){m(C[T],S,y),D=!0;break}if(C[T+1]&&C[T+1].match(/\W/)){m(C[T],y.slice(T+2),y),D=!0;break}else m(C[T],a.strings[C[T]]?"":!0,y)}A=y.slice(-1)[0],!D&&A!=="-"&&(i[d+1]&&!/^(-|--)[^-]/.test(i[d+1])&&!a.bools[A]&&(!c[A]||!l(A))?(m(A,i[d+1],y),d+=1):i[d+1]&&/^(true|false)$/.test(i[d+1])?(m(A,i[d+1]==="true",y),d+=1):m(A,a.strings[A]?"":!0,y))}else if((!a.unknownFn||a.unknownFn(y)!==!1)&&u._.push(a.strings._||!n(y)?y:Number(y)),o.stopEarly){u._.push.apply(u._,i.slice(d+1));break}}return Object.keys(f).forEach(function(x){r(u,x.split("."))||(g(u,x.split("."),f[x]),(c[x]||[]).forEach(function(F){g(u,F.split("."),f[x])}))}),o["--"]?u["--"]=O.slice():O.forEach(function(x){u._.push(x)}),u}}});function Mt(e,t,r=zt){const n=e.find(h=>h.length>0&&h[0]!=="-"),s={},i=["_"],o=[],a={};let c;for(const h in t){const g=t[h];g.type==="subcommand"?h===n&&(c=g):(g.alias&&(s[h]=g.alias),g.type==="string"||g.type==="string[]"?(i.push(h),g.deprecates&&i.push(...g.deprecates)):g.type==="boolean"&&(o.push(h),g.deprecates&&o.push(...g.deprecates)),g.global&&(a[h]=g))}if(c&&n){const h=a;for(const d in c.options)h[d]=c.options[d];const g=e.filter(d=>d!==n),m=r.getSubcommandReporter?r.getSubcommandReporter(n):void 0,O=Mt(g,h,m);return{[n]:O,_:[]}}const l=(0,Vt.default)(e,{string:i,boolean:o,alias:s}),f={},u=l;f._=l._.map(h=>String(h)).filter(h=>h.length>0),delete u._;for(const h in t){const g=t[h];if(g.type==="subcommand")continue;g.alias&&delete u[g.alias];let m=u[h];if(g.deprecates)for(const O of g.deprecates)u.hasOwnProperty(O)&&(m||(m=u[O],m&&r.onDeprecatedOption(O,g.deprecationMessage||$(1884,null,h))),delete u[O]);if(typeof m<"u"){if(g.type==="string[]"){if(Array.isArray(m)||(m=[m]),!g.allowEmptyValue){const O=m.filter(d=>d.length>0);O.length!==m.length&&(r.onEmptyValue(h),m=O.length>0?O:void 0)}}else g.type==="string"&&(Array.isArray(m)?(m=m.pop(),r.onMultipleValues(h,m)):!m&&!g.allowEmptyValue&&(r.onEmptyValue(h),m=void 0));f[h]=m,g.deprecationMessage&&r.onDeprecatedOption(h,g.deprecationMessage)}delete u[h]}for(const h in u)r.onUnknownOption(h);return f}function ui(e,t){let r="";return t.args&&(Array.isArray(t.args)?r=` <${t.args.join("> <")}>`:r=` <${t.args}>`),t.alias?`-${t.alias} --${e}${r}`:`--${e}${r}`}function fi(e,t){const r=[];for(const n in e){const s=e[n],i=ui(n,s);r.push([i,s.description])}return Ft(r,t)}function Ft(e,t){const n=e.reduce((o,a)=>Math.max(o,a[0].length),12)+2+1;if(t-n<25)return e.reduce((o,a)=>o.concat([`  ${a[0]}`,`      ${a[1]}`]),[]);const s=t-n-1,i=[];for(const o of e){const a=o[0],c=hi(o[1],s),l=Ut(n-a.length-2);i.push("  "+a+l+c[0]);for(let f=1;f<c.length;f++)i.push(Ut(n)+c[f])}return i}function Ut(e){return" ".repeat(e)}function hi(e,t){const r=[];for(;e.length;){let n=e.length<t?e.length:e.lastIndexOf(" ",t);n===0&&(n=t);const s=e.slice(0,n).trim();e=e.slice(n).trimStart(),r.push(s)}return r}function di(e,t,r,n,s){const i=process.stdout.isTTY&&process.stdout.columns||80,o=s?.noInputFiles!==!0?`[${$(1885,null)}...]`:"",a=[`${e} ${r}`];a.push(""),a.push(`${$(1886,null)}: ${t} [${$(1887,null)}]${o}`),a.push(""),s?.noPipe!==!0&&(s1?a.push($(1888,null,t)):a.push($(1889,null,t)),a.push(""));const c={},l=[];for(const f in n){const u=n[f];if(u.type==="subcommand")u.description&&l.push({command:f,description:u.description});else if(u.description&&u.cat){let h=c[u.cat];h||(c[u.cat]=h={}),h[f]=u}}for(const f in c){const u=f,h=c[u];h&&(a.push(qt[u]),a.push(...fi(h,i)),a.push(""))}return l.length&&(a.push($(1890,null)),a.push(...Ft(l.map(f=>[f.command,f.description]),i)),a.push("")),a.join(`
`)}function pi(e,t){return`${e||$(1891,null)}
${t||$(1892,null)}
${process.arch}`}var Vt,qt,_e,zt,gi=k({"out-build/vs/platform/environment/node/argv.js"(){"use strict";Vt=Kn(ci(),1),h1(),st(),qt={o:$(1842,null),e:$(1843,null),t:$(1844,null)},_e={tunnel:{type:"subcommand",description:"Make the current machine accessible from vscode.dev or other machines through a secure tunnel",options:{"cli-data-dir":{type:"string",args:"dir",description:$(1845,null)},"disable-telemetry":{type:"boolean"},"telemetry-level":{type:"string"},user:{type:"subcommand",options:{login:{type:"subcommand",options:{provider:{type:"string"},"access-token":{type:"string"}}}}}}},"serve-web":{type:"subcommand",description:"Run a server that displays the editor UI in browsers.",options:{"cli-data-dir":{type:"string",args:"dir",description:$(1846,null)},"disable-telemetry":{type:"boolean"},"telemetry-level":{type:"string"}}},diff:{type:"boolean",cat:"o",alias:"d",args:["file","file"],description:$(1847,null)},merge:{type:"boolean",cat:"o",alias:"m",args:["path1","path2","base","result"],description:$(1848,null)},add:{type:"boolean",cat:"o",alias:"a",args:"folder",description:$(1849,null)},remove:{type:"boolean",cat:"o",args:"folder",description:$(1850,null)},goto:{type:"boolean",cat:"o",alias:"g",args:"file:line[:character]",description:$(1851,null)},"new-window":{type:"boolean",cat:"o",alias:"n",description:$(1852,null)},"reuse-window":{type:"boolean",cat:"o",alias:"r",description:$(1853,null)},wait:{type:"boolean",cat:"o",alias:"w",description:$(1854,null)},waitMarkerFilePath:{type:"string"},locale:{type:"string",cat:"o",args:"locale",description:$(1855,null)},"user-data-dir":{type:"string",cat:"o",args:"dir",description:$(1856,null)},profile:{type:"string",cat:"o",args:"profileName",description:$(1857,null)},help:{type:"boolean",cat:"o",alias:"h",description:$(1858,null)},"extensions-dir":{type:"string",deprecates:["extensionHomePath"],cat:"e",args:"dir",description:$(1859,null)},"extensions-download-dir":{type:"string"},"builtin-extensions-dir":{type:"string"},"list-extensions":{type:"boolean",cat:"e",description:$(1860,null)},"show-versions":{type:"boolean",cat:"e",description:$(1861,null)},category:{type:"string",allowEmptyValue:!0,cat:"e",description:$(1862,null),args:"category"},"install-extension":{type:"string[]",cat:"e",args:"ext-id | path",description:$(1863,null)},"pre-release":{type:"boolean",cat:"e",description:$(1864,null)},"uninstall-extension":{type:"string[]",cat:"e",args:"ext-id",description:$(1865,null)},"update-extensions":{type:"boolean",cat:"e",description:$(1866,null)},"enable-proposed-api":{type:"string[]",allowEmptyValue:!0,cat:"e",args:"ext-id",description:$(1867,null)},"add-mcp":{type:"string[]",cat:"o",args:"json",description:$(1868,null)},version:{type:"boolean",cat:"t",alias:"v",description:$(1869,null)},verbose:{type:"boolean",cat:"t",global:!0,description:$(1870,null)},log:{type:"string[]",cat:"t",args:"level",global:!0,description:$(1871,null)},status:{type:"boolean",alias:"s",cat:"t",description:$(1872,null)},"prof-startup":{type:"boolean",cat:"t",description:$(1873,null)},"prof-append-timers":{type:"string"},"prof-duration-markers":{type:"string[]"},"prof-duration-markers-file":{type:"string"},"no-cached-data":{type:"boolean"},"prof-startup-prefix":{type:"string"},"prof-v8-extensions":{type:"boolean"},"disable-extensions":{type:"boolean",deprecates:["disableExtensions"],cat:"t",description:$(1874,null)},"disable-extension":{type:"string[]",cat:"t",args:"ext-id",description:$(1875,null)},sync:{type:"string",cat:"t",description:$(1876,null),args:["on | off"]},"inspect-extensions":{type:"string",allowEmptyValue:!0,deprecates:["debugPluginHost"],args:"port",cat:"t",description:$(1877,null)},"inspect-brk-extensions":{type:"string",allowEmptyValue:!0,deprecates:["debugBrkPluginHost"],args:"port",cat:"t",description:$(1878,null)},"disable-lcd-text":{type:"boolean",cat:"t",description:$(1879,null)},"disable-gpu":{type:"boolean",cat:"t",description:$(1880,null)},"disable-chromium-sandbox":{type:"boolean",cat:"t",description:$(1881,null)},sandbox:{type:"boolean"},"locate-shell-integration-path":{type:"string",cat:"t",args:["shell"],description:$(1882,null)},telemetry:{type:"boolean",cat:"t",description:$(1883,null)},remote:{type:"string",allowEmptyValue:!0},"folder-uri":{type:"string[]",cat:"o",args:"uri"},"file-uri":{type:"string[]",cat:"o",args:"uri"},"locate-extension":{type:"string[]"},extensionDevelopmentPath:{type:"string[]"},extensionDevelopmentKind:{type:"string[]"},extensionTestsPath:{type:"string"},extensionEnvironment:{type:"string"},debugId:{type:"string"},debugRenderer:{type:"boolean"},"inspect-ptyhost":{type:"string",allowEmptyValue:!0},"inspect-brk-ptyhost":{type:"string",allowEmptyValue:!0},"inspect-search":{type:"string",deprecates:["debugSearch"],allowEmptyValue:!0},"inspect-brk-search":{type:"string",deprecates:["debugBrkSearch"],allowEmptyValue:!0},"inspect-sharedprocess":{type:"string",allowEmptyValue:!0},"inspect-brk-sharedprocess":{type:"string",allowEmptyValue:!0},"export-default-configuration":{type:"string"},"install-source":{type:"string"},"enable-smoke-test-driver":{type:"boolean"},logExtensionHostCommunication:{type:"boolean"},"skip-release-notes":{type:"boolean"},"skip-welcome":{type:"boolean"},"disable-telemetry":{type:"boolean"},"disable-updates":{type:"boolean"},"use-inmemory-secretstorage":{type:"boolean",deprecates:["disable-keytar"]},"password-store":{type:"string"},"disable-workspace-trust":{type:"boolean"},"disable-crash-reporter":{type:"boolean"},"crash-reporter-directory":{type:"string"},"crash-reporter-id":{type:"string"},"skip-add-to-recently-opened":{type:"boolean"},"open-url":{type:"boolean"},"file-write":{type:"boolean"},"file-chmod":{type:"boolean"},"install-builtin-extension":{type:"string[]"},force:{type:"boolean"},"do-not-sync":{type:"boolean"},"do-not-include-pack-dependencies":{type:"boolean"},trace:{type:"boolean"},"trace-memory-infra":{type:"boolean"},"trace-category-filter":{type:"string"},"trace-options":{type:"string"},"preserve-env":{type:"boolean"},"force-user-env":{type:"boolean"},"force-disable-user-env":{type:"boolean"},"open-devtools":{type:"boolean"},"disable-gpu-sandbox":{type:"boolean"},logsPath:{type:"string"},"__enable-file-policy":{type:"boolean"},editSessionId:{type:"string"},continueOn:{type:"string"},"enable-coi":{type:"boolean"},"unresponsive-sample-interval":{type:"string"},"unresponsive-sample-period":{type:"string"},"no-proxy-server":{type:"boolean"},"no-sandbox":{type:"boolean",alias:"sandbox"},"proxy-server":{type:"string"},"proxy-bypass-list":{type:"string"},"proxy-pac-url":{type:"string"},"js-flags":{type:"string"},inspect:{type:"string",allowEmptyValue:!0},"inspect-brk":{type:"string",allowEmptyValue:!0},nolazy:{type:"boolean"},"force-device-scale-factor":{type:"string"},"force-renderer-accessibility":{type:"boolean"},"ignore-certificate-errors":{type:"boolean"},"allow-insecure-localhost":{type:"boolean"},"log-net-log":{type:"string"},vmodule:{type:"string"},_urls:{type:"string[]"},"disable-dev-shm-usage":{type:"boolean"},"profile-temp":{type:"boolean"},"ozone-platform":{type:"string"},"enable-tracing":{type:"string"},"trace-startup-format":{type:"string"},"trace-startup-file":{type:"string"},"trace-startup-duration":{type:"string"},"xdg-portal-required-version":{type:"string"},_:{type:"string[]"}},zt={onUnknownOption:()=>{},onMultipleValues:()=>{},onEmptyValue:()=>{},onDeprecatedOption:()=>{}}}});function mi(e,t){const r=Object.create(null);for(const n of e){const s=t(n);let i=r[s];i||(i=r[s]=[]),i.push(n)}return r}var Wt,vi,Ht=k({"out-build/vs/base/common/collections.js"(){"use strict";vi=class{static{Wt=Symbol.toStringTag}constructor(e,t){this.b=t,this.a=new Map,this[Wt]="SetWithKey";for(const r of e)this.add(r)}get size(){return this.a.size}add(e){const t=this.b(e);return this.a.set(t,e),this}delete(e){return this.a.delete(this.b(e))}has(e){return this.a.has(this.b(e))}*entries(){for(const e of this.a.values())yield[e,e]}keys(){return this.values()}*values(){for(const e of this.a.values())yield e}clear(){this.a.clear()}forEach(e,t){this.a.forEach(r=>e.call(t,r,r,this))}[Symbol.iterator](){return this.values()}}}});function Q1(e){yi(e)||Kt.onUnexpectedError(e)}function yi(e){return e instanceof Ce?!0:e instanceof Error&&e.name===Y1&&e.message===Y1}var Jt,Kt,Y1,Ce,Ee,N1=k({"out-build/vs/base/common/errors.js"(){"use strict";Jt=class{constructor(){this.b=[],this.a=function(e){setTimeout(()=>{throw e.stack?Ee.isErrorNoTelemetry(e)?new Ee(e.message+`

`+e.stack):new Error(e.message+`

`+e.stack):e},0)}}addListener(e){return this.b.push(e),()=>{this.d(e)}}c(e){this.b.forEach(t=>{t(e)})}d(e){this.b.splice(this.b.indexOf(e),1)}setUnexpectedErrorHandler(e){this.a=e}getUnexpectedErrorHandler(){return this.a}onUnexpectedError(e){this.a(e),this.c(e)}onUnexpectedExternalError(e){this.a(e)}},Kt=new Jt,Y1="Canceled",Ce=class extends Error{constructor(){super(Y1),this.name=this.message}},Ee=class Ye extends Error{constructor(t){super(t),this.name="CodeExpectedError"}static fromError(t){if(t instanceof Ye)return t;const r=new Ye;return r.message=t.message,r.stack=t.stack,r}static isErrorNoTelemetry(t){return t.name==="CodeExpectedError"}}}});function bi(e,t){const r=this;let n=!1,s;return function(){if(n)return s;if(n=!0,t)try{s=e.apply(r,arguments)}finally{t()}else s=e.apply(r,arguments);return s}}var Gt=k({"out-build/vs/base/common/functional.js"(){"use strict"}});function wi(e,t,r=0,n=e.length){let s=r,i=n;for(;s<i;){const o=Math.floor((s+i)/2);t(e[o])?s=o+1:i=o}return s-1}var _i,Ci=k({"out-build/vs/base/common/arraysFind.js"(){"use strict";_i=class Ln{static{this.assertInvariants=!1}constructor(t){this.e=t,this.c=0}findLastMonotonous(t){if(Ln.assertInvariants){if(this.d){for(const n of this.e)if(this.d(n)&&!t(n))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this.d=t}const r=wi(this.e,t,this.c);return this.c=r+1,r===-1?void 0:this.e[r]}}}});function Ei(e,t){return(r,n)=>t(e(r),e(n))}var Ae,Bt,Ai,$i=k({"out-build/vs/base/common/arrays.js"(){"use strict";Ci(),N1(),function(e){function t(i){return i<0}e.isLessThan=t;function r(i){return i<=0}e.isLessThanOrEqual=r;function n(i){return i>0}e.isGreaterThan=n;function s(i){return i===0}e.isNeitherLessOrGreaterThan=s,e.greaterThan=1,e.lessThan=-1,e.neitherLessOrGreaterThan=0}(Ae||(Ae={})),Bt=(e,t)=>e-t,Ai=class le{static{this.empty=new le(t=>{})}constructor(t){this.iterate=t}forEach(t){this.iterate(r=>(t(r),!0))}toArray(){const t=[];return this.iterate(r=>(t.push(r),!0)),t}filter(t){return new le(r=>this.iterate(n=>t(n)?r(n):!0))}map(t){return new le(r=>this.iterate(n=>r(t(n))))}some(t){let r=!1;return this.iterate(n=>(r=t(n),!r)),r}findFirst(t){let r;return this.iterate(n=>t(n)?(r=n,!1):!0),r}findLast(t){let r;return this.iterate(n=>(t(n)&&(r=n),!0)),r}findLastMaxBy(t){let r,n=!0;return this.iterate(s=>((n||Ae.isGreaterThan(t(s,r)))&&(n=!1,r=s),!0)),r}}}});function Oi(e){return Array.isArray(e)}var Zt,Qt,Yt,Xt,$e,Si,er,xi,tr,Pi=k({"out-build/vs/base/common/map.js"(){"use strict";Xt=class{constructor(e,t){this.uri=e,this.value=t}},$e=class F1{static{this.c=t=>t.toString()}constructor(t,r){if(this[Zt]="ResourceMap",t instanceof F1)this.d=new Map(t.d),this.e=r??F1.c;else if(Oi(t)){this.d=new Map,this.e=r??F1.c;for(const[n,s]of t)this.set(n,s)}else this.d=new Map,this.e=t??F1.c}set(t,r){return this.d.set(this.e(t),new Xt(t,r)),this}get(t){return this.d.get(this.e(t))?.value}has(t){return this.d.has(this.e(t))}get size(){return this.d.size}clear(){this.d.clear()}delete(t){return this.d.delete(this.e(t))}forEach(t,r){typeof r<"u"&&(t=t.bind(r));for(const[n,s]of this.d)t(s.value,s.uri,this)}*values(){for(const t of this.d.values())yield t.value}*keys(){for(const t of this.d.values())yield t.uri}*entries(){for(const t of this.d.values())yield[t.uri,t.value]}*[(Zt=Symbol.toStringTag,Symbol.iterator)](){for(const[,t]of this.d)yield[t.uri,t.value]}},Si=class{constructor(e,t){this[Qt]="ResourceSet",!e||typeof e=="function"?this.c=new $e(e):(this.c=new $e(t),e.forEach(this.add,this))}get size(){return this.c.size}add(e){return this.c.set(e,e),this}clear(){this.c.clear()}delete(e){return this.c.delete(e)}forEach(e,t){this.c.forEach((r,n)=>e.call(t,n,n,this))}has(e){return this.c.has(e)}entries(){return this.c.entries()}keys(){return this.c.keys()}values(){return this.c.keys()}[(Qt=Symbol.toStringTag,Symbol.iterator)](){return this.keys()}},function(e){e[e.None=0]="None",e[e.AsOld=1]="AsOld",e[e.AsNew=2]="AsNew"}(er||(er={})),xi=class{constructor(){this[Yt]="LinkedMap",this.c=new Map,this.d=void 0,this.e=void 0,this.f=0,this.g=0}clear(){this.c.clear(),this.d=void 0,this.e=void 0,this.f=0,this.g++}isEmpty(){return!this.d&&!this.e}get size(){return this.f}get first(){return this.d?.value}get last(){return this.e?.value}has(e){return this.c.has(e)}get(e,t=0){const r=this.c.get(e);if(r)return t!==0&&this.n(r,t),r.value}set(e,t,r=0){let n=this.c.get(e);if(n)n.value=t,r!==0&&this.n(n,r);else{switch(n={key:e,value:t,next:void 0,previous:void 0},r){case 0:this.l(n);break;case 1:this.k(n);break;case 2:this.l(n);break;default:this.l(n);break}this.c.set(e,n),this.f++}return this}delete(e){return!!this.remove(e)}remove(e){const t=this.c.get(e);if(t)return this.c.delete(e),this.m(t),this.f--,t.value}shift(){if(!this.d&&!this.e)return;if(!this.d||!this.e)throw new Error("Invalid list");const e=this.d;return this.c.delete(e.key),this.m(e),this.f--,e.value}forEach(e,t){const r=this.g;let n=this.d;for(;n;){if(t?e.bind(t)(n.value,n.key,this):e(n.value,n.key,this),this.g!==r)throw new Error("LinkedMap got modified during iteration.");n=n.next}}keys(){const e=this,t=this.g;let r=this.d;const n={[Symbol.iterator](){return n},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(r){const s={value:r.key,done:!1};return r=r.next,s}else return{value:void 0,done:!0}}};return n}values(){const e=this,t=this.g;let r=this.d;const n={[Symbol.iterator](){return n},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(r){const s={value:r.value,done:!1};return r=r.next,s}else return{value:void 0,done:!0}}};return n}entries(){const e=this,t=this.g;let r=this.d;const n={[Symbol.iterator](){return n},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(r){const s={value:[r.key,r.value],done:!1};return r=r.next,s}else return{value:void 0,done:!0}}};return n}[(Yt=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}h(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.d,r=this.size;for(;t&&r>e;)this.c.delete(t.key),t=t.next,r--;this.d=t,this.f=r,t&&(t.previous=void 0),this.g++}j(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.e,r=this.size;for(;t&&r>e;)this.c.delete(t.key),t=t.previous,r--;this.e=t,this.f=r,t&&(t.next=void 0),this.g++}k(e){if(!this.d&&!this.e)this.e=e;else if(this.d)e.next=this.d,this.d.previous=e;else throw new Error("Invalid list");this.d=e,this.g++}l(e){if(!this.d&&!this.e)this.d=e;else if(this.e)e.previous=this.e,this.e.next=e;else throw new Error("Invalid list");this.e=e,this.g++}m(e){if(e===this.d&&e===this.e)this.d=void 0,this.e=void 0;else if(e===this.d){if(!e.next)throw new Error("Invalid list");e.next.previous=void 0,this.d=e.next}else if(e===this.e){if(!e.previous)throw new Error("Invalid list");e.previous.next=void 0,this.e=e.previous}else{const t=e.next,r=e.previous;if(!t||!r)throw new Error("Invalid list");t.previous=r,r.next=t}e.next=void 0,e.previous=void 0,this.g++}n(e,t){if(!this.d||!this.e)throw new Error("Invalid list");if(!(t!==1&&t!==2)){if(t===1){if(e===this.d)return;const r=e.next,n=e.previous;e===this.e?(n.next=void 0,this.e=n):(r.previous=n,n.next=r),e.previous=void 0,e.next=this.d,this.d.previous=e,this.d=e,this.g++}else if(t===2){if(e===this.e)return;const r=e.next,n=e.previous;e===this.d?(r.previous=void 0,this.d=r):(r.previous=n,n.next=r),e.next=void 0,e.previous=this.e,this.e.next=e,this.e=e,this.g++}}}toJSON(){const e=[];return this.forEach((t,r)=>{e.push([r,t])}),e}fromJSON(e){this.clear();for(const[t,r]of e)this.set(t,r)}},tr=class{constructor(){this.c=new Map}add(e,t){let r=this.c.get(e);r||(r=new Set,this.c.set(e,r)),r.add(t)}delete(e,t){const r=this.c.get(e);r&&(r.delete(t),r.size===0&&this.c.delete(e))}forEach(e,t){const r=this.c.get(e);r&&r.forEach(t)}get(e){const t=this.c.get(e);return t||new Set}}}}),Li=k({"out-build/vs/base/common/assert.js"(){"use strict";N1()}});function Di(e){return!!e&&typeof e[Symbol.iterator]=="function"}var rr=k({"out-build/vs/base/common/types.js"(){"use strict";Li()}}),Oe,ki=k({"out-build/vs/base/common/iterator.js"(){"use strict";rr(),function(e){function t(b){return b&&typeof b=="object"&&typeof b[Symbol.iterator]=="function"}e.is=t;const r=Object.freeze([]);function n(){return r}e.empty=n;function*s(b){yield b}e.single=s;function i(b){return t(b)?b:s(b)}e.wrap=i;function o(b){return b||r}e.from=o;function*a(b){for(let C=b.length-1;C>=0;C--)yield b[C]}e.reverse=a;function c(b){return!b||b[Symbol.iterator]().next().done===!0}e.isEmpty=c;function l(b){return b[Symbol.iterator]().next().value}e.first=l;function f(b,C){let D=0;for(const T of b)if(C(T,D++))return!0;return!1}e.some=f;function u(b,C){for(const D of b)if(C(D))return D}e.find=u;function*h(b,C){for(const D of b)C(D)&&(yield D)}e.filter=h;function*g(b,C){let D=0;for(const T of b)yield C(T,D++)}e.map=g;function*m(b,C){let D=0;for(const T of b)yield*C(T,D++)}e.flatMap=m;function*O(...b){for(const C of b)Di(C)?yield*C:yield C}e.concat=O;function d(b,C,D){let T=D;for(const x of b)T=C(T,x);return T}e.reduce=d;function y(b){let C=0;for(const D of b)C++;return C}e.length=y;function*A(b,C,D=b.length){for(C<-b.length&&(C=0),C<0&&(C+=b.length),D<0?D+=b.length:D>b.length&&(D=b.length);C<D;C++)yield b[C]}e.slice=A;function S(b,C=Number.POSITIVE_INFINITY){const D=[];if(C===0)return[D,b];const T=b[Symbol.iterator]();for(let x=0;x<C;x++){const F=T.next();if(F.done)return[D,e.empty()];D.push(F.value)}return[D,{[Symbol.iterator](){return T}}]}e.consume=S;async function U(b){const C=[];for await(const D of b)C.push(D);return Promise.resolve(C)}e.asyncToArray=U}(Oe||(Oe={}))}});function Ni(e){b1=e}function Se(e){return b1?.trackDisposable(e),e}function xe(e){b1?.markAsDisposed(e)}function Pe(e,t){b1?.setParent(e,t)}function Ti(e,t){if(b1)for(const r of e)b1.setParent(r,t)}function nr(e){if(Oe.is(e)){const t=[];for(const r of e)if(r)try{r.dispose()}catch(n){t.push(n)}if(t.length===1)throw t[0];if(t.length>1)throw new AggregateError(t,"Encountered errors while disposing of store");return Array.isArray(e)?[]:e}else if(e)return e.dispose(),e}function ji(...e){const t=ir(()=>nr(e));return Ti(e,t),t}function ir(e){const t=Se({dispose:bi(()=>{xe(t),e()})});return t}var sr,b1,Ri,X1,E1,or=k({"out-build/vs/base/common/lifecycle.js"(){"use strict";if($i(),Ht(),Pi(),Gt(),ki(),sr=!1,b1=null,Ri=class Dn{constructor(){this.b=new Map}static{this.a=0}c(t){let r=this.b.get(t);return r||(r={parent:null,source:null,isSingleton:!1,value:t,idx:Dn.a++},this.b.set(t,r)),r}trackDisposable(t){const r=this.c(t);r.source||(r.source=new Error().stack)}setParent(t,r){const n=this.c(t);n.parent=r}markAsDisposed(t){this.b.delete(t)}markAsSingleton(t){this.c(t).isSingleton=!0}f(t,r){const n=r.get(t);if(n)return n;const s=t.parent?this.f(this.c(t.parent),r):t;return r.set(t,s),s}getTrackedDisposables(){const t=new Map;return[...this.b.entries()].filter(([,n])=>n.source!==null&&!this.f(n,t).isSingleton).flatMap(([n])=>n)}computeLeakingDisposables(t=10,r){let n;if(r)n=r;else{const c=new Map,l=[...this.b.values()].filter(u=>u.source!==null&&!this.f(u,c).isSingleton);if(l.length===0)return;const f=new Set(l.map(u=>u.value));if(n=l.filter(u=>!(u.parent&&f.has(u.parent))),n.length===0)throw new Error("There are cyclic diposable chains!")}if(!n)return;function s(c){function l(u,h){for(;u.length>0&&h.some(g=>typeof g=="string"?g===u[0]:u[0].match(g));)u.shift()}const f=c.source.split(`
`).map(u=>u.trim().replace("at ","")).filter(u=>u!=="");return l(f,["Error",/^trackDisposable \(.*\)$/,/^DisposableTracker.trackDisposable \(.*\)$/]),f.reverse()}const i=new tr;for(const c of n){const l=s(c);for(let f=0;f<=l.length;f++)i.add(l.slice(0,f).join(`
`),c)}n.sort(Ei(c=>c.idx,Bt));let o="",a=0;for(const c of n.slice(0,t)){a++;const l=s(c),f=[];for(let u=0;u<l.length;u++){let h=l[u];h=`(shared with ${i.get(l.slice(0,u+1).join(`
`)).size}/${n.length} leaks) at ${h}`;const m=i.get(l.slice(0,u).join(`
`)),O=mi([...m].map(d=>s(d)[u]),d=>d);delete O[l[u]];for(const[d,y]of Object.entries(O))f.unshift(`    - stacktraces of ${y.length} other leaks continue with ${d}`);f.unshift(h)}o+=`


==================== Leaking disposable ${a}/${n.length}: ${c.value.constructor.name} ====================
${f.join(`
`)}
============================================================

`}return n.length>t&&(o+=`


... and ${n.length-t} more leaking disposables

`),{leaks:n,details:o}}},sr){const e="__is_disposable_tracked__";Ni(new class{trackDisposable(t){const r=new Error("Potentially leaked disposable").stack;setTimeout(()=>{t[e]||console.log(r)},3e3)}setParent(t,r){if(t&&t!==E1.None)try{t[e]=!0}catch{}}markAsDisposed(t){if(t&&t!==E1.None)try{t[e]=!0}catch{}}markAsSingleton(t){}})}X1=class kn{static{this.DISABLE_DISPOSED_WARNING=!1}constructor(){this.f=new Set,this.g=!1,Se(this)}dispose(){this.g||(xe(this),this.g=!0,this.clear())}get isDisposed(){return this.g}clear(){if(this.f.size!==0)try{nr(this.f)}finally{this.f.clear()}}add(t){if(!t)return t;if(t===this)throw new Error("Cannot register a disposable on itself!");return Pe(t,this),this.g?kn.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this.f.add(t),t}delete(t){if(t){if(t===this)throw new Error("Cannot dispose a disposable on itself!");this.f.delete(t),t.dispose()}}deleteAndLeak(t){t&&this.f.has(t)&&(this.f.delete(t),Pe(t,null))}},E1=class{static{this.None=Object.freeze({dispose(){}})}constructor(){this.q=new X1,Se(this),Pe(this.q,this)}dispose(){xe(this),this.q.dispose()}B(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this.q.add(e)}}}}),Ii,Mi=k({"out-build/vs/base/common/linkedList.js"(){"use strict";Ii=class ce{static{this.Undefined=new ce(void 0)}constructor(t){this.element=t,this.next=ce.Undefined,this.prev=ce.Undefined}}}}),ar,lr,Fi=k({"out-build/vs/base/common/stopwatch.js"(){"use strict";ar=globalThis.performance.now.bind(globalThis.performance),lr=class Nn{static create(t){return new Nn(t)}constructor(t){this.c=t===!1?Date.now:ar,this.a=this.c(),this.b=-1}stop(){this.b=this.c()}reset(){this.a=this.c(),this.b=-1}elapsed(){return this.b!==-1?this.b-this.a:this.c()-this.a}}}}),Le,cr,T1,ur,De,fr,ee,hr,dr,pr,j1,gr,mr,t1,vr,yr=k({"out-build/vs/base/common/event.js"(){"use strict";Ht(),N1(),Gt(),or(),Mi(),Fi(),Le=!1,cr=!1,function(e){e.None=()=>E1.None;function t(v){if(cr){const{onDidAddListener:p}=v,_=ee.create();let w=0;v.onDidAddListener=()=>{++w===2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),_.print()),p?.()}}}function r(v,p){return g(v,()=>{},0,void 0,!0,void 0,p)}e.defer=r;function n(v){return(p,_=null,w)=>{let E=!1,L;return L=v(I=>{if(!E)return L?L.dispose():E=!0,p.call(_,I)},null,w),E&&L.dispose(),L}}e.once=n;function s(v,p){return e.once(e.filter(v,p))}e.onceIf=s;function i(v,p,_){return u((w,E=null,L)=>v(I=>w.call(E,p(I)),null,L),_)}e.map=i;function o(v,p,_){return u((w,E=null,L)=>v(I=>{p(I),w.call(E,I)},null,L),_)}e.forEach=o;function a(v,p,_){return u((w,E=null,L)=>v(I=>p(I)&&w.call(E,I),null,L),_)}e.filter=a;function c(v){return v}e.signal=c;function l(...v){return(p,_=null,w)=>{const E=ji(...v.map(L=>L(I=>p.call(_,I))));return h(E,w)}}e.any=l;function f(v,p,_,w){let E=_;return i(v,L=>(E=p(E,L),E),w)}e.reduce=f;function u(v,p){let _;const w={onWillAddFirstListener(){_=v(E.fire,E)},onDidRemoveLastListener(){_?.dispose()}};p||t(w);const E=new t1(w);return p?.add(E),E.event}function h(v,p){return p instanceof Array?p.push(v):p&&p.add(v),v}function g(v,p,_=100,w=!1,E=!1,L,I){let W,J,_1,z1=0,O1;const et={leakWarningThreshold:L,onWillAddFirstListener(){W=v(Rn=>{z1++,J=p(J,Rn),w&&!_1&&(W1.fire(J),J=void 0),O1=()=>{const In=J;J=void 0,_1=void 0,(!w||z1>1)&&W1.fire(In),z1=0},typeof _=="number"?(clearTimeout(_1),_1=setTimeout(O1,_)):_1===void 0&&(_1=0,queueMicrotask(O1))})},onWillRemoveListener(){E&&z1>0&&O1?.()},onDidRemoveLastListener(){O1=void 0,W.dispose()}};I||t(et);const W1=new t1(et);return I?.add(W1),W1.event}e.debounce=g;function m(v,p=0,_){return e.debounce(v,(w,E)=>w?(w.push(E),w):[E],p,void 0,!0,void 0,_)}e.accumulate=m;function O(v,p=(w,E)=>w===E,_){let w=!0,E;return a(v,L=>{const I=w||!p(L,E);return w=!1,E=L,I},_)}e.latch=O;function d(v,p,_){return[e.filter(v,p,_),e.filter(v,w=>!p(w),_)]}e.split=d;function y(v,p=!1,_=[],w){let E=_.slice(),L=v(J=>{E?E.push(J):W.fire(J)});w&&w.add(L);const I=()=>{E?.forEach(J=>W.fire(J)),E=null},W=new t1({onWillAddFirstListener(){L||(L=v(J=>W.fire(J)),w&&w.add(L))},onDidAddFirstListener(){E&&(p?setTimeout(I):I())},onDidRemoveLastListener(){L&&L.dispose(),L=null}});return w&&w.add(W),W.event}e.buffer=y;function A(v,p){return(w,E,L)=>{const I=p(new U);return v(function(W){const J=I.evaluate(W);J!==S&&w.call(E,J)},void 0,L)}}e.chain=A;const S=Symbol("HaltChainable");class U{constructor(){this.f=[]}map(p){return this.f.push(p),this}forEach(p){return this.f.push(_=>(p(_),_)),this}filter(p){return this.f.push(_=>p(_)?_:S),this}reduce(p,_){let w=_;return this.f.push(E=>(w=p(w,E),w)),this}latch(p=(_,w)=>_===w){let _=!0,w;return this.f.push(E=>{const L=_||!p(E,w);return _=!1,w=E,L?E:S}),this}evaluate(p){for(const _ of this.f)if(p=_(p),p===S)break;return p}}function b(v,p,_=w=>w){const w=(...W)=>I.fire(_(...W)),E=()=>v.on(p,w),L=()=>v.removeListener(p,w),I=new t1({onWillAddFirstListener:E,onDidRemoveLastListener:L});return I.event}e.fromNodeEventEmitter=b;function C(v,p,_=w=>w){const w=(...W)=>I.fire(_(...W)),E=()=>v.addEventListener(p,w),L=()=>v.removeEventListener(p,w),I=new t1({onWillAddFirstListener:E,onDidRemoveLastListener:L});return I.event}e.fromDOMEventEmitter=C;function D(v,p){return new Promise(_=>n(v)(_,null,p))}e.toPromise=D;function T(v){const p=new t1;return v.then(_=>{p.fire(_)},()=>{p.fire(void 0)}).finally(()=>{p.dispose()}),p.event}e.fromPromise=T;function x(v,p){return v(_=>p.fire(_))}e.forward=x;function F(v,p,_){return p(_),v(w=>p(w))}e.runAndSubscribe=F;class X{constructor(p,_){this._observable=p,this.f=0,this.g=!1;const w={onWillAddFirstListener:()=>{p.addObserver(this),this._observable.reportChanges()},onDidRemoveLastListener:()=>{p.removeObserver(this)}};_||t(w),this.emitter=new t1(w),_&&_.add(this.emitter)}beginUpdate(p){this.f++}handlePossibleChange(p){}handleChange(p,_){this.g=!0}endUpdate(p){this.f--,this.f===0&&(this._observable.reportChanges(),this.g&&(this.g=!1,this.emitter.fire(this._observable.get())))}}function R(v,p){return new X(v,p).emitter.event}e.fromObservable=R;function g1(v){return(p,_,w)=>{let E=0,L=!1;const I={beginUpdate(){E++},endUpdate(){E--,E===0&&(v.reportChanges(),L&&(L=!1,p.call(_)))},handlePossibleChange(){},handleChange(){L=!0}};v.addObserver(I),v.reportChanges();const W={dispose(){v.removeObserver(I)}};return w instanceof X1?w.add(W):Array.isArray(w)&&w.push(W),W}}e.fromObservableLight=g1}(T1||(T1={})),ur=class Xe{static{this.all=new Set}static{this.f=0}constructor(t){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${t}_${Xe.f++}`,Xe.all.add(this)}start(t){this.g=new lr,this.listenerCount=t}stop(){if(this.g){const t=this.g.elapsed();this.durations.push(t),this.elapsedOverall+=t,this.invocationCount+=1,this.g=void 0}}},De=-1,fr=class Tn{static{this.f=1}constructor(t,r,n=(Tn.f++).toString(16).padStart(3,"0")){this.j=t,this.threshold=r,this.name=n,this.h=0}dispose(){this.g?.clear()}check(t,r){const n=this.threshold;if(n<=0||r<n)return;this.g||(this.g=new Map);const s=this.g.get(t.value)||0;if(this.g.set(t.value,s+1),this.h-=1,this.h<=0){this.h=n*.5;const[i,o]=this.getMostFrequentStack(),a=`[${this.name}] potential listener LEAK detected, having ${r} listeners already. MOST frequent listener (${o}):`;console.warn(a),console.warn(i);const c=new hr(a,i);this.j(c)}return()=>{const i=this.g.get(t.value)||0;this.g.set(t.value,i-1)}}getMostFrequentStack(){if(!this.g)return;let t,r=0;for(const[n,s]of this.g)(!t||r<s)&&(t=[n,s],r=s);return t}},ee=class jn{static create(){const t=new Error;return new jn(t.stack??"")}constructor(t){this.value=t}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},hr=class extends Error{constructor(e,t){super(e),this.name="ListenerLeakError",this.stack=t}},dr=class extends Error{constructor(e,t){super(e),this.name="ListenerRefusalError",this.stack=t}},pr=0,j1=class{constructor(e){this.value=e,this.id=pr++}},gr=2,mr=(e,t)=>{if(e instanceof j1)t(e);else for(let r=0;r<e.length;r++){const n=e[r];n&&t(n)}},t1=class{constructor(e){this.z=0,this.f=e,this.g=De>0||this.f?.leakWarningThreshold?new fr(e?.onListenerError??Q1,this.f?.leakWarningThreshold??De):void 0,this.j=this.f?._profName?new ur(this.f._profName):void 0,this.w=this.f?.deliveryQueue}dispose(){if(!this.m){if(this.m=!0,this.w?.current===this&&this.w.reset(),this.u){if(Le){const e=this.u;queueMicrotask(()=>{mr(e,t=>t.stack?.print())})}this.u=void 0,this.z=0}this.f?.onDidRemoveLastListener?.(),this.g?.dispose()}}get event(){return this.q??=(e,t,r)=>{if(this.g&&this.z>this.g.threshold**2){const a=`[${this.g.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this.z} vs ${this.g.threshold})`;console.warn(a);const c=this.g.getMostFrequentStack()??["UNKNOWN stack",-1],l=new dr(`${a}. HINT: Stack shows most frequent listener (${c[1]}-times)`,c[0]);return(this.f?.onListenerError||Q1)(l),E1.None}if(this.m)return E1.None;t&&(e=e.bind(t));const n=new j1(e);let s,i;this.g&&this.z>=Math.ceil(this.g.threshold*.2)&&(n.stack=ee.create(),s=this.g.check(n.stack,this.z+1)),Le&&(n.stack=i??ee.create()),this.u?this.u instanceof j1?(this.w??=new vr,this.u=[this.u,n]):this.u.push(n):(this.f?.onWillAddFirstListener?.(this),this.u=n,this.f?.onDidAddFirstListener?.(this)),this.f?.onDidAddListener?.(this),this.z++;const o=ir(()=>{s?.(),this.A(n)});return r instanceof X1?r.add(o):Array.isArray(r)&&r.push(o),o},this.q}A(e){if(this.f?.onWillRemoveListener?.(this),!this.u)return;if(this.z===1){this.u=void 0,this.f?.onDidRemoveLastListener?.(this),this.z=0;return}const t=this.u,r=t.indexOf(e);if(r===-1)throw console.log("disposed?",this.m),console.log("size?",this.z),console.log("arr?",JSON.stringify(this.u)),new Error("Attempted to dispose unknown listener");this.z--,t[r]=void 0;const n=this.w.current===this;if(this.z*gr<=t.length){let s=0;for(let i=0;i<t.length;i++)t[i]?t[s++]=t[i]:n&&s<this.w.end&&(this.w.end--,s<this.w.i&&this.w.i--);t.length=s}}B(e,t){if(!e)return;const r=this.f?.onListenerError||Q1;if(!r){e.value(t);return}try{e.value(t)}catch(n){r(n)}}C(e){const t=e.current.u;for(;e.i<e.end;)this.B(t[e.i++],e.value);e.reset()}fire(e){if(this.w?.current&&(this.C(this.w),this.j?.stop()),this.j?.start(this.z),this.u)if(this.u instanceof j1)this.B(this.u,e);else{const t=this.w;t.enqueue(this,e,this.u.length),this.C(t)}this.j?.stop()}hasListeners(){return this.z>0}},vr=class{constructor(){this.i=-1,this.end=0}enqueue(e,t,r){this.i=0,this.end=r,this.current=e,this.value=t}reset(){this.i=this.end,this.current=void 0,this.value=void 0}}}}),ke,br,wr,_r=k({"out-build/vs/base/common/cancellation.js"(){"use strict";yr(),ke=Object.freeze(function(e,t){const r=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(r)}}}),function(e){function t(r){return r===e.None||r===e.Cancelled||r instanceof wr?!0:!r||typeof r!="object"?!1:typeof r.isCancellationRequested=="boolean"&&typeof r.onCancellationRequested=="function"}e.isCancellationToken=t,e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:T1.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:ke})}(br||(br={})),wr=class{constructor(){this.a=!1,this.b=null}cancel(){this.a||(this.a=!0,this.b&&(this.b.fire(void 0),this.dispose()))}get isCancellationRequested(){return this.a}get onCancellationRequested(){return this.a?ke:(this.b||(this.b=new t1),this.b.event)}dispose(){this.b&&(this.b.dispose(),this.b=null)}}}});function Ui(e){return e}var Cr,Vi=k({"out-build/vs/base/common/cache.js"(){"use strict";_r(),Cr=class{constructor(e,t){this.a=void 0,this.b=void 0,typeof e=="function"?(this.c=e,this.d=Ui):(this.c=t,this.d=e.getCacheKey)}get(e){const t=this.d(e);return this.b!==t&&(this.b=t,this.a=this.c(e)),this.a}}}}),Ne,Er=k({"out-build/vs/base/common/lazy.js"(){"use strict";Ne=class{constructor(e){this.d=e,this.a=!1}get hasValue(){return this.a}get value(){if(!this.a)try{this.b=this.d()}catch(e){this.c=e}finally{this.a=!0}if(this.c)throw this.c;return this.b}get rawValue(){return this.b}}}});function qi(e,t){return e<t?-1:e>t?1:0}function zi(e,t,r=0,n=e.length,s=0,i=t.length){for(;r<n&&s<i;r++,s++){const c=e.charCodeAt(r),l=t.charCodeAt(s);if(c<l)return-1;if(c>l)return 1}const o=n-r,a=i-s;return o<a?-1:o>a?1:0}function Ar(e,t,r=0,n=e.length,s=0,i=t.length){for(;r<n&&s<i;r++,s++){let c=e.charCodeAt(r),l=t.charCodeAt(s);if(c===l)continue;if(c>=128||l>=128)return zi(e.toLowerCase(),t.toLowerCase(),r,n,s,i);$r(c)&&(c-=32),$r(l)&&(l-=32);const f=c-l;if(f!==0)return f}const o=n-r,a=i-s;return o<a?-1:o>a?1:0}function $r(e){return e>=97&&e<=122}function Wi(e,t){return e.length===t.length&&Ar(e,t)===0}function Hi(e,t){const r=t.length;return t.length>e.length?!1:Ar(e,t,0,r)===0}function Ji(){return JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}var Or,Sr,xr,Ki,Gi,Pr,Bi,Lr,Zi,Qi,Te=k({"out-build/vs/base/common/strings.js"(){"use strict";Vi(),Er(),Or=/(?:\x1b\[|\x9b)[=?>!]?[\d;:]*["$#'* ]?[a-zA-Z@^`{}|~]/,Sr=/(?:\x1b\]|\x9d).*?(?:\x1b\\|\x07|\x9c)/,xr=/\x1b(?:[ #%\(\)\*\+\-\.\/]?[a-zA-Z0-9\|}~@])/,Ki=new RegExp("(?:"+[Or.source,Sr.source,xr.source].join("|")+")","g"),Gi="\uFEFF",function(e){e[e.Other=0]="Other",e[e.Prepend=1]="Prepend",e[e.CR=2]="CR",e[e.LF=3]="LF",e[e.Control=4]="Control",e[e.Extend=5]="Extend",e[e.Regional_Indicator=6]="Regional_Indicator",e[e.SpacingMark=7]="SpacingMark",e[e.L=8]="L",e[e.V=9]="V",e[e.T=10]="T",e[e.LV=11]="LV",e[e.LVT=12]="LVT",e[e.ZWJ=13]="ZWJ",e[e.Extended_Pictographic=14]="Extended_Pictographic"}(Pr||(Pr={})),Bi=class U1{static{this.c=null}static getInstance(){return U1.c||(U1.c=new U1),U1.c}constructor(){this.d=Ji()}getGraphemeBreakType(t){if(t<32)return t===10?3:t===13?2:4;if(t<127)return 0;const r=this.d,n=r.length/3;let s=1;for(;s<=n;)if(t<r[3*s])s=2*s;else if(t>r[3*s+1])s=2*s+1;else return r[3*s+2];return 0}},function(e){e[e.zwj=8205]="zwj",e[e.emojiVariantSelector=65039]="emojiVariantSelector",e[e.enclosingKeyCap=8419]="enclosingKeyCap",e[e.space=32]="space"}(Lr||(Lr={})),Zi=class V1{static{this.c=new Ne(()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,1523,96,8242,96,1370,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,118002,50,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,118003,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,118004,52,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,118005,53,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,118006,54,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,118007,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,118008,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,118009,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,117974,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,117975,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71913,67,71922,67,65315,67,8557,67,8450,67,8493,67,117976,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,117977,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,117978,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,117979,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,117980,71,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,117981,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,117983,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,117984,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,118001,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,117982,108,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,117985,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,117986,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,117987,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,118000,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,117988,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,117989,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,117990,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,117991,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,117992,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,117993,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,117994,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,117995,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71910,87,71919,87,117996,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,117997,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,117998,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,71909,90,66293,90,65338,90,8484,90,8488,90,117999,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65283,35,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"cs":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"es":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"fr":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"it":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ja":[8211,45,8218,44,65281,33,8216,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65292,44,65297,49,65307,59],"ko":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pt-BR":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ru":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"zh-hans":[160,32,65374,126,8218,44,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65297,49],"zh-hant":[8211,45,65374,126,8218,44,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89]}'))}static{this.d=new Cr({getCacheKey:JSON.stringify},t=>{function r(f){const u=new Map;for(let h=0;h<f.length;h+=2)u.set(f[h],f[h+1]);return u}function n(f,u){const h=new Map(f);for(const[g,m]of u)h.set(g,m);return h}function s(f,u){if(!f)return u;const h=new Map;for(const[g,m]of f)u.has(g)&&h.set(g,m);return h}const i=this.c.value;let o=t.filter(f=>!f.startsWith("_")&&f in i);o.length===0&&(o=["_default"]);let a;for(const f of o){const u=r(i[f]);a=s(a,u)}const c=r(i._common),l=n(c,a);return new V1(l)})}static getInstance(t){return V1.d.get(Array.from(t))}static{this.e=new Ne(()=>Object.keys(V1.c.value).filter(t=>!t.startsWith("_")))}static getLocales(){return V1.e.value}constructor(t){this.f=t}isAmbiguous(t){return this.f.has(t)}containsAmbiguousCharacter(t){for(let r=0;r<t.length;r++){const n=t.codePointAt(r);if(typeof n=="number"&&this.isAmbiguous(n))return!0}return!1}getPrimaryConfusable(t){return this.f.get(t)}getConfusableCodePoints(){return new Set(this.f.keys())}},Qi=class q1{static c(){return JSON.parse('{"_common":[11,12,13,127,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999],"cs":[173,8203,12288],"de":[173,8203,12288],"es":[8203,12288],"fr":[173,8203,12288],"it":[160,173,12288],"ja":[173],"ko":[173,12288],"pl":[173,8203,12288],"pt-BR":[173,8203,12288],"qps-ploc":[160,173,8203,12288],"ru":[173,12288],"tr":[160,173,8203,12288],"zh-hans":[160,173,8203,12288],"zh-hant":[173,12288]}')}static{this.d=void 0}static e(){return this.d||(this.d=new Set([...Object.values(q1.c())].flat())),this.d}static isInvisibleCharacter(t){return q1.e().has(t)}static containsInvisibleCharacter(t){for(let r=0;r<t.length;r++){const n=t.codePointAt(r);if(typeof n=="number"&&(q1.isInvisibleCharacter(n)||n===32))return!0}return!1}static get codePoints(){return q1.e()}}}});function p1(e){return e===47||e===92}function Dr(e){return e.replace(/[\\/]/g,j.sep)}function Yi(e){return e.indexOf("/")===-1&&(e=Dr(e)),/^[a-zA-Z]:(\/|$)/.test(e)&&(e="/"+e),e}function kr(e,t=j.sep){if(!e)return"";const r=e.length,n=e.charCodeAt(0);if(p1(n)){if(p1(e.charCodeAt(1))&&!p1(e.charCodeAt(2))){let i=3;const o=i;for(;i<r&&!p1(e.charCodeAt(i));i++);if(o!==i&&!p1(e.charCodeAt(i+1))){for(i+=1;i<r;i++)if(p1(e.charCodeAt(i)))return e.slice(0,i+1).replace(/[\\/]/g,t)}}return t}else if(Xi(n)&&e.charCodeAt(1)===58)return p1(e.charCodeAt(2))?e.slice(0,2)+t:e.slice(0,2);let s=e.indexOf("://");if(s!==-1){for(s+=3;s<r;s++)if(p1(e.charCodeAt(s)))return e.slice(0,s+1)}return""}function Nr(e,t,r,n=D1){if(e===t)return!0;if(!e||!t||t.length>e.length)return!1;if(r){if(!Hi(e,t))return!1;if(t.length===e.length)return!0;let i=t.length;return t.charAt(t.length-1)===n&&i--,e.charAt(i)===n}return t.charAt(t.length-1)!==n&&(t+=n),e.indexOf(t)===0}function Xi(e){return e>=65&&e<=90||e>=97&&e<=122}function Tr(e,t,r=8){let n="";for(let i=0;i<r;i++){let o;i===0&&s1&&!t&&(r===3||r===4)?o=Rr:o=jr,n+=o.charAt(Math.floor(Math.random()*o.length))}let s;return t?s=`${t}-${n}`:s=n,e?B1(e,s):s}var jr,Rr,je=k({"out-build/vs/base/common/extpath.js"(){"use strict";k1(),h1(),Te(),rr(),jr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",Rr="BDEFGHIJKMOQRSTUVWXYZbdefghijkmoqrstuvwxyz0123456789"}});import{writeFileSync as es}from"fs";import{tmpdir as ts}from"os";function rs(e){const t=Tr(ts());try{return es(t,""),e&&console.log(`Marker file for --wait created: ${t}`),t}catch(r){e&&console.error(`Failed to create marker file for --wait: ${r}`);return}}var ns=k({"out-build/vs/platform/environment/node/wait.js"(){"use strict";je()}});function is(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!Fr.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path){if(e.authority){if(!Ur.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(Vr.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}function ss(e,t){return!e&&!t?"file":e}function os(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==Q&&(t=Q+t):t=Q;break}return t}function Ir(e,t,r){let n,s=-1;for(let i=0;i<e.length;i++){const o=e.charCodeAt(i);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||o===45||o===46||o===95||o===126||t&&o===47||r&&o===91||r&&o===93||r&&o===58)s!==-1&&(n+=encodeURIComponent(e.substring(s,i)),s=-1),n!==void 0&&(n+=e.charAt(i));else{n===void 0&&(n=e.substr(0,i));const a=Me[o];a!==void 0?(s!==-1&&(n+=encodeURIComponent(e.substring(s,i)),s=-1),n+=a):s===-1&&(s=i)}}return s!==-1&&(n+=encodeURIComponent(e.substring(s))),n!==void 0?n:e}function as(e){let t;for(let r=0;r<e.length;r++){const n=e.charCodeAt(r);n===35||n===63?(t===void 0&&(t=e.substr(0,r)),t+=Me[n]):t!==void 0&&(t+=e[r])}return t!==void 0?t:e}function te(e,t){let r;return e.authority&&e.path.length>1&&e.scheme==="file"?r=`//${e.authority}${e.path}`:e.path.charCodeAt(0)===47&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&e.path.charCodeAt(2)===58?t?r=e.path.substr(1):r=e.path[1].toLowerCase()+e.path.substr(2):r=e.path,s1&&(r=r.replace(/\//g,"\\")),r}function Re(e,t){const r=t?as:Ir;let n="",{scheme:s,authority:i,path:o,query:a,fragment:c}=e;if(s&&(n+=s,n+=":"),(i||s==="file")&&(n+=Q,n+=Q),i){let l=i.indexOf("@");if(l!==-1){const f=i.substr(0,l);i=i.substr(l+1),l=f.lastIndexOf(":"),l===-1?n+=r(f,!1,!1):(n+=r(f.substr(0,l),!1,!1),n+=":",n+=r(f.substr(l+1),!1,!0)),n+="@"}i=i.toLowerCase(),l=i.lastIndexOf(":"),l===-1?n+=r(i,!1,!0):(n+=r(i.substr(0,l),!1,!0),n+=i.substr(l))}if(o){if(o.length>=3&&o.charCodeAt(0)===47&&o.charCodeAt(2)===58){const l=o.charCodeAt(1);l>=65&&l<=90&&(o=`/${String.fromCharCode(l+32)}:${o.substr(3)}`)}else if(o.length>=2&&o.charCodeAt(1)===58){const l=o.charCodeAt(0);l>=65&&l<=90&&(o=`${String.fromCharCode(l+32)}:${o.substr(2)}`)}n+=r(o,!0,!1)}return a&&(n+="?",n+=r(a,!1,!1)),c&&(n+="#",n+=t?c:Ir(c,!1,!1)),n}function Mr(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+Mr(e.substr(3)):e}}function re(e){return e.match(Fe)?e.replace(Fe,t=>Mr(t)):e}var Fr,Ur,Vr,M,Q,qr,Y,Ie,w1,Me,Fe,zr=k({"out-build/vs/base/common/uri.js"(){"use strict";k1(),h1(),Fr=/^\w[\w\d+.-]*$/,Ur=/^\//,Vr=/^\/\//,M="",Q="/",qr=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,Y=class ue{static isUri(t){return t instanceof ue?!0:t?typeof t.authority=="string"&&typeof t.fragment=="string"&&typeof t.path=="string"&&typeof t.query=="string"&&typeof t.scheme=="string"&&typeof t.fsPath=="string"&&typeof t.with=="function"&&typeof t.toString=="function":!1}constructor(t,r,n,s,i,o=!1){typeof t=="object"?(this.scheme=t.scheme||M,this.authority=t.authority||M,this.path=t.path||M,this.query=t.query||M,this.fragment=t.fragment||M):(this.scheme=ss(t,o),this.authority=r||M,this.path=os(this.scheme,n||M),this.query=s||M,this.fragment=i||M,is(this,o))}get fsPath(){return te(this,!1)}with(t){if(!t)return this;let{scheme:r,authority:n,path:s,query:i,fragment:o}=t;return r===void 0?r=this.scheme:r===null&&(r=M),n===void 0?n=this.authority:n===null&&(n=M),s===void 0?s=this.path:s===null&&(s=M),i===void 0?i=this.query:i===null&&(i=M),o===void 0?o=this.fragment:o===null&&(o=M),r===this.scheme&&n===this.authority&&s===this.path&&i===this.query&&o===this.fragment?this:new w1(r,n,s,i,o)}static parse(t,r=!1){const n=qr.exec(t);return n?new w1(n[2]||M,re(n[4]||M),re(n[5]||M),re(n[7]||M),re(n[9]||M),r):new w1(M,M,M,M,M)}static file(t){let r=M;if(s1&&(t=t.replace(/\\/g,Q)),t[0]===Q&&t[1]===Q){const n=t.indexOf(Q,2);n===-1?(r=t.substring(2),t=Q):(r=t.substring(2,n),t=t.substring(n)||Q)}return new w1("file",r,t,M,M)}static from(t,r){return new w1(t.scheme,t.authority,t.path,t.query,t.fragment,r)}static joinPath(t,...r){if(!t.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let n;return s1&&t.scheme==="file"?n=ue.file(q.join(te(t,!0),...r)).path:n=j.join(t.path,...r),t.with({path:n})}toString(t=!1){return Re(this,t)}toJSON(){return this}static revive(t){if(t){if(t instanceof ue)return t;{const r=new w1(t);return r._formatted=t.external??null,r._fsPath=t._sep===Ie?t.fsPath??null:null,r}}else return t}[Symbol.for("debug.description")](){return`URI(${this.toString()})`}},Ie=s1?1:void 0,w1=class extends Y{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=te(this,!1)),this._fsPath}toString(e=!1){return e?Re(this,!0):(this._formatted||(this._formatted=Re(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=Ie),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}},Me={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"},Fe=/(%[0-9A-Za-z][0-9A-Za-z])+/g}});function ls(e){return`${e.quality??"oss"}-${e.commit??"dev"}`}var H,Wr,Hr,Jr,Kr,Gr,Ue,cs,us,Br,Zr=k({"out-build/vs/base/common/network.js"(){"use strict";N1(),h1(),Te(),zr(),k1(),function(e){e.inMemory="inmemory",e.vscode="vscode",e.internal="private",e.walkThrough="walkThrough",e.walkThroughSnippet="walkThroughSnippet",e.http="http",e.https="https",e.file="file",e.mailto="mailto",e.untitled="untitled",e.data="data",e.command="command",e.vscodeRemote="vscode-remote",e.vscodeRemoteResource="vscode-remote-resource",e.vscodeManagedRemoteResource="vscode-managed-remote-resource",e.vscodeUserData="vscode-userdata",e.vscodeCustomEditor="vscode-custom-editor",e.vscodeNotebookCell="vscode-notebook-cell",e.vscodeNotebookCellMetadata="vscode-notebook-cell-metadata",e.vscodeNotebookCellMetadataDiff="vscode-notebook-cell-metadata-diff",e.vscodeNotebookCellOutput="vscode-notebook-cell-output",e.vscodeNotebookCellOutputDiff="vscode-notebook-cell-output-diff",e.vscodeNotebookMetadata="vscode-notebook-metadata",e.vscodeInteractiveInput="vscode-interactive-input",e.vscodeSettings="vscode-settings",e.vscodeWorkspaceTrust="vscode-workspace-trust",e.vscodeTerminal="vscode-terminal",e.vscodeChatCodeBlock="vscode-chat-code-block",e.vscodeChatCodeCompareBlock="vscode-chat-code-compare-block",e.vscodeChatSesssion="vscode-chat-editor",e.webviewPanel="webview-panel",e.vscodeWebview="vscode-webview",e.extension="extension",e.vscodeFileResource="vscode-file",e.tmp="tmp",e.vsls="vsls",e.vscodeSourceControl="vscode-scm",e.commentsInput="comment",e.codeSetting="code-setting",e.outputChannel="output",e.accessibleView="accessible-view"}(H||(H={})),Wr="tkn",Hr=class{constructor(){this.a=Object.create(null),this.b=Object.create(null),this.c=Object.create(null),this.d="http",this.e=null,this.f="/"}setPreferredWebSchema(e){this.d=e}setDelegate(e){this.e=e}setServerRootPath(e,t){this.f=j.join(t??"/",ls(e))}getServerRootPath(){return this.f}get g(){return j.join(this.f,H.vscodeRemoteResource)}set(e,t,r){this.a[e]=t,this.b[e]=r}setConnectionToken(e,t){this.c[e]=t}getPreferredWebSchema(){return this.d}rewrite(e){if(this.e)try{return this.e(e)}catch(o){return Q1(o),e}const t=e.authority;let r=this.a[t];r&&r.indexOf(":")!==-1&&r.indexOf("[")===-1&&(r=`[${r}]`);const n=this.b[t],s=this.c[t];let i=`path=${encodeURIComponent(e.path)}`;return typeof s=="string"&&(i+=`&${Wr}=${encodeURIComponent(s)}`),Y.from({scheme:vt?this.d:H.vscodeRemoteResource,authority:`${r}:${n}`,path:this.g,query:i})}},Jr=new Hr,Kr="vscode-app",Gr=class fe{static{this.a=Kr}asBrowserUri(t){const r=this.b(t);return this.uriToBrowserUri(r)}uriToBrowserUri(t){return t.scheme===H.vscodeRemote?Jr.rewrite(t):t.scheme===H.file&&(mt||bt===`${H.vscodeFileResource}://${fe.a}`)?t.with({scheme:H.vscodeFileResource,authority:t.authority||fe.a,query:null,fragment:null}):t}asFileUri(t){const r=this.b(t);return this.uriToFileUri(r)}uriToFileUri(t){return t.scheme===H.vscodeFileResource?t.with({scheme:H.file,authority:t.authority!==fe.a?t.authority:null,query:null,fragment:null}):t}b(t){if(Y.isUri(t))return t;if(globalThis._VSCODE_FILE_ROOT){const r=globalThis._VSCODE_FILE_ROOT;if(/^\w[\w\d+.-]*:\/\//.test(r))return Y.joinPath(Y.parse(r,!0),t);const n=B1(r,t);return Y.file(n)}throw new Error("Cannot determine URI for module id!")}},Ue=new Gr,cs=Object.freeze({"Cache-Control":"no-cache, no-store"}),us=Object.freeze({"Document-Policy":"include-js-call-stacks-in-crash-reports"}),function(e){const t=new Map([["1",{"Cross-Origin-Opener-Policy":"same-origin"}],["2",{"Cross-Origin-Embedder-Policy":"require-corp"}],["3",{"Cross-Origin-Opener-Policy":"same-origin","Cross-Origin-Embedder-Policy":"require-corp"}]]);e.CoopAndCoep=Object.freeze(t.get("3"));const r="vscode-coi";function n(i){let o;typeof i=="string"?o=new URL(i).searchParams:i instanceof URL?o=i.searchParams:Y.isUri(i)&&(o=new URL(i.toString(!0)).searchParams);const a=o?.get(r);if(a)return t.get(a)}e.getHeadersFromQuery=n;function s(i,o,a){if(!globalThis.crossOriginIsolated)return;const c=o&&a?"3":a?"2":"1";i instanceof URLSearchParams?i.set(r,c):i[r]=c}e.addSearchParam=s}(Br||(Br={}))}});function f1(e){return te(e,!0)}var ne,N,fs,hs,ds,ps,gs,ms,Qr,vs,ys,bs,ws,_s,Cs,Es,Ve,qe,As,$s,Yr,Os=k({"out-build/vs/base/common/resources.js"(){"use strict";je(),Zr(),k1(),h1(),Te(),zr(),ne=class{constructor(e){this.a=e}compare(e,t,r=!1){return e===t?0:qi(this.getComparisonKey(e,r),this.getComparisonKey(t,r))}isEqual(e,t,r=!1){return e===t?!0:!e||!t?!1:this.getComparisonKey(e,r)===this.getComparisonKey(t,r)}getComparisonKey(e,t=!1){return e.with({path:this.a(e)?e.path.toLowerCase():void 0,fragment:t?null:void 0}).toString()}ignorePathCasing(e){return this.a(e)}isEqualOrParent(e,t,r=!1){if(e.scheme===t.scheme){if(e.scheme===H.file)return Nr(f1(e),f1(t),this.a(e))&&e.query===t.query&&(r||e.fragment===t.fragment);if(Ve(e.authority,t.authority))return Nr(e.path,t.path,this.a(e),"/")&&e.query===t.query&&(r||e.fragment===t.fragment)}return!1}joinPath(e,...t){return Y.joinPath(e,...t)}basenameOrAuthority(e){return Qr(e)||e.authority}basename(e){return j.basename(e.path)}extname(e){return j.extname(e.path)}dirname(e){if(e.path.length===0)return e;let t;return e.scheme===H.file?t=Y.file(Z1(f1(e))).path:(t=j.dirname(e.path),e.authority&&t.length&&t.charCodeAt(0)!==47&&(console.error(`dirname("${e.toString})) resulted in a relative path`),t="/")),e.with({path:t})}normalizePath(e){if(!e.path.length)return e;let t;return e.scheme===H.file?t=Y.file(jt(f1(e))).path:t=j.normalize(e.path),e.with({path:t})}relativePath(e,t){if(e.scheme!==t.scheme||!Ve(e.authority,t.authority))return;if(e.scheme===H.file){const s=Rt(f1(e),f1(t));return s1?Dr(s):s}let r=e.path||"/";const n=t.path||"/";if(this.a(e)){let s=0;for(const i=Math.min(r.length,n.length);s<i&&!(r.charCodeAt(s)!==n.charCodeAt(s)&&r.charAt(s).toLowerCase()!==n.charAt(s).toLowerCase());s++);r=n.substr(0,s)+r.substr(s)}return j.relative(r,n)}resolvePath(e,t){if(e.scheme===H.file){const r=Y.file(we(f1(e),t));return e.with({authority:r.authority,path:r.path})}return t=Yi(t),e.with({path:j.resolve(e.path,t)})}isAbsolutePath(e){return!!e.path&&e.path[0]==="/"}isEqualAuthority(e,t){return e===t||e!==void 0&&t!==void 0&&Wi(e,t)}hasTrailingPathSeparator(e,t=D1){if(e.scheme===H.file){const r=f1(e);return r.length>kr(r).length&&r[r.length-1]===t}else{const r=e.path;return r.length>1&&r.charCodeAt(r.length-1)===47&&!/^[a-zA-Z]:(\/$|\\$)/.test(e.fsPath)}}removeTrailingPathSeparator(e,t=D1){return qe(e,t)?e.with({path:e.path.substr(0,e.path.length-1)}):e}addTrailingPathSeparator(e,t=D1){let r=!1;if(e.scheme===H.file){const n=f1(e);r=n!==void 0&&n.length===kr(n).length&&n[n.length-1]===t}else{t="/";const n=e.path;r=n.length===1&&n.charCodeAt(n.length-1)===47}return!r&&!qe(e,t)?e.with({path:e.path+"/"}):e}},N=new ne(()=>!1),fs=new ne(e=>e.scheme===H.file?!gt:!0),hs=new ne(e=>!0),ds=N.isEqual.bind(N),ps=N.isEqualOrParent.bind(N),gs=N.getComparisonKey.bind(N),ms=N.basenameOrAuthority.bind(N),Qr=N.basename.bind(N),vs=N.extname.bind(N),ys=N.dirname.bind(N),bs=N.joinPath.bind(N),ws=N.normalizePath.bind(N),_s=N.relativePath.bind(N),Cs=N.resolvePath.bind(N),Es=N.isAbsolutePath.bind(N),Ve=N.isEqualAuthority.bind(N),qe=N.hasTrailingPathSeparator.bind(N),As=N.removeTrailingPathSeparator.bind(N),$s=N.addTrailingPathSeparator.bind(N),function(e){e.META_DATA_LABEL="label",e.META_DATA_DESCRIPTION="description",e.META_DATA_SIZE="size",e.META_DATA_MIME="mime";function t(r){const n=new Map;r.path.substring(r.path.indexOf(";")+1,r.path.lastIndexOf(";")).split(";").forEach(o=>{const[a,c]=o.split(":");a&&c&&n.set(a,c)});const i=r.path.substring(0,r.path.indexOf(";"));return i&&n.set(e.META_DATA_MIME,i),n}e.parseMetaData=t}(Yr||(Yr={}))}}),Ss,xs=k({"out-build/vs/base/common/symbols.js"(){"use strict";Ss=Symbol("MicrotaskDelay")}}),Xr,en,Ps,ze,tn,rn,nn,sn,Ls,on=k({"out-build/vs/base/common/async.js"(){"use strict";_r(),N1(),yr(),or(),Os(),h1(),xs(),Er(),Xr=class{constructor(e){this.a=0,this.b=!1,this.f=e,this.g=[],this.d=0,this.h=new t1}whenIdle(){return this.size>0?T1.toPromise(this.onDrained):Promise.resolve()}get onDrained(){return this.h.event}get size(){return this.a}queue(e){if(this.b)throw new Error("Object has been disposed");return this.a++,new Promise((t,r)=>{this.g.push({factory:e,c:t,e:r}),this.j()})}j(){for(;this.g.length&&this.d<this.f;){const e=this.g.shift();this.d++;const t=e.factory();t.then(e.c,e.e),t.then(()=>this.k(),()=>this.k())}}k(){this.b||(this.d--,--this.a===0&&this.h.fire(),this.g.length>0&&this.j())}clear(){if(this.b)throw new Error("Object has been disposed");this.g.length=0,this.a=this.d}dispose(){this.b=!0,this.g.length=0,this.a=0,this.h.dispose()}},en=class extends Xr{constructor(){super(1)}},function(){typeof globalThis.requestIdleCallback!="function"||typeof globalThis.cancelIdleCallback!="function"?ze=(e,t,r)=>{Ct(()=>{if(n)return;const s=Date.now()+15;t(Object.freeze({didTimeout:!0,timeRemaining(){return Math.max(0,s-Date.now())}}))});let n=!1;return{dispose(){n||(n=!0)}}}:ze=(e,t,r)=>{const n=e.requestIdleCallback(t,typeof r=="number"?{timeout:r}:void 0);let s=!1;return{dispose(){s||(s=!0,e.cancelIdleCallback(n))}}},Ps=(e,t)=>ze(globalThis,e,t)}(),function(e){e[e.Resolved=0]="Resolved",e[e.Rejected=1]="Rejected"}(tn||(tn={})),rn=class{get isRejected(){return this.d?.outcome===1}get isResolved(){return this.d?.outcome===0}get isSettled(){return!!this.d}get value(){return this.d?.outcome===0?this.d?.value:void 0}constructor(){this.p=new Promise((e,t)=>{this.a=e,this.b=t})}complete(e){return new Promise(t=>{this.a(e),this.d={outcome:0,value:e},t()})}error(e){return new Promise(t=>{this.b(e),this.d={outcome:1,value:e},t()})}cancel(){return this.error(new Ce)}},function(e){async function t(n){let s;const i=await Promise.all(n.map(o=>o.then(a=>a,a=>{s||(s=a)})));if(typeof s<"u")throw s;return i}e.settled=t;function r(n){return new Promise(async(s,i)=>{try{await n(s,i)}catch(o){i(o)}})}e.withAsyncBody=r}(nn||(nn={})),function(e){e[e.Initial=0]="Initial",e[e.DoneOK=1]="DoneOK",e[e.DoneError=2]="DoneError"}(sn||(sn={})),Ls=class Z{static fromArray(t){return new Z(r=>{r.emitMany(t)})}static fromPromise(t){return new Z(async r=>{r.emitMany(await t)})}static fromPromisesResolveOrder(t){return new Z(async r=>{await Promise.all(t.map(async n=>r.emitOne(await n)))})}static merge(t){return new Z(async r=>{await Promise.all(t.map(async n=>{for await(const s of n)r.emitOne(s)}))})}static{this.EMPTY=Z.fromArray([])}constructor(t,r){this.a=0,this.b=[],this.d=null,this.f=r,this.g=new t1,queueMicrotask(async()=>{const n={emitOne:s=>this.h(s),emitMany:s=>this.j(s),reject:s=>this.l(s)};try{await Promise.resolve(t(n)),this.k()}catch(s){this.l(s)}finally{n.emitOne=void 0,n.emitMany=void 0,n.reject=void 0}})}[Symbol.asyncIterator](){let t=0;return{next:async()=>{do{if(this.a===2)throw this.d;if(t<this.b.length)return{done:!1,value:this.b[t++]};if(this.a===1)return{done:!0,value:void 0};await T1.toPromise(this.g.event)}while(!0)},return:async()=>(this.f?.(),{done:!0,value:void 0})}}static map(t,r){return new Z(async n=>{for await(const s of t)n.emitOne(r(s))})}map(t){return Z.map(this,t)}static filter(t,r){return new Z(async n=>{for await(const s of t)r(s)&&n.emitOne(s)})}filter(t){return Z.filter(this,t)}static coalesce(t){return Z.filter(t,r=>!!r)}coalesce(){return Z.coalesce(this)}static async toPromise(t){const r=[];for await(const n of t)r.push(n);return r}toPromise(){return Z.toPromise(this)}h(t){this.a===0&&(this.b.push(t),this.g.fire())}j(t){this.a===0&&(this.b=this.b.concat(t),this.g.fire())}k(){this.a===0&&(this.a=1,this.g.fire())}l(t){this.a===0&&(this.a=2,this.d=t,this.g.fire())}}}});import{exec as an}from"child_process";function Ds(e){const t=e.replace(/[^a-zA-Z0-9]/g,"").toLowerCase();return ln[t]||t}async function ks(e){let t;const r=process.env.VSCODE_CLI_ENCODING;r?(e&&console.log(`Found VSCODE_CLI_ENCODING variable: ${r}`),t=Promise.resolve(r)):s1?t=new Promise(s=>{e&&console.log('Running "chcp" to detect terminal encoding...'),an("chcp",(i,o,a)=>{if(o){e&&console.log(`Output from "chcp" command is: ${o}`);const c=Object.keys(We);for(const l of c)if(o.indexOf(l)>=0)return s(We[l])}return s(void 0)})}):t=new Promise(s=>{e&&console.log('Running "locale charmap" to detect terminal encoding...'),an("locale charmap",(i,o,a)=>s(o))});const n=await t;return e&&console.log(`Detected raw terminal encoding: ${n}`),!n||n.toLowerCase()==="utf-8"||n.toLowerCase()===He?He:Ds(n)}var We,ln,He,Ns=k({"out-build/vs/base/node/terminalEncoding.js"(){"use strict";h1(),We={437:"cp437",850:"cp850",852:"cp852",855:"cp855",857:"cp857",860:"cp860",861:"cp861",863:"cp863",865:"cp865",866:"cp866",869:"cp869",936:"cp936",1252:"cp1252"},ln={ibm866:"cp866",big5:"cp950"},He="utf8"}});import*as ie from"fs";import{tmpdir as Ts}from"os";function js(){try{return!process.stdin.isTTY}catch{}return!1}function Rs(){return Tr(Ts(),"code-stdin",3)}async function Is(e){await ie.promises.appendFile(e,""),await ie.promises.chmod(e,384)}async function Ms(e,t,r){let[n,s]=await Promise.all([ks(t),import("@vscode/iconv-lite-umd"),Is(e)]);s.default.encodingExists(n)||(console.log(`Unsupported terminal encoding: ${n}, falling back to UTF-8.`),n="utf8");const i=new en,o=s.default.getDecoder(n);process.stdin.on("data",a=>{const c=o.write(a);i.queue(()=>ie.promises.appendFile(e,c))}),process.stdin.on("end",()=>{const a=o.end();i.queue(async()=>{try{typeof a=="string"&&await ie.promises.appendFile(e,a)}finally{r?.()}})})}var Fs=k({"out-build/vs/platform/environment/node/stdin.js"(){"use strict";on(),je(),Ns()}}),cn={};Hn(cn,{$t3:()=>fn});import*as se from"fs";import*as un from"url";import*as R1 from"child_process";import*as Us from"http";async function fn(e,t){if(!A1&&!r1){console.log("Command is only available in WSL or inside a Visual Studio Code terminal.");return}const r={..._e,gitCredential:{type:"string"},openExternal:{type:"boolean"}},n=r1?gn:mn;for(const d in _e){const y=d;n(y)||delete r[y]}A1&&(r.openExternal={type:"boolean"});const i=Mt(t,r,{onMultipleValues:(d,y)=>{console.error(`Option '${d}' can only be defined once. Using value ${y}.`)},onEmptyValue:d=>{console.error(`Ignoring option '${d}': Value must not be empty.`)},onUnknownOption:d=>{console.error(`Ignoring option '${d}': not supported for ${e.executableName}.`)},onDeprecatedOption:(d,y)=>{console.warn(`Option '${d}' is deprecated: ${y}`)}}),o=ae?Ws:d=>d,a=!!i.verbose;if(i.help){console.log(di(e.productName,e.executableName,e.version,r));return}if(i.version){console.log(pi(e.version,e.commit));return}if(i["locate-shell-integration-path"]){let d;switch(i["locate-shell-integration-path"]){case"bash":d="shellIntegration-bash.sh";break;case"pwsh":d="shellIntegration.ps1";break;case"zsh":d="shellIntegration-rc.zsh";break;case"fish":d="shellIntegration.fish";break;default:throw new Error("Error using --locate-shell-integration-path: Invalid shell type")}console.log(B1(Hs(),"out","vs","workbench","contrib","terminal","common","scripts",d));return}if(A1&&i.openExternal){await zs(i._,a);return}let c=i.remote;(c==="local"||c==="false"||c==="")&&(c=null);const l=(i["folder-uri"]||[]).map(o);i["folder-uri"]=l;const f=(i["file-uri"]||[]).map(o);i["file-uri"]=f;const u=i._;let h=!1;for(const d of u)d==="-"?h=!0:pn(d,o,l,f);i._=[];let g,m;if(h&&js())try{if(m=yn,!m){m=Rs();const d=new rn;await Ms(m,a,()=>d.complete()),i.wait||(g=d.p)}pn(m,o,l,f),i["skip-add-to-recently-opened"]=!0,console.log(`Reading from stdin via: ${m}`)}catch(d){console.log(`Failed to create file to read via stdin: ${d.toString()}`)}i.extensionDevelopmentPath&&(i.extensionDevelopmentPath=i.extensionDevelopmentPath.map(d=>o(I1(d).href))),i.extensionTestsPath&&(i.extensionTestsPath=o(I1(i.extensionTestsPath).href));const O=i["crash-reporter-directory"];if(O!==void 0&&!O.match(/^([a-zA-Z]:[\\\/])/)){console.log(`The crash reporter directory '${O}' must be an absolute Windows path (e.g. c:/crashes)`);return}if(r1){if(i["install-extension"]!==void 0||i["uninstall-extension"]!==void 0||i["list-extensions"]||i["update-extensions"]){const A=[];i["install-extension"]?.forEach(U=>A.push("--install-extension",U)),i["uninstall-extension"]?.forEach(U=>A.push("--uninstall-extension",U)),["list-extensions","force","show-versions","category"].forEach(U=>{const b=i[U];b!==void 0&&A.push(`--${U}=${b}`)}),i["update-extensions"]&&A.push("--update-extensions"),R1.fork(Ue.asFileUri("server-main").fsPath,A,{stdio:"inherit"}).on("error",U=>console.log(U));return}const d=[];for(const A in i){const S=i[A];if(typeof S=="boolean")S&&d.push("--"+A);else if(Array.isArray(S))for(const U of S)d.push(`--${A}=${U.toString()}`);else S&&d.push(`--${A}=${S.toString()}`)}c!==null&&d.push(`--remote=${c||ae}`);const y=It(r1);if(y===".bat"||y===".cmd"){const A=vn||y1();a&&console.log(`Invoking: cmd.exe /C ${r1} ${d.join(" ")} in ${A}`),R1.spawn("cmd.exe",["/C",r1,...d],{stdio:"inherit",cwd:A})}else{const A=Z1(r1),S={...process.env,ELECTRON_RUN_AS_NODE:"1"};if(d.unshift("resources/app/out/cli.js"),a&&console.log(`Invoking: cd "${A}" && ELECTRON_RUN_AS_NODE=1 "${r1}" "${d.join('" "')}"`),Vs()){a&&console.log("Using pipes for output.");const U=R1.spawn(r1,d,{cwd:A,env:S,stdio:["inherit","pipe","pipe"]});U.stdout.on("data",b=>process.stdout.write(b)),U.stderr.on("data",b=>process.stderr.write(b))}else R1.spawn(r1,d,{cwd:A,env:S,stdio:"inherit"})}}else{if(i.status){await oe({type:"status"},a).then(y=>{console.log(y)}).catch(y=>{console.error("Error when requesting status:",y)});return}if(i["install-extension"]!==void 0||i["uninstall-extension"]!==void 0||i["list-extensions"]||i["update-extensions"]){await oe({type:"extensionManagement",list:i["list-extensions"]?{showVersions:i["show-versions"],category:i.category}:void 0,install:hn(i["install-extension"]),uninstall:hn(i["uninstall-extension"]),force:i.force},a).then(y=>{console.log(y)}).catch(y=>{console.error("Error when invoking the extension management command:",y)});return}let d;if(i.wait){if(!f.length){console.log("At least one file must be provided to wait for.");return}d=rs(a)}if(await oe({type:"open",fileURIs:f,folderURIs:l,diffMode:i.diff,mergeMode:i.merge,addMode:i.add,removeMode:i.remove,gotoLineMode:i.goto,forceReuseWindow:i["reuse-window"],forceNewWindow:i["new-window"],waitMarkerFilePath:d,remoteAuthority:c},a).catch(y=>{console.error("Error when invoking the open command:",y)}),d&&await qs(d),g&&await g,d&&m)try{se.unlinkSync(m)}catch{}}}function Vs(){if(process.env.WSL_DISTRO_NAME)try{return R1.execSync("uname -r",{encoding:"utf8"}).includes("-microsoft-")}catch{}return!1}async function qs(e){for(;se.existsSync(e);)await new Promise(t=>setTimeout(t,1e3))}async function zs(e,t){const r=[];for(const n of e)try{/^[a-z-]+:\/\/.+/.test(n)?r.push(un.parse(n).href):r.push(I1(n).href)}catch{console.log(`Invalid url: ${n}`)}r.length&&await oe({type:"openExternal",uris:r},t).catch(n=>{console.error("Error when invoking the open external command:",n)})}function oe(e,t){return t&&console.log(JSON.stringify(e,null,"  ")),new Promise((r,n)=>{const s=JSON.stringify(e);if(!A1){console.log("Message "+s),r("");return}const i={socketPath:A1,path:"/",method:"POST",headers:{"content-type":"application/json",accept:"application/json"}},o=Us.request(i,a=>{if(a.headers["content-type"]!=="application/json"){n("Error in response: Invalid content type: Expected 'application/json', is: "+a.headers["content-type"]);return}const c=[];a.setEncoding("utf8"),a.on("data",l=>{c.push(l)}),a.on("error",l=>dn("Error in response.",l)),a.on("end",()=>{const l=c.join("");try{const f=JSON.parse(l);a.statusCode===200?r(f):n(f)}catch{n("Error in response: Unable to parse response as JSON: "+l)}})});o.on("error",a=>dn("Error in request.",a)),o.write(s),o.end()})}function hn(e){return e?.map(t=>/\.vsix$/i.test(t)?I1(t).href:t)}function dn(e,t){console.error("Unable to connect to VS Code server: "+e),console.error(t),process.exit(1)}function I1(e){return e=e.trim(),e=we(bn,e),un.pathToFileURL(e)}function pn(e,t,r,n){const s=I1(e),i=t(s.href);try{const o=se.lstatSync(se.realpathSync(e));o.isFile()?n.push(i):o.isDirectory()?r.push(i):e==="/dev/null"&&n.push(i)}catch(o){o.code==="ENOENT"?n.push(i):console.log(`Problem accessing file ${e}. Ignoring file`,o)}}function Ws(e){return e.replace(/^file:\/\//,"vscode-remote://"+ae)}function Hs(){return Z1(Ue.asFileUri("").fsPath)}var gn,mn,A1,r1,vn,ae,yn,bn,wn,_n,Cn,En,An,Js=k({"out-build/vs/server/node/server.cli.js"(){"use strict";St(),k1(),gi(),ns(),Fs(),on(),Zr(),gn=e=>{switch(e){case"user-data-dir":case"extensions-dir":case"export-default-configuration":case"install-source":case"enable-smoke-test-driver":case"extensions-download-dir":case"builtin-extensions-dir":case"telemetry":return!1;default:return!0}},mn=e=>{switch(e){case"version":case"help":case"folder-uri":case"file-uri":case"add":case"diff":case"merge":case"wait":case"goto":case"reuse-window":case"new-window":case"status":case"install-extension":case"uninstall-extension":case"update-extensions":case"list-extensions":case"force":case"do-not-include-pack-dependencies":case"show-versions":case"category":case"verbose":case"remote":case"locate-shell-integration-path":return!0;default:return!1}},A1=process.env.VSCODE_IPC_HOOK_CLI,r1=process.env.VSCODE_CLIENT_COMMAND,vn=process.env.VSCODE_CLIENT_COMMAND_CWD,ae=process.env.VSCODE_CLI_AUTHORITY,yn=process.env.VSCODE_STDIN_FILE_PATH,bn=process.env.PWD||y1(),[,,wn,_n,Cn,En,...An]=process.argv,fn({productName:wn,version:_n,commit:Cn,executableName:En},An).then(null,e=>{console.error(e.message||e.stack||e)})}});delete process.env.ELECTRON_RUN_AS_NODE;import{dirname as Ks,join as Gs}from"path";import{fileURLToPath as Bs}from"url";import*as $n from"path";import"fs";import{fileURLToPath as Zs}from"url";import{createRequire as Qs}from"node:module";var Ys=Qs(import.meta.url),P2=$n.dirname(Zs(import.meta.url)),L2=process.platform==="win32";if(Error.stackTraceLimit=100,!process.env.VSCODE_HANDLES_SIGPIPE){let e=!1;process.on("SIGPIPE",()=>{e||(e=!0,console.error(new Error("Unexpected SIGPIPE")))})}function Xs(){try{typeof process.env.VSCODE_CWD!="string"&&(process.env.VSCODE_CWD=process.cwd()),process.platform==="win32"&&process.chdir($n.dirname(process.execPath))}catch(e){console.error(e)}}Xs();function e2(e){if(!process.env.VSCODE_DEV)return;if(!e)throw new Error("Missing injectPath");Ys("node:module").register("./bootstrap-import.js",{parentURL:import.meta.url,data:e})}import*as t2 from"path";import*as Je from"fs";import{fileURLToPath as r2}from"url";import{createRequire as n2,register as i2}from"node:module";import{createRequire as s2}from"node:module";var On=s2(import.meta.url),Ke={BUILD_INSERT_PRODUCT_CONFIGURATION:"BUILD_INSERT_PRODUCT_CONFIGURATION"};Ke.BUILD_INSERT_PRODUCT_CONFIGURATION&&(Ke=On("../product.json"));var Ge={"name":"Code","version":"1.100.3","private":true,"overrides":{"node-gyp-build":"4.8.1","kerberos@2.1.1":{"node-addon-api":"7.1.0"}},"type":"module"};Ge.BUILD_INSERT_PACKAGE_CONFIGURATION&&(Ge=On("../package.json"));var Sn=Ke,o2=Ge;function Be(e){const t=[];typeof e=="number"&&t.push("code/timeOrigin",e);function r(s,i){t.push(s,i?.startTime??Date.now())}function n(){const s=[];for(let i=0;i<t.length;i+=2)s.push({name:t[i],startTime:t[i+1]});return s}return{mark:r,getMarks:n}}function a2(){if(typeof performance=="object"&&typeof performance.mark=="function"&&!performance.nodeTiming)return typeof performance.timeOrigin!="number"&&!performance.timing?Be():{mark(e,t){performance.mark(e,t)},getMarks(){let e=performance.timeOrigin;typeof e!="number"&&(e=performance.timing.navigationStart||performance.timing.redirectStart||performance.timing.fetchStart);const t=[{name:"code/timeOrigin",startTime:Math.round(e)}];for(const r of performance.getEntriesByType("mark"))t.push({name:r.name,startTime:Math.round(e+r.startTime)});return t}};if(typeof process=="object"){const e=performance?.timeOrigin;return Be(e)}else return console.trace("perf-util loaded in UNKNOWN environment"),Be()}function l2(e){return e.MonacoPerformanceMarks||(e.MonacoPerformanceMarks=a2()),e.MonacoPerformanceMarks}var xn=l2(globalThis),$1=xn.mark,T2=xn.getMarks,c2=n2(import.meta.url),u2=t2.dirname(r2(import.meta.url));if((process.env.ELECTRON_RUN_AS_NODE||process.versions.electron)&&i2(`data:text/javascript;base64,${Buffer.from(`
	export async function resolve(specifier, context, nextResolve) {
		if (specifier === 'fs') {
			return {
				format: 'builtin',
				shortCircuit: true,
				url: 'node:original-fs'
			};
		}

		// Defer to the next hook in the chain, which would be the
		// Node.js default resolve if this is the last user-specified loader.
		return nextResolve(specifier, context);
	}`).toString("base64")}`,import.meta.url),globalThis._VSCODE_PRODUCT_JSON={...Sn},process.env.VSCODE_DEV)try{const e=c2("../product.overrides.json");globalThis._VSCODE_PRODUCT_JSON=Object.assign(globalThis._VSCODE_PRODUCT_JSON,e)}catch{}globalThis._VSCODE_PACKAGE_JSON={...o2},globalThis._VSCODE_FILE_ROOT=u2;var Ze=void 0;function f2(){return Ze||(Ze=h2()),Ze}async function h2(){$1("code/willLoadNls");let e,t;if(process.env.VSCODE_NLS_CONFIG)try{e=JSON.parse(process.env.VSCODE_NLS_CONFIG),e?.languagePack?.messagesFile?t=e.languagePack.messagesFile:e?.defaultMessagesFile&&(t=e.defaultMessagesFile),globalThis._VSCODE_NLS_LANGUAGE=e?.resolvedLanguage}catch(r){console.error(`Error reading VSCODE_NLS_CONFIG from environment: ${r}`)}if(!(process.env.VSCODE_DEV||!t)){try{globalThis._VSCODE_NLS_MESSAGES=JSON.parse((await Je.promises.readFile(t)).toString())}catch(r){if(console.error(`Error reading NLS messages file ${t}: ${r}`),e?.languagePack?.corruptMarkerFile)try{await Je.promises.writeFile(e.languagePack.corruptMarkerFile,"corrupted")}catch(n){console.error(`Error writing corrupted NLS marker file: ${n}`)}if(e?.defaultMessagesFile&&e.defaultMessagesFile!==t)try{globalThis._VSCODE_NLS_MESSAGES=JSON.parse((await Je.promises.readFile(e.defaultMessagesFile)).toString())}catch(n){console.error(`Error reading default NLS messages file ${e.defaultMessagesFile}: ${n}`)}}return $1("code/didLoadNls"),e}}async function d2(){await f2()}import*as a1 from"path";import*as l1 from"fs";async function p2({userLocale:e,osLocale:t,userDataPath:r,commit:n,nlsMetadataPath:s}){if($1("code/willGenerateNls"),process.env.VSCODE_DEV||e==="pseudo"||e.startsWith("en")||!n||!r)return M1(e,t,s);try{const i=await g2(r);if(!i)return M1(e,t,s);const o=m2(i,e);if(!o)return M1(e,t,s);const a=i[o],c=a?.translations?.vscode;if(!a||typeof a.hash!="string"||!a.translations||typeof c!="string"||!await Qe(c))return M1(e,t,s);const l=`${a.hash}.${o}`,f=a1.join(r,"clp",l),u=a1.join(f,n),h=a1.join(u,"nls.messages.json"),g=a1.join(f,"tcf.json"),m=a1.join(f,"corrupted.info");await Qe(m)&&await l1.promises.rm(f,{recursive:!0,force:!0,maxRetries:3});const O={userLocale:e,osLocale:t,resolvedLanguage:o,defaultMessagesFile:a1.join(s,"nls.messages.json"),languagePack:{translationsConfigFile:g,messagesFile:h,corruptMarkerFile:m},locale:e,availableLanguages:{"*":o},_languagePackId:l,_languagePackSupport:!0,_translationsConfigFile:g,_cacheRoot:f,_resolvedLanguagePackCoreLocation:u,_corruptedFile:m};if(await Qe(u))return v2(u).catch(()=>{}),$1("code/didGenerateNls"),O;const[,d,y,A]=await Promise.all([l1.promises.mkdir(u,{recursive:!0}),l1.promises.readFile(a1.join(s,"nls.keys.json"),"utf-8").then(b=>JSON.parse(b)),l1.promises.readFile(a1.join(s,"nls.messages.json"),"utf-8").then(b=>JSON.parse(b)),l1.promises.readFile(c,"utf-8").then(b=>JSON.parse(b))]),S=[];let U=0;for(const[b,C]of d){const D=A.contents[b];for(const T of C)S.push(D?.[T]||y[U]),U++}return await Promise.all([l1.promises.writeFile(h,JSON.stringify(S),"utf-8"),l1.promises.writeFile(g,JSON.stringify(a.translations),"utf-8")]),$1("code/didGenerateNls"),O}catch(i){console.error("Generating translation files failed.",i)}return M1(e,t,s)}async function g2(e){const t=a1.join(e,"languagepacks.json");try{return JSON.parse(await l1.promises.readFile(t,"utf-8"))}catch{return}}function m2(e,t){try{for(;t;){if(e[t])return t;const r=t.lastIndexOf("-");if(r>0)t=t.substring(0,r);else return}}catch(r){console.error("Resolving language pack configuration failed.",r)}}function M1(e,t,r){return $1("code/didGenerateNls"),{userLocale:e,osLocale:t,resolvedLanguage:"en",defaultMessagesFile:a1.join(r,"nls.messages.json"),locale:e,availableLanguages:{}}}async function Qe(e){try{return await l1.promises.access(e),!0}catch{return!1}}function v2(e){const t=new Date;return l1.promises.utimes(e,t,t)}var Pn=Ks(Bs(import.meta.url)),y2=await p2({userLocale:"en",osLocale:"en",commit:Sn.commit,userDataPath:"",nlsMetadataPath:Pn});process.env.VSCODE_NLS_CONFIG=JSON.stringify(y2),process.env.VSCODE_DEV?(process.env.VSCODE_DEV_INJECT_NODE_MODULE_LOOKUP_PATH=process.env.VSCODE_DEV_INJECT_NODE_MODULE_LOOKUP_PATH||Gs(Pn,"..","remote","node_modules"),e2(process.env.VSCODE_DEV_INJECT_NODE_MODULE_LOOKUP_PATH)):delete process.env.VSCODE_DEV_INJECT_NODE_MODULE_LOOKUP_PATH,await d2(),await Promise.resolve().then(()=>(Js(),cn));

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/core/server-cli.js.map
