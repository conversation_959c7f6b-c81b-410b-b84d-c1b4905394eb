*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[20:21:21] 




[20:21:22] Extension host agent started.
[20:21:22] [<unknown>][398bd571][ManagementConnection] New connection established.
[20:21:22] [<unknown>][40f6e061][ExtensionHostConnection] New connection established.
[20:21:22] [<unknown>][40f6e061][ExtensionHostConnection] <276> Launched Extension Host Process.
[20:21:23] ComputeTargetPlatform: linux-x64
[20:21:25] ComputeTargetPlatform: linux-x64
New EH opened, aborting shutdown
[20:26:22] New EH opened, aborting shutdown
[20:41:06] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
[20:41:06] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
[21:46:06] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
[21:46:07] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8080
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8080
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[22:06:52] Error: connect ECONNREFUSED 127.0.0.1:8080
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8080
}
