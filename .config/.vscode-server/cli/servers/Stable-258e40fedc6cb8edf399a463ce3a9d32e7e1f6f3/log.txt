*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[21:47:21] 




[21:47:21] Extension host agent started.
[21:47:21] [<unknown>][7bc5ce9f][ManagementConnection] New connection established.
[21:47:21] [<unknown>][f7be278f][ExtensionHostConnection] New connection established.
[21:47:21] [<unknown>][f7be278f][ExtensionHostConnection] <447> Launched Extension Host Process.
[21:47:21] ComputeTargetPlatform: linux-x64
[21:47:23] ComputeTargetPlatform: linux-x64
[21:47:59] [<unknown>][7bc5ce9f][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[21:47:59] [<unknown>][f7be278f][ExtensionHostConnection] <447> Extension Host Process exited with code: 0, signal: null.
Cancelling previous shutdown timeout
[21:47:59] Cancelling previous shutdown timeout
Last EH closed, waiting before shutting down
[21:47:59] Last EH closed, waiting before shutting down
[21:49:50] [<unknown>][334792a7][ManagementConnection] New connection established.
[21:49:50] [<unknown>][f5c6d9f5][ExtensionHostConnection] New connection established.
[21:49:50] [<unknown>][f5c6d9f5][ExtensionHostConnection] <1167> Launched Extension Host Process.
[21:50:20] Getting Manifest... augment.vscode-augment
[21:50:20] Installing extension: augment.vscode-augment {
  installPreReleaseVersion: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-x64' },
  isApplicationScoped: false,
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.100.3', date: '2025-06-02T13:30:54.273Z' }
}
[21:50:22] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 1292ms.
[21:50:22] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.472.3: augment.vscode-augment
[21:50:22] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.472.3
[21:50:22] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
New EH opened, aborting shutdown
[21:52:59] New EH opened, aborting shutdown
