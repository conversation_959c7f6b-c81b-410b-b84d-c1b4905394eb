# User Context
- User is building an accommodations booking engine and wants to utilize AI insights for improvements.
- User expects comprehensive end-to-end testing with proof of functionality before considering any implementation complete.
- User expects rapid implementation (minutes not weeks).
- User wants to be specifically called out when travel API functionality is insufficient or unclear, rather than making assumptions.

# Accommodation Search Specifics
- Multi-bedroom search should be implemented as a refinement/filter option, not as part of the main search form.