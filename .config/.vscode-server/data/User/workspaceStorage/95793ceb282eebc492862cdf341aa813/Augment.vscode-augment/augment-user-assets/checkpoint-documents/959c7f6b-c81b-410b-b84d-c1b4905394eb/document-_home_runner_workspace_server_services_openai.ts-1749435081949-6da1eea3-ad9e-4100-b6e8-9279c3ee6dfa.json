{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/openai.ts"}, "originalCode": "import OpenA<PERSON> from \"openai\";\nimport type { Property } from \"@db/schema\";\nimport logger, { logOperation, logError } from '../utils/logger.js';\nimport { sql } from 'drizzle-orm';\nimport { db } from '../../db/index.js';\nimport fetch from 'node-fetch';\nimport { contextService, SessionContext } from './contextService.js';\n\n// Create a silent logger for chat operations to prevent log messages from contaminating SSE streams\n// This is necessary because log messages can appear in the SSE stream and break JSON parsing\nconst silentLogger = {\n  info: (message: string, meta?: any) => {\n    // Only log to file, not to console\n    if (process.env.NODE_ENV === 'development') {\n      // In development, we can still write logs to file but not to console\n      // This prevents log messages from appearing in the SSE stream\n    }\n  },\n  debug: (message: string, meta?: any) => {\n    // Only log to file, not to console\n    if (process.env.NODE_ENV === 'development') {\n      // In development, we can still write logs to file but not to console\n    }\n  },\n  error: logger.error // Keep error logging intact\n};\n\n// Types for conversation management\nexport interface ConversationContext {\n  summary: string;\n  location?: {\n    name: string;\n    lat: number;\n    lng: number;\n    placeType?: string;\n  };\n  dateRange?: {\n    checkIn: string;\n    checkOut: string;\n  };\n  preferences?: {\n    amenities: string[];\n    propertyTypes: string[];\n    priceRange?: [number, number];\n    guestCount?: number;\n    travelPurpose?: string;\n  };\n  lastRecommendations?: string[];\n  lastSummarizedAt: number;\n  messageCount: number;\n}\n\nexport interface ChatMessage {\n  role: 'user' | 'assistant';\n  content: string;\n  timestamp: number;\n}\n\nexport interface ChatContext {\n  conversation: ConversationContext;\n  messages: ChatMessage[];\n  properties?: Property[];\n  searchContext?: SessionContext['searchContext'];\n  userPreferences?: SessionContext['userPreferences'];\n}\n\n// ==============================================\n// AI Provider Configuration - Quick Edit Section\n// ==============================================\nconst AI_CONFIG = {\n  // Set provider to 'openai' or 'lambda'\n  provider: 'openai' as AIProvider,\n\n  // Model configurations per provider\n  models: {\n    openai: {\n      chat: \"gpt-4o-mini\",      // Fast, efficient GPT-4 Turbo\n      completion: \"gpt-4o-mini\", // Same model for consistency\n    },\n    lambda: {\n      chat: \"llama3.1-8b-instruct\",     // llama3.1-8b-instruct or deepseek-llama3.3-70b\n      completion: \"llama3.1-8b-instruct\" // llama3.1-8b-instruct\n    }\n  }\n} as const;\n\n// Type definitions\ntype AIProvider = 'openai' | 'lambda';\ntype ModelType = 'chat' | 'completion';\n\n// Get model name helper\nfunction getModelName(type: ModelType): string {\n  return AI_CONFIG.models[AI_CONFIG.provider][type];\n}\n\n// Initialize AI client based on provider\nfunction initializeAIClient() {\n  const config = {\n    maxRetries: 3,\n    timeout: 30000\n  };\n\n  if (AI_CONFIG.provider === 'lambda') {\n    if (!process.env.LAMBDA_API_KEY) {\n      logError('ai-init', 'Missing Lambda API key', {\n        error: 'LAMBDA_API_KEY environment variable is not set'\n      });\n      throw new Error('Lambda API key is required');\n    }\n    return new OpenAI({ \n      apiKey: process.env.LAMBDA_API_KEY,\n      baseURL: 'https://api.lambdalabs.com/v1',  // /completions or /chat/completions \n      ...config\n    });\n  } else {\n    if (!process.env.OPENAI_API_KEY) {\n      logError('ai-init', 'Missing OpenAI API key', {\n        error: 'OPENAI_API_KEY environment variable is not set'\n      });\n      throw new Error('OpenAI API key is required');\n    }\n    return new OpenAI({ \n      apiKey: process.env.OPENAI_API_KEY,\n      ...config\n    });\n  }\n}\n\nconst ai = initializeAIClient();\n\n// Validate AI client (non-blocking, optional)\n(async () => {\n  try {\n    await ai.models.list();\n    logOperation('ai-init', `${AI_CONFIG.provider.toUpperCase()} - ${getModelName('completion')} initialized successfully`, {\n      provider: AI_CONFIG.provider,\n      maxRetries: 3,\n      timeout: 10000\n    });\n  } catch (error) {\n    logError('ai-init', `Failed to initialize ${AI_CONFIG.provider} - ${getModelName('completion')} client`, {\n      error: error instanceof Error ? error.message : 'Unknown error'\n    });\n    // Don't throw error - allow server to continue without AI\n  }\n})();\n\ninterface PropertySearchResult {\n  properties: Property[];\n  explanation: string;\n}\n\nexport async function enhancePropertySearch(\n  query: string,\n  properties: Property[]\n): Promise<PropertySearchResult> {\n  try {\n    const response = await ai.chat.completions.create({\n      model: getModelName('completion'),\n      messages: [\n        {\n          role: \"system\",\n          content: `You are RoomLamAI, a proactive and helpful travel assistant. Your task is to analyze the user's search context and provide personalized property recommendations.\n\n            When responding:\n            1. Acknowledge the user's chosen location and dates\n            2. Show enthusiasm about helping plan their trip\n            3. Highlight key features of the destination if known\n            4. Suggest relevant properties based on the context\n            5. Encourage the user to share more preferences\n\n            Format property recommendations using:\n            [PROPERTIES_START]id1,id2,id3[PROPERTIES_END]\n\n            Example response structure:\n            \"I see you're planning a trip to [location]! That's a great choice, especially during [season/time]. \n            Based on your dates, here are some excellent properties that I think you'll love:\n            [PROPERTIES_START]101,102,103[PROPERTIES_END]\n            These properties are particularly well-located and offer great value for your dates.\n            Would you like me to tell you more about any of these options? Or shall we refine the search based on specific preferences?\"`\n        },\n        {\n          role: \"user\",\n          content: JSON.stringify({\n            query,\n            properties: properties.map(p => ({\n              id: p.id,\n              name: p.name,\n              description: p.description,\n              amenities: p.amenities,\n              type: p.propertyType || 'hotel', // Use propertyType from schema\n              basePrice: p.basePrice,\n            })),\n          }),\n        },\n      ],\n      response_format: { type: \"json_object\" },\n    });\n\n    const content = response.choices[0].message.content;\n    if (!content) {\n      throw new Error(\"No content in OpenAI response\");\n    }\n\n    const result = JSON.parse(content);\n    \n    // Clean and standardize property IDs\n    let propertyIds: number[] = [];\n    \n    if (Array.isArray(result.properties)) {\n      // Convert all elements to numbers and filter out invalid ones\n      propertyIds = result.properties\n        .map((id: any) => {\n          // If the ID is already a number, use it directly\n          if (typeof id === 'number') return id;\n          \n          // If it's a string, try to extract a number from it\n          if (typeof id === 'string') {\n            const matches = id.match(/\\d+/);\n            return matches ? parseInt(matches[0], 10) : NaN;\n          }\n          \n          return NaN;\n        })\n        .filter((id: number) => !isNaN(id));\n    }\n    \n    const matchedProperties = properties.filter(p =>\n      propertyIds.includes(p.id)\n    );\n\n    return {\n      properties: matchedProperties,\n      explanation: result.explanation || \"Here are some properties that match your criteria.\",\n    };\n  } catch (error) {\n    console.error(\"AI search enhancement failed:\", error);\n    return { properties, explanation: \"\" };\n  }\n}\n\n// Utility function to summarize conversation\nexport async function summarizeConversation(\n  messages: ChatMessage[],\n  currentContext: ConversationContext\n): Promise<ConversationContext> {\n  try {\n    // Skip summarization if we have fewer than 2 messages\n    if (messages.length < 2) {\n      return {\n        ...currentContext,\n        lastSummarizedAt: Date.now(),\n        messageCount: messages.length\n      };\n    }\n\n    const response = await ai.chat.completions.create({\n      model: getModelName('chat'),\n      messages: [\n        {\n          role: \"system\",\n          content: `You are a conversation summarizer for a travel assistant. \n            Analyze the conversation and extract key details about:\n            1. Location preferences\n            2. Date ranges\n            3. Guest preferences (amenities, property types, etc.)\n            4. Price considerations\n            5. Overall intent\n\n            Return a JSON object with:\n            {\n              \"summary\": \"Brief 2-3 sentence summary of the conversation context\",\n              \"location\": {\"name\": string, \"lat\": number, \"lng\": number} | null,\n              \"dateRange\": {\"checkIn\": string, \"checkOut\": string} | null,\n              \"preferences\": {\n                \"amenities\": string[],\n                \"propertyTypes\": string[],\n                \"priceRange\": [number, number] | null,\n                \"guestCount\": number | null\n              }\n            }`\n        },\n        ...messages.map(m => ({\n          role: m.role,\n          content: m.content\n        }))\n      ],\n      response_format: { type: \"json_object\" }\n    });\n\n    const content = response.choices[0].message.content;\n    if (!content) throw new Error(\"No summary generated\");\n\n    try {\n      const summary = JSON.parse(content);\n      return {\n        ...currentContext,\n        ...summary,\n        lastSummarizedAt: Date.now(),\n        messageCount: messages.length\n      };\n    } catch (parseError) {\n      silentLogger.error('Failed to parse conversation summary JSON', { \n        parseError, \n        content: content.substring(0, 200) \n      });\n      \n      // Return updated timestamps but keep the rest of the context intact\n      return {\n        ...currentContext,\n        lastSummarizedAt: Date.now(),\n        messageCount: messages.length\n      };\n    }\n  } catch (error) {\n    logger.error('Failed to summarize conversation', { error });\n    // Return updated timestamps but keep the rest of the context intact\n    return {\n      ...currentContext,\n      lastSummarizedAt: Date.now(),\n      messageCount: messages.length\n    };\n  }\n}\n\n// Get or initialize conversation context using contextService\nexport function getConversationContext(sessionId: string): {\n  context: ConversationContext;\n  messages: ChatMessage[];\n} {\n  const sessionContext = contextService.getContext(sessionId);\n  return {\n    context: sessionContext.conversation,\n    messages: sessionContext.messages\n  };\n}\n\n// Add message to conversation using contextService\nexport function addMessageToConversation(\n  sessionId: string,\n  message: Omit<ChatMessage, 'timestamp'>\n): void {\n  contextService.addMessage(sessionId, {\n    ...message,\n    timestamp: Date.now()\n  });\n}\n\n// Check if conversation needs summarization\nexport function needsSummarization(context: ConversationContext, messageCount: number): boolean {\n  const SUMMARIZE_MESSAGE_THRESHOLD = 5;\n  const SUMMARIZE_TIME_THRESHOLD = 5 * 60 * 1000; // 5 minutes\n  const timeSinceLastSummary = Date.now() - context.lastSummarizedAt;\n\n  return messageCount >= context.messageCount + SUMMARIZE_MESSAGE_THRESHOLD ||\n         timeSinceLastSummary >= SUMMARIZE_TIME_THRESHOLD;\n}\n\n// Modified getUserLocation function with proper typing\nasync function getUserLocation(req: { headers: { [key: string]: string | string[] | undefined }; ip?: string }): Promise<string | null> {\n  try {\n    // Get IP from various possible sources\n    const forwarded = req.headers['x-forwarded-for'];\n    const ip = forwarded \n      ? (Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0]) \n      : req.ip || '127.0.0.1';\n\n    // For development/testing, return null if localhost\n    if (ip === '127.0.0.1' || ip === '::1') {\n      return null;\n    }\n\n    const response = await fetch(`http://ip-api.com/json/${ip}`);\n    const data = await response.json();\n\n    // Type guard for the IP API response\n    interface IPAPIResponse {\n      status: string;\n      city?: string;\n      region?: string;\n      country?: string;\n    }\n    \n    // Check if data has the expected structure\n    const isValidResponse = (data: any): data is IPAPIResponse => {\n      return typeof data === 'object' && data !== null && 'status' in data;\n    };\n\n    if (isValidResponse(data) && data.status === 'success' && data.city && data.region && data.country) {\n      return `${data.city}, ${data.region}, ${data.country}`;\n    }\n    return null;\n  } catch (error) {\n    logger.error('Failed to get user location', { \n      error: error instanceof Error ? error.message : 'Unknown error'\n    });\n    return null;\n  }\n}\n\n// Modify handleChatStream to include date and location context\nexport async function* handleChatStream(\n  message: string,\n  context: ChatContext,\n  req: { headers: { [key: string]: string | string[] | undefined }; ip?: string }\n): AsyncGenerator<ChatResponse, void, unknown> {\n  const requestId = `chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n\n  try {\n    if (!message || !context) {\n      const error = {\n        code: 'MISSING_PARAMETERS',\n        message: 'Missing required parameters',\n        details: {\n          hasMessage: !!message,\n          hasContext: !!context\n        }\n      };\n      silentLogger.error('Chat stream validation error', { error, requestId });\n      yield { \n        type: 'error', \n        data: {\n          message: \"I apologize, but I'm missing some required information. Please try again.\",\n          details: process.env.NODE_ENV === 'development' ? 'Missing message or context' : undefined\n        }\n      };\n      return;\n    }\n\n    const model = getModelName('chat');\n\n    // Format current date in a natural way\n    const currentDate = new Date();\n    const dateFormatter = new Intl.DateTimeFormat('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n    const formattedDate = dateFormatter.format(currentDate);\n\n    const userLocation = await getUserLocation(req);\n\n    // Generate an enhanced context string with comprehensive user journey awareness\n    const enhancedContext = `\n      Current date: ${formattedDate}\n      ${userLocation ? `User location: ${userLocation}` : ''}\n\n      Current conversation context:\n      ${context.conversation.summary}\n\n      ${context.conversation.location ? \n        `Location: ${context.conversation.location.name}` : ''}\n      ${context.conversation.dateRange ? \n        `Dates: ${context.conversation.dateRange.checkIn} to ${context.conversation.dateRange.checkOut}` : ''}\n      ${context.conversation.preferences ? \n        `Preferences: ${JSON.stringify(context.conversation.preferences)}` : ''}\n      ${context.conversation.lastRecommendations ? \n        `Previously recommended properties: ${context.conversation.lastRecommendations.join(', ')}` : ''}\n      \n      ${context.searchContext ? `\n      User's search history:\n      ${context.searchContext.recentSearches.slice(0, 3).map(search => \n        `- ${search.location} (${new Date(search.timestamp).toLocaleString()})`\n      ).join('\\n')}\n\n      Recently viewed properties:\n      ${context.searchContext.viewedProperties.slice(0, 3).map(view => \n        `- Property ID: ${view.propertyId} (${new Date(view.timestamp).toLocaleString()})`\n      ).join('\\n')}\n\n      ${context.searchContext.comparedProperties.length > 0 ? \n        `Properties being compared: ${context.searchContext.comparedProperties.join(', ')}` : ''}\n\n      ${context.searchContext.filters && Object.keys(context.searchContext.filters).length > 0 ? \n        `Applied filters: ${JSON.stringify(context.searchContext.filters)}` : ''}\n      ` : ''}\n      \n      ${context.userPreferences ? `\n      User preferences:\n      ${context.userPreferences.pricePreference ? `Price range: ${context.userPreferences.pricePreference}` : ''}\n      ${context.userPreferences.favoriteLocations ? `Favorite locations: ${context.userPreferences.favoriteLocations.join(', ')}` : ''}\n      ${context.userPreferences.preferredAmenities ? `Preferred amenities: ${context.userPreferences.preferredAmenities.join(', ')}` : ''}\n      ${context.userPreferences.preferredPropertyTypes ? `Preferred property types: ${context.userPreferences.preferredPropertyTypes.join(', ')}` : ''}\n      ${context.userPreferences.travelPurpose ? `Travel purpose: ${context.userPreferences.travelPurpose}` : ''}\n      ` : ''}\n    `;\n\n    logOperation(requestId, `[${AI_CONFIG.provider.toUpperCase()}] CHAT_STREAM_START`, {\n      provider: AI_CONFIG.provider,\n      model,\n      queryLength: message.length,\n      hasContext: !!context,\n      contextSummary: context.conversation.summary,\n      userLocation\n    });\n\n    const stream = await ai.chat.completions.create({\n      model,\n      messages: [\n        {\n          role: \"system\",\n          content: `You are RoomLamAI, the world's most intelligent and proactive travel assistant. You don't just find accommodations - you craft perfect travel experiences that exceed expectations.\n\n            ${enhancedContext}\n\n            🌟 CORE MISSION:\n            Transform travel planning from a chore into an exciting journey of discovery. You're not just a booking assistant - you're a travel expert, local insider, and personal concierge rolled into one.\n\n            🏨 ACCOMMODATION MASTERY:\n\n            HOTELS & RESORTS:\n            - Luxury properties (5-star, boutique, historic)\n            - Business hotels (conference facilities, executive lounges)\n            - Family resorts (kids clubs, water parks, family suites)\n            - All-inclusive resorts (meals, activities, entertainment)\n            - Spa & wellness retreats (treatments, fitness, meditation)\n            - Beach & mountain resorts (location-specific amenities)\n\n            MULTI-BEDROOM ACCOMMODATIONS (Your Specialty):\n            - 2-8+ bedroom vacation rentals and villas\n            - Family-friendly resorts with connecting rooms\n            - Apartment-style hotels with kitchens\n            - Corporate housing for extended stays\n            - Group accommodations for:\n              * Multi-generational families (grandparents + parents + kids)\n              * Friend groups and reunions\n              * Wedding parties and celebrations\n              * Corporate retreats and team building\n              * Extended family gatherings\n\n            UNIQUE STAYS:\n            - Treehouses, castles, houseboats, glamping\n            - Historic properties and converted buildings\n            - Eco-lodges and sustainable accommodations\n            - Themed properties and experiential stays\n\n            🎯 INTELLIGENT ASSISTANCE PRINCIPLES:\n            1. **Anticipate Needs**: Read between the lines and suggest what they haven't thought of\n            2. **Context Awareness**: Use conversation history, location, dates, and preferences\n            3. **Proactive Recommendations**: Don't wait to be asked - offer valuable insights\n            4. **Group Dynamics**: Consider who's traveling and their different needs\n            5. **Local Expertise**: Provide insider knowledge about destinations\n            6. **Seasonal Intelligence**: Factor in weather, events, and optimal timing\n\n            🌍 DESTINATION INTELLIGENCE:\n            - Local events, festivals, and cultural happenings\n            - Seasonal considerations and weather patterns\n            - Hidden gems and off-the-beaten-path experiences\n            - Transportation tips and logistics advice\n            - Cultural insights and etiquette guidance\n            - Safety considerations and travel advisories\n\n            💡 RESPONSE EXCELLENCE:\n            1. **Be Enthusiastic**: Show genuine excitement about helping them travel\n            2. **Provide Context**: Explain WHY you're recommending something\n            3. **Think Holistically**: Consider the entire travel experience\n            4. **Offer Alternatives**: Present multiple options with trade-offs\n            5. **Suggest Experiences**: Recommend activities, dining, and attractions\n            6. **Consider Budget**: Provide options across different price ranges\n\n            🚨 CRITICAL - ACTION Tag Format Rules (FOLLOW EXACTLY):\n\n            WHEN TO USE LOCATION ACTIONS:\n            - User mentions ANY city, country, or destination name\n            - User asks \"where should I go\" or similar destination questions\n            - User wants to \"visit\", \"travel to\", \"stay in\" any place\n            - User mentions specific locations like \"Paris\", \"Tokyo\", \"New York\"\n            - ALWAYS create ACTION:LOCATION tags for destination discussions\n\n            FORMAT: [ACTION:TYPE|LABEL|{JSON_DATA}]\n\n            For LOCATION actions (USE FREQUENTLY):\n            - TYPE: LOCATION\n            - LABEL: Full location name (e.g., \"Paris, France\", \"New York City, USA\")\n            - JSON_DATA: {\"lat\": number, \"lng\": number, \"name\": \"string\", \"description\": \"string\"}\n\n            🎯 PERFECT LOCATION EXAMPLES:\n            [ACTION:LOCATION|Paris, France|{\"lat\": 48.8566, \"lng\": 2.3522, \"name\": \"Paris, France\", \"description\": \"City of lights with iconic landmarks and romantic atmosphere\"}]\n            [ACTION:LOCATION|Tokyo, Japan|{\"lat\": 35.6762, \"lng\": 139.6503, \"name\": \"Tokyo, Japan\", \"description\": \"Modern metropolis blending tradition with cutting-edge technology\"}]\n            [ACTION:LOCATION|New York City, USA|{\"lat\": 40.7128, \"lng\": -74.0060, \"name\": \"New York City, USA\", \"description\": \"The city that never sleeps, iconic skyline and endless attractions\"}]\n            [ACTION:LOCATION|Miami Beach, Florida|{\"lat\": 25.7907, \"lng\": -80.1300, \"name\": \"Miami Beach, Florida\", \"description\": \"Tropical paradise with beautiful beaches and vibrant nightlife\"}]\n            [ACTION:LOCATION|San Francisco, California|{\"lat\": 37.7749, \"lng\": -122.4194, \"name\": \"San Francisco, California\", \"description\": \"Golden Gate city with hills, culture, and tech innovation\"}]\n\n            ⚠️ NEVER DO THESE:\n            ❌ [ACTION:LOCATION||{\"lat\": 25.7907, \"lng\": -80.1300}] - Missing label\n            ❌ [ACTION:LOCATION|Miami|{\"lat\": 25.7907}] - Missing required fields\n            ❌ [ACTION:LOCATION|Miami|lat: 25.7907, lng: -80.1300] - Invalid JSON\n\n            ✅ VALIDATION CHECKLIST:\n            ✅ Format: [ACTION:LOCATION|Full Location Name|{complete JSON}]\n            ✅ Label is descriptive and complete\n            ✅ JSON has lat, lng, name, description\n            ✅ All strings in quotes, numbers without quotes\n            ✅ JSON properly closed with }\n\n            Property Recommendation Format:\n            1. When recommending properties, ALWAYS use this EXACT format:\n               [PROPERTIES_START]id1,id2,id3[PROPERTIES_END]\n               - Use ONLY numeric property IDs\n               - Separate IDs with commas\n               - NO spaces, brackets, or other characters\n               - NO explanatory text inside the tags\n            2. After the property tags, explain why you recommended each property\n            3. Example of correct format:\n               [PROPERTIES_START]101,102,103[PROPERTIES_END]\n               Here's why I recommended these properties...`\n        },\n        ...context.messages.slice(-5).map(m => ({\n          role: m.role,\n          content: m.content\n        })),\n        {\n          role: \"user\",\n          content: message\n        }\n      ],\n      stream: true,\n      temperature: 0.7,\n      max_tokens: 1000,\n    });\n\n    let buffer = '';\n    let actionBuffer = '';\n\n    for await (const chunk of stream) {\n      try {\n        const content = chunk.choices[0]?.delta?.content || '';\n        \n        // Skip log messages that might have been mixed into the stream\n        if (content.includes('Initialize') || \n            content.includes('Added search') || \n            content.includes('Recorded property') || \n            content.includes('Updated filters') ||\n            content.includes('Added property to comparison') ||\n            content.includes('Recorded booking attempt') ||\n            content.includes('Updated conversation context') ||\n            content.includes('Added message')) {\n          \n          logger.warn('Filtered out log message from AI response stream', { \n            content: content.substring(0, 100),\n            requestId \n          });\n          continue;\n        }\n        \n        // Additional check to detect other log messages\n        if (typeof content === 'string' && (\n            content.startsWith('Initialize') || \n            content.startsWith('Added search') || \n            content.startsWith('Recorded property') || \n            content.startsWith('Updated filters') ||\n            content.startsWith('Added property to comparison') ||\n            content.startsWith('Recorded booking attempt') ||\n            content.startsWith('Updated conversation context') ||\n            content.startsWith('Added message'))) {\n          \n          logger.warn('Filtered out direct log message from AI response stream', { \n            content: content.substring(0, 100),\n            requestId \n          });\n          continue;\n        }\n        \n        buffer += content;\n        actionBuffer += content;\n      } catch (error) {\n        silentLogger.error('Error processing stream chunk', {\n          error: error instanceof Error ? error.message : 'Unknown error',\n          requestId\n        });\n        continue;\n      }\n\n      // Process action tags\n      while (actionBuffer.includes('[ACTION:') && actionBuffer.includes(']')) {\n        const actionStart = actionBuffer.indexOf('[ACTION:');\n        const actionEnd = actionBuffer.indexOf(']', actionStart) + 1;\n\n        if (actionStart >= 0 && actionEnd > 0) {\n          const actionTag = actionBuffer.substring(actionStart, actionEnd);\n          \n          // Improved validation with better error handling\n          const actionMatch = actionTag.match(/\\[ACTION:(\\w+)\\|(.*?)\\|(.*?)\\]/);\n          if (!actionMatch) {\n            // Try alternative patterns for common malformed tokens\n            const altMatch1 = actionTag.match(/\\[ACTION:(\\w+)\\|\\|(\\{.*?\\})\\]/); // Missing label: [ACTION:LOCATION||{...}]\n            const altMatch2 = actionTag.match(/\\[ACTION:(\\w+)\\|([^|]*)\\|(\\{[^}]*\\}?)\\]/); // More flexible pattern\n            \n            if (altMatch1) {\n              const [_, type, dataStr] = altMatch1;\n              logger.warn('Recovered ACTION token with missing label', { \n                original: actionTag,\n                type,\n                requestId \n              });\n              \n              try {\n                const data = JSON.parse(dataStr);\n                // Generate a default label based on the data\n                let defaultLabel = 'Unknown';\n                if (type.toLowerCase() === 'location' && data.lat && data.lng) {\n                  defaultLabel = data.name || `Location (${data.lat}, ${data.lng})`;\n                }\n                \n                yield { type: 'action', data: { type: type.toLowerCase(), label: defaultLabel, data } };\n              } catch (e) {\n                logger.warn('Failed to parse recovered ACTION token', { \n                  error: e instanceof Error ? e.message : 'Unknown error',\n                  actionTag,\n                  requestId \n                });\n              }\n            } else if (altMatch2) {\n              const [_, type, label, dataStr] = altMatch2;\n              logger.warn('Recovered ACTION token with flexible pattern', { \n                original: actionTag,\n                type,\n                label,\n                requestId \n              });\n              \n              try {\n                // Handle incomplete JSON by trying to complete it\n                let cleanedDataStr = dataStr.trim();\n                \n                // If JSON appears incomplete, try to fix common issues\n                if (cleanedDataStr.startsWith('{') && !cleanedDataStr.endsWith('}')) {\n                  // Check if it's just missing closing brace\n                  const openBraces = (cleanedDataStr.match(/\\{/g) || []).length;\n                  const closeBraces = (cleanedDataStr.match(/\\}/g) || []).length;\n                  if (openBraces > closeBraces) {\n                    cleanedDataStr += '}';\n                    logger.info('Attempted to fix incomplete JSON', { \n                      original: dataStr,\n                      fixed: cleanedDataStr,\n                      requestId \n                    });\n                  }\n                }\n                \n                const data = JSON.parse(cleanedDataStr);\n                \n                // Generate a default label if missing\n                let finalLabel = label || 'Unknown';\n                if (type.toLowerCase() === 'location' && data.lat && data.lng) {\n                  finalLabel = label || data.name || `Location (${data.lat}, ${data.lng})`;\n                }\n                \n                yield { type: 'action', data: { type: type.toLowerCase(), label: finalLabel, data } };\n              } catch (e) {\n                logger.warn('Failed to parse recovered ACTION token with flexible pattern', { \n                  error: e instanceof Error ? e.message : 'Unknown error',\n                  actionTag,\n                  dataStr,\n                  requestId \n                });\n              }\n            } else {\n              logger.warn('Invalid action tag format - no recovery possible', { \n                actionTag,\n                requestId \n              });\n            }\n            \n            actionBuffer = actionBuffer.substring(actionEnd);\n            continue;\n          }\n          \n          const [_, type, label, dataStr] = actionMatch;\n\n          if (type && dataStr) {\n            try {\n              // Validate that dataStr is valid JSON before parsing\n              if (!dataStr.trim().startsWith('{') && !dataStr.trim().startsWith('[')) {\n                logger.warn('Invalid JSON format in action data', { \n                  dataStr,\n                  actionTag,\n                  requestId \n                });\n                actionBuffer = actionBuffer.substring(actionEnd);\n                continue;\n              }\n              \n              // Handle case where JSON is incomplete (common issue)\n              let cleanedDataStr = dataStr.trim();\n              if (cleanedDataStr.startsWith('{') && !cleanedDataStr.endsWith('}')) {\n                // Check if it's just missing closing brace\n                const openBraces = (cleanedDataStr.match(/\\{/g) || []).length;\n                const closeBraces = (cleanedDataStr.match(/\\}/g) || []).length;\n                if (openBraces > closeBraces) {\n                  cleanedDataStr += '}';\n                  logger.info('Fixed incomplete JSON in action data', { \n                    original: dataStr,\n                    fixed: cleanedDataStr,\n                    requestId \n                  });\n                } else {\n                  // Still incomplete, wait for more content\n                  logger.warn('Potentially incomplete JSON in action data', { \n                    dataStr: cleanedDataStr,\n                    actionTag,\n                    requestId \n                  });\n                  actionBuffer = actionBuffer.substring(actionEnd);\n                  continue;\n                }\n              }\n              \n              const data = JSON.parse(cleanedDataStr);\n              \n              // Additional validation for location actions\n              if (type.toLowerCase() === 'location') {\n                if (!data.lat || !data.lng) {\n                  logger.warn('Location action missing coordinates', {\n                    actionTag,\n                    data,\n                    requestId\n                  });\n                  actionBuffer = actionBuffer.substring(actionEnd);\n                  continue;\n                }\n                \n                // Ensure name is present, use label as fallback\n                if (!data.name && label) {\n                  data.name = label;\n                  logger.info('Added missing name to location action from label', {\n                    label,\n                    requestId\n                  });\n                }\n              }\n              \n              // Use label if provided, otherwise generate from data\n              const finalLabel = label || (data.name || 'Unknown');\n              \n              yield { type: 'action', data: { type: type.toLowerCase(), label: finalLabel, data } };\n            } catch (e) {\n              logger.warn('Failed to parse action data', { \n                error: e instanceof Error ? e.message : 'Unknown error',\n                actionTag,\n                dataStr,\n                requestId \n              });\n            }\n          }\n\n          actionBuffer = actionBuffer.substring(actionEnd);\n        } else {\n          // No complete action tag found, wait for more content\n          break;\n        }\n      }\n\n      // Process property recommendations\n      if (buffer.includes('[PROPERTIES_START]') && buffer.includes('[PROPERTIES_END]')) {\n        const [beforeProps, rest] = buffer.split('[PROPERTIES_START]');\n        const [propsJson, afterProps] = rest.split('[PROPERTIES_END]');\n\n        if (beforeProps.trim()) {\n          yield { type: 'text', data: beforeProps.trim() };\n        }\n\n        try {\n          // Clean and standardize the property IDs format\n          const cleanedPropsJson = propsJson.trim()\n            // Remove any non-digit, non-comma characters\n            .replace(/[^\\d,]/g, '')\n            // Ensure there are no consecutive commas\n            .replace(/,+/g, ',')\n            // Remove leading/trailing commas\n            .replace(/^,|,$/g, '');\n            \n          if (cleanedPropsJson !== propsJson.trim()) {\n            logger.info('Standardized property IDs format', { \n              original: propsJson.trim(),\n              standardized: cleanedPropsJson,\n              requestId \n            });\n          }\n          \n          if (cleanedPropsJson === '') {\n            logger.warn('No valid property IDs found after cleaning', { \n              original: propsJson.trim(),\n              requestId \n            });\n          } else {\n            const propertyIds = cleanedPropsJson.split(',')\n              .map(id => parseInt(id.trim(), 10))\n              .filter(id => !isNaN(id)); // Remove any NaN values\n              \n            const recommendedProperties = context.properties?.filter(p => \n              propertyIds.includes(p.id)\n            ) || [];\n\n            if (recommendedProperties.length > 0) {\n              yield { type: 'properties', data: recommendedProperties };\n            }\n          }\n        } catch (e) {\n          silentLogger.error('Failed to parse property IDs', { error: e, propsJson });\n        }\n\n        buffer = afterProps || '';\n      }\n\n      // Stream regular text content\n      if (buffer.length > 0 && !buffer.includes('[PROPERTIES_START]')) {\n        const lastNewline = buffer.lastIndexOf('\\n');\n        if (lastNewline > 0) {\n          const text = buffer.substring(0, lastNewline);\n          // Format text with proper spacing\n          const formattedText = text\n            .split('\\n')\n            .map(line => line.trim())\n            .filter(line => line)\n            .join('\\n\\n');\n\n          if (formattedText.trim()) {\n            yield { type: 'text', data: formattedText };\n          }\n          buffer = buffer.substring(lastNewline + 1);\n        }\n      }\n    }\n\n    // Flush any remaining content\n    if (buffer.trim()) {\n      const formattedText = buffer.trim()\n        .split('\\n')\n        .map(line => line.trim())\n        .filter(line => line)\n        .join('\\n\\n');\n\n      if (formattedText.trim()) {\n        yield { type: 'text', data: formattedText };\n      }\n    }\n\n    silentLogger.info('Chat stream completed', {\n      requestId,\n      provider: AI_CONFIG.provider,\n      model\n    });\n\n  } catch (error) {\n    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';\n    const errorDetails = {\n      message,\n      context: {\n        hasProperties: !!context.properties,\n        messageLength: message.length\n      },\n      error: errorMessage,\n      stack: error instanceof Error ? error.stack : undefined,\n      requestId\n    };\n\n    silentLogger.error('Chat stream error', {\n      ...errorDetails,\n      error: typeof errorDetails.error === 'string' ? errorDetails.error : (error instanceof Error ? error.message : 'Unknown error'),\n      stack: error instanceof Error ? error.stack : undefined\n    });\n\n    yield { \n      type: 'error', \n      data: {\n        message: \"I apologize, but I encountered an error while processing your request. Please try again.\",\n        details: process.env.NODE_ENV === 'development' ? errorMessage : undefined\n      }\n    };\n  }\n}\n\nexport async function getPersonalizedRecommendations(\n  userId: number,\n  searchHistory: string[],\n  viewedProperties: Property[]\n): Promise<number[]> {\n  try {\n    const response = await ai.chat.completions.create({\n      model: getModelName('completion'),\n      messages: [\n        {\n          role: \"system\",\n          content: `You are an intelligent recommendation system designed to provide personalized hotel suggestions.\n\n            Consider the following factors when making recommendations:\n            1. User's search patterns and preferences shown in their search history\n            2. Properties they've viewed but not booked (potential interest areas)\n            3. Price ranges they typically look at\n            4. Common amenities in their viewed properties\n            5. Preferred locations and property types\n\n            Analyze patterns to identify:\n            - Preferred amenities and features\n            - Price sensitivity\n            - Location preferences\n            - Property type preferences (luxury, budget, family-friendly, etc.)\n\n            Return a JSON object with:\n            {\n              \"recommendedProperties\": number[], // Array of property IDs\n              \"reasoning\": string,              // Explanation of recommendations\n              \"userPreferences\": {              // Identified user preferences\n                \"priceRange\": string,\n                \"amenities\": string[],\n                \"propertyTypes\": string[],\n                \"locations\": string[]\n              }\n            }`\n        },\n        {\n          role: \"user\",\n          content: JSON.stringify({\n            searchHistory,\n            viewedProperties: viewedProperties.map(p => ({\n              id: p.id,\n              type: p.propertyType || 'hotel', // Use propertyType from schema instead of type\n              amenities: p.amenities,\n              basePrice: p.basePrice,\n              location: {\n                city: p.city,\n                state: p.state,\n                country: p.country\n              }\n            })),\n          }),\n        },\n      ],\n      response_format: { type: \"json_object\" },\n    });\n\n    const content = response.choices[0].message.content;\n    if (!content) {\n      throw new Error(\"No content in OpenAI response\");\n    }\n\n    const result = JSON.parse(content);\n\n    // Store the user preferences for future use\n    if (result.userPreferences) {\n      // TODO: Implement storing user preferences in the database\n      console.log('User preferences identified:', result.userPreferences);\n    }\n\n    return result.recommendedProperties || [];\n  } catch (error) {\n    console.error(\"AI recommendations failed:\", error);\n    return [];\n  }\n}\n\nexport async function analyzeSentiment(text: string): Promise<{\n  rating: number;\n  confidence: number;\n}> {\n  try {\n    const response = await ai.chat.completions.create({\n      model: getModelName('completion'),\n      messages: [\n        {\n          role: \"system\",\n          content:\n            \"You are a sentiment analysis expert. Analyze the sentiment of the text and provide a rating from 1 to 5 stars and a confidence score between 0 and 1. Return JSON in format: { 'rating': number, 'confidence': number }\",\n        },\n        {\n          role: \"user\",\n          content: text,\n        },\n      ],\n      response_format: { type: \"json_object\" },\n    });\n\n    const content = response.choices[0].message.content;\n    if (!content) {\n      throw new Error(\"No content in OpenAI response\");\n    }\n\n    const result = JSON.parse(content);\n    return {\n      rating: Math.max(1, Math.min(5, Math.round(result.rating))),\n      confidence: Math.max(0, Math.min(1, result.confidence)),\n    };\n  } catch (error) {\n    console.error(\"AI sentiment analysis failed:\", error);\n    return { rating: 3, confidence: 0 };\n  }\n}\n\n// Add ChatResponse type and export it\nexport interface ChatResponse {\n  type: 'text' | 'properties' | 'action' | 'error' | 'location';\n  data: any;\n}\n\n// Define more specific response types for better type safety\nexport type TextResponse = {\n  type: 'text';\n  data: string;\n};\n\nexport type PropertiesResponse = {\n  type: 'properties';\n  data: Property[];\n};\n\nexport type LocationResponse = {\n  type: 'location';\n  data: {\n    name: string;\n    lat: number;\n    lng: number;\n    placeType?: string;\n  };\n};\n\nexport type ActionResponse = {\n  type: 'action';\n  data: {\n    type: string;\n    label: string;\n    data: any;\n  };\n};\n\nexport type ErrorResponse = {\n  type: 'error';\n  data: {\n    message: string;\n    details?: string;\n  };\n};\n\n// Union type of all specific response types\nexport type TypedChatResponse = TextResponse | PropertiesResponse | LocationResponse | ActionResponse | ErrorResponse;\n\n// Add property matching types\nexport interface PropertyRecommendation {\n  name: string;\n  latitude?: number;\n  longitude?: number;\n  maxDistance?: number;\n  description?: string;\n  highlights?: string[];\n}\n\nexport interface PropertyMatch {\n  property: Property | null;\n  searchParams?: {\n    latitude: number;\n    longitude: number;\n    name: string;\n    radius?: number;\n  };\n  recommendation: PropertyRecommendation;\n}\n\n// Add property matching function\nexport const findMatchingProperty = async (\n  recommendation: PropertyRecommendation,\n  requestId: string\n): Promise<PropertyMatch> => {\n  const { name, latitude, longitude, maxDistance = 2 } = recommendation;\n\n  try {\n    // First try to find in database\n    const dbMatch = latitude && longitude \n      ? await db.query.properties.findFirst({\n          where: sql`\n            (point(${latitude}, ${longitude}) <@> point(CAST(latitude AS float), CAST(longitude AS float)) <= ${maxDistance})\n            ${name ? sql`AND similarity(LOWER(name), LOWER(${name})) > 0.3` : sql``}\n          `,\n          orderBy: name ? sql`similarity(LOWER(name), LOWER(${name})) DESC` : undefined\n        })\n      : name \n        ? await db.query.properties.findFirst({\n            where: sql`similarity(LOWER(name), LOWER(${name})) > 0.3`,\n            orderBy: sql`similarity(LOWER(name), LOWER(${name})) DESC`\n          })\n        : null;\n\n    if (dbMatch) {\n      logOperation(requestId, 'PROPERTY_MATCHED_DB', {\n        recommendedName: name,\n        matchedName: dbMatch.name,\n        matchedId: dbMatch.id\n      });\n      return { property: dbMatch, recommendation };\n    }\n\n    // If no database match and we have coordinates, prepare for TravSrv search\n    if (latitude && longitude) {\n      logOperation(requestId, 'PROPERTY_NOT_FOUND_DB', {\n        recommendedName: name,\n        location: `${latitude},${longitude}`,\n        willSearchTravSrv: true\n      });\n\n      return {\n        property: null,\n        searchParams: {\n          latitude,\n          longitude,\n          name,\n          radius: maxDistance\n        },\n        recommendation\n      };\n    }\n\n    logOperation(requestId, 'PROPERTY_MATCH_FAILED', {\n      recommendedName: name,\n      reason: 'No coordinates provided and no database match'\n    });\n    return { property: null, recommendation };\n\n  } catch (error) {\n    silentLogger.error('Property matching failed', { \n      error,\n      recommendation,\n      requestId\n    });\n    return { property: null, recommendation };\n  }\n};", "modifiedCode": "import OpenA<PERSON> from \"openai\";\nimport type { Property } from \"@db/schema\";\nimport logger, { logOperation, logError } from '../utils/logger.js';\nimport { sql } from 'drizzle-orm';\nimport { db } from '../../db/index.js';\nimport fetch from 'node-fetch';\nimport { contextService, SessionContext } from './contextService.js';\n\n// Create a silent logger for chat operations to prevent log messages from contaminating SSE streams\n// This is necessary because log messages can appear in the SSE stream and break JSON parsing\nconst silentLogger = {\n  info: (message: string, meta?: any) => {\n    // Only log to file, not to console\n    if (process.env.NODE_ENV === 'development') {\n      // In development, we can still write logs to file but not to console\n      // This prevents log messages from appearing in the SSE stream\n    }\n  },\n  debug: (message: string, meta?: any) => {\n    // Only log to file, not to console\n    if (process.env.NODE_ENV === 'development') {\n      // In development, we can still write logs to file but not to console\n    }\n  },\n  error: logger.error // Keep error logging intact\n};\n\n// Types for conversation management\nexport interface ConversationContext {\n  summary: string;\n  location?: {\n    name: string;\n    lat: number;\n    lng: number;\n    placeType?: string;\n  };\n  dateRange?: {\n    checkIn: string;\n    checkOut: string;\n  };\n  preferences?: {\n    amenities: string[];\n    propertyTypes: string[];\n    priceRange?: [number, number];\n    guestCount?: number;\n    travelPurpose?: string;\n  };\n  lastRecommendations?: string[];\n  lastSummarizedAt: number;\n  messageCount: number;\n}\n\nexport interface ChatMessage {\n  role: 'user' | 'assistant';\n  content: string;\n  timestamp: number;\n}\n\nexport interface ChatContext {\n  conversation: ConversationContext;\n  messages: ChatMessage[];\n  properties?: Property[];\n  searchContext?: SessionContext['searchContext'];\n  userPreferences?: SessionContext['userPreferences'];\n}\n\n// ==============================================\n// AI Provider Configuration - Quick Edit Section\n// ==============================================\nconst AI_CONFIG = {\n  // Set provider to 'openai' or 'lambda'\n  provider: 'openai' as AIProvider,\n\n  // Model configurations per provider\n  models: {\n    openai: {\n      chat: \"gpt-4o-mini\",      // Fast, efficient GPT-4 Turbo\n      completion: \"gpt-4o-mini\", // Same model for consistency\n    },\n    lambda: {\n      chat: \"llama3.1-8b-instruct\",     // llama3.1-8b-instruct or deepseek-llama3.3-70b\n      completion: \"llama3.1-8b-instruct\" // llama3.1-8b-instruct\n    }\n  }\n} as const;\n\n// Type definitions\ntype AIProvider = 'openai' | 'lambda';\ntype ModelType = 'chat' | 'completion';\n\n// Get model name helper\nfunction getModelName(type: ModelType): string {\n  return AI_CONFIG.models[AI_CONFIG.provider][type];\n}\n\n// Initialize AI client based on provider\nfunction initializeAIClient() {\n  const config = {\n    maxRetries: 3,\n    timeout: 30000\n  };\n\n  if (AI_CONFIG.provider === 'lambda') {\n    if (!process.env.LAMBDA_API_KEY) {\n      logError('ai-init', 'Missing Lambda API key', {\n        error: 'LAMBDA_API_KEY environment variable is not set'\n      });\n      throw new Error('Lambda API key is required');\n    }\n    return new OpenAI({ \n      apiKey: process.env.LAMBDA_API_KEY,\n      baseURL: 'https://api.lambdalabs.com/v1',  // /completions or /chat/completions \n      ...config\n    });\n  } else {\n    if (!process.env.OPENAI_API_KEY) {\n      logError('ai-init', 'Missing OpenAI API key', {\n        error: 'OPENAI_API_KEY environment variable is not set'\n      });\n      throw new Error('OpenAI API key is required');\n    }\n    return new OpenAI({ \n      apiKey: process.env.OPENAI_API_KEY,\n      ...config\n    });\n  }\n}\n\nconst ai = initializeAIClient();\n\n// Validate AI client (non-blocking, optional)\n(async () => {\n  try {\n    await ai.models.list();\n    logOperation('ai-init', `${AI_CONFIG.provider.toUpperCase()} - ${getModelName('completion')} initialized successfully`, {\n      provider: AI_CONFIG.provider,\n      maxRetries: 3,\n      timeout: 10000\n    });\n  } catch (error) {\n    logError('ai-init', `Failed to initialize ${AI_CONFIG.provider} - ${getModelName('completion')} client`, {\n      error: error instanceof Error ? error.message : 'Unknown error'\n    });\n    // Don't throw error - allow server to continue without AI\n  }\n})();\n\ninterface PropertySearchResult {\n  properties: Property[];\n  explanation: string;\n}\n\nexport async function enhancePropertySearch(\n  query: string,\n  properties: Property[]\n): Promise<PropertySearchResult> {\n  try {\n    const response = await ai.chat.completions.create({\n      model: getModelName('completion'),\n      messages: [\n        {\n          role: \"system\",\n          content: `You are RoomLamAI, a proactive and helpful travel assistant. Your task is to analyze the user's search context and provide personalized property recommendations.\n\n            When responding:\n            1. Acknowledge the user's chosen location and dates\n            2. Show enthusiasm about helping plan their trip\n            3. Highlight key features of the destination if known\n            4. Suggest relevant properties based on the context\n            5. Encourage the user to share more preferences\n\n            Format property recommendations using:\n            [PROPERTIES_START]id1,id2,id3[PROPERTIES_END]\n\n            Example response structure:\n            \"I see you're planning a trip to [location]! That's a great choice, especially during [season/time]. \n            Based on your dates, here are some excellent properties that I think you'll love:\n            [PROPERTIES_START]101,102,103[PROPERTIES_END]\n            These properties are particularly well-located and offer great value for your dates.\n            Would you like me to tell you more about any of these options? Or shall we refine the search based on specific preferences?\"`\n        },\n        {\n          role: \"user\",\n          content: JSON.stringify({\n            query,\n            properties: properties.map(p => ({\n              id: p.id,\n              name: p.name,\n              description: p.description,\n              amenities: p.amenities,\n              type: p.propertyType || 'hotel', // Use propertyType from schema\n              basePrice: p.basePrice,\n            })),\n          }),\n        },\n      ],\n      response_format: { type: \"json_object\" },\n    });\n\n    const content = response.choices[0].message.content;\n    if (!content) {\n      throw new Error(\"No content in OpenAI response\");\n    }\n\n    const result = JSON.parse(content);\n    \n    // Clean and standardize property IDs\n    let propertyIds: number[] = [];\n    \n    if (Array.isArray(result.properties)) {\n      // Convert all elements to numbers and filter out invalid ones\n      propertyIds = result.properties\n        .map((id: any) => {\n          // If the ID is already a number, use it directly\n          if (typeof id === 'number') return id;\n          \n          // If it's a string, try to extract a number from it\n          if (typeof id === 'string') {\n            const matches = id.match(/\\d+/);\n            return matches ? parseInt(matches[0], 10) : NaN;\n          }\n          \n          return NaN;\n        })\n        .filter((id: number) => !isNaN(id));\n    }\n    \n    const matchedProperties = properties.filter(p =>\n      propertyIds.includes(p.id)\n    );\n\n    return {\n      properties: matchedProperties,\n      explanation: result.explanation || \"Here are some properties that match your criteria.\",\n    };\n  } catch (error) {\n    console.error(\"AI search enhancement failed:\", error);\n    return { properties, explanation: \"\" };\n  }\n}\n\n// Utility function to summarize conversation\nexport async function summarizeConversation(\n  messages: ChatMessage[],\n  currentContext: ConversationContext\n): Promise<ConversationContext> {\n  try {\n    // Skip summarization if we have fewer than 2 messages\n    if (messages.length < 2) {\n      return {\n        ...currentContext,\n        lastSummarizedAt: Date.now(),\n        messageCount: messages.length\n      };\n    }\n\n    const response = await ai.chat.completions.create({\n      model: getModelName('chat'),\n      messages: [\n        {\n          role: \"system\",\n          content: `You are a conversation summarizer for a travel assistant. \n            Analyze the conversation and extract key details about:\n            1. Location preferences\n            2. Date ranges\n            3. Guest preferences (amenities, property types, etc.)\n            4. Price considerations\n            5. Overall intent\n\n            Return a JSON object with:\n            {\n              \"summary\": \"Brief 2-3 sentence summary of the conversation context\",\n              \"location\": {\"name\": string, \"lat\": number, \"lng\": number} | null,\n              \"dateRange\": {\"checkIn\": string, \"checkOut\": string} | null,\n              \"preferences\": {\n                \"amenities\": string[],\n                \"propertyTypes\": string[],\n                \"priceRange\": [number, number] | null,\n                \"guestCount\": number | null\n              }\n            }`\n        },\n        ...messages.map(m => ({\n          role: m.role,\n          content: m.content\n        }))\n      ],\n      response_format: { type: \"json_object\" }\n    });\n\n    const content = response.choices[0].message.content;\n    if (!content) throw new Error(\"No summary generated\");\n\n    try {\n      const summary = JSON.parse(content);\n      return {\n        ...currentContext,\n        ...summary,\n        lastSummarizedAt: Date.now(),\n        messageCount: messages.length\n      };\n    } catch (parseError) {\n      silentLogger.error('Failed to parse conversation summary JSON', { \n        parseError, \n        content: content.substring(0, 200) \n      });\n      \n      // Return updated timestamps but keep the rest of the context intact\n      return {\n        ...currentContext,\n        lastSummarizedAt: Date.now(),\n        messageCount: messages.length\n      };\n    }\n  } catch (error) {\n    logger.error('Failed to summarize conversation', { error });\n    // Return updated timestamps but keep the rest of the context intact\n    return {\n      ...currentContext,\n      lastSummarizedAt: Date.now(),\n      messageCount: messages.length\n    };\n  }\n}\n\n// Get or initialize conversation context using contextService\nexport function getConversationContext(sessionId: string): {\n  context: ConversationContext;\n  messages: ChatMessage[];\n} {\n  const sessionContext = contextService.getContext(sessionId);\n  return {\n    context: sessionContext.conversation,\n    messages: sessionContext.messages\n  };\n}\n\n// Add message to conversation using contextService\nexport function addMessageToConversation(\n  sessionId: string,\n  message: Omit<ChatMessage, 'timestamp'>\n): void {\n  contextService.addMessage(sessionId, {\n    ...message,\n    timestamp: Date.now()\n  });\n}\n\n// Check if conversation needs summarization\nexport function needsSummarization(context: ConversationContext, messageCount: number): boolean {\n  const SUMMARIZE_MESSAGE_THRESHOLD = 5;\n  const SUMMARIZE_TIME_THRESHOLD = 5 * 60 * 1000; // 5 minutes\n  const timeSinceLastSummary = Date.now() - context.lastSummarizedAt;\n\n  return messageCount >= context.messageCount + SUMMARIZE_MESSAGE_THRESHOLD ||\n         timeSinceLastSummary >= SUMMARIZE_TIME_THRESHOLD;\n}\n\n// Modified getUserLocation function with proper typing\nasync function getUserLocation(req: { headers: { [key: string]: string | string[] | undefined }; ip?: string }): Promise<string | null> {\n  try {\n    // Get IP from various possible sources\n    const forwarded = req.headers['x-forwarded-for'];\n    const ip = forwarded \n      ? (Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0]) \n      : req.ip || '127.0.0.1';\n\n    // For development/testing, return null if localhost\n    if (ip === '127.0.0.1' || ip === '::1') {\n      return null;\n    }\n\n    const response = await fetch(`http://ip-api.com/json/${ip}`);\n    const data = await response.json();\n\n    // Type guard for the IP API response\n    interface IPAPIResponse {\n      status: string;\n      city?: string;\n      region?: string;\n      country?: string;\n    }\n    \n    // Check if data has the expected structure\n    const isValidResponse = (data: any): data is IPAPIResponse => {\n      return typeof data === 'object' && data !== null && 'status' in data;\n    };\n\n    if (isValidResponse(data) && data.status === 'success' && data.city && data.region && data.country) {\n      return `${data.city}, ${data.region}, ${data.country}`;\n    }\n    return null;\n  } catch (error) {\n    logger.error('Failed to get user location', { \n      error: error instanceof Error ? error.message : 'Unknown error'\n    });\n    return null;\n  }\n}\n\n// Modify handleChatStream to include date and location context\nexport async function* handleChatStream(\n  message: string,\n  context: ChatContext,\n  req: { headers: { [key: string]: string | string[] | undefined }; ip?: string }\n): AsyncGenerator<ChatResponse, void, unknown> {\n  const requestId = `chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n\n  try {\n    if (!message || !context) {\n      const error = {\n        code: 'MISSING_PARAMETERS',\n        message: 'Missing required parameters',\n        details: {\n          hasMessage: !!message,\n          hasContext: !!context\n        }\n      };\n      silentLogger.error('Chat stream validation error', { error, requestId });\n      yield { \n        type: 'error', \n        data: {\n          message: \"I apologize, but I'm missing some required information. Please try again.\",\n          details: process.env.NODE_ENV === 'development' ? 'Missing message or context' : undefined\n        }\n      };\n      return;\n    }\n\n    const model = getModelName('chat');\n\n    // Format current date in a natural way\n    const currentDate = new Date();\n    const dateFormatter = new Intl.DateTimeFormat('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n    const formattedDate = dateFormatter.format(currentDate);\n\n    const userLocation = await getUserLocation(req);\n\n    // Generate an enhanced context string with comprehensive user journey awareness\n    const enhancedContext = `\n      Current date: ${formattedDate}\n      ${userLocation ? `User location: ${userLocation}` : ''}\n\n      Current conversation context:\n      ${context.conversation.summary}\n\n      ${context.conversation.location ? \n        `Location: ${context.conversation.location.name}` : ''}\n      ${context.conversation.dateRange ? \n        `Dates: ${context.conversation.dateRange.checkIn} to ${context.conversation.dateRange.checkOut}` : ''}\n      ${context.conversation.preferences ? \n        `Preferences: ${JSON.stringify(context.conversation.preferences)}` : ''}\n      ${context.conversation.lastRecommendations ? \n        `Previously recommended properties: ${context.conversation.lastRecommendations.join(', ')}` : ''}\n      \n      ${context.searchContext ? `\n      User's search history:\n      ${context.searchContext.recentSearches.slice(0, 3).map(search => \n        `- ${search.location} (${new Date(search.timestamp).toLocaleString()})`\n      ).join('\\n')}\n\n      Recently viewed properties:\n      ${context.searchContext.viewedProperties.slice(0, 3).map(view => \n        `- Property ID: ${view.propertyId} (${new Date(view.timestamp).toLocaleString()})`\n      ).join('\\n')}\n\n      ${context.searchContext.comparedProperties.length > 0 ? \n        `Properties being compared: ${context.searchContext.comparedProperties.join(', ')}` : ''}\n\n      ${context.searchContext.filters && Object.keys(context.searchContext.filters).length > 0 ? \n        `Applied filters: ${JSON.stringify(context.searchContext.filters)}` : ''}\n      ` : ''}\n      \n      ${context.userPreferences ? `\n      User preferences:\n      ${context.userPreferences.pricePreference ? `Price range: ${context.userPreferences.pricePreference}` : ''}\n      ${context.userPreferences.favoriteLocations ? `Favorite locations: ${context.userPreferences.favoriteLocations.join(', ')}` : ''}\n      ${context.userPreferences.preferredAmenities ? `Preferred amenities: ${context.userPreferences.preferredAmenities.join(', ')}` : ''}\n      ${context.userPreferences.preferredPropertyTypes ? `Preferred property types: ${context.userPreferences.preferredPropertyTypes.join(', ')}` : ''}\n      ${context.userPreferences.travelPurpose ? `Travel purpose: ${context.userPreferences.travelPurpose}` : ''}\n      ` : ''}\n    `;\n\n    logOperation(requestId, `[${AI_CONFIG.provider.toUpperCase()}] CHAT_STREAM_START`, {\n      provider: AI_CONFIG.provider,\n      model,\n      queryLength: message.length,\n      hasContext: !!context,\n      contextSummary: context.conversation.summary,\n      userLocation\n    });\n\n    const stream = await ai.chat.completions.create({\n      model,\n      messages: [\n        {\n          role: \"system\",\n          content: `You are RoomLamAI, the world's most intelligent and proactive travel assistant. You don't just find accommodations - you craft perfect travel experiences that exceed expectations.\n\n            ${enhancedContext}\n\n            🌟 CORE MISSION:\n            Transform travel planning from a chore into an exciting journey of discovery. You're not just a booking assistant - you're a travel expert, local insider, and personal concierge rolled into one.\n\n            🏨 ACCOMMODATION MASTERY:\n\n            HOTELS & RESORTS:\n            - Luxury properties (5-star, boutique, historic)\n            - Business hotels (conference facilities, executive lounges)\n            - Family resorts (kids clubs, water parks, family suites)\n            - All-inclusive resorts (meals, activities, entertainment)\n            - Spa & wellness retreats (treatments, fitness, meditation)\n            - Beach & mountain resorts (location-specific amenities)\n\n            MULTI-BEDROOM ACCOMMODATIONS (Your Specialty):\n            - 2-8+ bedroom vacation rentals and villas\n            - Family-friendly resorts with connecting rooms\n            - Apartment-style hotels with kitchens\n            - Corporate housing for extended stays\n            - Group accommodations for:\n              * Multi-generational families (grandparents + parents + kids)\n              * Friend groups and reunions\n              * Wedding parties and celebrations\n              * Corporate retreats and team building\n              * Extended family gatherings\n\n            UNIQUE STAYS:\n            - Treehouses, castles, houseboats, glamping\n            - Historic properties and converted buildings\n            - Eco-lodges and sustainable accommodations\n            - Themed properties and experiential stays\n\n            🎯 INTELLIGENT ASSISTANCE PRINCIPLES:\n            1. **Anticipate Needs**: Read between the lines and suggest what they haven't thought of\n            2. **Context Awareness**: Use conversation history, location, dates, and preferences\n            3. **Proactive Recommendations**: Don't wait to be asked - offer valuable insights\n            4. **Group Dynamics**: Consider who's traveling and their different needs\n            5. **Local Expertise**: Provide insider knowledge about destinations\n            6. **Seasonal Intelligence**: Factor in weather, events, and optimal timing\n\n            🌍 DESTINATION INTELLIGENCE:\n            - Local events, festivals, and cultural happenings\n            - Seasonal considerations and weather patterns\n            - Hidden gems and off-the-beaten-path experiences\n            - Transportation tips and logistics advice\n            - Cultural insights and etiquette guidance\n            - Safety considerations and travel advisories\n\n            💡 RESPONSE EXCELLENCE:\n            1. **Be Enthusiastic**: Show genuine excitement about helping them travel\n            2. **Provide Context**: Explain WHY you're recommending something\n            3. **Think Holistically**: Consider the entire travel experience\n            4. **Offer Alternatives**: Present multiple options with trade-offs\n            5. **Suggest Experiences**: Recommend activities, dining, and attractions\n            6. **Consider Budget**: Provide options across different price ranges\n\n            🚨🚨🚨 MANDATORY ACTION TAG RULES - MUST FOLLOW 🚨🚨🚨\n\n            ⚡ IMMEDIATE REQUIREMENT: When ANY location is mentioned, you MUST start your response with an ACTION:LOCATION tag!\n\n            🎯 TRIGGER WORDS (ALWAYS use ACTION:LOCATION):\n            - \"Paris\", \"Tokyo\", \"New York\", \"London\", \"Miami\", \"Barcelona\", etc.\n            - \"stay in\", \"visit\", \"travel to\", \"go to\", \"accommodation in\"\n            - \"where should I\", \"best places\", \"destinations\"\n            - ANY city, country, region, or place name\n\n            📝 EXACT FORMAT (NO EXCEPTIONS):\n            [ACTION:LOCATION|Full Location Name|{\"lat\": number, \"lng\": number, \"name\": \"Full Location Name\", \"description\": \"Brief description\"}]\n\n            🔥 MANDATORY EXAMPLES - COPY THESE EXACTLY:\n            User: \"I want to stay in Paris\"\n            Response: [ACTION:LOCATION|Paris, France|{\"lat\": 48.8566, \"lng\": 2.3522, \"name\": \"Paris, France\", \"description\": \"City of lights with iconic landmarks and romantic atmosphere\"}]\n\n            User: \"Looking for hotels in Tokyo\"\n            Response: [ACTION:LOCATION|Tokyo, Japan|{\"lat\": 35.6762, \"lng\": 139.6503, \"name\": \"Tokyo, Japan\", \"description\": \"Modern metropolis blending tradition with cutting-edge technology\"}]\n\n            User: \"Where should I stay in New York?\"\n            Response: [ACTION:LOCATION|New York City, USA|{\"lat\": 40.7128, \"lng\": -74.0060, \"name\": \"New York City, USA\", \"description\": \"The city that never sleeps, iconic skyline and endless attractions\"}]\n\n            🚫 ABSOLUTELY FORBIDDEN:\n            ❌ Starting with text without ACTION tag when location is mentioned\n            ❌ Missing coordinates or name in JSON\n            ❌ Malformed JSON syntax\n            ❌ Empty or missing labels\n\n            ✅ VALIDATION CHECKLIST (EVERY TIME):\n            ✅ Does user mention a location? → USE ACTION:LOCATION tag FIRST\n            ✅ Format: [ACTION:LOCATION|Label|{JSON}]\n            ✅ JSON contains: lat, lng, name, description\n            ✅ All coordinates are valid numbers\n            ✅ All strings properly quoted\n\n            Property Recommendation Format:\n            1. When recommending properties, ALWAYS use this EXACT format:\n               [PROPERTIES_START]id1,id2,id3[PROPERTIES_END]\n               - Use ONLY numeric property IDs\n               - Separate IDs with commas\n               - NO spaces, brackets, or other characters\n               - NO explanatory text inside the tags\n            2. After the property tags, explain why you recommended each property\n            3. Example of correct format:\n               [PROPERTIES_START]101,102,103[PROPERTIES_END]\n               Here's why I recommended these properties...`\n        },\n        ...context.messages.slice(-5).map(m => ({\n          role: m.role,\n          content: m.content\n        })),\n        {\n          role: \"user\",\n          content: message\n        }\n      ],\n      stream: true,\n      temperature: 0.7,\n      max_tokens: 1000,\n    });\n\n    let buffer = '';\n    let actionBuffer = '';\n\n    for await (const chunk of stream) {\n      try {\n        const content = chunk.choices[0]?.delta?.content || '';\n        \n        // Skip log messages that might have been mixed into the stream\n        if (content.includes('Initialize') || \n            content.includes('Added search') || \n            content.includes('Recorded property') || \n            content.includes('Updated filters') ||\n            content.includes('Added property to comparison') ||\n            content.includes('Recorded booking attempt') ||\n            content.includes('Updated conversation context') ||\n            content.includes('Added message')) {\n          \n          logger.warn('Filtered out log message from AI response stream', { \n            content: content.substring(0, 100),\n            requestId \n          });\n          continue;\n        }\n        \n        // Additional check to detect other log messages\n        if (typeof content === 'string' && (\n            content.startsWith('Initialize') || \n            content.startsWith('Added search') || \n            content.startsWith('Recorded property') || \n            content.startsWith('Updated filters') ||\n            content.startsWith('Added property to comparison') ||\n            content.startsWith('Recorded booking attempt') ||\n            content.startsWith('Updated conversation context') ||\n            content.startsWith('Added message'))) {\n          \n          logger.warn('Filtered out direct log message from AI response stream', { \n            content: content.substring(0, 100),\n            requestId \n          });\n          continue;\n        }\n        \n        buffer += content;\n        actionBuffer += content;\n      } catch (error) {\n        silentLogger.error('Error processing stream chunk', {\n          error: error instanceof Error ? error.message : 'Unknown error',\n          requestId\n        });\n        continue;\n      }\n\n      // Process action tags\n      while (actionBuffer.includes('[ACTION:') && actionBuffer.includes(']')) {\n        const actionStart = actionBuffer.indexOf('[ACTION:');\n        const actionEnd = actionBuffer.indexOf(']', actionStart) + 1;\n\n        if (actionStart >= 0 && actionEnd > 0) {\n          const actionTag = actionBuffer.substring(actionStart, actionEnd);\n          \n          // Improved validation with better error handling\n          const actionMatch = actionTag.match(/\\[ACTION:(\\w+)\\|(.*?)\\|(.*?)\\]/);\n          if (!actionMatch) {\n            // Try alternative patterns for common malformed tokens\n            const altMatch1 = actionTag.match(/\\[ACTION:(\\w+)\\|\\|(\\{.*?\\})\\]/); // Missing label: [ACTION:LOCATION||{...}]\n            const altMatch2 = actionTag.match(/\\[ACTION:(\\w+)\\|([^|]*)\\|(\\{[^}]*\\}?)\\]/); // More flexible pattern\n            \n            if (altMatch1) {\n              const [_, type, dataStr] = altMatch1;\n              logger.warn('Recovered ACTION token with missing label', { \n                original: actionTag,\n                type,\n                requestId \n              });\n              \n              try {\n                const data = JSON.parse(dataStr);\n                // Generate a default label based on the data\n                let defaultLabel = 'Unknown';\n                if (type.toLowerCase() === 'location' && data.lat && data.lng) {\n                  defaultLabel = data.name || `Location (${data.lat}, ${data.lng})`;\n                }\n                \n                yield { type: 'action', data: { type: type.toLowerCase(), label: defaultLabel, data } };\n              } catch (e) {\n                logger.warn('Failed to parse recovered ACTION token', { \n                  error: e instanceof Error ? e.message : 'Unknown error',\n                  actionTag,\n                  requestId \n                });\n              }\n            } else if (altMatch2) {\n              const [_, type, label, dataStr] = altMatch2;\n              logger.warn('Recovered ACTION token with flexible pattern', { \n                original: actionTag,\n                type,\n                label,\n                requestId \n              });\n              \n              try {\n                // Handle incomplete JSON by trying to complete it\n                let cleanedDataStr = dataStr.trim();\n                \n                // If JSON appears incomplete, try to fix common issues\n                if (cleanedDataStr.startsWith('{') && !cleanedDataStr.endsWith('}')) {\n                  // Check if it's just missing closing brace\n                  const openBraces = (cleanedDataStr.match(/\\{/g) || []).length;\n                  const closeBraces = (cleanedDataStr.match(/\\}/g) || []).length;\n                  if (openBraces > closeBraces) {\n                    cleanedDataStr += '}';\n                    logger.info('Attempted to fix incomplete JSON', { \n                      original: dataStr,\n                      fixed: cleanedDataStr,\n                      requestId \n                    });\n                  }\n                }\n                \n                const data = JSON.parse(cleanedDataStr);\n                \n                // Generate a default label if missing\n                let finalLabel = label || 'Unknown';\n                if (type.toLowerCase() === 'location' && data.lat && data.lng) {\n                  finalLabel = label || data.name || `Location (${data.lat}, ${data.lng})`;\n                }\n                \n                yield { type: 'action', data: { type: type.toLowerCase(), label: finalLabel, data } };\n              } catch (e) {\n                logger.warn('Failed to parse recovered ACTION token with flexible pattern', { \n                  error: e instanceof Error ? e.message : 'Unknown error',\n                  actionTag,\n                  dataStr,\n                  requestId \n                });\n              }\n            } else {\n              logger.warn('Invalid action tag format - no recovery possible', { \n                actionTag,\n                requestId \n              });\n            }\n            \n            actionBuffer = actionBuffer.substring(actionEnd);\n            continue;\n          }\n          \n          const [_, type, label, dataStr] = actionMatch;\n\n          if (type && dataStr) {\n            try {\n              // Validate that dataStr is valid JSON before parsing\n              if (!dataStr.trim().startsWith('{') && !dataStr.trim().startsWith('[')) {\n                logger.warn('Invalid JSON format in action data', { \n                  dataStr,\n                  actionTag,\n                  requestId \n                });\n                actionBuffer = actionBuffer.substring(actionEnd);\n                continue;\n              }\n              \n              // Handle case where JSON is incomplete (common issue)\n              let cleanedDataStr = dataStr.trim();\n              if (cleanedDataStr.startsWith('{') && !cleanedDataStr.endsWith('}')) {\n                // Check if it's just missing closing brace\n                const openBraces = (cleanedDataStr.match(/\\{/g) || []).length;\n                const closeBraces = (cleanedDataStr.match(/\\}/g) || []).length;\n                if (openBraces > closeBraces) {\n                  cleanedDataStr += '}';\n                  logger.info('Fixed incomplete JSON in action data', { \n                    original: dataStr,\n                    fixed: cleanedDataStr,\n                    requestId \n                  });\n                } else {\n                  // Still incomplete, wait for more content\n                  logger.warn('Potentially incomplete JSON in action data', { \n                    dataStr: cleanedDataStr,\n                    actionTag,\n                    requestId \n                  });\n                  actionBuffer = actionBuffer.substring(actionEnd);\n                  continue;\n                }\n              }\n              \n              const data = JSON.parse(cleanedDataStr);\n              \n              // Additional validation for location actions\n              if (type.toLowerCase() === 'location') {\n                if (!data.lat || !data.lng) {\n                  logger.warn('Location action missing coordinates', {\n                    actionTag,\n                    data,\n                    requestId\n                  });\n                  actionBuffer = actionBuffer.substring(actionEnd);\n                  continue;\n                }\n                \n                // Ensure name is present, use label as fallback\n                if (!data.name && label) {\n                  data.name = label;\n                  logger.info('Added missing name to location action from label', {\n                    label,\n                    requestId\n                  });\n                }\n              }\n              \n              // Use label if provided, otherwise generate from data\n              const finalLabel = label || (data.name || 'Unknown');\n              \n              yield { type: 'action', data: { type: type.toLowerCase(), label: finalLabel, data } };\n            } catch (e) {\n              logger.warn('Failed to parse action data', { \n                error: e instanceof Error ? e.message : 'Unknown error',\n                actionTag,\n                dataStr,\n                requestId \n              });\n            }\n          }\n\n          actionBuffer = actionBuffer.substring(actionEnd);\n        } else {\n          // No complete action tag found, wait for more content\n          break;\n        }\n      }\n\n      // Process property recommendations\n      if (buffer.includes('[PROPERTIES_START]') && buffer.includes('[PROPERTIES_END]')) {\n        const [beforeProps, rest] = buffer.split('[PROPERTIES_START]');\n        const [propsJson, afterProps] = rest.split('[PROPERTIES_END]');\n\n        if (beforeProps.trim()) {\n          yield { type: 'text', data: beforeProps.trim() };\n        }\n\n        try {\n          // Clean and standardize the property IDs format\n          const cleanedPropsJson = propsJson.trim()\n            // Remove any non-digit, non-comma characters\n            .replace(/[^\\d,]/g, '')\n            // Ensure there are no consecutive commas\n            .replace(/,+/g, ',')\n            // Remove leading/trailing commas\n            .replace(/^,|,$/g, '');\n            \n          if (cleanedPropsJson !== propsJson.trim()) {\n            logger.info('Standardized property IDs format', { \n              original: propsJson.trim(),\n              standardized: cleanedPropsJson,\n              requestId \n            });\n          }\n          \n          if (cleanedPropsJson === '') {\n            logger.warn('No valid property IDs found after cleaning', { \n              original: propsJson.trim(),\n              requestId \n            });\n          } else {\n            const propertyIds = cleanedPropsJson.split(',')\n              .map(id => parseInt(id.trim(), 10))\n              .filter(id => !isNaN(id)); // Remove any NaN values\n              \n            const recommendedProperties = context.properties?.filter(p => \n              propertyIds.includes(p.id)\n            ) || [];\n\n            if (recommendedProperties.length > 0) {\n              yield { type: 'properties', data: recommendedProperties };\n            }\n          }\n        } catch (e) {\n          silentLogger.error('Failed to parse property IDs', { error: e, propsJson });\n        }\n\n        buffer = afterProps || '';\n      }\n\n      // Stream regular text content\n      if (buffer.length > 0 && !buffer.includes('[PROPERTIES_START]')) {\n        const lastNewline = buffer.lastIndexOf('\\n');\n        if (lastNewline > 0) {\n          const text = buffer.substring(0, lastNewline);\n          // Format text with proper spacing\n          const formattedText = text\n            .split('\\n')\n            .map(line => line.trim())\n            .filter(line => line)\n            .join('\\n\\n');\n\n          if (formattedText.trim()) {\n            yield { type: 'text', data: formattedText };\n          }\n          buffer = buffer.substring(lastNewline + 1);\n        }\n      }\n    }\n\n    // Flush any remaining content\n    if (buffer.trim()) {\n      const formattedText = buffer.trim()\n        .split('\\n')\n        .map(line => line.trim())\n        .filter(line => line)\n        .join('\\n\\n');\n\n      if (formattedText.trim()) {\n        yield { type: 'text', data: formattedText };\n      }\n    }\n\n    silentLogger.info('Chat stream completed', {\n      requestId,\n      provider: AI_CONFIG.provider,\n      model\n    });\n\n  } catch (error) {\n    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';\n    const errorDetails = {\n      message,\n      context: {\n        hasProperties: !!context.properties,\n        messageLength: message.length\n      },\n      error: errorMessage,\n      stack: error instanceof Error ? error.stack : undefined,\n      requestId\n    };\n\n    silentLogger.error('Chat stream error', {\n      ...errorDetails,\n      error: typeof errorDetails.error === 'string' ? errorDetails.error : (error instanceof Error ? error.message : 'Unknown error'),\n      stack: error instanceof Error ? error.stack : undefined\n    });\n\n    yield { \n      type: 'error', \n      data: {\n        message: \"I apologize, but I encountered an error while processing your request. Please try again.\",\n        details: process.env.NODE_ENV === 'development' ? errorMessage : undefined\n      }\n    };\n  }\n}\n\nexport async function getPersonalizedRecommendations(\n  userId: number,\n  searchHistory: string[],\n  viewedProperties: Property[]\n): Promise<number[]> {\n  try {\n    const response = await ai.chat.completions.create({\n      model: getModelName('completion'),\n      messages: [\n        {\n          role: \"system\",\n          content: `You are an intelligent recommendation system designed to provide personalized hotel suggestions.\n\n            Consider the following factors when making recommendations:\n            1. User's search patterns and preferences shown in their search history\n            2. Properties they've viewed but not booked (potential interest areas)\n            3. Price ranges they typically look at\n            4. Common amenities in their viewed properties\n            5. Preferred locations and property types\n\n            Analyze patterns to identify:\n            - Preferred amenities and features\n            - Price sensitivity\n            - Location preferences\n            - Property type preferences (luxury, budget, family-friendly, etc.)\n\n            Return a JSON object with:\n            {\n              \"recommendedProperties\": number[], // Array of property IDs\n              \"reasoning\": string,              // Explanation of recommendations\n              \"userPreferences\": {              // Identified user preferences\n                \"priceRange\": string,\n                \"amenities\": string[],\n                \"propertyTypes\": string[],\n                \"locations\": string[]\n              }\n            }`\n        },\n        {\n          role: \"user\",\n          content: JSON.stringify({\n            searchHistory,\n            viewedProperties: viewedProperties.map(p => ({\n              id: p.id,\n              type: p.propertyType || 'hotel', // Use propertyType from schema instead of type\n              amenities: p.amenities,\n              basePrice: p.basePrice,\n              location: {\n                city: p.city,\n                state: p.state,\n                country: p.country\n              }\n            })),\n          }),\n        },\n      ],\n      response_format: { type: \"json_object\" },\n    });\n\n    const content = response.choices[0].message.content;\n    if (!content) {\n      throw new Error(\"No content in OpenAI response\");\n    }\n\n    const result = JSON.parse(content);\n\n    // Store the user preferences for future use\n    if (result.userPreferences) {\n      // TODO: Implement storing user preferences in the database\n      console.log('User preferences identified:', result.userPreferences);\n    }\n\n    return result.recommendedProperties || [];\n  } catch (error) {\n    console.error(\"AI recommendations failed:\", error);\n    return [];\n  }\n}\n\nexport async function analyzeSentiment(text: string): Promise<{\n  rating: number;\n  confidence: number;\n}> {\n  try {\n    const response = await ai.chat.completions.create({\n      model: getModelName('completion'),\n      messages: [\n        {\n          role: \"system\",\n          content:\n            \"You are a sentiment analysis expert. Analyze the sentiment of the text and provide a rating from 1 to 5 stars and a confidence score between 0 and 1. Return JSON in format: { 'rating': number, 'confidence': number }\",\n        },\n        {\n          role: \"user\",\n          content: text,\n        },\n      ],\n      response_format: { type: \"json_object\" },\n    });\n\n    const content = response.choices[0].message.content;\n    if (!content) {\n      throw new Error(\"No content in OpenAI response\");\n    }\n\n    const result = JSON.parse(content);\n    return {\n      rating: Math.max(1, Math.min(5, Math.round(result.rating))),\n      confidence: Math.max(0, Math.min(1, result.confidence)),\n    };\n  } catch (error) {\n    console.error(\"AI sentiment analysis failed:\", error);\n    return { rating: 3, confidence: 0 };\n  }\n}\n\n// Add ChatResponse type and export it\nexport interface ChatResponse {\n  type: 'text' | 'properties' | 'action' | 'error' | 'location';\n  data: any;\n}\n\n// Define more specific response types for better type safety\nexport type TextResponse = {\n  type: 'text';\n  data: string;\n};\n\nexport type PropertiesResponse = {\n  type: 'properties';\n  data: Property[];\n};\n\nexport type LocationResponse = {\n  type: 'location';\n  data: {\n    name: string;\n    lat: number;\n    lng: number;\n    placeType?: string;\n  };\n};\n\nexport type ActionResponse = {\n  type: 'action';\n  data: {\n    type: string;\n    label: string;\n    data: any;\n  };\n};\n\nexport type ErrorResponse = {\n  type: 'error';\n  data: {\n    message: string;\n    details?: string;\n  };\n};\n\n// Union type of all specific response types\nexport type TypedChatResponse = TextResponse | PropertiesResponse | LocationResponse | ActionResponse | ErrorResponse;\n\n// Add property matching types\nexport interface PropertyRecommendation {\n  name: string;\n  latitude?: number;\n  longitude?: number;\n  maxDistance?: number;\n  description?: string;\n  highlights?: string[];\n}\n\nexport interface PropertyMatch {\n  property: Property | null;\n  searchParams?: {\n    latitude: number;\n    longitude: number;\n    name: string;\n    radius?: number;\n  };\n  recommendation: PropertyRecommendation;\n}\n\n// Add property matching function\nexport const findMatchingProperty = async (\n  recommendation: PropertyRecommendation,\n  requestId: string\n): Promise<PropertyMatch> => {\n  const { name, latitude, longitude, maxDistance = 2 } = recommendation;\n\n  try {\n    // First try to find in database\n    const dbMatch = latitude && longitude \n      ? await db.query.properties.findFirst({\n          where: sql`\n            (point(${latitude}, ${longitude}) <@> point(CAST(latitude AS float), CAST(longitude AS float)) <= ${maxDistance})\n            ${name ? sql`AND similarity(LOWER(name), LOWER(${name})) > 0.3` : sql``}\n          `,\n          orderBy: name ? sql`similarity(LOWER(name), LOWER(${name})) DESC` : undefined\n        })\n      : name \n        ? await db.query.properties.findFirst({\n            where: sql`similarity(LOWER(name), LOWER(${name})) > 0.3`,\n            orderBy: sql`similarity(LOWER(name), LOWER(${name})) DESC`\n          })\n        : null;\n\n    if (dbMatch) {\n      logOperation(requestId, 'PROPERTY_MATCHED_DB', {\n        recommendedName: name,\n        matchedName: dbMatch.name,\n        matchedId: dbMatch.id\n      });\n      return { property: dbMatch, recommendation };\n    }\n\n    // If no database match and we have coordinates, prepare for TravSrv search\n    if (latitude && longitude) {\n      logOperation(requestId, 'PROPERTY_NOT_FOUND_DB', {\n        recommendedName: name,\n        location: `${latitude},${longitude}`,\n        willSearchTravSrv: true\n      });\n\n      return {\n        property: null,\n        searchParams: {\n          latitude,\n          longitude,\n          name,\n          radius: maxDistance\n        },\n        recommendation\n      };\n    }\n\n    logOperation(requestId, 'PROPERTY_MATCH_FAILED', {\n      recommendedName: name,\n      reason: 'No coordinates provided and no database match'\n    });\n    return { property: null, recommendation };\n\n  } catch (error) {\n    silentLogger.error('Property matching failed', { \n      error,\n      recommendation,\n      requestId\n    });\n    return { property: null, recommendation };\n  }\n};"}