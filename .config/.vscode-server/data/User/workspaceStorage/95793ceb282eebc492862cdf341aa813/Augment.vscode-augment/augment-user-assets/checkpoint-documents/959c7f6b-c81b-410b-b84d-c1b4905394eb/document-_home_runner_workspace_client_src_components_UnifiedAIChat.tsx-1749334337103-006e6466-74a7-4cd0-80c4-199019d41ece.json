{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/UnifiedAIChat.tsx"}, "originalCode": "import React, { useState, useRef, useEffect, useCallback } from 'react';\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { Badge } from '@/components/ui/badge';\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar';\nimport { useToast } from '@/hooks/use-toast';\nimport { useLocation } from 'wouter';\nimport {\n  Send,\n  MapPin,\n  Calendar,\n  Users,\n  Star,\n  Hotel,\n  Sparkles,\n  Loader2,\n  X,\n  Maximize2,\n  Minimize2,\n  Bot,\n  User,\n  Globe,\n  Home,\n  Bed,\n  Car,\n  Utensils,\n  Wifi,\n  Waves,\n  Mountain,\n  TreePine,\n  Building,\n  Heart,\n  TrendingUp,\n  Clock,\n  DollarSign,\n  Camera,\n  Navigation\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { getSessionId } from '@/lib/session';\n\ninterface Message {\n  id: string;\n  role: 'user' | 'assistant' | 'system';\n  content: string;\n  timestamp: Date;\n  type?: 'text' | 'location' | 'properties' | 'action' | 'error' | 'experience' | 'insight' | 'recommendation';\n  data?: any;\n  metadata?: {\n    confidence?: number;\n    sources?: string[];\n    actionable?: boolean;\n    priority?: 'low' | 'medium' | 'high';\n  };\n}\n\ninterface PropertyRecommendation {\n  id: number;\n  name: string;\n  type: string;\n  bedrooms?: number;\n  price: number;\n  currency: string;\n  rating: number;\n  image: string;\n  highlights: string[];\n  suitabilityScore?: number;\n  reasonForRecommendation: string;\n}\n\ninterface ExperienceRecommendation {\n  id: string;\n  name: string;\n  type: 'attraction' | 'restaurant' | 'activity' | 'event' | 'transportation';\n  description: string;\n  location: string;\n  duration?: string;\n  priceRange?: string;\n  bestTime?: string;\n  bookingRequired?: boolean;\n  image?: string;\n}\n\ninterface TravelInsight {\n  type: 'weather' | 'events' | 'pricing' | 'crowds' | 'local_tips' | 'safety';\n  title: string;\n  content: string;\n  importance: 'low' | 'medium' | 'high';\n  actionable: boolean;\n  icon?: string;\n}\n\ninterface UnifiedAIChatProps {\n  context?: {\n    location?: string | null;\n    checkIn?: string | null;\n    checkOut?: string | null;\n    guests?: string | null;\n    rooms?: string | null;\n    bedrooms?: string | null;\n    properties?: any[];\n    filters?: any;\n    searchHistory?: any[];\n    groupType?: 'family' | 'friends' | 'corporate' | 'wedding' | 'reunion' | 'multi_generational';\n    travelPurpose?: string;\n    budget?: { min?: number; max?: number; currency?: string };\n    preferences?: {\n      propertyTypes?: string[];\n      amenities?: string[];\n      accessibility?: string[];\n    };\n  };\n  variant?: 'modal' | 'embedded' | 'floating' | 'sidebar';\n  onClose?: () => void;\n  onNavigate?: (path: string, params?: any) => void;\n  onPropertySelect?: (property: any) => void;\n  onSearchUpdate?: (searchParams: any) => void;\n  className?: string;\n  showWelcome?: boolean;\n}\n\nexport default function UnifiedAIChat({\n  context,\n  variant = 'modal',\n  onClose,\n  onNavigate,\n  onPropertySelect,\n  onSearchUpdate,\n  className,\n  showWelcome = true\n}: UnifiedAIChatProps) {\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [input, setInput] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [isMaximized, setIsMaximized] = useState(false);\n  const [sessionId] = useState(() => getSessionId());\n  const [_, navigate] = useLocation();\n  const { toast } = useToast();\n  const scrollAreaRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n  const [conversationContext, setConversationContext] = useState({\n    userPreferences: {},\n    searchHistory: [],\n    currentFocus: null as string | null,\n    travelStyle: null as string | null\n  });\n  const initializationRef = useRef({\n    hasInitialized: false,\n    hasProcessedInitialMessage: false,\n    messageBeingSent: false\n  });\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    if (scrollAreaRef.current) {\n      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Initialize with enhanced welcome message and process any stored initial message\n  useEffect(() => {\n    if (initializationRef.current.hasInitialized) return;\n    initializationRef.current.hasInitialized = true;\n\n    if (showWelcome) {\n      // Add enhanced welcome message with context awareness\n      const welcomeMessage: Message = {\n        id: `system-welcome-${Date.now()}`,\n        role: 'system',\n        content: generateWelcomeMessage(context),\n        timestamp: new Date(),\n        type: 'text',\n        metadata: {\n          confidence: 1.0,\n          actionable: true,\n          priority: 'high'\n        }\n      };\n\n      setMessages([welcomeMessage]);\n    }\n\n    // Check for stored initial message from \"Plan with AI\" button\n    const storedHistory = localStorage.getItem('chatHistory');\n    const triggerFlag = localStorage.getItem('ai_chat_trigger');\n    \n    if (triggerFlag === 'true' && storedHistory && !initializationRef.current.hasProcessedInitialMessage) {\n      try {\n        const parsedHistory = JSON.parse(storedHistory);\n        const lastMessage = parsedHistory[parsedHistory.length - 1];\n        \n        if (lastMessage && lastMessage.role === 'user' && !initializationRef.current.messageBeingSent) {\n          initializationRef.current.hasProcessedInitialMessage = true;\n          initializationRef.current.messageBeingSent = true;\n          \n          // Clear trigger flag\n          localStorage.removeItem('ai_chat_trigger');\n          \n          // Add user message and send to AI\n          const userMessage: Message = {\n            id: lastMessage.id || `user-${Date.now()}`,\n            role: 'user',\n            content: lastMessage.content,\n            timestamp: new Date(),\n            type: 'text'\n          };\n          \n          setMessages(prev => [...prev, userMessage]);\n          sendMessageToAI(lastMessage.content);\n        }\n      } catch (error) {\n        console.error('Failed to process stored message:', error);\n      }\n    }\n  }, [showWelcome, context]);\n\n  // Helper function to generate contextual welcome message\n  const generateWelcomeMessage = (context?: any): string => {\n    if (context?.location && context?.checkIn) {\n      return `🌟 Welcome! I see you're planning a trip to ${context.location} starting ${new Date(context.checkIn).toLocaleDateString()}. I'm your AI travel expert, and I'm excited to help you create an amazing experience!\n\nWhether you need accommodations, local insights, or activity recommendations, I'm here to make your travel planning effortless and enjoyable.`;\n    }\n\n    if (context?.bedrooms && parseInt(context.bedrooms) > 1) {\n      return `🏡 Welcome! I specialize in finding perfect multi-bedroom accommodations for groups and families. With ${context.bedrooms} bedrooms needed, I'll help you discover amazing properties that bring everyone together comfortably.\n\nLet's find you the ideal place to create lasting memories!`;\n    }\n\n    return `✨ Welcome to your AI Travel Companion! I'm not just here to find you accommodations - I'm your personal travel expert who understands what makes trips truly special.\n\nTell me about your travel dreams, and I'll help bring them to life with personalized recommendations, insider tips, and thoughtful suggestions you won't find anywhere else.`;\n  };\n\n  const sendMessageToAI = useCallback(async (messageContent: string) => {\n    if (initializationRef.current.messageBeingSent && messageContent !== input) {\n      // This is an initial message being processed\n    } else {\n      setIsLoading(true);\n    }\n\n    try {\n      const response = await fetch('/api/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: messageContent,\n          context: {\n            ...context,\n            conversation: conversationContext,\n            userPreferences: conversationContext.userPreferences,\n            searchHistory: conversationContext.searchHistory\n          },\n          sessionId,\n          extractLocation: true,\n          enhancedMode: true\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const reader = response.body?.getReader();\n      if (!reader) {\n        throw new Error('No response body');\n      }\n\n      let assistantMessage: Message = {\n        id: `assistant-${Date.now()}`,\n        role: 'assistant',\n        content: '',\n        timestamp: new Date(),\n        type: 'text'\n      };\n\n      setMessages(prev => [...prev, assistantMessage]);\n\n      const decoder = new TextDecoder();\n      let buffer = '';\n\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) break;\n\n        buffer += decoder.decode(value, { stream: true });\n        const lines = buffer.split('\\n');\n        buffer = lines.pop() || '';\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            const data = line.slice(6);\n            if (data === '[DONE]') continue;\n\n            try {\n              const parsed = JSON.parse(data);\n              \n              if (parsed.type === 'text') {\n                assistantMessage.content += parsed.data;\n                setMessages(prev =>\n                  prev.map(msg =>\n                    msg.id === assistantMessage.id\n                      ? { ...msg, content: assistantMessage.content }\n                      : msg\n                  )\n                );\n              } else if (parsed.type === 'location') {\n                handleLocationResponse(parsed.data);\n              } else if (parsed.type === 'properties') {\n                handlePropertiesResponse(parsed.data);\n              } else if (parsed.type === 'action') {\n                handleActionResponse(parsed.data);\n              } else if (parsed.type === 'experience') {\n                handleExperienceResponse(parsed.data);\n              } else if (parsed.type === 'insight') {\n                handleInsightResponse(parsed.data);\n              }\n            } catch (error) {\n              console.error('Failed to parse streaming response:', error);\n            }\n          }\n        }\n      }\n\n    } catch (error) {\n      console.error('Chat error:', error);\n      toast({\n        title: 'Error',\n        description: 'Failed to send message. Please try again.',\n        variant: 'destructive',\n      });\n      \n      const errorMessage: Message = {\n        id: `error-${Date.now()}`,\n        role: 'assistant',\n        content: 'I apologize, but I encountered an error. Please try again.',\n        timestamp: new Date(),\n        type: 'error'\n      };\n      \n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n      initializationRef.current.messageBeingSent = false;\n    }\n  }, [context, sessionId, toast, input]);\n\n  const handleLocationResponse = (locationData: any) => {\n    // Update conversation context\n    setConversationContext(prev => ({\n      ...prev,\n      currentFocus: 'location',\n      searchHistory: [...prev.searchHistory, { location: locationData.name, timestamp: new Date() }]\n    }));\n\n    // Navigate to search results with enhanced parameters\n    const searchParams = new URLSearchParams({\n      locationName: locationData.name,\n      lat: locationData.lat.toString(),\n      lng: locationData.lng.toString(),\n      checkIn: context?.checkIn || new Date(Date.now() + 86400000).toISOString().split('T')[0],\n      checkOut: context?.checkOut || new Date(Date.now() + 2 * 86400000).toISOString().split('T')[0],\n      guests: context?.guests || '2',\n      rooms: context?.rooms || '1',\n      bedrooms: context?.bedrooms || '1',\n      ...(context?.groupType && { groupType: context.groupType })\n    });\n\n    if (onNavigate) {\n      onNavigate(`/results?${searchParams.toString()}`);\n    } else {\n      navigate(`/results?${searchParams.toString()}`);\n    }\n\n    if (onSearchUpdate) {\n      onSearchUpdate({\n        location: locationData.name,\n        coordinates: { lat: locationData.lat, lng: locationData.lng }\n      });\n    }\n\n    onClose?.();\n  };\n\n  const handlePropertiesResponse = (properties: any[]) => {\n    const propertyMessage: Message = {\n      id: `properties-${Date.now()}`,\n      role: 'assistant',\n      content: `I found ${properties.length} perfect accommodations for you:`,\n      timestamp: new Date(),\n      type: 'properties',\n      data: properties,\n      metadata: {\n        confidence: 0.9,\n        actionable: true,\n        priority: 'high'\n      }\n    };\n\n    setMessages(prev => [...prev, propertyMessage]);\n  };\n\n  const handleActionResponse = (actionData: any) => {\n    const actionMessage: Message = {\n      id: `action-${Date.now()}`,\n      role: 'assistant',\n      content: actionData.label,\n      timestamp: new Date(),\n      type: 'action',\n      data: actionData,\n      metadata: {\n        actionable: true,\n        priority: 'medium'\n      }\n    };\n\n    setMessages(prev => [...prev, actionMessage]);\n  };\n\n  const handleExperienceResponse = (experiences: ExperienceRecommendation[]) => {\n    const experienceMessage: Message = {\n      id: `experience-${Date.now()}`,\n      role: 'assistant',\n      content: `Here are some amazing experiences I recommend:`,\n      timestamp: new Date(),\n      type: 'experience',\n      data: experiences,\n      metadata: {\n        confidence: 0.8,\n        actionable: true,\n        priority: 'medium'\n      }\n    };\n\n    setMessages(prev => [...prev, experienceMessage]);\n  };\n\n  const handleInsightResponse = (insights: TravelInsight[]) => {\n    const insightMessage: Message = {\n      id: `insight-${Date.now()}`,\n      role: 'assistant',\n      content: `Here are some important travel insights:`,\n      timestamp: new Date(),\n      type: 'insight',\n      data: insights,\n      metadata: {\n        confidence: 0.9,\n        actionable: true,\n        priority: insights.some(i => i.importance === 'high') ? 'high' : 'medium'\n      }\n    };\n\n    setMessages(prev => [...prev, insightMessage]);\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!input.trim() || isLoading) return;\n\n    const userMessage: Message = {\n      id: `user-${Date.now()}`,\n      role: 'user',\n      content: input.trim(),\n      timestamp: new Date(),\n      type: 'text'\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    sendMessageToAI(input.trim());\n    setInput('');\n  };\n\n  const handleQuickAction = (action: string) => {\n    setInput(action);\n    inputRef.current?.focus();\n  };\n\n  const containerClasses = cn(\n    'flex flex-col',\n    {\n      'fixed inset-4 z-50 bg-background border rounded-lg shadow-lg': variant === 'modal',\n      'w-full h-full': variant === 'embedded',\n      'fixed bottom-4 right-4 w-96 h-[500px] z-50 bg-background border rounded-lg shadow-lg': variant === 'floating',\n      'fixed inset-2 z-50': variant === 'modal' && isMaximized,\n    },\n    className\n  );\n\n  const quickActions = [\n    { icon: <Globe className=\"w-4 h-4\" />, label: 'Explore Destinations', action: 'Show me popular travel destinations' },\n    { icon: <Calendar className=\"w-4 h-4\" />, label: 'Plan My Trip', action: 'Help me plan a trip' },\n    { icon: <Star className=\"w-4 h-4\" />, label: 'Find Deals', action: 'Find me the best hotel deals' },\n    { icon: <Sparkles className=\"w-4 h-4\" />, label: 'Inspire Me', action: 'Inspire me with unique travel ideas' }\n  ];\n\n  return (\n    <Card className={containerClasses}>\n      <CardHeader className=\"border-b bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20\">\n        <CardTitle className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center\">\n              <Bot className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <h2 className=\"text-xl font-bold\">AI Travel Companion</h2>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                Your intelligent travel planning assistant\n              </p>\n            </div>\n          </div>\n          <div className=\"flex items-center gap-2\">\n            {variant === 'modal' && (\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => setIsMaximized(!isMaximized)}\n              >\n                {isMaximized ? <Minimize2 className=\"w-4 h-4\" /> : <Maximize2 className=\"w-4 h-4\" />}\n              </Button>\n            )}\n            {onClose && (\n              <Button variant=\"ghost\" size=\"icon\" onClick={onClose}>\n                <X className=\"w-4 h-4\" />\n              </Button>\n            )}\n          </div>\n        </CardTitle>\n      </CardHeader>\n\n      <CardContent className=\"flex-1 p-0\">\n        <ScrollArea ref={scrollAreaRef} className=\"h-full p-4\">\n          <div className=\"space-y-4\">\n            {messages.map((message) => (\n              <div\n                key={message.id}\n                className={cn(\n                  'flex gap-3',\n                  message.role === 'user' ? 'justify-end' : 'justify-start'\n                )}\n              >\n                {message.role !== 'user' && (\n                  <Avatar className=\"w-8 h-8\">\n                    <AvatarFallback>\n                      <Bot className=\"w-4 h-4\" />\n                    </AvatarFallback>\n                  </Avatar>\n                )}\n                \n                <div\n                  className={cn(\n                    'max-w-[80%] rounded-lg px-4 py-2',\n                    message.role === 'user'\n                      ? 'bg-primary text-primary-foreground'\n                      : message.role === 'system'\n                      ? 'bg-muted text-muted-foreground'\n                      : 'bg-secondary text-secondary-foreground'\n                  )}\n                >\n                  <p className=\"text-sm\">{message.content}</p>\n                  {message.type === 'properties' && message.data && (\n                    <div className=\"mt-2 space-y-2\">\n                      {message.data.slice(0, 3).map((property: any, index: number) => (\n                        <div key={index} className=\"p-2 border rounded bg-background\">\n                          <h4 className=\"font-medium\">{property.name}</h4>\n                          <p className=\"text-xs text-muted-foreground\">{property.address}</p>\n                          <div className=\"flex items-center gap-2 mt-1\">\n                            <Star className=\"w-3 h-3 fill-yellow-400 text-yellow-400\" />\n                            <span className=\"text-xs\">{property.rating || 'N/A'}</span>\n                            <span className=\"text-xs font-medium\">${property.basePrice}/night</span>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  )}\n                </div>\n\n                {message.role === 'user' && (\n                  <Avatar className=\"w-8 h-8\">\n                    <AvatarFallback>\n                      <User className=\"w-4 h-4\" />\n                    </AvatarFallback>\n                  </Avatar>\n                )}\n              </div>\n            ))}\n\n            {isLoading && (\n              <div className=\"flex gap-3 justify-start\">\n                <Avatar className=\"w-8 h-8\">\n                  <AvatarFallback>\n                    <Bot className=\"w-4 h-4\" />\n                  </AvatarFallback>\n                </Avatar>\n                <div className=\"bg-secondary text-secondary-foreground rounded-lg px-4 py-2\">\n                  <div className=\"flex items-center gap-2\">\n                    <Loader2 className=\"w-4 h-4 animate-spin\" />\n                    <span className=\"text-sm\">Thinking...</span>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {messages.length === 1 && (\n              <div className=\"grid grid-cols-2 gap-2 mt-4\">\n                {quickActions.map((action, index) => (\n                  <Button\n                    key={index}\n                    variant=\"outline\"\n                    size=\"sm\"\n                    className=\"h-auto p-3 text-left\"\n                    onClick={() => handleQuickAction(action.action)}\n                  >\n                    <div className=\"flex items-center gap-2\">\n                      {action.icon}\n                      <span className=\"text-xs\">{action.label}</span>\n                    </div>\n                  </Button>\n                ))}\n              </div>\n            )}\n          </div>\n        </ScrollArea>\n      </CardContent>\n\n      <CardFooter className=\"border-t p-4 bg-gray-50 dark:bg-gray-900/50\">\n        <form onSubmit={handleSubmit} className=\"flex gap-3 w-full\">\n          <Input\n            ref={inputRef}\n            value={input}\n            onChange={(e) => setInput(e.target.value)}\n            placeholder=\"Ask me anything about travel...\"\n            className=\"flex-1\"\n            disabled={isLoading}\n          />\n          <Button type=\"submit\" disabled={!input.trim() || isLoading}>\n            <Send className=\"w-4 h-4\" />\n          </Button>\n        </form>\n      </CardFooter>\n    </Card>\n  );\n}\n", "modifiedCode": "import React, { useState, useRef, useEffect, useCallback } from 'react';\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { Badge } from '@/components/ui/badge';\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar';\nimport { useToast } from '@/hooks/use-toast';\nimport { useLocation } from 'wouter';\nimport {\n  Send,\n  MapPin,\n  Calendar,\n  Users,\n  Star,\n  Hotel,\n  Sparkles,\n  Loader2,\n  X,\n  Maximize2,\n  Minimize2,\n  Bot,\n  User,\n  Globe,\n  Home,\n  Bed,\n  Car,\n  Utensils,\n  Wifi,\n  Waves,\n  Mountain,\n  TreePine,\n  Building,\n  Heart,\n  TrendingUp,\n  Clock,\n  DollarSign,\n  Camera,\n  Navigation\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { getSessionId } from '@/lib/session';\n\ninterface Message {\n  id: string;\n  role: 'user' | 'assistant' | 'system';\n  content: string;\n  timestamp: Date;\n  type?: 'text' | 'location' | 'properties' | 'action' | 'error' | 'experience' | 'insight' | 'recommendation';\n  data?: any;\n  metadata?: {\n    confidence?: number;\n    sources?: string[];\n    actionable?: boolean;\n    priority?: 'low' | 'medium' | 'high';\n  };\n}\n\ninterface PropertyRecommendation {\n  id: number;\n  name: string;\n  type: string;\n  bedrooms?: number;\n  price: number;\n  currency: string;\n  rating: number;\n  image: string;\n  highlights: string[];\n  suitabilityScore?: number;\n  reasonForRecommendation: string;\n}\n\ninterface ExperienceRecommendation {\n  id: string;\n  name: string;\n  type: 'attraction' | 'restaurant' | 'activity' | 'event' | 'transportation';\n  description: string;\n  location: string;\n  duration?: string;\n  priceRange?: string;\n  bestTime?: string;\n  bookingRequired?: boolean;\n  image?: string;\n}\n\ninterface TravelInsight {\n  type: 'weather' | 'events' | 'pricing' | 'crowds' | 'local_tips' | 'safety';\n  title: string;\n  content: string;\n  importance: 'low' | 'medium' | 'high';\n  actionable: boolean;\n  icon?: string;\n}\n\ninterface UnifiedAIChatProps {\n  context?: {\n    location?: string | null;\n    checkIn?: string | null;\n    checkOut?: string | null;\n    guests?: string | null;\n    rooms?: string | null;\n    bedrooms?: string | null;\n    properties?: any[];\n    filters?: any;\n    searchHistory?: any[];\n    groupType?: 'family' | 'friends' | 'corporate' | 'wedding' | 'reunion' | 'multi_generational';\n    travelPurpose?: string;\n    budget?: { min?: number; max?: number; currency?: string };\n    preferences?: {\n      propertyTypes?: string[];\n      amenities?: string[];\n      accessibility?: string[];\n    };\n  };\n  variant?: 'modal' | 'embedded' | 'floating' | 'sidebar';\n  onClose?: () => void;\n  onNavigate?: (path: string, params?: any) => void;\n  onPropertySelect?: (property: any) => void;\n  onSearchUpdate?: (searchParams: any) => void;\n  className?: string;\n  showWelcome?: boolean;\n}\n\nexport default function UnifiedAIChat({\n  context,\n  variant = 'modal',\n  onClose,\n  onNavigate,\n  onPropertySelect,\n  onSearchUpdate,\n  className,\n  showWelcome = true\n}: UnifiedAIChatProps) {\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [input, setInput] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [isMaximized, setIsMaximized] = useState(false);\n  const [sessionId] = useState(() => getSessionId());\n  const [_, navigate] = useLocation();\n  const { toast } = useToast();\n  const scrollAreaRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n  const [conversationContext, setConversationContext] = useState({\n    userPreferences: {},\n    searchHistory: [],\n    currentFocus: null as string | null,\n    travelStyle: null as string | null\n  });\n  const initializationRef = useRef({\n    hasInitialized: false,\n    hasProcessedInitialMessage: false,\n    messageBeingSent: false\n  });\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    if (scrollAreaRef.current) {\n      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Initialize with enhanced welcome message and process any stored initial message\n  useEffect(() => {\n    if (initializationRef.current.hasInitialized) return;\n    initializationRef.current.hasInitialized = true;\n\n    if (showWelcome) {\n      // Add enhanced welcome message with context awareness\n      const welcomeMessage: Message = {\n        id: `system-welcome-${Date.now()}`,\n        role: 'system',\n        content: generateWelcomeMessage(context),\n        timestamp: new Date(),\n        type: 'text',\n        metadata: {\n          confidence: 1.0,\n          actionable: true,\n          priority: 'high'\n        }\n      };\n\n      setMessages([welcomeMessage]);\n    }\n\n    // Check for stored initial message from \"Plan with AI\" button\n    const storedHistory = localStorage.getItem('chatHistory');\n    const triggerFlag = localStorage.getItem('ai_chat_trigger');\n    \n    if (triggerFlag === 'true' && storedHistory && !initializationRef.current.hasProcessedInitialMessage) {\n      try {\n        const parsedHistory = JSON.parse(storedHistory);\n        const lastMessage = parsedHistory[parsedHistory.length - 1];\n        \n        if (lastMessage && lastMessage.role === 'user' && !initializationRef.current.messageBeingSent) {\n          initializationRef.current.hasProcessedInitialMessage = true;\n          initializationRef.current.messageBeingSent = true;\n          \n          // Clear trigger flag\n          localStorage.removeItem('ai_chat_trigger');\n          \n          // Add user message and send to AI\n          const userMessage: Message = {\n            id: lastMessage.id || `user-${Date.now()}`,\n            role: 'user',\n            content: lastMessage.content,\n            timestamp: new Date(),\n            type: 'text'\n          };\n          \n          setMessages(prev => [...prev, userMessage]);\n          sendMessageToAI(lastMessage.content);\n        }\n      } catch (error) {\n        console.error('Failed to process stored message:', error);\n      }\n    }\n  }, [showWelcome, context]);\n\n  // Helper function to generate contextual welcome message\n  const generateWelcomeMessage = (context?: any): string => {\n    if (context?.location && context?.checkIn) {\n      return `🌟 Welcome! I see you're planning a trip to ${context.location} starting ${new Date(context.checkIn).toLocaleDateString()}. I'm your AI travel expert, and I'm excited to help you create an amazing experience!\n\nWhether you need accommodations, local insights, or activity recommendations, I'm here to make your travel planning effortless and enjoyable.`;\n    }\n\n    if (context?.bedrooms && parseInt(context.bedrooms) > 1) {\n      return `🏡 Welcome! I specialize in finding perfect multi-bedroom accommodations for groups and families. With ${context.bedrooms} bedrooms needed, I'll help you discover amazing properties that bring everyone together comfortably.\n\nLet's find you the ideal place to create lasting memories!`;\n    }\n\n    return `✨ Welcome to your AI Travel Companion! I'm not just here to find you accommodations - I'm your personal travel expert who understands what makes trips truly special.\n\nTell me about your travel dreams, and I'll help bring them to life with personalized recommendations, insider tips, and thoughtful suggestions you won't find anywhere else.`;\n  };\n\n  const sendMessageToAI = useCallback(async (messageContent: string) => {\n    if (initializationRef.current.messageBeingSent && messageContent !== input) {\n      // This is an initial message being processed\n    } else {\n      setIsLoading(true);\n    }\n\n    try {\n      const response = await fetch('/api/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: messageContent,\n          context: {\n            ...context,\n            conversation: conversationContext,\n            userPreferences: conversationContext.userPreferences,\n            searchHistory: conversationContext.searchHistory\n          },\n          sessionId,\n          extractLocation: true,\n          enhancedMode: true\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const reader = response.body?.getReader();\n      if (!reader) {\n        throw new Error('No response body');\n      }\n\n      let assistantMessage: Message = {\n        id: `assistant-${Date.now()}`,\n        role: 'assistant',\n        content: '',\n        timestamp: new Date(),\n        type: 'text'\n      };\n\n      setMessages(prev => [...prev, assistantMessage]);\n\n      const decoder = new TextDecoder();\n      let buffer = '';\n\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) break;\n\n        buffer += decoder.decode(value, { stream: true });\n        const lines = buffer.split('\\n');\n        buffer = lines.pop() || '';\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            const data = line.slice(6);\n            if (data === '[DONE]') continue;\n\n            try {\n              const parsed = JSON.parse(data);\n              \n              if (parsed.type === 'text') {\n                assistantMessage.content += parsed.data;\n                setMessages(prev =>\n                  prev.map(msg =>\n                    msg.id === assistantMessage.id\n                      ? { ...msg, content: assistantMessage.content }\n                      : msg\n                  )\n                );\n              } else if (parsed.type === 'location') {\n                handleLocationResponse(parsed.data);\n              } else if (parsed.type === 'properties') {\n                handlePropertiesResponse(parsed.data);\n              } else if (parsed.type === 'action') {\n                handleActionResponse(parsed.data);\n              } else if (parsed.type === 'experience') {\n                handleExperienceResponse(parsed.data);\n              } else if (parsed.type === 'insight') {\n                handleInsightResponse(parsed.data);\n              }\n            } catch (error) {\n              console.error('Failed to parse streaming response:', error);\n            }\n          }\n        }\n      }\n\n    } catch (error) {\n      console.error('Chat error:', error);\n      toast({\n        title: 'Error',\n        description: 'Failed to send message. Please try again.',\n        variant: 'destructive',\n      });\n      \n      const errorMessage: Message = {\n        id: `error-${Date.now()}`,\n        role: 'assistant',\n        content: 'I apologize, but I encountered an error. Please try again.',\n        timestamp: new Date(),\n        type: 'error'\n      };\n      \n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n      initializationRef.current.messageBeingSent = false;\n    }\n  }, [context, sessionId, toast, input]);\n\n  const handleLocationResponse = (locationData: any) => {\n    // Update conversation context\n    setConversationContext(prev => ({\n      ...prev,\n      currentFocus: 'location',\n      searchHistory: [...prev.searchHistory, { location: locationData.name, timestamp: new Date() }]\n    }));\n\n    // Navigate to search results with enhanced parameters\n    const searchParams = new URLSearchParams({\n      locationName: locationData.name,\n      lat: locationData.lat.toString(),\n      lng: locationData.lng.toString(),\n      checkIn: context?.checkIn || new Date(Date.now() + 86400000).toISOString().split('T')[0],\n      checkOut: context?.checkOut || new Date(Date.now() + 2 * 86400000).toISOString().split('T')[0],\n      guests: context?.guests || '2',\n      rooms: context?.rooms || '1',\n      bedrooms: context?.bedrooms || '1',\n      ...(context?.groupType && { groupType: context.groupType })\n    });\n\n    if (onNavigate) {\n      onNavigate(`/results?${searchParams.toString()}`);\n    } else {\n      navigate(`/results?${searchParams.toString()}`);\n    }\n\n    if (onSearchUpdate) {\n      onSearchUpdate({\n        location: locationData.name,\n        coordinates: { lat: locationData.lat, lng: locationData.lng }\n      });\n    }\n\n    onClose?.();\n  };\n\n  const handlePropertiesResponse = (properties: any[]) => {\n    const propertyMessage: Message = {\n      id: `properties-${Date.now()}`,\n      role: 'assistant',\n      content: `I found ${properties.length} perfect accommodations for you:`,\n      timestamp: new Date(),\n      type: 'properties',\n      data: properties,\n      metadata: {\n        confidence: 0.9,\n        actionable: true,\n        priority: 'high'\n      }\n    };\n\n    setMessages(prev => [...prev, propertyMessage]);\n  };\n\n  const handleActionResponse = (actionData: any) => {\n    const actionMessage: Message = {\n      id: `action-${Date.now()}`,\n      role: 'assistant',\n      content: actionData.label,\n      timestamp: new Date(),\n      type: 'action',\n      data: actionData,\n      metadata: {\n        actionable: true,\n        priority: 'medium'\n      }\n    };\n\n    setMessages(prev => [...prev, actionMessage]);\n  };\n\n  const handleExperienceResponse = (experiences: ExperienceRecommendation[]) => {\n    const experienceMessage: Message = {\n      id: `experience-${Date.now()}`,\n      role: 'assistant',\n      content: `Here are some amazing experiences I recommend:`,\n      timestamp: new Date(),\n      type: 'experience',\n      data: experiences,\n      metadata: {\n        confidence: 0.8,\n        actionable: true,\n        priority: 'medium'\n      }\n    };\n\n    setMessages(prev => [...prev, experienceMessage]);\n  };\n\n  const handleInsightResponse = (insights: TravelInsight[]) => {\n    const insightMessage: Message = {\n      id: `insight-${Date.now()}`,\n      role: 'assistant',\n      content: `Here are some important travel insights:`,\n      timestamp: new Date(),\n      type: 'insight',\n      data: insights,\n      metadata: {\n        confidence: 0.9,\n        actionable: true,\n        priority: insights.some(i => i.importance === 'high') ? 'high' : 'medium'\n      }\n    };\n\n    setMessages(prev => [...prev, insightMessage]);\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!input.trim() || isLoading) return;\n\n    const userMessage: Message = {\n      id: `user-${Date.now()}`,\n      role: 'user',\n      content: input.trim(),\n      timestamp: new Date(),\n      type: 'text'\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    sendMessageToAI(input.trim());\n    setInput('');\n  };\n\n  const handleQuickAction = (action: string) => {\n    setInput(action);\n    inputRef.current?.focus();\n  };\n\n  const containerClasses = cn(\n    'flex flex-col',\n    {\n      'fixed inset-4 z-50 bg-background border rounded-lg shadow-lg': variant === 'modal',\n      'w-full h-full': variant === 'embedded',\n      'fixed bottom-4 right-4 w-96 h-[500px] z-50 bg-background border rounded-lg shadow-lg': variant === 'floating',\n      'fixed inset-2 z-50': variant === 'modal' && isMaximized,\n    },\n    className\n  );\n\n  // Enhanced quick actions based on context\n  const getQuickActions = () => {\n    const baseActions = [\n      { icon: <Globe className=\"w-4 h-4\" />, label: 'Explore Destinations', action: 'Show me trending travel destinations for this season' },\n      { icon: <Home className=\"w-4 h-4\" />, label: 'Multi-Bedroom Stays', action: 'Find me vacation rentals with multiple bedrooms for a group' },\n      { icon: <Sparkles className=\"w-4 h-4\" />, label: 'Unique Experiences', action: 'Suggest unique accommodations and local experiences' },\n      { icon: <TrendingUp className=\"w-4 h-4\" />, label: 'Best Deals', action: 'Find me the best accommodation deals and travel tips' }\n    ];\n\n    // Context-aware actions\n    if (context?.location) {\n      baseActions.unshift({\n        icon: <MapPin className=\"w-4 h-4\" />,\n        label: `Explore ${context.location}`,\n        action: `Tell me about the best accommodations and experiences in ${context.location}`\n      });\n    }\n\n    if (context?.bedrooms && parseInt(context.bedrooms) > 1) {\n      baseActions.unshift({\n        icon: <Bed className=\"w-4 h-4\" />,\n        label: `${context.bedrooms}-Bedroom Options`,\n        action: `Show me ${context.bedrooms}-bedroom accommodations perfect for my group`\n      });\n    }\n\n    if (context?.groupType) {\n      const groupLabels = {\n        family: 'Family-Friendly',\n        friends: 'Group Getaway',\n        corporate: 'Business Travel',\n        wedding: 'Wedding Party',\n        reunion: 'Family Reunion',\n        multi_generational: 'Multi-Gen Trip'\n      };\n\n      baseActions.unshift({\n        icon: <Users className=\"w-4 h-4\" />,\n        label: groupLabels[context.groupType] || 'Group Travel',\n        action: `Help me plan the perfect ${context.groupType} trip with ideal accommodations`\n      });\n    }\n\n    return baseActions.slice(0, 6); // Limit to 6 actions\n  };\n\n  const quickActions = getQuickActions();\n\n  return (\n    <Card className={containerClasses}>\n      <CardHeader className=\"border-b bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20\">\n        <CardTitle className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center\">\n              <Bot className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <h2 className=\"text-xl font-bold\">AI Travel Companion</h2>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                Your intelligent travel planning assistant\n              </p>\n            </div>\n          </div>\n          <div className=\"flex items-center gap-2\">\n            {variant === 'modal' && (\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => setIsMaximized(!isMaximized)}\n              >\n                {isMaximized ? <Minimize2 className=\"w-4 h-4\" /> : <Maximize2 className=\"w-4 h-4\" />}\n              </Button>\n            )}\n            {onClose && (\n              <Button variant=\"ghost\" size=\"icon\" onClick={onClose}>\n                <X className=\"w-4 h-4\" />\n              </Button>\n            )}\n          </div>\n        </CardTitle>\n      </CardHeader>\n\n      <CardContent className=\"flex-1 p-0\">\n        <ScrollArea ref={scrollAreaRef} className=\"h-full p-4\">\n          <div className=\"space-y-4\">\n            {messages.map((message) => (\n              <div\n                key={message.id}\n                className={cn(\n                  'flex gap-3',\n                  message.role === 'user' ? 'justify-end' : 'justify-start'\n                )}\n              >\n                {message.role !== 'user' && (\n                  <Avatar className=\"w-8 h-8\">\n                    <AvatarFallback>\n                      <Bot className=\"w-4 h-4\" />\n                    </AvatarFallback>\n                  </Avatar>\n                )}\n                \n                <div\n                  className={cn(\n                    'max-w-[80%] rounded-lg px-4 py-2',\n                    message.role === 'user'\n                      ? 'bg-primary text-primary-foreground'\n                      : message.role === 'system'\n                      ? 'bg-muted text-muted-foreground'\n                      : 'bg-secondary text-secondary-foreground'\n                  )}\n                >\n                  <p className=\"text-sm\">{message.content}</p>\n                  {message.type === 'properties' && message.data && (\n                    <div className=\"mt-2 space-y-2\">\n                      {message.data.slice(0, 3).map((property: any, index: number) => (\n                        <div key={index} className=\"p-2 border rounded bg-background\">\n                          <h4 className=\"font-medium\">{property.name}</h4>\n                          <p className=\"text-xs text-muted-foreground\">{property.address}</p>\n                          <div className=\"flex items-center gap-2 mt-1\">\n                            <Star className=\"w-3 h-3 fill-yellow-400 text-yellow-400\" />\n                            <span className=\"text-xs\">{property.rating || 'N/A'}</span>\n                            <span className=\"text-xs font-medium\">${property.basePrice}/night</span>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  )}\n                </div>\n\n                {message.role === 'user' && (\n                  <Avatar className=\"w-8 h-8\">\n                    <AvatarFallback>\n                      <User className=\"w-4 h-4\" />\n                    </AvatarFallback>\n                  </Avatar>\n                )}\n              </div>\n            ))}\n\n            {isLoading && (\n              <div className=\"flex gap-3 justify-start\">\n                <Avatar className=\"w-8 h-8\">\n                  <AvatarFallback>\n                    <Bot className=\"w-4 h-4\" />\n                  </AvatarFallback>\n                </Avatar>\n                <div className=\"bg-secondary text-secondary-foreground rounded-lg px-4 py-2\">\n                  <div className=\"flex items-center gap-2\">\n                    <Loader2 className=\"w-4 h-4 animate-spin\" />\n                    <span className=\"text-sm\">Thinking...</span>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {messages.length === 1 && (\n              <div className=\"grid grid-cols-2 gap-2 mt-4\">\n                {quickActions.map((action, index) => (\n                  <Button\n                    key={index}\n                    variant=\"outline\"\n                    size=\"sm\"\n                    className=\"h-auto p-3 text-left\"\n                    onClick={() => handleQuickAction(action.action)}\n                  >\n                    <div className=\"flex items-center gap-2\">\n                      {action.icon}\n                      <span className=\"text-xs\">{action.label}</span>\n                    </div>\n                  </Button>\n                ))}\n              </div>\n            )}\n          </div>\n        </ScrollArea>\n      </CardContent>\n\n      <CardFooter className=\"border-t p-4 bg-gray-50 dark:bg-gray-900/50\">\n        <form onSubmit={handleSubmit} className=\"flex gap-3 w-full\">\n          <Input\n            ref={inputRef}\n            value={input}\n            onChange={(e) => setInput(e.target.value)}\n            placeholder=\"Ask me anything about travel...\"\n            className=\"flex-1\"\n            disabled={isLoading}\n          />\n          <Button type=\"submit\" disabled={!input.trim() || isLoading}>\n            <Send className=\"w-4 h-4\" />\n          </Button>\n        </form>\n      </CardFooter>\n    </Card>\n  );\n}\n"}