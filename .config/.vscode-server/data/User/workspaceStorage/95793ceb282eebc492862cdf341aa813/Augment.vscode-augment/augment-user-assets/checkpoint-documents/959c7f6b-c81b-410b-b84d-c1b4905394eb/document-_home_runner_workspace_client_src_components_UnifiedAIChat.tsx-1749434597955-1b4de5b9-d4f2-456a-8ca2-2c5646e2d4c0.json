{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/UnifiedAIChat.tsx"}, "originalCode": "import React, { useState, useRef, useEffect, useCallback } from 'react';\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { Badge } from '@/components/ui/badge';\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar';\nimport { useToast } from '@/hooks/use-toast';\nimport { useLocation } from 'wouter';\nimport {\n  Send,\n  MapPin,\n  Calendar,\n  Users,\n  Star,\n  Hotel,\n  Sparkles,\n  Loader2,\n  X,\n  Maximize2,\n  Minimize2,\n  Bot,\n  User,\n  Globe,\n  Home,\n  Bed,\n  Car,\n  Utensils,\n  Wifi,\n  Waves,\n  Mountain,\n  TreePine,\n  Building,\n  Heart,\n  TrendingUp,\n  Clock,\n  DollarSign,\n  Camera,\n  Navigation,\n  Search,\n  MessageCircle,\n  Eye\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { getSessionId } from '@/lib/session';\n\ninterface Message {\n  id: string;\n  role: 'user' | 'assistant' | 'system';\n  content: string;\n  timestamp: Date;\n  type?: 'text' | 'location' | 'properties' | 'action' | 'error' | 'experience' | 'insight' | 'recommendation';\n  data?: any;\n  metadata?: {\n    confidence?: number;\n    sources?: string[];\n    actionable?: boolean;\n    priority?: 'low' | 'medium' | 'high';\n  };\n}\n\ninterface PropertyRecommendation {\n  id: number;\n  name: string;\n  type: string;\n  bedrooms?: number;\n  price: number;\n  currency: string;\n  rating: number;\n  image: string;\n  highlights: string[];\n  suitabilityScore?: number;\n  reasonForRecommendation: string;\n}\n\ninterface ExperienceRecommendation {\n  id: string;\n  name: string;\n  type: 'attraction' | 'restaurant' | 'activity' | 'event' | 'transportation';\n  description: string;\n  location: string;\n  duration?: string;\n  priceRange?: string;\n  bestTime?: string;\n  bookingRequired?: boolean;\n  image?: string;\n}\n\ninterface TravelInsight {\n  type: 'weather' | 'events' | 'pricing' | 'crowds' | 'local_tips' | 'safety';\n  title: string;\n  content: string;\n  importance: 'low' | 'medium' | 'high';\n  actionable: boolean;\n  icon?: string;\n}\n\ninterface UnifiedAIChatProps {\n  context?: {\n    location?: string | null;\n    checkIn?: string | null;\n    checkOut?: string | null;\n    guests?: string | null;\n    rooms?: string | null;\n    bedrooms?: string | null;\n    properties?: any[];\n    filters?: any;\n    searchHistory?: any[];\n    groupType?: 'family' | 'friends' | 'corporate' | 'wedding' | 'reunion' | 'multi_generational';\n    travelPurpose?: string;\n    budget?: { min?: number; max?: number; currency?: string };\n    preferences?: {\n      propertyTypes?: string[];\n      amenities?: string[];\n      accessibility?: string[];\n    };\n  };\n  variant?: 'modal' | 'embedded' | 'floating' | 'sidebar';\n  onClose?: () => void;\n  onNavigate?: (path: string, params?: any) => void;\n  onPropertySelect?: (property: any) => void;\n  onSearchUpdate?: (searchParams: any) => void;\n  className?: string;\n  showWelcome?: boolean;\n}\n\n// Helper functions for enhanced geography integration\nconst getTimezoneFromCoordinates = (lat: number, lng: number): string => {\n  // Simplified timezone detection based on longitude\n  const timezoneOffset = Math.round(lng / 15);\n  return `UTC${timezoneOffset >= 0 ? '+' : ''}${timezoneOffset}`;\n};\n\nconst getRegionFromCoordinates = (lat: number, lng: number): string => {\n  // Simplified region detection\n  if (lat > 60) return 'Arctic';\n  if (lat > 35 && lng > -10 && lng < 40) return 'Europe';\n  if (lat > 25 && lat < 50 && lng > -130 && lng < -60) return 'North America';\n  if (lat > -35 && lat < 35 && lng > -20 && lng < 50) return 'Africa/Middle East';\n  if (lat > -50 && lat < 35 && lng > 60 && lng < 150) return 'Asia';\n  if (lat > -50 && lat < -10 && lng > 110 && lng < 180) return 'Australia/Oceania';\n  if (lat > -60 && lat < 15 && lng > -90 && lng < -30) return 'South America';\n  return 'International';\n};\n\nexport default function UnifiedAIChat({\n  context,\n  variant = 'modal',\n  onClose,\n  onNavigate,\n  onPropertySelect,\n  onSearchUpdate,\n  className,\n  showWelcome = true\n}: UnifiedAIChatProps) {\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [input, setInput] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [isMaximized, setIsMaximized] = useState(false);\n  const [sessionId] = useState(() => getSessionId());\n  const [_, navigate] = useLocation();\n  const { toast } = useToast();\n  const scrollAreaRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n  const [conversationContext, setConversationContext] = useState({\n    userPreferences: {},\n    searchHistory: [] as Array<{ location: string; timestamp: Date }>,\n    currentFocus: null as string | null,\n    travelStyle: null as string | null,\n    lastKnownLocation: null as {\n      name: string;\n      coordinates: { lat: number; lng: number };\n      timestamp: Date\n    } | null,\n    geographicContext: {\n      preferredRegions: [] as string[],\n      visitedLocations: [] as string[],\n      travelRadius: null as number | null\n    }\n  });\n  const initializationRef = useRef({\n    hasInitialized: false,\n    hasProcessedInitialMessage: false,\n    messageBeingSent: false\n  });\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    if (scrollAreaRef.current) {\n      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Initialize with enhanced welcome message and process any stored initial message\n  useEffect(() => {\n    if (initializationRef.current.hasInitialized) return;\n    initializationRef.current.hasInitialized = true;\n\n    if (showWelcome) {\n      // Add enhanced welcome message with context awareness\n      const welcomeMessage: Message = {\n        id: `system-welcome-${Date.now()}`,\n        role: 'system',\n        content: generateWelcomeMessage(context),\n        timestamp: new Date(),\n        type: 'text',\n        metadata: {\n          confidence: 1.0,\n          actionable: true,\n          priority: 'high'\n        }\n      };\n\n      setMessages([welcomeMessage]);\n    }\n\n    // Check for stored initial message from \"Plan with AI\" button\n    const storedHistory = localStorage.getItem('chatHistory');\n    const triggerFlag = localStorage.getItem('ai_chat_trigger');\n    \n    if (triggerFlag === 'true' && storedHistory && !initializationRef.current.hasProcessedInitialMessage) {\n      try {\n        const parsedHistory = JSON.parse(storedHistory);\n        const lastMessage = parsedHistory[parsedHistory.length - 1];\n        \n        if (lastMessage && lastMessage.role === 'user' && !initializationRef.current.messageBeingSent) {\n          initializationRef.current.hasProcessedInitialMessage = true;\n          initializationRef.current.messageBeingSent = true;\n          \n          // Clear trigger flag\n          localStorage.removeItem('ai_chat_trigger');\n          \n          // Add user message and send to AI\n          const userMessage: Message = {\n            id: lastMessage.id || `user-${Date.now()}`,\n            role: 'user',\n            content: lastMessage.content,\n            timestamp: new Date(),\n            type: 'text'\n          };\n          \n          setMessages(prev => [...prev, userMessage]);\n          sendMessageToAI(lastMessage.content);\n        }\n      } catch (error) {\n        console.error('Failed to process stored message:', error);\n      }\n    }\n  }, [showWelcome, context]);\n\n  // Helper function to generate contextual welcome message\n  const generateWelcomeMessage = (context?: any): string => {\n    if (context?.location && context?.checkIn) {\n      return `🌟 Welcome! I see you're planning a trip to ${context.location} starting ${new Date(context.checkIn).toLocaleDateString()}. I'm your AI travel expert, and I'm excited to help you create an amazing experience!\n\nWhether you need accommodations, local insights, or activity recommendations, I'm here to make your travel planning effortless and enjoyable.`;\n    }\n\n    if (context?.bedrooms && parseInt(context.bedrooms) > 1) {\n      return `🏡 Welcome! I specialize in finding perfect multi-bedroom accommodations for groups and families. With ${context.bedrooms} bedrooms needed, I'll help you discover amazing properties that bring everyone together comfortably.\n\nLet's find you the ideal place to create lasting memories!`;\n    }\n\n    return `🌟 **Your Personal AI Travel Companion** is here!\n\nI'm not just a search tool - I'm your expert travel advisor with deep knowledge of destinations worldwide.\n\n🎯 **I can help you:**\n• **🗺️ Discover** amazing destinations with insider knowledge\n• **🏨 Find** perfect accommodations for any group size\n• **✨ Plan** unforgettable experiences and activities\n• **💡 Provide** local insights, weather, and timing tips\n• **🎪 Navigate** like a local with expert recommendations\n\n**Ready to plan something amazing? Just tell me your destination or describe your dream trip!**`;\n  };\n\n  const sendMessageToAI = useCallback(async (messageContent: string) => {\n    if (initializationRef.current.messageBeingSent && messageContent !== input) {\n      // This is an initial message being processed\n    } else {\n      setIsLoading(true);\n    }\n\n    try {\n      const response = await fetch('/api/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: messageContent,\n          context: {\n            ...context,\n            conversation: conversationContext,\n            userPreferences: conversationContext.userPreferences,\n            searchHistory: conversationContext.searchHistory\n          },\n          sessionId,\n          extractLocation: true,\n          enhancedMode: true\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const reader = response.body?.getReader();\n      if (!reader) {\n        throw new Error('No response body');\n      }\n\n      let assistantMessage: Message = {\n        id: `assistant-${Date.now()}`,\n        role: 'assistant',\n        content: '',\n        timestamp: new Date(),\n        type: 'text'\n      };\n\n      setMessages(prev => [...prev, assistantMessage]);\n\n      const decoder = new TextDecoder();\n      let buffer = '';\n\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) break;\n\n        buffer += decoder.decode(value, { stream: true });\n        const lines = buffer.split('\\n');\n        buffer = lines.pop() || '';\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            const data = line.slice(6);\n            if (data === '[DONE]') continue;\n\n            try {\n              const parsed = JSON.parse(data);\n              \n              if (parsed.type === 'text') {\n                assistantMessage.content += parsed.data;\n                setMessages(prev =>\n                  prev.map(msg =>\n                    msg.id === assistantMessage.id\n                      ? { ...msg, content: assistantMessage.content }\n                      : msg\n                  )\n                );\n              } else if (parsed.type === 'location') {\n                handleLocationResponse(parsed.data);\n              } else if (parsed.type === 'properties') {\n                handlePropertiesResponse(parsed.data);\n              } else if (parsed.type === 'action') {\n                handleActionResponse(parsed.data);\n              } else if (parsed.type === 'experience') {\n                handleExperienceResponse(parsed.data);\n              } else if (parsed.type === 'insight') {\n                handleInsightResponse(parsed.data);\n              }\n            } catch (error) {\n              console.error('Failed to parse streaming response:', error);\n            }\n          }\n        }\n      }\n\n    } catch (error) {\n      console.error('Chat error:', error);\n      toast({\n        title: 'Error',\n        description: 'Failed to send message. Please try again.',\n        variant: 'destructive',\n      });\n      \n      const errorMessage: Message = {\n        id: `error-${Date.now()}`,\n        role: 'assistant',\n        content: 'I apologize, but I encountered an error. Please try again.',\n        timestamp: new Date(),\n        type: 'error'\n      };\n      \n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n      initializationRef.current.messageBeingSent = false;\n    }\n  }, [context, sessionId, toast, input]);\n\n  const handleLocationResponse = (locationData: any) => {\n    // Enhanced conversation context with geographic intelligence\n    setConversationContext(prev => ({\n      ...prev,\n      currentFocus: 'location',\n      searchHistory: [...prev.searchHistory, { location: locationData.name, timestamp: new Date() }],\n      lastKnownLocation: {\n        name: locationData.name,\n        coordinates: { lat: locationData.lat, lng: locationData.lng },\n        timestamp: new Date()\n      }\n    }));\n\n    // Generate context-aware content based on location and user context\n    const generateContextualContent = () => {\n      let content = `🗺️ Perfect! I found **${locationData.name}** for you.`;\n\n      // Add context-specific insights\n      if (context?.bedrooms && parseInt(context.bedrooms) > 1) {\n        content += ` This is an excellent choice for your ${context.bedrooms}-bedroom group accommodation needs!`;\n      }\n\n      if (context?.groupType) {\n        const groupMessages = {\n          family: \"Perfect for your family vacation with plenty of family-friendly options!\",\n          friends: \"Great choice for your group getaway with amazing nightlife and activities!\",\n          corporate: \"Excellent business destination with top-tier conference facilities!\",\n          wedding: \"Romantic and beautiful location perfect for your wedding celebration!\",\n          reunion: \"Wonderful spot for bringing everyone together with spacious accommodations!\",\n          multi_generational: \"Ideal for multi-generational travel with something for everyone!\"\n        };\n        content += ` ${groupMessages[context.groupType] || 'Perfect for your group travel needs!'}`;\n      }\n\n      return content;\n    };\n\n    // Create an enhanced interactive location message\n    const locationMessage: Message = {\n      id: `location-${Date.now()}`,\n      role: 'assistant',\n      content: generateContextualContent(),\n      timestamp: new Date(),\n      type: 'location',\n      data: {\n        ...locationData,\n        contextualInsights: {\n          groupType: context?.groupType,\n          bedrooms: context?.bedrooms,\n          travelPurpose: context?.travelPurpose,\n          hasSpecialNeeds: !!(context?.bedrooms && parseInt(context.bedrooms) > 1)\n        }\n      },\n      metadata: {\n        actionable: true,\n        priority: 'high',\n        geography: {\n          coordinates: { lat: locationData.lat, lng: locationData.lng },\n          name: locationData.name,\n          type: locationData.type || 'destination',\n          timezone: getTimezoneFromCoordinates(locationData.lat, locationData.lng),\n          region: getRegionFromCoordinates(locationData.lat, locationData.lng)\n        }\n      }\n    };\n\n    setMessages(prev => [...prev, locationMessage]);\n\n    // 🚀 BACKGROUND PROPERTY SEARCH - Search properties automatically but don't navigate\n    if (context?.checkIn && context?.checkOut) {\n      console.log('🔍 Starting background property search for:', locationData.name);\n\n      // Trigger background property search\n      setTimeout(async () => {\n        try {\n          const searchParams = new URLSearchParams({\n            locationName: locationData.name,\n            lat: locationData.lat.toString(),\n            lng: locationData.lng.toString(),\n            checkIn: context.checkIn || new Date(Date.now() + 86400000).toISOString().split('T')[0],\n            checkOut: context.checkOut || new Date(Date.now() + 2 * 86400000).toISOString().split('T')[0],\n            guests: context.guests || '2',\n            rooms: context.rooms || '1',\n            pageSize: '5' // Limit for preview\n          });\n\n          const response = await fetch(`/api/properties/search?${searchParams.toString()}`);\n          if (response.ok) {\n            const data = await response.json();\n            if (data.properties && data.properties.length > 0) {\n              // Add property preview message\n              const propertyPreviewMessage: Message = {\n                id: `property-preview-${Date.now()}`,\n                role: 'assistant',\n                content: `🏨 Great news! I found ${data.properties.length} amazing properties in ${locationData.name}:`,\n                timestamp: new Date(),\n                type: 'properties',\n                data: data.properties.slice(0, 3), // Show top 3\n                metadata: {\n                  confidence: 0.9,\n                  actionable: true,\n                  priority: 'high',\n                  searchContext: {\n                    location: locationData.name,\n                    coordinates: { lat: locationData.lat, lng: locationData.lng },\n                    totalFound: data.properties.length\n                  }\n                }\n              };\n\n              setMessages(prev => [...prev, propertyPreviewMessage]);\n            }\n          }\n        } catch (error) {\n          console.error('Background property search failed:', error);\n        }\n      }, 1500); // Small delay to let location card render first\n    }\n  };\n\n  const handlePropertiesResponse = (properties: any[]) => {\n    const propertyMessage: Message = {\n      id: `properties-${Date.now()}`,\n      role: 'assistant',\n      content: `I found ${properties.length} perfect accommodations for you:`,\n      timestamp: new Date(),\n      type: 'properties',\n      data: properties,\n      metadata: {\n        confidence: 0.9,\n        actionable: true,\n        priority: 'high'\n      }\n    };\n\n    setMessages(prev => [...prev, propertyMessage]);\n  };\n\n  const handleActionResponse = (actionData: any) => {\n    // Handle different action types\n    if (actionData.type === 'location') {\n      // Delegate to location handler for proper rendering\n      handleLocationResponse(actionData.data);\n      return;\n    }\n\n    // For other action types, create a generic action message\n    const actionMessage: Message = {\n      id: `action-${Date.now()}`,\n      role: 'assistant',\n      content: actionData.label || 'Here\\'s what I found:',\n      timestamp: new Date(),\n      type: 'action',\n      data: actionData,\n      metadata: {\n        actionable: true,\n        priority: 'medium'\n      }\n    };\n\n    setMessages(prev => [...prev, actionMessage]);\n  };\n\n  const handleExperienceResponse = (experiences: ExperienceRecommendation[]) => {\n    const experienceMessage: Message = {\n      id: `experience-${Date.now()}`,\n      role: 'assistant',\n      content: `Here are some amazing experiences I recommend:`,\n      timestamp: new Date(),\n      type: 'experience',\n      data: experiences,\n      metadata: {\n        confidence: 0.8,\n        actionable: true,\n        priority: 'medium'\n      }\n    };\n\n    setMessages(prev => [...prev, experienceMessage]);\n  };\n\n  const handleInsightResponse = (insights: TravelInsight[]) => {\n    const insightMessage: Message = {\n      id: `insight-${Date.now()}`,\n      role: 'assistant',\n      content: `Here are some important travel insights:`,\n      timestamp: new Date(),\n      type: 'insight',\n      data: insights,\n      metadata: {\n        confidence: 0.9,\n        actionable: true,\n        priority: insights.some(i => i.importance === 'high') ? 'high' : 'medium'\n      }\n    };\n\n    setMessages(prev => [...prev, insightMessage]);\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!input.trim() || isLoading) return;\n\n    const userMessage: Message = {\n      id: `user-${Date.now()}`,\n      role: 'user',\n      content: input.trim(),\n      timestamp: new Date(),\n      type: 'text'\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    sendMessageToAI(input.trim());\n    setInput('');\n  };\n\n  const handleQuickAction = (action: string) => {\n    console.log('🚀 Quick action triggered:', action);\n\n    // Create user message immediately\n    const userMessage: Message = {\n      id: `user-${Date.now()}`,\n      role: 'user',\n      content: action,\n      timestamp: new Date(),\n      type: 'text'\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n\n    // Send to AI immediately\n    sendMessageToAI(action);\n\n    // Clear input\n    setInput('');\n  };\n\n  const containerClasses = cn(\n    'flex flex-col',\n    {\n      'fixed inset-4 z-50 bg-background border rounded-lg shadow-lg': variant === 'modal',\n      'w-full h-full': variant === 'embedded',\n      'fixed bottom-4 right-4 w-96 h-[500px] z-50 bg-background border rounded-lg shadow-lg': variant === 'floating',\n      'fixed inset-2 z-50': variant === 'modal' && isMaximized,\n    },\n    className\n  );\n\n  // 🧠 SMART Context-Aware Quick Actions with Geography Intelligence\n  const getQuickActions = () => {\n    const baseActions = [];\n\n    // 🗺️ Geography-first actions based on last known location\n    if (conversationContext.lastKnownLocation) {\n      const location = conversationContext.lastKnownLocation;\n      baseActions.push({\n        icon: <Navigation className=\"w-4 h-4\" />,\n        label: `Around ${location.name}`,\n        description: \"Discover nearby attractions, restaurants, and local activities\",\n        action: `Show me nearby attractions, restaurants, and activities within walking distance of ${location.name}. Include local transportation options!`\n      });\n\n      baseActions.push({\n        icon: <MapPin className=\"w-4 h-4\" />,\n        label: `Best Areas in ${location.name}`,\n        description: \"Find the perfect neighborhood for your stay\",\n        action: `Which neighborhoods in ${location.name} are best for staying? Include safety, convenience, and local character insights!`\n      });\n    }\n\n    // 🏠 Context-aware accommodation actions\n    if (context?.bedrooms && parseInt(context.bedrooms) > 1) {\n      baseActions.unshift({\n        icon: <Bed className=\"w-4 h-4\" />,\n        label: `${context.bedrooms}-Bedroom Gems`,\n        description: `Perfect multi-bedroom accommodations for your group`,\n        action: `Find amazing ${context.bedrooms}-bedroom properties with great reviews and perfect locations for my group size`\n      });\n    }\n\n    // 👥 Group-specific intelligent actions\n    if (context?.groupType) {\n      const smartGroupActions = {\n        family: {\n          icon: <Users className=\"w-4 h-4\" />,\n          label: 'Family Paradise',\n          description: 'Kid-friendly stays with pools and activities',\n          action: 'Find family-friendly accommodations with pools, kids activities, and nearby attractions. Include safety ratings and family reviews!'\n        },\n        friends: {\n          icon: <Sparkles className=\"w-4 h-4\" />,\n          label: 'Epic Group Trip',\n          description: 'Perfect spots for groups with great nightlife',\n          action: 'Show me party-friendly accommodations with great nightlife nearby, group activities, and spaces for everyone to hang out!'\n        },\n        corporate: {\n          icon: <TrendingUp className=\"w-4 h-4\" />,\n          label: 'Business Excellence',\n          description: 'Professional hotels with business amenities',\n          action: 'Find business hotels with conference facilities, high-speed internet, and convenient airport/downtown access!'\n        },\n        wedding: {\n          icon: <Heart className=\"w-4 h-4\" />,\n          label: 'Wedding Magic',\n          description: 'Romantic venues perfect for celebrations',\n          action: 'Suggest romantic venues and accommodations perfect for wedding parties, with photo opportunities and celebration spaces!'\n        },\n        reunion: {\n          icon: <Home className=\"w-4 h-4\" />,\n          label: 'Reunion Central',\n          description: 'Large spaces for family gatherings',\n          action: 'Find large vacation rentals perfect for family reunions with common areas, kitchens, and space for everyone to gather!'\n        },\n        multi_generational: {\n          icon: <Users className=\"w-4 h-4\" />,\n          label: 'Multi-Gen Perfect',\n          description: 'Accessible comfort for all ages',\n          action: 'Show me accommodations with accessibility features, varied activity options, and comfort for all ages from kids to grandparents!'\n        }\n      };\n\n      if (smartGroupActions[context.groupType]) {\n        baseActions.unshift(smartGroupActions[context.groupType]);\n      }\n    }\n\n    // 🌍 Smart destination discovery\n    if (!conversationContext.lastKnownLocation) {\n      baseActions.push({\n        icon: <Globe className=\"w-4 h-4\" />,\n        label: 'Discover Destinations',\n        description: 'Trending places to visit this season',\n        action: 'Show me trending travel destinations for this season with insider tips on the best areas to stay!'\n      });\n    }\n\n    // 💎 Unique experiences based on context\n    baseActions.push({\n      icon: <Sparkles className=\"w-4 h-4\" />,\n      label: 'Unique Stays',\n      description: conversationContext.lastKnownLocation\n        ? `Special accommodations in ${conversationContext.lastKnownLocation.name}`\n        : 'Extraordinary stays around the world',\n      action: conversationContext.lastKnownLocation\n        ? `Find unique and memorable accommodations in ${conversationContext.lastKnownLocation.name} - treehouses, historic properties, or local gems!`\n        : 'Suggest unique accommodations worldwide - castles, treehouses, houseboats, and other extraordinary stays!'\n    });\n\n    // 💰 Smart deals and timing\n    baseActions.push({\n      icon: <TrendingUp className=\"w-4 h-4\" />,\n      label: 'Best Value',\n      description: context?.checkIn ? 'Great deals for your travel dates' : 'Current best accommodation deals',\n      action: context?.checkIn\n        ? `Find the best accommodation deals for my ${context.checkIn} travel dates with insider tips on saving money!`\n        : 'Show me the best accommodation deals right now and tips for finding great value!'\n    });\n\n    return baseActions.slice(0, 6); // Limit to 6 most relevant actions\n  };\n\n  const quickActions = getQuickActions();\n\n  return (\n    <Card className={containerClasses}>\n      <CardHeader className=\"border-b bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20\">\n        <CardTitle className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center\">\n              <Bot className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <h2 className=\"text-xl font-bold\">AI Travel Companion</h2>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                Your intelligent travel planning assistant\n              </p>\n            </div>\n          </div>\n          <div className=\"flex items-center gap-2\">\n            {variant === 'modal' && (\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => setIsMaximized(!isMaximized)}\n              >\n                {isMaximized ? <Minimize2 className=\"w-4 h-4\" /> : <Maximize2 className=\"w-4 h-4\" />}\n              </Button>\n            )}\n            {onClose && (\n              <Button variant=\"ghost\" size=\"icon\" onClick={onClose}>\n                <X className=\"w-4 h-4\" />\n              </Button>\n            )}\n          </div>\n        </CardTitle>\n      </CardHeader>\n\n      <CardContent className=\"flex-1 p-0\">\n        <ScrollArea ref={scrollAreaRef} className=\"h-full p-4\">\n          <div className=\"space-y-4\">\n            {messages.map((message) => (\n              <div\n                key={message.id}\n                className={cn(\n                  'flex gap-3',\n                  message.role === 'user' ? 'justify-end' : 'justify-start'\n                )}\n              >\n                {message.role !== 'user' && (\n                  <Avatar className=\"w-8 h-8\">\n                    <AvatarFallback>\n                      <Bot className=\"w-4 h-4\" />\n                    </AvatarFallback>\n                  </Avatar>\n                )}\n                \n                <div\n                  className={cn(\n                    'max-w-[85%] rounded-lg px-4 py-3',\n                    message.role === 'user'\n                      ? 'bg-primary text-primary-foreground'\n                      : message.role === 'system'\n                      ? 'bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 text-foreground border'\n                      : 'bg-secondary text-secondary-foreground'\n                  )}\n                >\n                  <div className=\"flex items-start gap-2\">\n                    {message.metadata?.priority === 'high' && (\n                      <Sparkles className=\"w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0\" />\n                    )}\n                    <div className=\"flex-1\">\n                      {/* Enhanced text formatting for better readability */}\n                      <div className=\"text-sm leading-relaxed space-y-3\">\n                        {message.content.split('\\n\\n').map((paragraph, index) => {\n                          // Handle different types of content\n                          if (paragraph.trim().startsWith('**') && paragraph.trim().endsWith('**')) {\n                            // Bold headers\n                            return (\n                              <h4 key={index} className=\"font-bold text-base text-gray-900 dark:text-gray-100 mt-4 mb-2\">\n                                {paragraph.replace(/\\*\\*/g, '')}\n                              </h4>\n                            );\n                          } else if (paragraph.includes('•') || paragraph.includes('-')) {\n                            // Lists\n                            return (\n                              <div key={index} className=\"space-y-1\">\n                                {paragraph.split('\\n').map((line, lineIndex) => {\n                                  if (line.trim().startsWith('•') || line.trim().startsWith('-')) {\n                                    return (\n                                      <div key={lineIndex} className=\"flex items-start gap-2\">\n                                        <span className=\"text-blue-500 mt-1\">•</span>\n                                        <span>{line.replace(/^[•-]\\s*/, '')}</span>\n                                      </div>\n                                    );\n                                  }\n                                  return line.trim() ? <p key={lineIndex}>{line}</p> : null;\n                                })}\n                              </div>\n                            );\n                          } else if (paragraph.trim()) {\n                            // Regular paragraphs\n                            return (\n                              <p key={index} className=\"text-gray-700 dark:text-gray-300\">\n                                {paragraph.split('**').map((part, partIndex) =>\n                                  partIndex % 2 === 1 ?\n                                    <strong key={partIndex} className=\"font-semibold text-gray-900 dark:text-gray-100\">{part}</strong> :\n                                    part\n                                )}\n                              </p>\n                            );\n                          }\n                          return null;\n                        })}\n                      </div>\n\n                      {/* 🌟 AMAZING Interactive Location Display */}\n                      {message.type === 'location' && message.data && (\n                        <div className=\"mt-4 p-6 border-2 border-blue-200 dark:border-blue-700 rounded-2xl bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-900/30 dark:via-indigo-900/30 dark:to-purple-900/30 shadow-lg hover:shadow-xl transition-all duration-300\">\n                          {/* Header with location badge */}\n                          <div className=\"flex items-center justify-between mb-4\">\n                            <div className=\"flex items-center gap-2\">\n                              <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></div>\n                              <span className=\"text-xs font-medium text-green-700 dark:text-green-400 uppercase tracking-wide\">\n                                📍 DESTINATION FOUND\n                              </span>\n                            </div>\n                            <Badge className=\"bg-gradient-to-r from-yellow-400 to-orange-500 text-white border-0\">\n                              ⭐ Perfect Match\n                            </Badge>\n                          </div>\n\n                          <div className=\"flex items-start gap-6\">\n                            {/* Enhanced location icon */}\n                            <div className=\"relative\">\n                              <div className=\"w-20 h-20 rounded-2xl bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-700 flex items-center justify-center flex-shrink-0 shadow-lg\">\n                                <MapPin className=\"w-10 h-10 text-white\" />\n                              </div>\n                              <div className=\"absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\">\n                                <span className=\"text-white text-xs font-bold\">✓</span>\n                              </div>\n                            </div>\n\n                            <div className=\"flex-1\">\n                              {/* Location name with enhanced typography */}\n                              <h4 className=\"font-bold text-2xl text-gray-900 dark:text-gray-100 mb-2 leading-tight\">\n                                {message.data.name}\n                              </h4>\n\n                              {/* Coordinates with better formatting */}\n                              <div className=\"flex items-center gap-2 mb-3\">\n                                <div className=\"flex items-center gap-1 px-3 py-1 bg-white/60 dark:bg-gray-800/60 rounded-full\">\n                                  <Globe className=\"w-3 h-3 text-blue-600\" />\n                                  <span className=\"text-xs font-mono text-gray-600 dark:text-gray-400\">\n                                    {message.data.lat?.toFixed(4)}, {message.data.lng?.toFixed(4)}\n                                  </span>\n                                </div>\n                                {message.data.type && (\n                                  <Badge variant=\"outline\" className=\"text-xs bg-white/60 dark:bg-gray-800/60\">\n                                    {message.data.type}\n                                  </Badge>\n                                )}\n                              </div>\n\n                              {/* Enhanced Description with Context */}\n                              {message.data.description && (\n                                <p className=\"text-sm text-gray-700 dark:text-gray-300 mb-4 italic bg-white/40 dark:bg-gray-800/40 p-3 rounded-lg\">\n                                  💫 {message.data.description}\n                                </p>\n                              )}\n\n                              {/* 🌍 Real-Time Context Panel */}\n                              <div className=\"bg-white/40 dark:bg-gray-800/40 rounded-lg p-3 mb-4\">\n                                <div className=\"grid grid-cols-2 gap-3 text-xs\">\n                                  <div className=\"flex items-center gap-2\">\n                                    <Clock className=\"w-3 h-3 text-blue-600\" />\n                                    <span className=\"text-gray-600 dark:text-gray-400\">\n                                      {message.metadata?.geography?.timezone || 'Local Time'}\n                                    </span>\n                                  </div>\n                                  <div className=\"flex items-center gap-2\">\n                                    <Globe className=\"w-3 h-3 text-green-600\" />\n                                    <span className=\"text-gray-600 dark:text-gray-400\">\n                                      {message.metadata?.geography?.region || 'Region'}\n                                    </span>\n                                  </div>\n                                  {message.data.contextualInsights?.groupType && (\n                                    <div className=\"flex items-center gap-2 col-span-2\">\n                                      <Users className=\"w-3 h-3 text-purple-600\" />\n                                      <span className=\"text-gray-600 dark:text-gray-400\">\n                                        Optimized for {message.data.contextualInsights.groupType} travel\n                                      </span>\n                                    </div>\n                                  )}\n                                </div>\n                              </div>\n\n                              {/* 🗺️ ENHANCED Geography & Maps Integration */}\n                              <div className=\"space-y-4 mt-4\">\n                                {/* Interactive Mini Map Preview */}\n                                <div className=\"bg-white/60 dark:bg-gray-800/60 rounded-xl p-4 border border-blue-200/50\">\n                                  <div className=\"flex items-center gap-3 mb-3\">\n                                    <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n                                      <MapPin className=\"w-4 h-4 text-white\" />\n                                    </div>\n                                    <div>\n                                      <h6 className=\"font-semibold text-sm\">📍 Geographic Context</h6>\n                                      <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                                        Lat: {message.data.lat?.toFixed(6)} • Lng: {message.data.lng?.toFixed(6)}\n                                      </p>\n                                    </div>\n                                  </div>\n\n                                  {/* Mini map placeholder with interactive preview */}\n                                  <div\n                                    className=\"w-full h-24 bg-gradient-to-br from-blue-100 to-green-100 dark:from-blue-900/30 dark:to-green-900/30 rounded-lg border-2 border-dashed border-blue-300 dark:border-blue-600 flex items-center justify-center cursor-pointer hover:bg-gradient-to-br hover:from-blue-200 hover:to-green-200 transition-all duration-200\"\n                                    onClick={() => {\n                                      const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${message.data.lat},${message.data.lng}&zoom=12`;\n                                      window.open(mapsUrl, '_blank');\n                                    }}\n                                  >\n                                    <div className=\"text-center\">\n                                      <Globe className=\"w-6 h-6 mx-auto mb-1 text-blue-600\" />\n                                      <p className=\"text-xs font-medium text-blue-700 dark:text-blue-300\">\n                                        🗺️ Click to view interactive map\n                                      </p>\n                                    </div>\n                                  </div>\n                                </div>\n\n                                {/* Enhanced action buttons with geography focus */}\n                                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3\">\n                                  <Button\n                                    size=\"sm\"\n                                    onClick={() => {\n                                      const searchParams = new URLSearchParams({\n                                        locationName: message.data.name,\n                                        lat: message.data.lat.toString(),\n                                        lng: message.data.lng.toString(),\n                                        checkIn: context?.checkIn || new Date(Date.now() + 86400000).toISOString().split('T')[0],\n                                        checkOut: context?.checkOut || new Date(Date.now() + 2 * 86400000).toISOString().split('T')[0],\n                                        guests: context?.guests || '2',\n                                        rooms: context?.rooms || '1'\n                                      });\n\n                                      if (onNavigate) {\n                                        onNavigate(`/results?${searchParams.toString()}`);\n                                      } else {\n                                        navigate(`/results?${searchParams.toString()}`);\n                                      }\n                                      onClose?.();\n                                    }}\n                                    className=\"bg-gradient-to-r from-blue-500 via-indigo-600 to-purple-700 hover:from-blue-600 hover:via-indigo-700 hover:to-purple-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105\"\n                                  >\n                                    <Search className=\"w-4 h-4 mr-2\" />\n                                    🏨 Search Here\n                                  </Button>\n\n                                  <Button\n                                    variant=\"outline\"\n                                    size=\"sm\"\n                                    onClick={() => {\n                                      const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${message.data.lat},${message.data.lng}&zoom=15`;\n                                      window.open(mapsUrl, '_blank');\n                                    }}\n                                    className=\"border-blue-300 hover:bg-blue-50 dark:border-blue-600 dark:hover:bg-blue-900/30 transition-all duration-200\"\n                                  >\n                                    <Globe className=\"w-4 h-4 mr-2\" />\n                                    🗺️ Full Map\n                                  </Button>\n\n                                  <Button\n                                    variant=\"outline\"\n                                    size=\"sm\"\n                                    onClick={() => {\n                                      setInput(`Show me nearby attractions, restaurants, and points of interest around ${message.data.name}. Include walking distances and local transportation options!`);\n                                      inputRef.current?.focus();\n                                    }}\n                                    className=\"border-green-300 hover:bg-green-50 dark:border-green-600 dark:hover:bg-green-900/30 transition-all duration-200\"\n                                  >\n                                    <Navigation className=\"w-4 h-4 mr-2\" />\n                                    🎯 Explore Area\n                                  </Button>\n\n                                  <Button\n                                    variant=\"outline\"\n                                    size=\"sm\"\n                                    onClick={() => {\n                                      setInput(`Tell me about ${message.data.name} - best neighborhoods to stay, local culture, weather patterns, and insider travel tips!`);\n                                      inputRef.current?.focus();\n                                    }}\n                                    className=\"border-purple-300 hover:bg-purple-50 dark:border-purple-600 dark:hover:bg-purple-900/30 transition-all duration-200\"\n                                  >\n                                    <Sparkles className=\"w-4 h-4 mr-2\" />\n                                    ✨ Local Insights\n                                  </Button>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n\n                          {/* Bottom action bar */}\n                          <div className=\"mt-6 pt-4 border-t border-blue-200/50 dark:border-blue-700/50\">\n                            <div className=\"flex items-center justify-between text-xs text-gray-600 dark:text-gray-400\">\n                              <span className=\"flex items-center gap-1\">\n                                <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                                Ready to explore this destination\n                              </span>\n                              <span className=\"flex items-center gap-1\">\n                                <Clock className=\"w-3 h-3\" />\n                                Just found • {new Date().toLocaleTimeString()}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Enhanced Property Display */}\n                      {message.type === 'properties' && message.data && (\n                        <div className=\"mt-4 space-y-4\">\n                          {message.data.slice(0, 3).map((property: PropertyRecommendation, index: number) => (\n                            <div\n                              key={index}\n                              className=\"p-4 border rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 hover:shadow-lg transition-all duration-200\"\n                            >\n                              <div className=\"flex items-start gap-4\">\n                                <div className=\"w-20 h-20 rounded-xl bg-gradient-to-br from-purple-500 to-blue-600 flex items-center justify-center flex-shrink-0\">\n                                  <Hotel className=\"w-8 h-8 text-white\" />\n                                </div>\n                                <div className=\"flex-1 min-w-0\">\n                                  <div className=\"flex items-start justify-between gap-2 mb-2\">\n                                    <h4 className=\"font-bold text-base text-gray-900 dark:text-gray-100 leading-tight\">\n                                      {property.name}\n                                    </h4>\n                                    <div className=\"flex items-center gap-1 flex-shrink-0 bg-yellow-100 dark:bg-yellow-900/30 px-2 py-1 rounded-full\">\n                                      <Star className=\"w-3 h-3 fill-yellow-500 text-yellow-500\" />\n                                      <span className=\"text-xs font-medium text-yellow-700 dark:text-yellow-300\">\n                                        {property.rating}\n                                      </span>\n                                    </div>\n                                  </div>\n\n                                  <div className=\"flex items-center gap-4 mb-3 text-sm text-gray-600 dark:text-gray-400\">\n                                    {property.bedrooms && (\n                                      <div className=\"flex items-center gap-1\">\n                                        <Bed className=\"w-4 h-4\" />\n                                        <span>{property.bedrooms} bedroom{property.bedrooms > 1 ? 's' : ''}</span>\n                                      </div>\n                                    )}\n                                    <div className=\"flex items-center gap-1\">\n                                      <MapPin className=\"w-4 h-4\" />\n                                      <span>Prime location</span>\n                                    </div>\n                                  </div>\n\n                                  <div className=\"flex items-center justify-between mb-3\">\n                                    <div className=\"flex items-baseline gap-1\">\n                                      <span className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n                                        ${property.price}\n                                      </span>\n                                      <span className=\"text-sm text-gray-500\">\n                                        /{property.currency === 'USD' ? 'night' : property.currency}\n                                      </span>\n                                    </div>\n                                    {property.suitabilityScore && property.suitabilityScore > 80 && (\n                                      <Badge className=\"bg-gradient-to-r from-green-500 to-emerald-600 text-white\">\n                                        🎯 Perfect Match\n                                      </Badge>\n                                    )}\n                                  </div>\n\n                                  {property.highlights && property.highlights.length > 0 && (\n                                    <div className=\"flex flex-wrap gap-2 mb-3\">\n                                      {property.highlights.slice(0, 3).map((highlight, idx) => (\n                                        <Badge key={idx} variant=\"outline\" className=\"text-xs bg-blue-50 dark:bg-blue-900/30 border-blue-200 dark:border-blue-700\">\n                                          ✨ {highlight}\n                                        </Badge>\n                                      ))}\n                                    </div>\n                                  )}\n\n                                  {property.reasonForRecommendation && (\n                                    <p className=\"text-sm text-gray-700 dark:text-gray-300 mb-3 italic bg-blue-50 dark:bg-blue-900/20 p-2 rounded-lg\">\n                                      💡 \"{property.reasonForRecommendation}\"\n                                    </p>\n                                  )}\n\n                                  <div className=\"flex gap-2\">\n                                    <Button\n                                      size=\"sm\"\n                                      onClick={() => onPropertySelect?.(property)}\n                                      className=\"bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 flex-1\"\n                                    >\n                                      <Eye className=\"w-4 h-4 mr-2\" />\n                                      View Details\n                                    </Button>\n                                    <Button\n                                      variant=\"outline\"\n                                      size=\"sm\"\n                                      onClick={() => {\n                                        setInput(`Tell me more about ${property.name} and why it's perfect for my trip`);\n                                        inputRef.current?.focus();\n                                      }}\n                                    >\n                                      <MessageCircle className=\"w-4 h-4\" />\n                                    </Button>\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n\n                          {message.data.length > 3 && (\n                            <div className=\"mt-4 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-xl border border-indigo-200 dark:border-indigo-700\">\n                              <div className=\"text-center\">\n                                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">\n                                  🎉 I found <strong>{message.data.length} amazing properties</strong> that match your needs!\n                                </p>\n                                <Button\n                                  size=\"lg\"\n                                  className=\"w-full bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700\"\n                                  onClick={() => {\n                                    // Navigate to results with context\n                                    const searchParams = new URLSearchParams({\n                                      ...(context?.location && { locationName: context.location }),\n                                      ...(context?.checkIn && { checkIn: context.checkIn }),\n                                      ...(context?.checkOut && { checkOut: context.checkOut }),\n                                      ...(context?.guests && { guests: context.guests }),\n                                      ...(context?.rooms && { rooms: context.rooms }),\n                                      aiRecommended: 'true'\n                                    });\n\n                                    if (onNavigate) {\n                                      onNavigate(`/results?${searchParams.toString()}`);\n                                    } else {\n                                      navigate(`/results?${searchParams.toString()}`);\n                                    }\n                                    onClose?.();\n                                  }}\n                                >\n                                  <Search className=\"w-5 h-5 mr-2\" />\n                                  Explore All {message.data.length} Properties\n                                </Button>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                      )}\n\n                      {/* Experience Recommendations */}\n                      {message.type === 'experience' && message.data && (\n                        <div className=\"mt-3 space-y-2\">\n                          {message.data.map((experience: ExperienceRecommendation, index: number) => (\n                            <div key={index} className=\"p-3 border rounded-lg bg-background\">\n                              <div className=\"flex items-start gap-3\">\n                                <div className=\"w-8 h-8 rounded-full bg-accent flex items-center justify-center flex-shrink-0\">\n                                  {experience.type === 'restaurant' && <Utensils className=\"w-4 h-4\" />}\n                                  {experience.type === 'attraction' && <Camera className=\"w-4 h-4\" />}\n                                  {experience.type === 'activity' && <Mountain className=\"w-4 h-4\" />}\n                                  {experience.type === 'transportation' && <Car className=\"w-4 h-4\" />}\n                                  {experience.type === 'event' && <Calendar className=\"w-4 h-4\" />}\n                                </div>\n                                <div className=\"flex-1\">\n                                  <h5 className=\"font-medium text-sm\">{experience.name}</h5>\n                                  <p className=\"text-xs text-muted-foreground mt-1\">{experience.description}</p>\n                                  <div className=\"flex items-center gap-4 mt-2 text-xs text-muted-foreground\">\n                                    {experience.duration && (\n                                      <div className=\"flex items-center gap-1\">\n                                        <Clock className=\"w-3 h-3\" />\n                                        {experience.duration}\n                                      </div>\n                                    )}\n                                    {experience.priceRange && (\n                                      <div className=\"flex items-center gap-1\">\n                                        <DollarSign className=\"w-3 h-3\" />\n                                        {experience.priceRange}\n                                      </div>\n                                    )}\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      )}\n\n                      {/* Travel Insights */}\n                      {message.type === 'insight' && message.data && (\n                        <div className=\"mt-3 space-y-2\">\n                          {message.data.map((insight: TravelInsight, index: number) => (\n                            <div\n                              key={index}\n                              className={cn(\n                                \"p-3 border rounded-lg\",\n                                insight.importance === 'high' ? 'border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-900/20' :\n                                insight.importance === 'medium' ? 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20' :\n                                'border-gray-200 bg-gray-50 dark:border-gray-800 dark:bg-gray-900/20'\n                              )}\n                            >\n                              <div className=\"flex items-start gap-2\">\n                                <div className={cn(\n                                  \"w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0\",\n                                  insight.importance === 'high' ? 'bg-orange-100 dark:bg-orange-900' :\n                                  insight.importance === 'medium' ? 'bg-blue-100 dark:bg-blue-900' :\n                                  'bg-gray-100 dark:bg-gray-900'\n                                )}>\n                                  {insight.type === 'weather' && <Waves className=\"w-3 h-3\" />}\n                                  {insight.type === 'events' && <Calendar className=\"w-3 h-3\" />}\n                                  {insight.type === 'pricing' && <TrendingUp className=\"w-3 h-3\" />}\n                                  {insight.type === 'local_tips' && <Navigation className=\"w-3 h-3\" />}\n                                  {insight.type === 'safety' && <Heart className=\"w-3 h-3\" />}\n                                </div>\n                                <div className=\"flex-1\">\n                                  <h5 className=\"font-medium text-sm\">{insight.title}</h5>\n                                  <p className=\"text-xs text-muted-foreground mt-1\">{insight.content}</p>\n                                  {insight.actionable && (\n                                    <Badge variant=\"outline\" className=\"text-xs mt-2\">\n                                      Actionable Tip\n                                    </Badge>\n                                  )}\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      )}\n\n                      {/* Action Items */}\n                      {message.type === 'action' && message.data && (\n                        <div className=\"mt-3\">\n                          <Button\n                            variant=\"outline\"\n                            size=\"sm\"\n                            onClick={() => {\n                              if (message.data.type === 'location') {\n                                handleLocationResponse(message.data.data);\n                              } else if (message.data.type === 'search') {\n                                setInput(message.data.label);\n                                inputRef.current?.focus();\n                              }\n                            }}\n                          >\n                            {message.data.label}\n                          </Button>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                {message.role === 'user' && (\n                  <Avatar className=\"w-8 h-8\">\n                    <AvatarFallback>\n                      <User className=\"w-4 h-4\" />\n                    </AvatarFallback>\n                  </Avatar>\n                )}\n              </div>\n            ))}\n\n            {isLoading && (\n              <div className=\"flex gap-3 justify-start\">\n                <Avatar className=\"w-8 h-8\">\n                  <AvatarFallback>\n                    <Bot className=\"w-4 h-4\" />\n                  </AvatarFallback>\n                </Avatar>\n                <div className=\"bg-secondary text-secondary-foreground rounded-lg px-4 py-2\">\n                  <div className=\"flex items-center gap-2\">\n                    <Loader2 className=\"w-4 h-4 animate-spin\" />\n                    <span className=\"text-sm\">Thinking...</span>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {messages.length === 1 && (\n              <div className=\"p-6 space-y-6\">\n                <div className=\"text-center\">\n                  <h3 className=\"text-xl font-bold text-gray-900 dark:text-gray-100 mb-3\">\n                    ✨ What can I help you with today?\n                  </h3>\n                  <p className=\"text-gray-600 dark:text-gray-400 mb-6\">\n                    {context?.location\n                      ? `I see you're interested in ${context.location}. Let me help you find the perfect place to stay!`\n                      : \"I'm your AI travel expert - just tell me where you'd like to go or what you're looking for!\"\n                    }\n                  </p>\n                  {/* Debug context - remove in production */}\n                  {process.env.NODE_ENV === 'development' && context && (\n                    <div className=\"text-xs text-gray-400 mb-4 p-2 bg-gray-100 dark:bg-gray-800 rounded\">\n                      Debug Context: {JSON.stringify(context, null, 2)}\n                    </div>\n                  )}\n                </div>\n                <div className=\"space-y-3\">\n                  {quickActions.slice(0, 4).map((action, index) => (\n                    <Button\n                      key={index}\n                      variant=\"outline\"\n                      className=\"w-full h-auto p-4 text-left hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-blue-900/20 dark:hover:to-purple-900/20 transition-all duration-200 border-2 hover:border-blue-200\"\n                      onClick={() => {\n                        console.log('Quick action clicked:', action.action);\n                        handleQuickAction(action.action);\n                      }}\n                    >\n                      <div className=\"flex items-center gap-4 w-full\">\n                        <div className=\"w-12 h-12 rounded-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 flex items-center justify-center flex-shrink-0\">\n                          {action.icon}\n                        </div>\n                        <div className=\"flex-1 text-left\">\n                          <div className=\"font-semibold text-base text-gray-900 dark:text-gray-100\">{action.label}</div>\n                          <div className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                            {action.description || (action.action.length > 80 ? action.action.substring(0, 80) + '...' : action.action)}\n                          </div>\n                        </div>\n                        <div className=\"text-blue-500\">\n                          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                          </svg>\n                        </div>\n                      </div>\n                    </Button>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        </ScrollArea>\n      </CardContent>\n\n      <CardFooter className=\"border-t p-4 bg-gray-50 dark:bg-gray-900/50\">\n        <form onSubmit={handleSubmit} className=\"flex gap-3 w-full\">\n          <Input\n            ref={inputRef}\n            value={input}\n            onChange={(e) => setInput(e.target.value)}\n            placeholder=\"Ask me anything about travel...\"\n            className=\"flex-1\"\n            disabled={isLoading}\n          />\n          <Button type=\"submit\" disabled={!input.trim() || isLoading}>\n            <Send className=\"w-4 h-4\" />\n          </Button>\n        </form>\n      </CardFooter>\n    </Card>\n  );\n}\n", "modifiedCode": "import React, { useState, useRef, useEffect, useCallback } from 'react';\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { Badge } from '@/components/ui/badge';\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar';\nimport { useToast } from '@/hooks/use-toast';\nimport { useLocation } from 'wouter';\nimport {\n  Send,\n  MapPin,\n  Calendar,\n  Users,\n  Star,\n  Hotel,\n  Sparkles,\n  Loader2,\n  X,\n  Maximize2,\n  Minimize2,\n  Bot,\n  User,\n  Globe,\n  Home,\n  Bed,\n  Car,\n  Utensils,\n  Wifi,\n  Waves,\n  Mountain,\n  TreePine,\n  Building,\n  Heart,\n  TrendingUp,\n  Clock,\n  DollarSign,\n  Camera,\n  Navigation,\n  Search,\n  MessageCircle,\n  Eye\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { getSessionId } from '@/lib/session';\n\ninterface Message {\n  id: string;\n  role: 'user' | 'assistant' | 'system';\n  content: string;\n  timestamp: Date;\n  type?: 'text' | 'location' | 'properties' | 'action' | 'error' | 'experience' | 'insight' | 'recommendation';\n  data?: any;\n  metadata?: {\n    confidence?: number;\n    sources?: string[];\n    actionable?: boolean;\n    priority?: 'low' | 'medium' | 'high';\n  };\n}\n\ninterface PropertyRecommendation {\n  id: number;\n  name: string;\n  type: string;\n  bedrooms?: number;\n  price: number;\n  currency: string;\n  rating: number;\n  image: string;\n  highlights: string[];\n  suitabilityScore?: number;\n  reasonForRecommendation: string;\n}\n\ninterface ExperienceRecommendation {\n  id: string;\n  name: string;\n  type: 'attraction' | 'restaurant' | 'activity' | 'event' | 'transportation';\n  description: string;\n  location: string;\n  duration?: string;\n  priceRange?: string;\n  bestTime?: string;\n  bookingRequired?: boolean;\n  image?: string;\n}\n\ninterface TravelInsight {\n  type: 'weather' | 'events' | 'pricing' | 'crowds' | 'local_tips' | 'safety';\n  title: string;\n  content: string;\n  importance: 'low' | 'medium' | 'high';\n  actionable: boolean;\n  icon?: string;\n}\n\ninterface UnifiedAIChatProps {\n  context?: {\n    location?: string | null;\n    checkIn?: string | null;\n    checkOut?: string | null;\n    guests?: string | null;\n    rooms?: string | null;\n    bedrooms?: string | null;\n    properties?: any[];\n    filters?: any;\n    searchHistory?: any[];\n    groupType?: 'family' | 'friends' | 'corporate' | 'wedding' | 'reunion' | 'multi_generational';\n    travelPurpose?: string;\n    budget?: { min?: number; max?: number; currency?: string };\n    preferences?: {\n      propertyTypes?: string[];\n      amenities?: string[];\n      accessibility?: string[];\n    };\n  };\n  variant?: 'modal' | 'embedded' | 'floating' | 'sidebar';\n  onClose?: () => void;\n  onNavigate?: (path: string, params?: any) => void;\n  onPropertySelect?: (property: any) => void;\n  onSearchUpdate?: (searchParams: any) => void;\n  className?: string;\n  showWelcome?: boolean;\n}\n\n// Helper functions for enhanced geography integration\nconst getTimezoneFromCoordinates = (lat: number, lng: number): string => {\n  // Simplified timezone detection based on longitude\n  const timezoneOffset = Math.round(lng / 15);\n  return `UTC${timezoneOffset >= 0 ? '+' : ''}${timezoneOffset}`;\n};\n\nconst getRegionFromCoordinates = (lat: number, lng: number): string => {\n  // Simplified region detection\n  if (lat > 60) return 'Arctic';\n  if (lat > 35 && lng > -10 && lng < 40) return 'Europe';\n  if (lat > 25 && lat < 50 && lng > -130 && lng < -60) return 'North America';\n  if (lat > -35 && lat < 35 && lng > -20 && lng < 50) return 'Africa/Middle East';\n  if (lat > -50 && lat < 35 && lng > 60 && lng < 150) return 'Asia';\n  if (lat > -50 && lat < -10 && lng > 110 && lng < 180) return 'Australia/Oceania';\n  if (lat > -60 && lat < 15 && lng > -90 && lng < -30) return 'South America';\n  return 'International';\n};\n\nexport default function UnifiedAIChat({\n  context,\n  variant = 'modal',\n  onClose,\n  onNavigate,\n  onPropertySelect,\n  onSearchUpdate,\n  className,\n  showWelcome = true\n}: UnifiedAIChatProps) {\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [input, setInput] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [isMaximized, setIsMaximized] = useState(false);\n  const [sessionId] = useState(() => getSessionId());\n  const [_, navigate] = useLocation();\n  const { toast } = useToast();\n  const scrollAreaRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n  const [conversationContext, setConversationContext] = useState({\n    userPreferences: {},\n    searchHistory: [] as Array<{ location: string; timestamp: Date }>,\n    currentFocus: null as string | null,\n    travelStyle: null as string | null,\n    lastKnownLocation: null as {\n      name: string;\n      coordinates: { lat: number; lng: number };\n      timestamp: Date\n    } | null,\n    geographicContext: {\n      preferredRegions: [] as string[],\n      visitedLocations: [] as string[],\n      travelRadius: null as number | null\n    }\n  });\n  const initializationRef = useRef({\n    hasInitialized: false,\n    hasProcessedInitialMessage: false,\n    messageBeingSent: false\n  });\n\n  // Auto-scroll to bottom when new messages arrive\n  useEffect(() => {\n    if (scrollAreaRef.current) {\n      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;\n    }\n  }, [messages]);\n\n  // Initialize with enhanced welcome message and process any stored initial message\n  useEffect(() => {\n    if (initializationRef.current.hasInitialized) return;\n    initializationRef.current.hasInitialized = true;\n\n    if (showWelcome) {\n      // Add enhanced welcome message with context awareness\n      const welcomeMessage: Message = {\n        id: `system-welcome-${Date.now()}`,\n        role: 'system',\n        content: generateWelcomeMessage(context),\n        timestamp: new Date(),\n        type: 'text',\n        metadata: {\n          confidence: 1.0,\n          actionable: true,\n          priority: 'high'\n        }\n      };\n\n      setMessages([welcomeMessage]);\n    }\n\n    // Check for stored initial message from \"Plan with AI\" button\n    const storedHistory = localStorage.getItem('chatHistory');\n    const triggerFlag = localStorage.getItem('ai_chat_trigger');\n    \n    if (triggerFlag === 'true' && storedHistory && !initializationRef.current.hasProcessedInitialMessage) {\n      try {\n        const parsedHistory = JSON.parse(storedHistory);\n        const lastMessage = parsedHistory[parsedHistory.length - 1];\n        \n        if (lastMessage && lastMessage.role === 'user' && !initializationRef.current.messageBeingSent) {\n          initializationRef.current.hasProcessedInitialMessage = true;\n          initializationRef.current.messageBeingSent = true;\n          \n          // Clear trigger flag\n          localStorage.removeItem('ai_chat_trigger');\n          \n          // Add user message and send to AI\n          const userMessage: Message = {\n            id: lastMessage.id || `user-${Date.now()}`,\n            role: 'user',\n            content: lastMessage.content,\n            timestamp: new Date(),\n            type: 'text'\n          };\n          \n          setMessages(prev => [...prev, userMessage]);\n          sendMessageToAI(lastMessage.content);\n        }\n      } catch (error) {\n        console.error('Failed to process stored message:', error);\n      }\n    }\n  }, [showWelcome, context]);\n\n  // Helper function to generate contextual welcome message\n  const generateWelcomeMessage = (context?: any): string => {\n    if (context?.location && context?.checkIn) {\n      return `🌟 Welcome! I see you're planning a trip to ${context.location} starting ${new Date(context.checkIn).toLocaleDateString()}. I'm your AI travel expert, and I'm excited to help you create an amazing experience!\n\nWhether you need accommodations, local insights, or activity recommendations, I'm here to make your travel planning effortless and enjoyable.`;\n    }\n\n    if (context?.bedrooms && parseInt(context.bedrooms) > 1) {\n      return `🏡 Welcome! I specialize in finding perfect multi-bedroom accommodations for groups and families. With ${context.bedrooms} bedrooms needed, I'll help you discover amazing properties that bring everyone together comfortably.\n\nLet's find you the ideal place to create lasting memories!`;\n    }\n\n    return `🌟 **Your Personal AI Travel Companion** is here!\n\nI'm not just a search tool - I'm your expert travel advisor with deep knowledge of destinations worldwide.\n\n🎯 **I can help you:**\n• **🗺️ Discover** amazing destinations with insider knowledge\n• **🏨 Find** perfect accommodations for any group size\n• **✨ Plan** unforgettable experiences and activities\n• **💡 Provide** local insights, weather, and timing tips\n• **🎪 Navigate** like a local with expert recommendations\n\n**Ready to plan something amazing? Just tell me your destination or describe your dream trip!**`;\n  };\n\n  const sendMessageToAI = useCallback(async (messageContent: string) => {\n    if (initializationRef.current.messageBeingSent && messageContent !== input) {\n      // This is an initial message being processed\n    } else {\n      setIsLoading(true);\n    }\n\n    try {\n      const response = await fetch('/api/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: messageContent,\n          context: {\n            ...context,\n            conversation: conversationContext,\n            userPreferences: conversationContext.userPreferences,\n            searchHistory: conversationContext.searchHistory\n          },\n          sessionId,\n          extractLocation: true,\n          enhancedMode: true\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const reader = response.body?.getReader();\n      if (!reader) {\n        throw new Error('No response body');\n      }\n\n      let assistantMessage: Message = {\n        id: `assistant-${Date.now()}`,\n        role: 'assistant',\n        content: '',\n        timestamp: new Date(),\n        type: 'text'\n      };\n\n      setMessages(prev => [...prev, assistantMessage]);\n\n      const decoder = new TextDecoder();\n      let buffer = '';\n\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) break;\n\n        buffer += decoder.decode(value, { stream: true });\n        const lines = buffer.split('\\n');\n        buffer = lines.pop() || '';\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            const data = line.slice(6);\n            if (data === '[DONE]') continue;\n\n            try {\n              const parsed = JSON.parse(data);\n              \n              if (parsed.type === 'text') {\n                assistantMessage.content += parsed.data;\n                setMessages(prev =>\n                  prev.map(msg =>\n                    msg.id === assistantMessage.id\n                      ? { ...msg, content: assistantMessage.content }\n                      : msg\n                  )\n                );\n              } else if (parsed.type === 'location') {\n                handleLocationResponse(parsed.data);\n              } else if (parsed.type === 'properties') {\n                handlePropertiesResponse(parsed.data);\n              } else if (parsed.type === 'action') {\n                handleActionResponse(parsed.data);\n              } else if (parsed.type === 'experience') {\n                handleExperienceResponse(parsed.data);\n              } else if (parsed.type === 'insight') {\n                handleInsightResponse(parsed.data);\n              }\n            } catch (error) {\n              console.error('Failed to parse streaming response:', error);\n            }\n          }\n        }\n      }\n\n    } catch (error) {\n      console.error('Chat error:', error);\n      toast({\n        title: 'Error',\n        description: 'Failed to send message. Please try again.',\n        variant: 'destructive',\n      });\n      \n      const errorMessage: Message = {\n        id: `error-${Date.now()}`,\n        role: 'assistant',\n        content: 'I apologize, but I encountered an error. Please try again.',\n        timestamp: new Date(),\n        type: 'error'\n      };\n      \n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n      initializationRef.current.messageBeingSent = false;\n    }\n  }, [context, sessionId, toast, input]);\n\n  const handleLocationResponse = (locationData: any) => {\n    // Enhanced conversation context with geographic intelligence\n    setConversationContext(prev => ({\n      ...prev,\n      currentFocus: 'location',\n      searchHistory: [...prev.searchHistory, { location: locationData.name, timestamp: new Date() }],\n      lastKnownLocation: {\n        name: locationData.name,\n        coordinates: { lat: locationData.lat, lng: locationData.lng },\n        timestamp: new Date()\n      }\n    }));\n\n    // Generate context-aware content based on location and user context\n    const generateContextualContent = () => {\n      let content = `🗺️ Perfect! I found **${locationData.name}** for you.`;\n\n      // Add context-specific insights\n      if (context?.bedrooms && parseInt(context.bedrooms) > 1) {\n        content += ` This is an excellent choice for your ${context.bedrooms}-bedroom group accommodation needs!`;\n      }\n\n      if (context?.groupType) {\n        const groupMessages = {\n          family: \"Perfect for your family vacation with plenty of family-friendly options!\",\n          friends: \"Great choice for your group getaway with amazing nightlife and activities!\",\n          corporate: \"Excellent business destination with top-tier conference facilities!\",\n          wedding: \"Romantic and beautiful location perfect for your wedding celebration!\",\n          reunion: \"Wonderful spot for bringing everyone together with spacious accommodations!\",\n          multi_generational: \"Ideal for multi-generational travel with something for everyone!\"\n        };\n        content += ` ${groupMessages[context.groupType] || 'Perfect for your group travel needs!'}`;\n      }\n\n      return content;\n    };\n\n    // Create an enhanced interactive location message\n    const locationMessage: Message = {\n      id: `location-${Date.now()}`,\n      role: 'assistant',\n      content: generateContextualContent(),\n      timestamp: new Date(),\n      type: 'location',\n      data: {\n        ...locationData,\n        contextualInsights: {\n          groupType: context?.groupType,\n          bedrooms: context?.bedrooms,\n          travelPurpose: context?.travelPurpose,\n          hasSpecialNeeds: !!(context?.bedrooms && parseInt(context.bedrooms) > 1)\n        }\n      },\n      metadata: {\n        actionable: true,\n        priority: 'high',\n        geography: {\n          coordinates: { lat: locationData.lat, lng: locationData.lng },\n          name: locationData.name,\n          type: locationData.type || 'destination',\n          timezone: getTimezoneFromCoordinates(locationData.lat, locationData.lng),\n          region: getRegionFromCoordinates(locationData.lat, locationData.lng)\n        }\n      }\n    };\n\n    setMessages(prev => [...prev, locationMessage]);\n\n    // 🚀 BACKGROUND PROPERTY SEARCH - Search properties automatically but don't navigate\n    if (context?.checkIn && context?.checkOut) {\n      console.log('🔍 Starting background property search for:', locationData.name);\n\n      // Trigger background property search\n      setTimeout(async () => {\n        try {\n          const searchParams = new URLSearchParams({\n            locationName: locationData.name,\n            lat: locationData.lat.toString(),\n            lng: locationData.lng.toString(),\n            checkIn: context.checkIn || new Date(Date.now() + 86400000).toISOString().split('T')[0],\n            checkOut: context.checkOut || new Date(Date.now() + 2 * 86400000).toISOString().split('T')[0],\n            guests: context.guests || '2',\n            rooms: context.rooms || '1',\n            pageSize: '5' // Limit for preview\n          });\n\n          const response = await fetch(`/api/properties/search?${searchParams.toString()}`);\n          if (response.ok) {\n            const data = await response.json();\n            if (data.properties && data.properties.length > 0) {\n              // Add property preview message\n              const propertyPreviewMessage: Message = {\n                id: `property-preview-${Date.now()}`,\n                role: 'assistant',\n                content: `🏨 Great news! I found ${data.properties.length} amazing properties in ${locationData.name}:`,\n                timestamp: new Date(),\n                type: 'properties',\n                data: data.properties.slice(0, 3), // Show top 3\n                metadata: {\n                  confidence: 0.9,\n                  actionable: true,\n                  priority: 'high',\n                  searchContext: {\n                    location: locationData.name,\n                    coordinates: { lat: locationData.lat, lng: locationData.lng },\n                    totalFound: data.properties.length\n                  }\n                }\n              };\n\n              setMessages(prev => [...prev, propertyPreviewMessage]);\n            }\n          }\n        } catch (error) {\n          console.error('Background property search failed:', error);\n        }\n      }, 1500); // Small delay to let location card render first\n    }\n  };\n\n  const handlePropertiesResponse = (properties: any[]) => {\n    const propertyMessage: Message = {\n      id: `properties-${Date.now()}`,\n      role: 'assistant',\n      content: `I found ${properties.length} perfect accommodations for you:`,\n      timestamp: new Date(),\n      type: 'properties',\n      data: properties,\n      metadata: {\n        confidence: 0.9,\n        actionable: true,\n        priority: 'high'\n      }\n    };\n\n    setMessages(prev => [...prev, propertyMessage]);\n  };\n\n  const handleActionResponse = (actionData: any) => {\n    // Handle different action types\n    if (actionData.type === 'location') {\n      // Delegate to location handler for proper rendering\n      handleLocationResponse(actionData.data);\n      return;\n    }\n\n    // For other action types, create a generic action message\n    const actionMessage: Message = {\n      id: `action-${Date.now()}`,\n      role: 'assistant',\n      content: actionData.label || 'Here\\'s what I found:',\n      timestamp: new Date(),\n      type: 'action',\n      data: actionData,\n      metadata: {\n        actionable: true,\n        priority: 'medium'\n      }\n    };\n\n    setMessages(prev => [...prev, actionMessage]);\n  };\n\n  const handleExperienceResponse = (experiences: ExperienceRecommendation[]) => {\n    const experienceMessage: Message = {\n      id: `experience-${Date.now()}`,\n      role: 'assistant',\n      content: `Here are some amazing experiences I recommend:`,\n      timestamp: new Date(),\n      type: 'experience',\n      data: experiences,\n      metadata: {\n        confidence: 0.8,\n        actionable: true,\n        priority: 'medium'\n      }\n    };\n\n    setMessages(prev => [...prev, experienceMessage]);\n  };\n\n  const handleInsightResponse = (insights: TravelInsight[]) => {\n    const insightMessage: Message = {\n      id: `insight-${Date.now()}`,\n      role: 'assistant',\n      content: `Here are some important travel insights:`,\n      timestamp: new Date(),\n      type: 'insight',\n      data: insights,\n      metadata: {\n        confidence: 0.9,\n        actionable: true,\n        priority: insights.some(i => i.importance === 'high') ? 'high' : 'medium'\n      }\n    };\n\n    setMessages(prev => [...prev, insightMessage]);\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!input.trim() || isLoading) return;\n\n    const userMessage: Message = {\n      id: `user-${Date.now()}`,\n      role: 'user',\n      content: input.trim(),\n      timestamp: new Date(),\n      type: 'text'\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    sendMessageToAI(input.trim());\n    setInput('');\n  };\n\n  const handleQuickAction = (action: string) => {\n    console.log('🚀 Quick action triggered:', action);\n\n    // Create user message immediately\n    const userMessage: Message = {\n      id: `user-${Date.now()}`,\n      role: 'user',\n      content: action,\n      timestamp: new Date(),\n      type: 'text'\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n\n    // Send to AI immediately\n    sendMessageToAI(action);\n\n    // Clear input\n    setInput('');\n  };\n\n  const containerClasses = cn(\n    'flex flex-col',\n    {\n      'fixed inset-4 z-50 bg-background border rounded-lg shadow-lg': variant === 'modal',\n      'w-full h-full': variant === 'embedded',\n      'fixed bottom-4 right-4 w-96 h-[500px] z-50 bg-background border rounded-lg shadow-lg': variant === 'floating',\n      'fixed inset-2 z-50': variant === 'modal' && isMaximized,\n    },\n    className\n  );\n\n  // 🧠 SMART Context-Aware Quick Actions with Geography Intelligence\n  const getQuickActions = () => {\n    const baseActions = [];\n\n    // 🗺️ Geography-first actions based on last known location\n    if (conversationContext.lastKnownLocation) {\n      const location = conversationContext.lastKnownLocation;\n      baseActions.push({\n        icon: <Navigation className=\"w-4 h-4\" />,\n        label: `Around ${location.name}`,\n        description: \"Discover nearby attractions, restaurants, and local activities\",\n        action: `Show me nearby attractions, restaurants, and activities within walking distance of ${location.name}. Include local transportation options!`\n      });\n\n      baseActions.push({\n        icon: <MapPin className=\"w-4 h-4\" />,\n        label: `Best Areas in ${location.name}`,\n        description: \"Find the perfect neighborhood for your stay\",\n        action: `Which neighborhoods in ${location.name} are best for staying? Include safety, convenience, and local character insights!`\n      });\n    }\n\n    // 🏠 Context-aware accommodation actions\n    if (context?.bedrooms && parseInt(context.bedrooms) > 1) {\n      baseActions.unshift({\n        icon: <Bed className=\"w-4 h-4\" />,\n        label: `${context.bedrooms}-Bedroom Gems`,\n        description: `Perfect multi-bedroom accommodations for your group`,\n        action: `Find amazing ${context.bedrooms}-bedroom properties with great reviews and perfect locations for my group size`\n      });\n    }\n\n    // 👥 Group-specific intelligent actions\n    if (context?.groupType) {\n      const smartGroupActions = {\n        family: {\n          icon: <Users className=\"w-4 h-4\" />,\n          label: 'Family Paradise',\n          description: 'Kid-friendly stays with pools and activities',\n          action: 'Find family-friendly accommodations with pools, kids activities, and nearby attractions. Include safety ratings and family reviews!'\n        },\n        friends: {\n          icon: <Sparkles className=\"w-4 h-4\" />,\n          label: 'Epic Group Trip',\n          description: 'Perfect spots for groups with great nightlife',\n          action: 'Show me party-friendly accommodations with great nightlife nearby, group activities, and spaces for everyone to hang out!'\n        },\n        corporate: {\n          icon: <TrendingUp className=\"w-4 h-4\" />,\n          label: 'Business Excellence',\n          description: 'Professional hotels with business amenities',\n          action: 'Find business hotels with conference facilities, high-speed internet, and convenient airport/downtown access!'\n        },\n        wedding: {\n          icon: <Heart className=\"w-4 h-4\" />,\n          label: 'Wedding Magic',\n          description: 'Romantic venues perfect for celebrations',\n          action: 'Suggest romantic venues and accommodations perfect for wedding parties, with photo opportunities and celebration spaces!'\n        },\n        reunion: {\n          icon: <Home className=\"w-4 h-4\" />,\n          label: 'Reunion Central',\n          description: 'Large spaces for family gatherings',\n          action: 'Find large vacation rentals perfect for family reunions with common areas, kitchens, and space for everyone to gather!'\n        },\n        multi_generational: {\n          icon: <Users className=\"w-4 h-4\" />,\n          label: 'Multi-Gen Perfect',\n          description: 'Accessible comfort for all ages',\n          action: 'Show me accommodations with accessibility features, varied activity options, and comfort for all ages from kids to grandparents!'\n        }\n      };\n\n      if (smartGroupActions[context.groupType]) {\n        baseActions.unshift(smartGroupActions[context.groupType]);\n      }\n    }\n\n    // 🌍 Smart destination discovery\n    if (!conversationContext.lastKnownLocation) {\n      baseActions.push({\n        icon: <Globe className=\"w-4 h-4\" />,\n        label: 'Discover Destinations',\n        description: 'Trending places to visit this season',\n        action: 'Show me trending travel destinations for this season with insider tips on the best areas to stay!'\n      });\n    }\n\n    // 💎 Unique experiences based on context\n    baseActions.push({\n      icon: <Sparkles className=\"w-4 h-4\" />,\n      label: 'Unique Stays',\n      description: conversationContext.lastKnownLocation\n        ? `Special accommodations in ${conversationContext.lastKnownLocation.name}`\n        : 'Extraordinary stays around the world',\n      action: conversationContext.lastKnownLocation\n        ? `Find unique and memorable accommodations in ${conversationContext.lastKnownLocation.name} - treehouses, historic properties, or local gems!`\n        : 'Suggest unique accommodations worldwide - castles, treehouses, houseboats, and other extraordinary stays!'\n    });\n\n    // 💰 Smart deals and timing\n    baseActions.push({\n      icon: <TrendingUp className=\"w-4 h-4\" />,\n      label: 'Best Value',\n      description: context?.checkIn ? 'Great deals for your travel dates' : 'Current best accommodation deals',\n      action: context?.checkIn\n        ? `Find the best accommodation deals for my ${context.checkIn} travel dates with insider tips on saving money!`\n        : 'Show me the best accommodation deals right now and tips for finding great value!'\n    });\n\n    return baseActions.slice(0, 6); // Limit to 6 most relevant actions\n  };\n\n  const quickActions = getQuickActions();\n\n  return (\n    <Card className={containerClasses}>\n      <CardHeader className=\"border-b bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20\">\n        <CardTitle className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center\">\n              <Bot className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <h2 className=\"text-xl font-bold\">AI Travel Companion</h2>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                Your intelligent travel planning assistant\n              </p>\n            </div>\n          </div>\n          <div className=\"flex items-center gap-2\">\n            {variant === 'modal' && (\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => setIsMaximized(!isMaximized)}\n              >\n                {isMaximized ? <Minimize2 className=\"w-4 h-4\" /> : <Maximize2 className=\"w-4 h-4\" />}\n              </Button>\n            )}\n            {onClose && (\n              <Button variant=\"ghost\" size=\"icon\" onClick={onClose}>\n                <X className=\"w-4 h-4\" />\n              </Button>\n            )}\n          </div>\n        </CardTitle>\n      </CardHeader>\n\n      <CardContent className=\"flex-1 p-0 overflow-hidden\">\n        <ScrollArea ref={scrollAreaRef} className=\"h-full\">\n          <div className=\"p-4 space-y-4 max-w-full\">\n            {messages.map((message) => (\n              <div\n                key={message.id}\n                className={cn(\n                  'flex gap-3',\n                  message.role === 'user' ? 'justify-end' : 'justify-start'\n                )}\n              >\n                {message.role !== 'user' && (\n                  <Avatar className=\"w-8 h-8\">\n                    <AvatarFallback>\n                      <Bot className=\"w-4 h-4\" />\n                    </AvatarFallback>\n                  </Avatar>\n                )}\n                \n                <div\n                  className={cn(\n                    'max-w-[85%] rounded-lg px-4 py-3',\n                    message.role === 'user'\n                      ? 'bg-primary text-primary-foreground'\n                      : message.role === 'system'\n                      ? 'bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 text-foreground border'\n                      : 'bg-secondary text-secondary-foreground'\n                  )}\n                >\n                  <div className=\"flex items-start gap-2\">\n                    {message.metadata?.priority === 'high' && (\n                      <Sparkles className=\"w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0\" />\n                    )}\n                    <div className=\"flex-1\">\n                      {/* Enhanced text formatting for better readability */}\n                      <div className=\"text-sm leading-relaxed space-y-3\">\n                        {message.content.split('\\n\\n').map((paragraph, index) => {\n                          // Handle different types of content\n                          if (paragraph.trim().startsWith('**') && paragraph.trim().endsWith('**')) {\n                            // Bold headers\n                            return (\n                              <h4 key={index} className=\"font-bold text-base text-gray-900 dark:text-gray-100 mt-4 mb-2\">\n                                {paragraph.replace(/\\*\\*/g, '')}\n                              </h4>\n                            );\n                          } else if (paragraph.includes('•') || paragraph.includes('-')) {\n                            // Lists\n                            return (\n                              <div key={index} className=\"space-y-1\">\n                                {paragraph.split('\\n').map((line, lineIndex) => {\n                                  if (line.trim().startsWith('•') || line.trim().startsWith('-')) {\n                                    return (\n                                      <div key={lineIndex} className=\"flex items-start gap-2\">\n                                        <span className=\"text-blue-500 mt-1\">•</span>\n                                        <span>{line.replace(/^[•-]\\s*/, '')}</span>\n                                      </div>\n                                    );\n                                  }\n                                  return line.trim() ? <p key={lineIndex}>{line}</p> : null;\n                                })}\n                              </div>\n                            );\n                          } else if (paragraph.trim()) {\n                            // Regular paragraphs\n                            return (\n                              <p key={index} className=\"text-gray-700 dark:text-gray-300\">\n                                {paragraph.split('**').map((part, partIndex) =>\n                                  partIndex % 2 === 1 ?\n                                    <strong key={partIndex} className=\"font-semibold text-gray-900 dark:text-gray-100\">{part}</strong> :\n                                    part\n                                )}\n                              </p>\n                            );\n                          }\n                          return null;\n                        })}\n                      </div>\n\n                      {/* 🌟 AMAZING Interactive Location Display */}\n                      {message.type === 'location' && message.data && (\n                        <div className=\"mt-4 p-6 border-2 border-blue-200 dark:border-blue-700 rounded-2xl bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-900/30 dark:via-indigo-900/30 dark:to-purple-900/30 shadow-lg hover:shadow-xl transition-all duration-300\">\n                          {/* Header with location badge */}\n                          <div className=\"flex items-center justify-between mb-4\">\n                            <div className=\"flex items-center gap-2\">\n                              <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></div>\n                              <span className=\"text-xs font-medium text-green-700 dark:text-green-400 uppercase tracking-wide\">\n                                📍 DESTINATION FOUND\n                              </span>\n                            </div>\n                            <Badge className=\"bg-gradient-to-r from-yellow-400 to-orange-500 text-white border-0\">\n                              ⭐ Perfect Match\n                            </Badge>\n                          </div>\n\n                          <div className=\"flex items-start gap-6\">\n                            {/* Enhanced location icon */}\n                            <div className=\"relative\">\n                              <div className=\"w-20 h-20 rounded-2xl bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-700 flex items-center justify-center flex-shrink-0 shadow-lg\">\n                                <MapPin className=\"w-10 h-10 text-white\" />\n                              </div>\n                              <div className=\"absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\">\n                                <span className=\"text-white text-xs font-bold\">✓</span>\n                              </div>\n                            </div>\n\n                            <div className=\"flex-1\">\n                              {/* Location name with enhanced typography */}\n                              <h4 className=\"font-bold text-2xl text-gray-900 dark:text-gray-100 mb-2 leading-tight\">\n                                {message.data.name}\n                              </h4>\n\n                              {/* Coordinates with better formatting */}\n                              <div className=\"flex items-center gap-2 mb-3\">\n                                <div className=\"flex items-center gap-1 px-3 py-1 bg-white/60 dark:bg-gray-800/60 rounded-full\">\n                                  <Globe className=\"w-3 h-3 text-blue-600\" />\n                                  <span className=\"text-xs font-mono text-gray-600 dark:text-gray-400\">\n                                    {message.data.lat?.toFixed(4)}, {message.data.lng?.toFixed(4)}\n                                  </span>\n                                </div>\n                                {message.data.type && (\n                                  <Badge variant=\"outline\" className=\"text-xs bg-white/60 dark:bg-gray-800/60\">\n                                    {message.data.type}\n                                  </Badge>\n                                )}\n                              </div>\n\n                              {/* Enhanced Description with Context */}\n                              {message.data.description && (\n                                <p className=\"text-sm text-gray-700 dark:text-gray-300 mb-4 italic bg-white/40 dark:bg-gray-800/40 p-3 rounded-lg\">\n                                  💫 {message.data.description}\n                                </p>\n                              )}\n\n                              {/* 🌍 Real-Time Context Panel */}\n                              <div className=\"bg-white/40 dark:bg-gray-800/40 rounded-lg p-3 mb-4\">\n                                <div className=\"grid grid-cols-2 gap-3 text-xs\">\n                                  <div className=\"flex items-center gap-2\">\n                                    <Clock className=\"w-3 h-3 text-blue-600\" />\n                                    <span className=\"text-gray-600 dark:text-gray-400\">\n                                      {message.metadata?.geography?.timezone || 'Local Time'}\n                                    </span>\n                                  </div>\n                                  <div className=\"flex items-center gap-2\">\n                                    <Globe className=\"w-3 h-3 text-green-600\" />\n                                    <span className=\"text-gray-600 dark:text-gray-400\">\n                                      {message.metadata?.geography?.region || 'Region'}\n                                    </span>\n                                  </div>\n                                  {message.data.contextualInsights?.groupType && (\n                                    <div className=\"flex items-center gap-2 col-span-2\">\n                                      <Users className=\"w-3 h-3 text-purple-600\" />\n                                      <span className=\"text-gray-600 dark:text-gray-400\">\n                                        Optimized for {message.data.contextualInsights.groupType} travel\n                                      </span>\n                                    </div>\n                                  )}\n                                </div>\n                              </div>\n\n                              {/* 🗺️ ENHANCED Geography & Maps Integration */}\n                              <div className=\"space-y-4 mt-4\">\n                                {/* Interactive Mini Map Preview */}\n                                <div className=\"bg-white/60 dark:bg-gray-800/60 rounded-xl p-4 border border-blue-200/50\">\n                                  <div className=\"flex items-center gap-3 mb-3\">\n                                    <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n                                      <MapPin className=\"w-4 h-4 text-white\" />\n                                    </div>\n                                    <div>\n                                      <h6 className=\"font-semibold text-sm\">📍 Geographic Context</h6>\n                                      <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                                        Lat: {message.data.lat?.toFixed(6)} • Lng: {message.data.lng?.toFixed(6)}\n                                      </p>\n                                    </div>\n                                  </div>\n\n                                  {/* Mini map placeholder with interactive preview */}\n                                  <div\n                                    className=\"w-full h-24 bg-gradient-to-br from-blue-100 to-green-100 dark:from-blue-900/30 dark:to-green-900/30 rounded-lg border-2 border-dashed border-blue-300 dark:border-blue-600 flex items-center justify-center cursor-pointer hover:bg-gradient-to-br hover:from-blue-200 hover:to-green-200 transition-all duration-200\"\n                                    onClick={() => {\n                                      const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${message.data.lat},${message.data.lng}&zoom=12`;\n                                      window.open(mapsUrl, '_blank');\n                                    }}\n                                  >\n                                    <div className=\"text-center\">\n                                      <Globe className=\"w-6 h-6 mx-auto mb-1 text-blue-600\" />\n                                      <p className=\"text-xs font-medium text-blue-700 dark:text-blue-300\">\n                                        🗺️ Click to view interactive map\n                                      </p>\n                                    </div>\n                                  </div>\n                                </div>\n\n                                {/* Enhanced action buttons with geography focus */}\n                                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3\">\n                                  <Button\n                                    size=\"sm\"\n                                    onClick={() => {\n                                      const searchParams = new URLSearchParams({\n                                        locationName: message.data.name,\n                                        lat: message.data.lat.toString(),\n                                        lng: message.data.lng.toString(),\n                                        checkIn: context?.checkIn || new Date(Date.now() + 86400000).toISOString().split('T')[0],\n                                        checkOut: context?.checkOut || new Date(Date.now() + 2 * 86400000).toISOString().split('T')[0],\n                                        guests: context?.guests || '2',\n                                        rooms: context?.rooms || '1'\n                                      });\n\n                                      if (onNavigate) {\n                                        onNavigate(`/results?${searchParams.toString()}`);\n                                      } else {\n                                        navigate(`/results?${searchParams.toString()}`);\n                                      }\n                                      onClose?.();\n                                    }}\n                                    className=\"bg-gradient-to-r from-blue-500 via-indigo-600 to-purple-700 hover:from-blue-600 hover:via-indigo-700 hover:to-purple-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105\"\n                                  >\n                                    <Search className=\"w-4 h-4 mr-2\" />\n                                    🏨 Search Here\n                                  </Button>\n\n                                  <Button\n                                    variant=\"outline\"\n                                    size=\"sm\"\n                                    onClick={() => {\n                                      const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${message.data.lat},${message.data.lng}&zoom=15`;\n                                      window.open(mapsUrl, '_blank');\n                                    }}\n                                    className=\"border-blue-300 hover:bg-blue-50 dark:border-blue-600 dark:hover:bg-blue-900/30 transition-all duration-200\"\n                                  >\n                                    <Globe className=\"w-4 h-4 mr-2\" />\n                                    🗺️ Full Map\n                                  </Button>\n\n                                  <Button\n                                    variant=\"outline\"\n                                    size=\"sm\"\n                                    onClick={() => {\n                                      setInput(`Show me nearby attractions, restaurants, and points of interest around ${message.data.name}. Include walking distances and local transportation options!`);\n                                      inputRef.current?.focus();\n                                    }}\n                                    className=\"border-green-300 hover:bg-green-50 dark:border-green-600 dark:hover:bg-green-900/30 transition-all duration-200\"\n                                  >\n                                    <Navigation className=\"w-4 h-4 mr-2\" />\n                                    🎯 Explore Area\n                                  </Button>\n\n                                  <Button\n                                    variant=\"outline\"\n                                    size=\"sm\"\n                                    onClick={() => {\n                                      setInput(`Tell me about ${message.data.name} - best neighborhoods to stay, local culture, weather patterns, and insider travel tips!`);\n                                      inputRef.current?.focus();\n                                    }}\n                                    className=\"border-purple-300 hover:bg-purple-50 dark:border-purple-600 dark:hover:bg-purple-900/30 transition-all duration-200\"\n                                  >\n                                    <Sparkles className=\"w-4 h-4 mr-2\" />\n                                    ✨ Local Insights\n                                  </Button>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n\n                          {/* Bottom action bar */}\n                          <div className=\"mt-6 pt-4 border-t border-blue-200/50 dark:border-blue-700/50\">\n                            <div className=\"flex items-center justify-between text-xs text-gray-600 dark:text-gray-400\">\n                              <span className=\"flex items-center gap-1\">\n                                <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                                Ready to explore this destination\n                              </span>\n                              <span className=\"flex items-center gap-1\">\n                                <Clock className=\"w-3 h-3\" />\n                                Just found • {new Date().toLocaleTimeString()}\n                              </span>\n                            </div>\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Enhanced Property Display */}\n                      {message.type === 'properties' && message.data && (\n                        <div className=\"mt-4 space-y-4\">\n                          {message.data.slice(0, 3).map((property: PropertyRecommendation, index: number) => (\n                            <div\n                              key={index}\n                              className=\"p-4 border rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 hover:shadow-lg transition-all duration-200\"\n                            >\n                              <div className=\"flex items-start gap-4\">\n                                <div className=\"w-20 h-20 rounded-xl bg-gradient-to-br from-purple-500 to-blue-600 flex items-center justify-center flex-shrink-0\">\n                                  <Hotel className=\"w-8 h-8 text-white\" />\n                                </div>\n                                <div className=\"flex-1 min-w-0\">\n                                  <div className=\"flex items-start justify-between gap-2 mb-2\">\n                                    <h4 className=\"font-bold text-base text-gray-900 dark:text-gray-100 leading-tight\">\n                                      {property.name}\n                                    </h4>\n                                    <div className=\"flex items-center gap-1 flex-shrink-0 bg-yellow-100 dark:bg-yellow-900/30 px-2 py-1 rounded-full\">\n                                      <Star className=\"w-3 h-3 fill-yellow-500 text-yellow-500\" />\n                                      <span className=\"text-xs font-medium text-yellow-700 dark:text-yellow-300\">\n                                        {property.rating}\n                                      </span>\n                                    </div>\n                                  </div>\n\n                                  <div className=\"flex items-center gap-4 mb-3 text-sm text-gray-600 dark:text-gray-400\">\n                                    {property.bedrooms && (\n                                      <div className=\"flex items-center gap-1\">\n                                        <Bed className=\"w-4 h-4\" />\n                                        <span>{property.bedrooms} bedroom{property.bedrooms > 1 ? 's' : ''}</span>\n                                      </div>\n                                    )}\n                                    <div className=\"flex items-center gap-1\">\n                                      <MapPin className=\"w-4 h-4\" />\n                                      <span>Prime location</span>\n                                    </div>\n                                  </div>\n\n                                  <div className=\"flex items-center justify-between mb-3\">\n                                    <div className=\"flex items-baseline gap-1\">\n                                      <span className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n                                        ${property.price}\n                                      </span>\n                                      <span className=\"text-sm text-gray-500\">\n                                        /{property.currency === 'USD' ? 'night' : property.currency}\n                                      </span>\n                                    </div>\n                                    {property.suitabilityScore && property.suitabilityScore > 80 && (\n                                      <Badge className=\"bg-gradient-to-r from-green-500 to-emerald-600 text-white\">\n                                        🎯 Perfect Match\n                                      </Badge>\n                                    )}\n                                  </div>\n\n                                  {property.highlights && property.highlights.length > 0 && (\n                                    <div className=\"flex flex-wrap gap-2 mb-3\">\n                                      {property.highlights.slice(0, 3).map((highlight, idx) => (\n                                        <Badge key={idx} variant=\"outline\" className=\"text-xs bg-blue-50 dark:bg-blue-900/30 border-blue-200 dark:border-blue-700\">\n                                          ✨ {highlight}\n                                        </Badge>\n                                      ))}\n                                    </div>\n                                  )}\n\n                                  {property.reasonForRecommendation && (\n                                    <p className=\"text-sm text-gray-700 dark:text-gray-300 mb-3 italic bg-blue-50 dark:bg-blue-900/20 p-2 rounded-lg\">\n                                      💡 \"{property.reasonForRecommendation}\"\n                                    </p>\n                                  )}\n\n                                  <div className=\"flex gap-2\">\n                                    <Button\n                                      size=\"sm\"\n                                      onClick={() => onPropertySelect?.(property)}\n                                      className=\"bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 flex-1\"\n                                    >\n                                      <Eye className=\"w-4 h-4 mr-2\" />\n                                      View Details\n                                    </Button>\n                                    <Button\n                                      variant=\"outline\"\n                                      size=\"sm\"\n                                      onClick={() => {\n                                        setInput(`Tell me more about ${property.name} and why it's perfect for my trip`);\n                                        inputRef.current?.focus();\n                                      }}\n                                    >\n                                      <MessageCircle className=\"w-4 h-4\" />\n                                    </Button>\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n\n                          {message.data.length > 3 && (\n                            <div className=\"mt-4 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-xl border border-indigo-200 dark:border-indigo-700\">\n                              <div className=\"text-center\">\n                                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">\n                                  🎉 I found <strong>{message.data.length} amazing properties</strong> that match your needs!\n                                </p>\n                                <Button\n                                  size=\"lg\"\n                                  className=\"w-full bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700\"\n                                  onClick={() => {\n                                    // Navigate to results with context\n                                    const searchParams = new URLSearchParams({\n                                      ...(context?.location && { locationName: context.location }),\n                                      ...(context?.checkIn && { checkIn: context.checkIn }),\n                                      ...(context?.checkOut && { checkOut: context.checkOut }),\n                                      ...(context?.guests && { guests: context.guests }),\n                                      ...(context?.rooms && { rooms: context.rooms }),\n                                      aiRecommended: 'true'\n                                    });\n\n                                    if (onNavigate) {\n                                      onNavigate(`/results?${searchParams.toString()}`);\n                                    } else {\n                                      navigate(`/results?${searchParams.toString()}`);\n                                    }\n                                    onClose?.();\n                                  }}\n                                >\n                                  <Search className=\"w-5 h-5 mr-2\" />\n                                  Explore All {message.data.length} Properties\n                                </Button>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                      )}\n\n                      {/* Experience Recommendations */}\n                      {message.type === 'experience' && message.data && (\n                        <div className=\"mt-3 space-y-2\">\n                          {message.data.map((experience: ExperienceRecommendation, index: number) => (\n                            <div key={index} className=\"p-3 border rounded-lg bg-background\">\n                              <div className=\"flex items-start gap-3\">\n                                <div className=\"w-8 h-8 rounded-full bg-accent flex items-center justify-center flex-shrink-0\">\n                                  {experience.type === 'restaurant' && <Utensils className=\"w-4 h-4\" />}\n                                  {experience.type === 'attraction' && <Camera className=\"w-4 h-4\" />}\n                                  {experience.type === 'activity' && <Mountain className=\"w-4 h-4\" />}\n                                  {experience.type === 'transportation' && <Car className=\"w-4 h-4\" />}\n                                  {experience.type === 'event' && <Calendar className=\"w-4 h-4\" />}\n                                </div>\n                                <div className=\"flex-1\">\n                                  <h5 className=\"font-medium text-sm\">{experience.name}</h5>\n                                  <p className=\"text-xs text-muted-foreground mt-1\">{experience.description}</p>\n                                  <div className=\"flex items-center gap-4 mt-2 text-xs text-muted-foreground\">\n                                    {experience.duration && (\n                                      <div className=\"flex items-center gap-1\">\n                                        <Clock className=\"w-3 h-3\" />\n                                        {experience.duration}\n                                      </div>\n                                    )}\n                                    {experience.priceRange && (\n                                      <div className=\"flex items-center gap-1\">\n                                        <DollarSign className=\"w-3 h-3\" />\n                                        {experience.priceRange}\n                                      </div>\n                                    )}\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      )}\n\n                      {/* Travel Insights */}\n                      {message.type === 'insight' && message.data && (\n                        <div className=\"mt-3 space-y-2\">\n                          {message.data.map((insight: TravelInsight, index: number) => (\n                            <div\n                              key={index}\n                              className={cn(\n                                \"p-3 border rounded-lg\",\n                                insight.importance === 'high' ? 'border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-900/20' :\n                                insight.importance === 'medium' ? 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20' :\n                                'border-gray-200 bg-gray-50 dark:border-gray-800 dark:bg-gray-900/20'\n                              )}\n                            >\n                              <div className=\"flex items-start gap-2\">\n                                <div className={cn(\n                                  \"w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0\",\n                                  insight.importance === 'high' ? 'bg-orange-100 dark:bg-orange-900' :\n                                  insight.importance === 'medium' ? 'bg-blue-100 dark:bg-blue-900' :\n                                  'bg-gray-100 dark:bg-gray-900'\n                                )}>\n                                  {insight.type === 'weather' && <Waves className=\"w-3 h-3\" />}\n                                  {insight.type === 'events' && <Calendar className=\"w-3 h-3\" />}\n                                  {insight.type === 'pricing' && <TrendingUp className=\"w-3 h-3\" />}\n                                  {insight.type === 'local_tips' && <Navigation className=\"w-3 h-3\" />}\n                                  {insight.type === 'safety' && <Heart className=\"w-3 h-3\" />}\n                                </div>\n                                <div className=\"flex-1\">\n                                  <h5 className=\"font-medium text-sm\">{insight.title}</h5>\n                                  <p className=\"text-xs text-muted-foreground mt-1\">{insight.content}</p>\n                                  {insight.actionable && (\n                                    <Badge variant=\"outline\" className=\"text-xs mt-2\">\n                                      Actionable Tip\n                                    </Badge>\n                                  )}\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      )}\n\n                      {/* Action Items */}\n                      {message.type === 'action' && message.data && (\n                        <div className=\"mt-3\">\n                          <Button\n                            variant=\"outline\"\n                            size=\"sm\"\n                            onClick={() => {\n                              if (message.data.type === 'location') {\n                                handleLocationResponse(message.data.data);\n                              } else if (message.data.type === 'search') {\n                                setInput(message.data.label);\n                                inputRef.current?.focus();\n                              }\n                            }}\n                          >\n                            {message.data.label}\n                          </Button>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                {message.role === 'user' && (\n                  <Avatar className=\"w-8 h-8\">\n                    <AvatarFallback>\n                      <User className=\"w-4 h-4\" />\n                    </AvatarFallback>\n                  </Avatar>\n                )}\n              </div>\n            ))}\n\n            {isLoading && (\n              <div className=\"flex gap-3 justify-start\">\n                <Avatar className=\"w-8 h-8\">\n                  <AvatarFallback>\n                    <Bot className=\"w-4 h-4\" />\n                  </AvatarFallback>\n                </Avatar>\n                <div className=\"bg-secondary text-secondary-foreground rounded-lg px-4 py-2\">\n                  <div className=\"flex items-center gap-2\">\n                    <Loader2 className=\"w-4 h-4 animate-spin\" />\n                    <span className=\"text-sm\">Thinking...</span>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {messages.length === 1 && (\n              <div className=\"p-6 space-y-6\">\n                <div className=\"text-center\">\n                  <h3 className=\"text-xl font-bold text-gray-900 dark:text-gray-100 mb-3\">\n                    ✨ What can I help you with today?\n                  </h3>\n                  <p className=\"text-gray-600 dark:text-gray-400 mb-6\">\n                    {context?.location\n                      ? `I see you're interested in ${context.location}. Let me help you find the perfect place to stay!`\n                      : \"I'm your AI travel expert - just tell me where you'd like to go or what you're looking for!\"\n                    }\n                  </p>\n                  {/* Debug context - remove in production */}\n                  {process.env.NODE_ENV === 'development' && context && (\n                    <div className=\"text-xs text-gray-400 mb-4 p-2 bg-gray-100 dark:bg-gray-800 rounded\">\n                      Debug Context: {JSON.stringify(context, null, 2)}\n                    </div>\n                  )}\n                </div>\n                <div className=\"space-y-3\">\n                  {quickActions.slice(0, 4).map((action, index) => (\n                    <Button\n                      key={index}\n                      variant=\"outline\"\n                      className=\"w-full h-auto p-4 text-left hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-blue-900/20 dark:hover:to-purple-900/20 transition-all duration-200 border-2 hover:border-blue-200\"\n                      onClick={() => {\n                        console.log('Quick action clicked:', action.action);\n                        handleQuickAction(action.action);\n                      }}\n                    >\n                      <div className=\"flex items-center gap-4 w-full\">\n                        <div className=\"w-12 h-12 rounded-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 flex items-center justify-center flex-shrink-0\">\n                          {action.icon}\n                        </div>\n                        <div className=\"flex-1 text-left\">\n                          <div className=\"font-semibold text-base text-gray-900 dark:text-gray-100\">{action.label}</div>\n                          <div className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                            {action.description || (action.action.length > 80 ? action.action.substring(0, 80) + '...' : action.action)}\n                          </div>\n                        </div>\n                        <div className=\"text-blue-500\">\n                          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                          </svg>\n                        </div>\n                      </div>\n                    </Button>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        </ScrollArea>\n      </CardContent>\n\n      <CardFooter className=\"border-t p-4 bg-gray-50 dark:bg-gray-900/50\">\n        <form onSubmit={handleSubmit} className=\"flex gap-3 w-full\">\n          <Input\n            ref={inputRef}\n            value={input}\n            onChange={(e) => setInput(e.target.value)}\n            placeholder=\"Ask me anything about travel...\"\n            className=\"flex-1\"\n            disabled={isLoading}\n          />\n          <Button type=\"submit\" disabled={!input.trim() || isLoading}>\n            <Send className=\"w-4 h-4\" />\n          </Button>\n        </form>\n      </CardFooter>\n    </Card>\n  );\n}\n"}