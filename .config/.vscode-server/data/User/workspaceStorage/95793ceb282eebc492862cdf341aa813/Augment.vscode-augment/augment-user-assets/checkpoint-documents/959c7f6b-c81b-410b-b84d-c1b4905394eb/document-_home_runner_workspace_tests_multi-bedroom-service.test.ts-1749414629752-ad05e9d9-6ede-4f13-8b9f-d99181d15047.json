{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/multi-bedroom-service.test.ts"}, "modifiedCode": "import { describe, it, expect, beforeEach, vi } from 'vitest';\nimport { searchMultiBedroomAccommodations, MultiBedroomSearchParams } from '../server/services/multiBedroomService.js';\nimport * as travsrvService from '../server/services/travsrv.js';\nimport * as geocodingService from '../server/services/geocodingService.js';\n\n// Mock external services\nvi.mock('../server/services/travsrv.js');\nvi.mock('../server/services/geocodingService.js');\n\ndescribe('Multi-Bedroom Accommodation Service', () => {\n  const mockLocationData = {\n    name: 'Miami Beach',\n    lat: 25.7617,\n    lng: -80.1918,\n    placeType: 'locality'\n  };\n\n  const mockProperties = [\n    {\n      id: 1,\n      externalId: 'villa-001',\n      name: 'Luxury 4-Bedroom Beach Villa',\n      description: 'Stunning 4-bedroom, 3-bathroom villa with ocean views, full kitchen, private pool, and game room. Perfect for families and groups up to 12 guests.',\n      latitude: 25.7617,\n      longitude: -80.1918,\n      address: '123 Ocean Drive',\n      city: 'Miami Beach',\n      state: 'FL',\n      country: 'USA',\n      rating: 4.8,\n      reviewCount: 156,\n      basePrice: 450,\n      currency: 'USD',\n      propertyType: 'villa',\n      amenities: ['pool', 'kitchen', 'wifi', 'parking', 'bbq', 'game_room', 'family_friendly', 'laundry'],\n      images: ['villa1.jpg', 'villa2.jpg', 'villa3.jpg']\n    },\n    {\n      id: 2,\n      externalId: 'resort-002',\n      name: 'Family Resort with 2-Bedroom Suites',\n      description: 'All-inclusive family resort featuring 2-bedroom, 2-bathroom suites with kitchenette, kids club, and multiple pools.',\n      latitude: 25.7907,\n      longitude: -80.1300,\n      address: '456 Resort Boulevard',\n      city: 'Miami Beach',\n      state: 'FL',\n      country: 'USA',\n      rating: 4.6,\n      reviewCount: 289,\n      basePrice: 320,\n      currency: 'USD',\n      propertyType: 'resort',\n      amenities: ['pool', 'kids_club', 'restaurant', 'wifi', 'spa', 'family_friendly'],\n      images: ['resort1.jpg', 'resort2.jpg']\n    }\n  ];\n\n  beforeEach(() => {\n    vi.clearAllMocks();\n    vi.mocked(geocodingService.geocodeLocation).mockResolvedValue(mockLocationData);\n    vi.mocked(travsrvService.searchTravSrvProperties).mockResolvedValue(mockProperties);\n    vi.mocked(travsrvService.getPropertyAvailability).mockResolvedValue({\n      ratePlans: {\n        'standard': {\n          code: 'standard',\n          description: 'Standard Rate',\n          commissionStatus: 'active',\n          rooms: {\n            'room1': {\n              rate: 450,\n              currency: 'USD',\n              description: 'Master bedroom with king bed',\n              maxOccupancy: 2\n            }\n          }\n        }\n      }\n    });\n  });\n\n  describe('Basic Multi-Bedroom Search', () => {\n    it('should search for multi-bedroom accommodations successfully', async () => {\n      const searchParams: MultiBedroomSearchParams = {\n        location: 'Miami Beach',\n        checkIn: '2024-06-01',\n        checkOut: '2024-06-03',\n        adults: 6,\n        children: 2,\n        bedrooms: 3,\n        groupType: 'family'\n      };\n\n      const results = await searchMultiBedroomAccommodations(searchParams);\n\n      expect(results).toBeDefined();\n      expect(Array.isArray(results)).toBe(true);\n      expect(results.length).toBeGreaterThan(0);\n\n      expect(geocodingService.geocodeLocation).toHaveBeenCalledWith('Miami Beach');\n      expect(travsrvService.searchTravSrvProperties).toHaveBeenCalledWith({\n        latitude: 25.7617,\n        longitude: -80.1918,\n        inDate: '2024-06-01',\n        outDate: '2024-06-03',\n        adults: 6,\n        children: 2,\n        rooms: 3,\n        radius: 25\n      });\n    });\n\n    it('should filter properties by minimum bedroom requirements', async () => {\n      const searchParams: MultiBedroomSearchParams = {\n        location: 'Miami Beach',\n        checkIn: '2024-06-01',\n        checkOut: '2024-06-03',\n        adults: 8,\n        bedrooms: 3,\n        minBedrooms: 3\n      };\n\n      const results = await searchMultiBedroomAccommodations(searchParams);\n\n      results.forEach(property => {\n        expect(property.accommodations.bedrooms).toBeGreaterThanOrEqual(3);\n      });\n    });\n  });\n\n  describe('Group Type Optimization', () => {\n    it('should optimize results for family groups', async () => {\n      const searchParams: MultiBedroomSearchParams = {\n        location: 'Miami Beach',\n        checkIn: '2024-06-01',\n        checkOut: '2024-06-03',\n        adults: 6,\n        children: 3,\n        bedrooms: 3,\n        groupType: 'family'\n      };\n\n      const results = await searchMultiBedroomAccommodations(searchParams);\n\n      const familyResort = results.find(p => p.name.includes('Family Resort'));\n      if (familyResort) {\n        expect(familyResort.suitabilityScore).toBeGreaterThan(70);\n        expect(familyResort.recommendations.bestFor).toContain('Family vacations with children');\n      }\n    });\n\n    it('should optimize results for corporate groups', async () => {\n      const searchParams: MultiBedroomSearchParams = {\n        location: 'Miami Beach',\n        checkIn: '2024-06-01',\n        checkOut: '2024-06-03',\n        adults: 8,\n        bedrooms: 4,\n        groupType: 'corporate'\n      };\n\n      const results = await searchMultiBedroomAccommodations(searchParams);\n\n      results.forEach(property => {\n        if (property.amenities.wifi) {\n          expect(property.suitabilityScore).toBeGreaterThan(60);\n        }\n      });\n    });\n  });\n\n  describe('Property Enhancement', () => {\n    it('should correctly analyze bedroom configurations', async () => {\n      const searchParams: MultiBedroomSearchParams = {\n        location: 'Miami Beach',\n        checkIn: '2024-06-01',\n        checkOut: '2024-06-03',\n        adults: 8,\n        bedrooms: 4\n      };\n\n      const results = await searchMultiBedroomAccommodations(searchParams);\n\n      const villa = results.find(p => p.name.includes('4-Bedroom'));\n      if (villa) {\n        expect(villa.accommodations.bedrooms).toBe(4);\n        expect(villa.accommodations.bathrooms).toBe(3);\n        expect(villa.accommodations.maxOccupancy).toBeGreaterThanOrEqual(8);\n        expect(villa.accommodations.bedConfiguration).toContain('King Bed');\n        expect(villa.accommodations.livingSpaces).toContain('Full Kitchen');\n      }\n    });\n\n    it('should analyze amenities correctly', async () => {\n      const searchParams: MultiBedroomSearchParams = {\n        location: 'Miami Beach',\n        checkIn: '2024-06-01',\n        checkOut: '2024-06-03',\n        adults: 6,\n        bedrooms: 3\n      };\n\n      const results = await searchMultiBedroomAccommodations(searchParams);\n\n      const villa = results.find(p => p.name.includes('Villa'));\n      if (villa) {\n        expect(villa.amenities.kitchen).toBe(true);\n        expect(villa.amenities.pool).toBe(true);\n        expect(villa.amenities.wifi).toBe(true);\n        expect(villa.amenities.parking).toBe(true);\n        expect(villa.amenities.bbq).toBe(true);\n        expect(villa.amenities.gameRoom).toBe(true);\n        expect(villa.amenities.laundry).toBe(true);\n      }\n    });\n\n    it('should calculate pricing correctly', async () => {\n      const searchParams: MultiBedroomSearchParams = {\n        location: 'Miami Beach',\n        checkIn: '2024-06-01',\n        checkOut: '2024-06-03',\n        adults: 6,\n        bedrooms: 3\n      };\n\n      const results = await searchMultiBedroomAccommodations(searchParams);\n\n      const villa = results.find(p => p.name.includes('Villa'));\n      if (villa) {\n        expect(villa.pricing.basePrice).toBe(450);\n        expect(villa.pricing.currency).toBe('USD');\n        expect(villa.pricing.pricePerNight).toBe(450);\n        \n        const expectedBaseTotal = 450 * 2;\n        expect(villa.pricing.totalPrice).toBeGreaterThan(expectedBaseTotal);\n        \n        expect(villa.pricing.fees.length).toBeGreaterThan(0);\n        expect(villa.pricing.fees.some(fee => fee.type === 'cleaning')).toBe(true);\n      }\n    });\n  });\n\n  describe('Error Handling', () => {\n    it('should handle geocoding failures gracefully', async () => {\n      vi.mocked(geocodingService.geocodeLocation).mockResolvedValue(null);\n\n      const searchParams: MultiBedroomSearchParams = {\n        location: 'Unknown Location',\n        checkIn: '2024-06-01',\n        checkOut: '2024-06-03',\n        adults: 4,\n        bedrooms: 2\n      };\n\n      await expect(searchMultiBedroomAccommodations(searchParams))\n        .rejects.toThrow('Unable to find location: Unknown Location');\n    });\n\n    it('should handle empty search results', async () => {\n      vi.mocked(travsrvService.searchTravSrvProperties).mockResolvedValue([]);\n\n      const searchParams: MultiBedroomSearchParams = {\n        location: 'Miami Beach',\n        checkIn: '2024-06-01',\n        checkOut: '2024-06-03',\n        adults: 4,\n        bedrooms: 2\n      };\n\n      const results = await searchMultiBedroomAccommodations(searchParams);\n      expect(results).toEqual([]);\n    });\n  });\n\n  describe('Performance', () => {\n    it('should complete searches within reasonable time', async () => {\n      const startTime = Date.now();\n\n      const searchParams: MultiBedroomSearchParams = {\n        location: 'Miami Beach',\n        checkIn: '2024-06-01',\n        checkOut: '2024-06-03',\n        adults: 6,\n        bedrooms: 3\n      };\n\n      await searchMultiBedroomAccommodations(searchParams);\n\n      const duration = Date.now() - startTime;\n      expect(duration).toBeLessThan(5000);\n    });\n  });\n});\n"}