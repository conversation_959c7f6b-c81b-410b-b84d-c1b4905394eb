{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/SearchForm.tsx"}, "originalCode": "import { useF<PERSON>, ControllerRenderProps } from \"react-hook-form\";\nimport { useLocation } from \"wouter\";\nimport { Button } from \"@/components/ui/button.js\";\nimport { Input } from \"@/components/ui/input.js\";\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n} from \"@/components/ui/form.js\";\nimport { Calendar } from \"@/components/ui/calendar.js\";\nimport { format, addDays, startOfDay } from \"date-fns\";\nimport { CalendarIcon } from \"lucide-react\";\nimport { cn } from \"@/lib/utils.js\";\nimport { useToast } from \"@/hooks/use-toast.js\";\nimport { Popover, PopoverContent, PopoverTrigger } from \"@/components/ui/popover.js\";\nimport { DateRange } from \"react-day-picker\";\nimport { useEffect, useRef, useCallback, useState } from \"react\";\nimport { DatePickerWithRange } from \"@/components/ui/date-range-picker.js\";\n\n// Add Google Maps types\ntype AutocompleteInstance = {\n  getPlace: () => {\n    geometry?: {\n      location: {\n        lat: () => number;\n        lng: () => number;\n      };\n    };\n    formatted_address?: string;\n    name?: string;\n    types?: string[];\n  };\n  addListener: (event: string, handler: () => void) => void;\n};\n\ndeclare global {\n  interface Window {\n    google: {\n      maps: {\n        places: {\n          Autocomplete: new (\n            input: HTMLInputElement, \n            options?: {\n              types?: string[];\n              fields?: string[];\n            }\n          ) => AutocompleteInstance;\n        };\n        event: {\n          clearInstanceListeners: (instance: any) => void;\n        };\n      };\n    };\n    initGoogleMaps?: () => void;\n  }\n}\n\nexport interface SearchFormValues {\n  location: string;\n  lat: number | null;\n  lng: number | null;\n  locationName: string;\n  placeType: string;\n  checkIn: string;\n  checkOut: string;\n  guests: number;\n  rooms: number;\n  bedrooms?: number;\n}\n\nexport interface SearchFormProps {\n  onSubmit: (data: SearchFormValues) => void;\n  formRef?: React.RefObject<{ submit: () => void }>;\n  onChange?: (data: SearchFormValues) => void;\n}\n\nexport default function SearchForm({ onSubmit: onSubmitProp, formRef, onChange }: SearchFormProps) {\n  const [_, setLocation] = useLocation();\n  const { toast } = useToast();\n  const autocompleteRef = useRef<AutocompleteInstance | null>(null);\n  const inputRef = useRef<HTMLInputElement | null>(null);\n  const today = startOfDay(new Date());\n  const [isGoogleMapsLoaded, setIsGoogleMapsLoaded] = useState(false);\n  const [googleMapsKey, setGoogleMapsKey] = useState<string | null>(null);\n  const keyFetchAttempted = useRef(false);\n  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);\n  const [dateRange, setDateRange] = useState<DateRange>({\n    from: undefined,\n    to: undefined\n  });\n\n  // Add window resize handler\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth < 768);\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  const form = useForm<SearchFormValues>({\n    defaultValues: {\n      location: \"\",\n      lat: null,\n      lng: null,\n      locationName: \"\",\n      placeType: \"\",\n      checkIn: \"\",\n      checkOut: \"\",\n      guests: 2,\n      rooms: 1\n    },\n  });\n\n  // Fetch API key once\n  useEffect(() => {\n    const fetchApiKey = async () => {\n      if (keyFetchAttempted.current) return;\n      keyFetchAttempted.current = true;\n\n      try {\n        const response = await fetch('/api/config');\n        const config = await response.json();\n        if (config.googleMapsApiKey) {\n          setGoogleMapsKey(config.googleMapsApiKey);\n        } else {\n          throw new Error('Google Maps API key not found');\n        }\n      } catch (error) {\n        console.error('Failed to fetch API key:', error);\n        toast({\n          title: \"Error\",\n          description: \"Failed to initialize location search.\",\n          variant: \"destructive\",\n        });\n      }\n    };\n\n    fetchApiKey();\n  }, [toast]);\n\n  // Load Google Maps script\n  useEffect(() => {\n    if (!googleMapsKey) return;\n\n    const loadGoogleMaps = async () => {\n      try {\n        // Check if script is already loaded and initialized properly\n        if (window.google?.maps?.places?.Autocomplete) {\n          setIsGoogleMapsLoaded(true);\n          return;\n        }\n\n        // Remove any existing scripts and callbacks\n        const cleanup = () => {\n          const scripts = document.querySelectorAll('script[src*=\"maps.googleapis.com\"]');\n          scripts.forEach(script => script.remove());\n          // Fix TypeScript error with optional chaining\n          if (window.google) {\n            window.google = undefined as any;\n          }\n        };\n        cleanup();\n\n        // Create a script element\n        const script = document.createElement('script');\n        script.src = `https://maps.googleapis.com/maps/api/js?key=${googleMapsKey}&libraries=places`;\n        script.async = true;\n\n        // Create a promise to handle script loading\n        await new Promise<void>((resolve, reject) => {\n          script.addEventListener('load', () => {\n            if (window.google?.maps?.places?.Autocomplete) {\n              setIsGoogleMapsLoaded(true);\n              resolve();\n            } else {\n              reject(new Error('Google Maps failed to initialize'));\n            }\n          });\n\n          script.addEventListener('error', () => {\n            cleanup();\n            reject(new Error('Failed to load Google Maps script'));\n          });\n\n          document.head.appendChild(script);\n        });\n\n      } catch (error) {\n        console.error('Failed to load Google Maps:', error);\n        toast({\n          title: \"Error\",\n          description: \"Location search is temporarily unavailable.\",\n          variant: \"destructive\",\n        });\n      }\n    };\n\n    loadGoogleMaps();\n\n    // Cleanup on unmount\n    return () => {\n      const scripts = document.querySelectorAll('script[src*=\"maps.googleapis.com\"]');\n      scripts.forEach(script => {\n        if (!window.google?.maps?.places?.Autocomplete) {\n          script.remove();\n        }\n      });\n    };\n  }, [googleMapsKey, toast]);\n\n  // Initialize autocomplete when input is ready and Google Maps is loaded\n  useEffect(() => {\n    if (inputRef.current && isGoogleMapsLoaded && window.google?.maps?.places) {\n      // Prevent form submission on enter\n      const handleKeyDown = (e: KeyboardEvent) => {\n        if (e.key === 'Enter') {\n          e.preventDefault();\n        }\n      };\n      inputRef.current.addEventListener('keydown', handleKeyDown);\n\n      const autocomplete = new window.google.maps.places.Autocomplete(inputRef.current, {\n        types: ['(cities)'],  // Only use cities type\n        fields: ['geometry.location', 'formatted_address', 'name', 'types']\n      });\n\n      autocomplete.addListener('place_changed', () => {\n        const place = autocomplete.getPlace();\n        \n        if (!place.geometry?.location) {\n          toast({\n            title: \"Error\",\n            description: \"Please select a location from the suggestions.\",\n            variant: \"destructive\",\n          });\n          return;\n        }\n\n        const lat = place.geometry.location.lat();\n        const lng = place.geometry.location.lng();\n        const locationName = place.formatted_address || place.name || '';\n        \n        let placeType = 'unknown';\n        if (place.types && place.types.length > 0) {\n          const specificTypes = place.types.filter(type => \n            !['political', 'geocode'].includes(type)\n          );\n          placeType = specificTypes[0] || place.types[0];\n        }\n\n        form.setValue('location', locationName);\n        form.setValue('locationName', locationName);\n        form.setValue('lat', lat);\n        form.setValue('lng', lng);\n        form.setValue('placeType', placeType);\n      });\n\n      autocompleteRef.current = autocomplete;\n\n      // Cleanup function\n      return () => {\n        if (inputRef.current) {\n          inputRef.current.removeEventListener('keydown', handleKeyDown);\n        }\n        if (autocomplete) {\n          window.google?.maps?.event?.clearInstanceListeners(autocomplete);\n        }\n      };\n    }\n  }, [inputRef.current, isGoogleMapsLoaded, form, toast]);\n\n  const handleSubmit = async (data: SearchFormValues) => {\n    if (!data.lat || !data.lng) {\n      toast({\n        title: \"Error\",\n        description: \"Please select a location from the dropdown\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    // If dates are not provided, set default dates (tomorrow and day after)\n    let checkIn = data.checkIn;\n    let checkOut = data.checkOut;\n    \n    if (!checkIn || !checkOut) {\n      const tomorrow = addDays(new Date(), 1);\n      const dayAfter = addDays(new Date(), 2);\n      checkIn = format(tomorrow, \"yyyy-MM-dd\");\n      checkOut = format(dayAfter, \"yyyy-MM-dd\");\n      \n      // Optionally show a toast to inform the user about default dates\n      if (!data.checkIn && !data.checkOut) {\n        toast({\n          title: \"Default dates used\",\n          description: `Using ${format(tomorrow, \"MMM d\")} - ${format(dayAfter, \"MMM d\")}. You can change dates on the results page.`,\n          variant: \"default\",\n        });\n      }\n    }\n\n    try {\n      // Construct search URL with all required parameters\n      const searchParams = new URLSearchParams({\n        lat: data.lat.toString(),\n        lng: data.lng.toString(),\n        locationName: data.locationName || '',\n        checkIn: checkIn,\n        checkOut: checkOut,\n        guests: data.guests.toString(),\n        rooms: data.rooms.toString()\n      });\n      \n      // Log the URL parameters for debugging\n      console.log('Submitting search with params:', Object.fromEntries(searchParams.entries()));\n      \n      // Use window.location.href to ensure full URL path\n      const searchUrl = `/results?${searchParams.toString()}`;\n      console.log('Navigating to:', searchUrl);\n      \n      // Update URL and trigger navigation\n      window.history.pushState({}, '', searchUrl);\n      setLocation(searchUrl);\n      \n      // Call the provided onSubmit prop with the form data (including default dates)\n      if (onSubmitProp) {\n        onSubmitProp({\n          ...data,\n          checkIn,\n          checkOut\n        });\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n      toast({\n        title: \"Error\",\n        description: \"An error occurred while searching. Please try again.\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  // Handle input ref assignment\n  const handleInputRef = useCallback((element: HTMLInputElement | null) => {\n    inputRef.current = element;\n  }, []);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      if (autocompleteRef.current && window.google?.maps) {\n        window.google.maps.event.clearInstanceListeners(autocompleteRef.current);\n      }\n    };\n  }, []);\n\n  // Create a submit function that can be called externally\n  useEffect(() => {\n    if (formRef) {\n      formRef.current = {\n        submit: () => form.handleSubmit(handleSubmit)()\n      };\n    }\n  }, [form, handleSubmit, formRef]);\n\n  // Watch form values and call onChange\n  useEffect(() => {\n    const subscription = form.watch((data) => {\n      onChange?.(data as SearchFormValues);\n    });\n    return () => subscription.unsubscribe();\n  }, [form, onChange]);\n\n  return (\n    <Form {...form}>\n      <form onSubmit={form.handleSubmit(handleSubmit)} className=\"space-y-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-12 gap-4\">\n          <FormField\n            control={form.control}\n            name=\"location\"\n            render={({ field }: { field: ControllerRenderProps<SearchFormValues, 'location'> }) => {\n              const { ref: _ref, ...fieldWithoutRef } = field;\n              return (\n                <FormItem className=\"md:col-span-5\">\n                  <FormLabel>Where</FormLabel>\n                  <FormControl>\n                    <Input\n                      ref={handleInputRef}\n                      placeholder=\"Enter destination\"\n                      {...fieldWithoutRef}\n                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {\n                        field.onChange(e);\n                        // Clear location data when user starts typing\n                        if (e.target.value === '') {\n                          form.setValue('lat', null);\n                          form.setValue('lng', null);\n                          form.setValue('locationName', '');\n                        }\n                      }}\n                      className=\"w-full\"\n                      autoComplete=\"off\" // Prevent browser autocomplete from interfering\n                    />\n                  </FormControl>\n                </FormItem>\n              );\n            }}\n          />\n\n          <div className=\"md:col-span-5\">\n            <FormField\n              control={form.control}\n              name=\"checkIn\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel>Dates (optional)</FormLabel>\n                  <FormControl>\n                    <DatePickerWithRange\n                      date={dateRange}\n                      onChange={(range: DateRange) => {\n                        setDateRange(range);\n                        if (range.from) {\n                          form.setValue(\"checkIn\", format(range.from, \"yyyy-MM-dd\"));\n                        } else {\n                          form.setValue(\"checkIn\", \"\");\n                        }\n                        if (range.to) {\n                          form.setValue(\"checkOut\", format(range.to, \"yyyy-MM-dd\"));\n                        } else {\n                          form.setValue(\"checkOut\", \"\");\n                        }\n                      }}\n                      className=\"w-full\"\n                    />\n                  </FormControl>\n                </FormItem>\n              )}\n            />\n          </div>\n\n          <FormField\n            control={form.control}\n            name=\"guests\"\n            render={({ field }: { field: ControllerRenderProps<SearchFormValues, 'guests'> }) => (\n              <FormItem className=\"md:col-span-2\">\n                <FormLabel>Guests</FormLabel>\n                <FormControl>\n                  <Input\n                    type=\"number\"\n                    min={1}\n                    max={10}\n                    {...field}\n                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => field.onChange(parseInt(e.target.value))}\n                  />\n                </FormControl>\n              </FormItem>\n            )}\n          />\n        </div>\n      </form>\n    </Form>\n  );\n}", "modifiedCode": "import { useF<PERSON>, ControllerRenderProps } from \"react-hook-form\";\nimport { useLocation } from \"wouter\";\nimport { Button } from \"@/components/ui/button.js\";\nimport { Input } from \"@/components/ui/input.js\";\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n} from \"@/components/ui/form.js\";\nimport { Calendar } from \"@/components/ui/calendar.js\";\nimport { format, addDays, startOfDay } from \"date-fns\";\nimport { CalendarIcon } from \"lucide-react\";\nimport { cn } from \"@/lib/utils.js\";\nimport { useToast } from \"@/hooks/use-toast.js\";\nimport { Popover, PopoverContent, PopoverTrigger } from \"@/components/ui/popover.js\";\nimport { DateRange } from \"react-day-picker\";\nimport { useEffect, useRef, useCallback, useState } from \"react\";\nimport { DatePickerWithRange } from \"@/components/ui/date-range-picker.js\";\n\n// Add Google Maps types\ntype AutocompleteInstance = {\n  getPlace: () => {\n    geometry?: {\n      location: {\n        lat: () => number;\n        lng: () => number;\n      };\n    };\n    formatted_address?: string;\n    name?: string;\n    types?: string[];\n  };\n  addListener: (event: string, handler: () => void) => void;\n};\n\ndeclare global {\n  interface Window {\n    google: {\n      maps: {\n        places: {\n          Autocomplete: new (\n            input: HTMLInputElement, \n            options?: {\n              types?: string[];\n              fields?: string[];\n            }\n          ) => AutocompleteInstance;\n        };\n        event: {\n          clearInstanceListeners: (instance: any) => void;\n        };\n      };\n    };\n    initGoogleMaps?: () => void;\n  }\n}\n\nexport interface SearchFormValues {\n  location: string;\n  lat: number | null;\n  lng: number | null;\n  locationName: string;\n  placeType: string;\n  checkIn: string;\n  checkOut: string;\n  guests: number;\n  rooms: number;\n  bedrooms?: number;\n}\n\nexport interface SearchFormProps {\n  onSubmit: (data: SearchFormValues) => void;\n  formRef?: React.RefObject<{ submit: () => void }>;\n  onChange?: (data: SearchFormValues) => void;\n}\n\nexport default function SearchForm({ onSubmit: onSubmitProp, formRef, onChange }: SearchFormProps) {\n  const [_, setLocation] = useLocation();\n  const { toast } = useToast();\n  const autocompleteRef = useRef<AutocompleteInstance | null>(null);\n  const inputRef = useRef<HTMLInputElement | null>(null);\n  const today = startOfDay(new Date());\n  const [isGoogleMapsLoaded, setIsGoogleMapsLoaded] = useState(false);\n  const [googleMapsKey, setGoogleMapsKey] = useState<string | null>(null);\n  const keyFetchAttempted = useRef(false);\n  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);\n  const [dateRange, setDateRange] = useState<DateRange>({\n    from: undefined,\n    to: undefined\n  });\n\n  // Add window resize handler\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth < 768);\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  const form = useForm<SearchFormValues>({\n    defaultValues: {\n      location: \"\",\n      lat: null,\n      lng: null,\n      locationName: \"\",\n      placeType: \"\",\n      checkIn: \"\",\n      checkOut: \"\",\n      guests: 2,\n      rooms: 1,\n      bedrooms: 1\n    },\n  });\n\n  // Fetch API key once\n  useEffect(() => {\n    const fetchApiKey = async () => {\n      if (keyFetchAttempted.current) return;\n      keyFetchAttempted.current = true;\n\n      try {\n        const response = await fetch('/api/config');\n        const config = await response.json();\n        if (config.googleMapsApiKey) {\n          setGoogleMapsKey(config.googleMapsApiKey);\n        } else {\n          throw new Error('Google Maps API key not found');\n        }\n      } catch (error) {\n        console.error('Failed to fetch API key:', error);\n        toast({\n          title: \"Error\",\n          description: \"Failed to initialize location search.\",\n          variant: \"destructive\",\n        });\n      }\n    };\n\n    fetchApiKey();\n  }, [toast]);\n\n  // Load Google Maps script\n  useEffect(() => {\n    if (!googleMapsKey) return;\n\n    const loadGoogleMaps = async () => {\n      try {\n        // Check if script is already loaded and initialized properly\n        if (window.google?.maps?.places?.Autocomplete) {\n          setIsGoogleMapsLoaded(true);\n          return;\n        }\n\n        // Remove any existing scripts and callbacks\n        const cleanup = () => {\n          const scripts = document.querySelectorAll('script[src*=\"maps.googleapis.com\"]');\n          scripts.forEach(script => script.remove());\n          // Fix TypeScript error with optional chaining\n          if (window.google) {\n            window.google = undefined as any;\n          }\n        };\n        cleanup();\n\n        // Create a script element\n        const script = document.createElement('script');\n        script.src = `https://maps.googleapis.com/maps/api/js?key=${googleMapsKey}&libraries=places`;\n        script.async = true;\n\n        // Create a promise to handle script loading\n        await new Promise<void>((resolve, reject) => {\n          script.addEventListener('load', () => {\n            if (window.google?.maps?.places?.Autocomplete) {\n              setIsGoogleMapsLoaded(true);\n              resolve();\n            } else {\n              reject(new Error('Google Maps failed to initialize'));\n            }\n          });\n\n          script.addEventListener('error', () => {\n            cleanup();\n            reject(new Error('Failed to load Google Maps script'));\n          });\n\n          document.head.appendChild(script);\n        });\n\n      } catch (error) {\n        console.error('Failed to load Google Maps:', error);\n        toast({\n          title: \"Error\",\n          description: \"Location search is temporarily unavailable.\",\n          variant: \"destructive\",\n        });\n      }\n    };\n\n    loadGoogleMaps();\n\n    // Cleanup on unmount\n    return () => {\n      const scripts = document.querySelectorAll('script[src*=\"maps.googleapis.com\"]');\n      scripts.forEach(script => {\n        if (!window.google?.maps?.places?.Autocomplete) {\n          script.remove();\n        }\n      });\n    };\n  }, [googleMapsKey, toast]);\n\n  // Initialize autocomplete when input is ready and Google Maps is loaded\n  useEffect(() => {\n    if (inputRef.current && isGoogleMapsLoaded && window.google?.maps?.places) {\n      // Prevent form submission on enter\n      const handleKeyDown = (e: KeyboardEvent) => {\n        if (e.key === 'Enter') {\n          e.preventDefault();\n        }\n      };\n      inputRef.current.addEventListener('keydown', handleKeyDown);\n\n      const autocomplete = new window.google.maps.places.Autocomplete(inputRef.current, {\n        types: ['(cities)'],  // Only use cities type\n        fields: ['geometry.location', 'formatted_address', 'name', 'types']\n      });\n\n      autocomplete.addListener('place_changed', () => {\n        const place = autocomplete.getPlace();\n        \n        if (!place.geometry?.location) {\n          toast({\n            title: \"Error\",\n            description: \"Please select a location from the suggestions.\",\n            variant: \"destructive\",\n          });\n          return;\n        }\n\n        const lat = place.geometry.location.lat();\n        const lng = place.geometry.location.lng();\n        const locationName = place.formatted_address || place.name || '';\n        \n        let placeType = 'unknown';\n        if (place.types && place.types.length > 0) {\n          const specificTypes = place.types.filter(type => \n            !['political', 'geocode'].includes(type)\n          );\n          placeType = specificTypes[0] || place.types[0];\n        }\n\n        form.setValue('location', locationName);\n        form.setValue('locationName', locationName);\n        form.setValue('lat', lat);\n        form.setValue('lng', lng);\n        form.setValue('placeType', placeType);\n      });\n\n      autocompleteRef.current = autocomplete;\n\n      // Cleanup function\n      return () => {\n        if (inputRef.current) {\n          inputRef.current.removeEventListener('keydown', handleKeyDown);\n        }\n        if (autocomplete) {\n          window.google?.maps?.event?.clearInstanceListeners(autocomplete);\n        }\n      };\n    }\n  }, [inputRef.current, isGoogleMapsLoaded, form, toast]);\n\n  const handleSubmit = async (data: SearchFormValues) => {\n    if (!data.lat || !data.lng) {\n      toast({\n        title: \"Error\",\n        description: \"Please select a location from the dropdown\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    // If dates are not provided, set default dates (tomorrow and day after)\n    let checkIn = data.checkIn;\n    let checkOut = data.checkOut;\n    \n    if (!checkIn || !checkOut) {\n      const tomorrow = addDays(new Date(), 1);\n      const dayAfter = addDays(new Date(), 2);\n      checkIn = format(tomorrow, \"yyyy-MM-dd\");\n      checkOut = format(dayAfter, \"yyyy-MM-dd\");\n      \n      // Optionally show a toast to inform the user about default dates\n      if (!data.checkIn && !data.checkOut) {\n        toast({\n          title: \"Default dates used\",\n          description: `Using ${format(tomorrow, \"MMM d\")} - ${format(dayAfter, \"MMM d\")}. You can change dates on the results page.`,\n          variant: \"default\",\n        });\n      }\n    }\n\n    try {\n      // Construct search URL with all required parameters\n      const searchParams = new URLSearchParams({\n        lat: data.lat.toString(),\n        lng: data.lng.toString(),\n        locationName: data.locationName || '',\n        checkIn: checkIn,\n        checkOut: checkOut,\n        guests: data.guests.toString(),\n        rooms: data.rooms.toString()\n      });\n      \n      // Log the URL parameters for debugging\n      console.log('Submitting search with params:', Object.fromEntries(searchParams.entries()));\n      \n      // Use window.location.href to ensure full URL path\n      const searchUrl = `/results?${searchParams.toString()}`;\n      console.log('Navigating to:', searchUrl);\n      \n      // Update URL and trigger navigation\n      window.history.pushState({}, '', searchUrl);\n      setLocation(searchUrl);\n      \n      // Call the provided onSubmit prop with the form data (including default dates)\n      if (onSubmitProp) {\n        onSubmitProp({\n          ...data,\n          checkIn,\n          checkOut\n        });\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n      toast({\n        title: \"Error\",\n        description: \"An error occurred while searching. Please try again.\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  // Handle input ref assignment\n  const handleInputRef = useCallback((element: HTMLInputElement | null) => {\n    inputRef.current = element;\n  }, []);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      if (autocompleteRef.current && window.google?.maps) {\n        window.google.maps.event.clearInstanceListeners(autocompleteRef.current);\n      }\n    };\n  }, []);\n\n  // Create a submit function that can be called externally\n  useEffect(() => {\n    if (formRef) {\n      formRef.current = {\n        submit: () => form.handleSubmit(handleSubmit)()\n      };\n    }\n  }, [form, handleSubmit, formRef]);\n\n  // Watch form values and call onChange\n  useEffect(() => {\n    const subscription = form.watch((data) => {\n      onChange?.(data as SearchFormValues);\n    });\n    return () => subscription.unsubscribe();\n  }, [form, onChange]);\n\n  return (\n    <Form {...form}>\n      <form onSubmit={form.handleSubmit(handleSubmit)} className=\"space-y-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-12 gap-4\">\n          <FormField\n            control={form.control}\n            name=\"location\"\n            render={({ field }: { field: ControllerRenderProps<SearchFormValues, 'location'> }) => {\n              const { ref: _ref, ...fieldWithoutRef } = field;\n              return (\n                <FormItem className=\"md:col-span-5\">\n                  <FormLabel>Where</FormLabel>\n                  <FormControl>\n                    <Input\n                      ref={handleInputRef}\n                      placeholder=\"Enter destination\"\n                      {...fieldWithoutRef}\n                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {\n                        field.onChange(e);\n                        // Clear location data when user starts typing\n                        if (e.target.value === '') {\n                          form.setValue('lat', null);\n                          form.setValue('lng', null);\n                          form.setValue('locationName', '');\n                        }\n                      }}\n                      className=\"w-full\"\n                      autoComplete=\"off\" // Prevent browser autocomplete from interfering\n                    />\n                  </FormControl>\n                </FormItem>\n              );\n            }}\n          />\n\n          <div className=\"md:col-span-5\">\n            <FormField\n              control={form.control}\n              name=\"checkIn\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel>Dates (optional)</FormLabel>\n                  <FormControl>\n                    <DatePickerWithRange\n                      date={dateRange}\n                      onChange={(range: DateRange) => {\n                        setDateRange(range);\n                        if (range.from) {\n                          form.setValue(\"checkIn\", format(range.from, \"yyyy-MM-dd\"));\n                        } else {\n                          form.setValue(\"checkIn\", \"\");\n                        }\n                        if (range.to) {\n                          form.setValue(\"checkOut\", format(range.to, \"yyyy-MM-dd\"));\n                        } else {\n                          form.setValue(\"checkOut\", \"\");\n                        }\n                      }}\n                      className=\"w-full\"\n                    />\n                  </FormControl>\n                </FormItem>\n              )}\n            />\n          </div>\n\n          <FormField\n            control={form.control}\n            name=\"guests\"\n            render={({ field }: { field: ControllerRenderProps<SearchFormValues, 'guests'> }) => (\n              <FormItem className=\"md:col-span-2\">\n                <FormLabel>Guests</FormLabel>\n                <FormControl>\n                  <Input\n                    type=\"number\"\n                    min={1}\n                    max={10}\n                    {...field}\n                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => field.onChange(parseInt(e.target.value))}\n                  />\n                </FormControl>\n              </FormItem>\n            )}\n          />\n        </div>\n      </form>\n    </Form>\n  );\n}"}