{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/ai-travel-assistant.test.ts"}, "modifiedCode": "import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';\nimport request from 'supertest';\nimport { app } from '../server/index.js';\nimport { db } from '../db/index.js';\nimport { properties, users, reservations } from '../db/schema.js';\nimport { eq } from 'drizzle-orm';\n\ndescribe('Ultimate AI Travel Assistant - End-to-End Tests', () => {\n  let testUser: any;\n  let testProperties: any[];\n  let authCookie: string;\n\n  beforeEach(async () => {\n    // Create test user\n    const userResult = await db.insert(users).values({\n      email: '<EMAIL>',\n      firstName: 'Test',\n      lastName: 'User',\n      password: 'hashedpassword'\n    }).returning();\n    testUser = userResult[0];\n\n    // Create test properties with multi-bedroom options\n    const propertyData = [\n      {\n        externalId: 'test-villa-001',\n        name: 'Luxury 4-Bedroom Beach Villa',\n        description: 'Stunning 4-bedroom villa with ocean views, full kitchen, private pool, and game room. Perfect for families and groups.',\n        latitude: 25.7617,\n        longitude: -80.1918,\n        address: '123 Ocean Drive',\n        city: 'Miami Beach',\n        state: 'FL',\n        country: 'USA',\n        rating: 4.8,\n        reviewCount: 156,\n        basePrice: 450,\n        currency: 'USD',\n        propertyType: 'villa',\n        amenities: ['pool', 'kitchen', 'wifi', 'parking', 'bbq', 'game_room', 'family_friendly'],\n        images: ['villa1.jpg', 'villa2.jpg', 'villa3.jpg']\n      },\n      {\n        externalId: 'test-resort-002',\n        name: 'Family Resort with 2-Bedroom Suites',\n        description: 'All-inclusive family resort featuring 2-bedroom suites with kitchenette, kids club, and multiple pools.',\n        latitude: 25.7907,\n        longitude: -80.1300,\n        address: '456 Resort Boulevard',\n        city: 'Miami Beach',\n        state: 'FL',\n        country: 'USA',\n        rating: 4.6,\n        reviewCount: 289,\n        basePrice: 320,\n        currency: 'USD',\n        propertyType: 'resort',\n        amenities: ['pool', 'kids_club', 'restaurant', 'wifi', 'spa', 'family_friendly'],\n        images: ['resort1.jpg', 'resort2.jpg']\n      },\n      {\n        externalId: 'test-apartment-003',\n        name: 'Downtown 3-Bedroom Corporate Apartment',\n        description: 'Modern 3-bedroom apartment in business district with high-speed wifi, meeting space, and concierge.',\n        latitude: 25.7753,\n        longitude: -80.1937,\n        address: '789 Business Center',\n        city: 'Miami',\n        state: 'FL',\n        country: 'USA',\n        rating: 4.4,\n        reviewCount: 94,\n        basePrice: 280,\n        currency: 'USD',\n        propertyType: 'apartment',\n        amenities: ['wifi', 'business_center', 'concierge', 'parking', 'kitchen'],\n        images: ['apt1.jpg', 'apt2.jpg']\n      }\n    ];\n\n    const insertedProperties = await db.insert(properties).values(propertyData).returning();\n    testProperties = insertedProperties;\n\n    // Simulate authentication\n    authCookie = 'session=test-session-id';\n  });\n\n  afterEach(async () => {\n    // Cleanup\n    await db.delete(reservations).where(eq(reservations.userId, testUser.id));\n    await db.delete(properties).where(eq(properties.externalId, 'test-villa-001'));\n    await db.delete(properties).where(eq(properties.externalId, 'test-resort-002'));\n    await db.delete(properties).where(eq(properties.externalId, 'test-apartment-003'));\n    await db.delete(users).where(eq(users.id, testUser.id));\n  });\n\n  describe('AI Chat Intelligence', () => {\n    it('should understand multi-bedroom accommodation requests', async () => {\n      const response = await request(app)\n        .post('/api/chat')\n        .set('Cookie', authCookie)\n        .send({\n          message: 'I need a 4-bedroom villa in Miami Beach for my family reunion with 12 people',\n          context: {\n            conversation: { summary: '', messages: [] },\n            extractLocation: true,\n            enhancedMode: true\n          },\n          sessionId: 'test-session'\n        });\n\n      expect(response.status).toBe(200);\n      \n      // Should detect location\n      expect(response.body).toContain('Miami Beach');\n      \n      // Should understand group size and bedroom requirements\n      expect(response.body.toLowerCase()).toContain('4-bedroom');\n      expect(response.body.toLowerCase()).toContain('family');\n      \n      // Should provide relevant recommendations\n      expect(response.body.toLowerCase()).toContain('villa');\n    });\n\n    it('should provide contextual recommendations based on group type', async () => {\n      const response = await request(app)\n        .post('/api/chat')\n        .send({\n          message: 'Planning a corporate retreat for 20 people in Miami',\n          context: {\n            conversation: { summary: '', messages: [] },\n            groupType: 'corporate',\n            enhancedMode: true\n          },\n          sessionId: 'test-session'\n        });\n\n      expect(response.status).toBe(200);\n      \n      // Should understand corporate context\n      expect(response.body.toLowerCase()).toContain('business');\n      expect(response.body.toLowerCase()).toContain('meeting');\n      \n      // Should suggest appropriate amenities\n      expect(response.body.toLowerCase()).toContain('wifi');\n    });\n\n    it('should handle streaming responses with action tokens', async () => {\n      const response = await request(app)\n        .post('/api/chat')\n        .send({\n          message: 'Show me beachfront properties in Miami',\n          context: {\n            conversation: { summary: '', messages: [] },\n            enhancedMode: true\n          },\n          sessionId: 'test-session'\n        });\n\n      expect(response.status).toBe(200);\n      expect(response.headers['content-type']).toContain('text/plain');\n      \n      // Should contain streaming data\n      expect(response.text).toContain('data:');\n    });\n  });\n\n  describe('Multi-Bedroom Search Integration', () => {\n    it('should search for multi-bedroom accommodations', async () => {\n      const response = await request(app)\n        .get('/api/properties/search')\n        .query({\n          lat: 25.7617,\n          lng: -80.1918,\n          checkIn: '2024-06-01',\n          checkOut: '2024-06-03',\n          guests: '8',\n          rooms: '4',\n          bedrooms: '4',\n          groupType: 'family'\n        });\n\n      expect(response.status).toBe(200);\n      expect(response.body.properties).toBeDefined();\n      expect(response.body.properties.length).toBeGreaterThan(0);\n      \n      // Should include the 4-bedroom villa\n      const villa = response.body.properties.find((p: any) => \n        p.name.includes('4-Bedroom Beach Villa')\n      );\n      expect(villa).toBeDefined();\n    });\n\n    it('should filter properties by bedroom count', async () => {\n      const response = await request(app)\n        .get('/api/properties/search')\n        .query({\n          lat: 25.7617,\n          lng: -80.1918,\n          checkIn: '2024-06-01',\n          checkOut: '2024-06-03',\n          guests: '6',\n          bedrooms: '3',\n          minBedrooms: '3'\n        });\n\n      expect(response.status).toBe(200);\n      \n      // Should only return properties with 3+ bedrooms\n      const properties = response.body.properties;\n      properties.forEach((property: any) => {\n        const bedroomCount = extractBedroomCount(property.description || property.name);\n        expect(bedroomCount).toBeGreaterThanOrEqual(3);\n      });\n    });\n\n    it('should provide suitability scores for group types', async () => {\n      const response = await request(app)\n        .get('/api/properties/search')\n        .query({\n          lat: 25.7617,\n          lng: -80.1918,\n          checkIn: '2024-06-01',\n          checkOut: '2024-06-03',\n          guests: '4',\n          bedrooms: '2',\n          groupType: 'family'\n        });\n\n      expect(response.status).toBe(200);\n      \n      // Family resort should score higher for family groups\n      const familyResort = response.body.properties.find((p: any) => \n        p.name.includes('Family Resort')\n      );\n      \n      if (familyResort) {\n        expect(familyResort.suitabilityScore).toBeGreaterThan(70);\n      }\n    });\n  });\n\n  describe('Enhanced Property Details', () => {\n    it('should provide detailed property information', async () => {\n      const property = testProperties[0]; // Villa\n      \n      const response = await request(app)\n        .get(`/api/properties/${property.id}`);\n\n      expect(response.status).toBe(200);\n      expect(response.body).toMatchObject({\n        id: property.id,\n        name: property.name,\n        description: property.description,\n        amenities: expect.arrayContaining(['pool', 'kitchen', 'wifi'])\n      });\n    });\n\n    it('should include bedroom configuration analysis', async () => {\n      const response = await request(app)\n        .get(`/api/properties/${testProperties[0].id}/details`);\n\n      expect(response.status).toBe(200);\n      \n      // Should analyze bedroom configuration\n      expect(response.body.accommodations).toBeDefined();\n      expect(response.body.accommodations.bedrooms).toBe(4);\n      expect(response.body.accommodations.maxOccupancy).toBeGreaterThan(8);\n    });\n  });\n\n  describe('AI Response Quality', () => {\n    it('should provide personalized recommendations', async () => {\n      const response = await request(app)\n        .post('/api/chat')\n        .send({\n          message: 'I have elderly grandparents and young kids traveling together',\n          context: {\n            conversation: { \n              summary: 'User planning multi-generational family trip',\n              messages: []\n            },\n            groupType: 'multi_generational',\n            enhancedMode: true\n          },\n          sessionId: 'test-session'\n        });\n\n      expect(response.status).toBe(200);\n      \n      // Should understand accessibility needs\n      expect(response.body.toLowerCase()).toContain('accessible');\n      \n      // Should suggest family-friendly amenities\n      expect(response.body.toLowerCase()).toContain('family');\n    });\n\n    it('should provide local insights and experiences', async () => {\n      const response = await request(app)\n        .post('/api/chat')\n        .send({\n          message: 'What should we do in Miami Beach with kids?',\n          context: {\n            conversation: { summary: '', messages: [] },\n            location: 'Miami Beach',\n            enhancedMode: true\n          },\n          sessionId: 'test-session'\n        });\n\n      expect(response.status).toBe(200);\n      \n      // Should provide local recommendations\n      expect(response.body.toLowerCase()).toContain('beach');\n      expect(response.body.toLowerCase()).toContain('family');\n    });\n\n    it('should handle complex travel scenarios', async () => {\n      const response = await request(app)\n        .post('/api/chat')\n        .send({\n          message: 'Planning a wedding party trip for 30 people, need multiple accommodations near each other',\n          context: {\n            conversation: { summary: '', messages: [] },\n            groupType: 'wedding',\n            enhancedMode: true\n          },\n          sessionId: 'test-session'\n        });\n\n      expect(response.status).toBe(200);\n      \n      // Should understand large group logistics\n      expect(response.body.toLowerCase()).toContain('multiple');\n      expect(response.body.toLowerCase()).toContain('wedding');\n      \n      // Should suggest coordination strategies\n      expect(response.body.toLowerCase()).toContain('near');\n    });\n  });\n\n  describe('Performance and Reliability', () => {\n    it('should handle concurrent AI requests', async () => {\n      const requests = Array(5).fill(null).map(() =>\n        request(app)\n          .post('/api/chat')\n          .send({\n            message: 'Find me a vacation rental',\n            context: {\n              conversation: { summary: '', messages: [] },\n              enhancedMode: true\n            },\n            sessionId: `test-session-${Math.random()}`\n          })\n      );\n\n      const responses = await Promise.all(requests);\n      \n      responses.forEach(response => {\n        expect(response.status).toBe(200);\n      });\n    });\n\n    it('should maintain conversation context', async () => {\n      const sessionId = 'test-context-session';\n      \n      // First message\n      const response1 = await request(app)\n        .post('/api/chat')\n        .send({\n          message: 'I want to visit Miami Beach',\n          context: {\n            conversation: { summary: '', messages: [] },\n            enhancedMode: true\n          },\n          sessionId\n        });\n\n      expect(response1.status).toBe(200);\n\n      // Follow-up message should remember context\n      const response2 = await request(app)\n        .post('/api/chat')\n        .send({\n          message: 'Show me 3-bedroom options',\n          context: {\n            conversation: { \n              summary: 'User wants to visit Miami Beach',\n              messages: [\n                { role: 'user', content: 'I want to visit Miami Beach' },\n                { role: 'assistant', content: response1.body }\n              ]\n            },\n            location: 'Miami Beach',\n            enhancedMode: true\n          },\n          sessionId\n        });\n\n      expect(response2.status).toBe(200);\n      \n      // Should remember Miami Beach context\n      expect(response2.body.toLowerCase()).toContain('miami');\n      expect(response2.body.toLowerCase()).toContain('3-bedroom');\n    });\n  });\n});\n\n// Helper function to extract bedroom count from text\nfunction extractBedroomCount(text: string): number {\n  const match = text.toLowerCase().match(/(\\d+)[-\\s]*bedroom/);\n  return match ? parseInt(match[1]) : 1;\n}\n"}