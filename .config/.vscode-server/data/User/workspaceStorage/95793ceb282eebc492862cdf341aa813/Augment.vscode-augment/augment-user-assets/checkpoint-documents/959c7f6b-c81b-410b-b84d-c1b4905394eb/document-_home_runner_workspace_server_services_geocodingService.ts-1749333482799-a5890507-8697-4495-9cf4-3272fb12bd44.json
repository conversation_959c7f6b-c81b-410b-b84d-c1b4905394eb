{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/geocodingService.ts"}, "modifiedCode": "import { Client } from '@googlemaps/google-maps-services-js';\nimport logger from '../utils/logger.js';\n\nconst googleMapsClient = new Client({});\n\nexport interface LocationData {\n  name: string;\n  lat: number;\n  lng: number;\n  placeType: string;\n  placeId?: string;\n  formattedAddress?: string;\n  components?: {\n    city?: string;\n    state?: string;\n    country?: string;\n    postalCode?: string;\n  };\n}\n\nexport interface NearbyPlace {\n  name: string;\n  placeId: string;\n  lat: number;\n  lng: number;\n  types: string[];\n  rating?: number;\n  priceLevel?: number;\n  vicinity?: string;\n}\n\n/**\n * Geocode a location string to coordinates and place details\n */\nexport async function geocodeLocation(locationQuery: string): Promise<LocationData | null> {\n  try {\n    if (!process.env.GOOGLE_MAPS_API_KEY) {\n      logger.warn('Google Maps API key not configured, using fallback');\n      return getFallbackLocation(locationQuery);\n    }\n\n    const response = await googleMapsClient.geocode({\n      params: {\n        address: locationQuery,\n        key: process.env.GOOGLE_MAPS_API_KEY,\n      },\n    });\n\n    if (response.data.results.length === 0) {\n      logger.warn('No geocoding results found', { query: locationQuery });\n      return getFallbackLocation(locationQuery);\n    }\n\n    const result = response.data.results[0];\n    const location = result.geometry.location;\n    \n    // Extract address components\n    const components: LocationData['components'] = {};\n    result.address_components.forEach(component => {\n      if (component.types.includes('locality')) {\n        components.city = component.long_name;\n      } else if (component.types.includes('administrative_area_level_1')) {\n        components.state = component.short_name;\n      } else if (component.types.includes('country')) {\n        components.country = component.long_name;\n      } else if (component.types.includes('postal_code')) {\n        components.postalCode = component.long_name;\n      }\n    });\n\n    // Determine place type\n    const placeType = getPlaceType(result.types);\n\n    return {\n      name: result.formatted_address.split(',')[0], // First part is usually the main name\n      lat: location.lat,\n      lng: location.lng,\n      placeType,\n      placeId: result.place_id,\n      formattedAddress: result.formatted_address,\n      components\n    };\n\n  } catch (error) {\n    logger.error('Geocoding failed', {\n      error: error instanceof Error ? error.message : 'Unknown error',\n      query: locationQuery\n    });\n    return getFallbackLocation(locationQuery);\n  }\n}\n\n/**\n * Reverse geocode coordinates to location details\n */\nexport async function reverseGeocode(lat: number, lng: number): Promise<LocationData | null> {\n  try {\n    if (!process.env.GOOGLE_MAPS_API_KEY) {\n      return null;\n    }\n\n    const response = await googleMapsClient.reverseGeocode({\n      params: {\n        latlng: { lat, lng },\n        key: process.env.GOOGLE_MAPS_API_KEY,\n      },\n    });\n\n    if (response.data.results.length === 0) {\n      return null;\n    }\n\n    const result = response.data.results[0];\n    const location = result.geometry.location;\n    \n    return {\n      name: result.formatted_address.split(',')[0],\n      lat: location.lat,\n      lng: location.lng,\n      placeType: getPlaceType(result.types),\n      placeId: result.place_id,\n      formattedAddress: result.formatted_address\n    };\n\n  } catch (error) {\n    logger.error('Reverse geocoding failed', {\n      error: error instanceof Error ? error.message : 'Unknown error',\n      coordinates: `${lat},${lng}`\n    });\n    return null;\n  }\n}\n\n/**\n * Find nearby places of interest\n */\nexport async function findNearbyPlaces(\n  lat: number, \n  lng: number, \n  radius: number = 5000,\n  type?: string\n): Promise<NearbyPlace[]> {\n  try {\n    if (!process.env.GOOGLE_MAPS_API_KEY) {\n      return [];\n    }\n\n    const response = await googleMapsClient.placesNearby({\n      params: {\n        location: { lat, lng },\n        radius,\n        type: type as any,\n        key: process.env.GOOGLE_MAPS_API_KEY,\n      },\n    });\n\n    return response.data.results.map(place => ({\n      name: place.name || 'Unknown',\n      placeId: place.place_id || '',\n      lat: place.geometry?.location.lat || lat,\n      lng: place.geometry?.location.lng || lng,\n      types: place.types || [],\n      rating: place.rating,\n      priceLevel: place.price_level,\n      vicinity: place.vicinity\n    }));\n\n  } catch (error) {\n    logger.error('Nearby places search failed', {\n      error: error instanceof Error ? error.message : 'Unknown error',\n      coordinates: `${lat},${lng}`,\n      radius,\n      type\n    });\n    return [];\n  }\n}\n\n/**\n * Get place details by place ID\n */\nexport async function getPlaceDetails(placeId: string): Promise<LocationData | null> {\n  try {\n    if (!process.env.GOOGLE_MAPS_API_KEY) {\n      return null;\n    }\n\n    const response = await googleMapsClient.placeDetails({\n      params: {\n        place_id: placeId,\n        key: process.env.GOOGLE_MAPS_API_KEY,\n      },\n    });\n\n    const place = response.data.result;\n    if (!place.geometry?.location) {\n      return null;\n    }\n\n    return {\n      name: place.name || 'Unknown',\n      lat: place.geometry.location.lat,\n      lng: place.geometry.location.lng,\n      placeType: getPlaceType(place.types || []),\n      placeId: place.place_id,\n      formattedAddress: place.formatted_address\n    };\n\n  } catch (error) {\n    logger.error('Place details fetch failed', {\n      error: error instanceof Error ? error.message : 'Unknown error',\n      placeId\n    });\n    return null;\n  }\n}\n\n/**\n * Determine place type from Google Maps types array\n */\nfunction getPlaceType(types: string[]): string {\n  const typeHierarchy = [\n    'establishment',\n    'point_of_interest',\n    'lodging',\n    'tourist_attraction',\n    'locality',\n    'sublocality',\n    'neighborhood',\n    'administrative_area_level_1',\n    'administrative_area_level_2',\n    'country'\n  ];\n\n  for (const type of typeHierarchy) {\n    if (types.includes(type)) {\n      return type;\n    }\n  }\n\n  return 'unknown';\n}\n\n/**\n * Fallback location data for when geocoding fails\n */\nfunction getFallbackLocation(query: string): LocationData | null {\n  const fallbackLocations: Record<string, LocationData> = {\n    'new york': { name: 'New York', lat: 40.7128, lng: -74.0060, placeType: 'locality' },\n    'los angeles': { name: 'Los Angeles', lat: 34.0522, lng: -118.2437, placeType: 'locality' },\n    'chicago': { name: 'Chicago', lat: 41.8781, lng: -87.6298, placeType: 'locality' },\n    'miami': { name: 'Miami', lat: 25.7617, lng: -80.1918, placeType: 'locality' },\n    'san francisco': { name: 'San Francisco', lat: 37.7749, lng: -122.4194, placeType: 'locality' },\n    'las vegas': { name: 'Las Vegas', lat: 36.1699, lng: -115.1398, placeType: 'locality' },\n    'orlando': { name: 'Orlando', lat: 28.5383, lng: -81.3792, placeType: 'locality' },\n    'seattle': { name: 'Seattle', lat: 47.6062, lng: -122.3321, placeType: 'locality' },\n    'boston': { name: 'Boston', lat: 42.3601, lng: -71.0589, placeType: 'locality' },\n    'london': { name: 'London', lat: 51.5074, lng: -0.1278, placeType: 'locality' },\n    'paris': { name: 'Paris', lat: 48.8566, lng: 2.3522, placeType: 'locality' },\n    'tokyo': { name: 'Tokyo', lat: 35.6762, lng: 139.6503, placeType: 'locality' },\n    'sydney': { name: 'Sydney', lat: -33.8688, lng: 151.2093, placeType: 'locality' },\n    'dubai': { name: 'Dubai', lat: 25.2048, lng: 55.2708, placeType: 'locality' }\n  };\n\n  const normalizedQuery = query.toLowerCase().trim();\n  \n  // Try exact match first\n  if (fallbackLocations[normalizedQuery]) {\n    return fallbackLocations[normalizedQuery];\n  }\n\n  // Try partial match\n  for (const [key, location] of Object.entries(fallbackLocations)) {\n    if (normalizedQuery.includes(key) || key.includes(normalizedQuery)) {\n      return location;\n    }\n  }\n\n  logger.warn('No fallback location found', { query });\n  return null;\n}\n"}