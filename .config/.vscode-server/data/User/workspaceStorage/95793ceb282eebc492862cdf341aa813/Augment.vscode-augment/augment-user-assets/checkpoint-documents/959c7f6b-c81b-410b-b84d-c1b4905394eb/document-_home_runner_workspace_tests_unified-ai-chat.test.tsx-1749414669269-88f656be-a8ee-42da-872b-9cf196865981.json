{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/unified-ai-chat.test.tsx"}, "modifiedCode": "import { describe, it, expect, beforeEach, vi } from 'vitest';\nimport { render, screen, fireEvent, waitFor } from '@testing-library/react';\nimport { BrowserRouter } from 'react-router-dom';\nimport UnifiedAIChat from '../client/src/components/UnifiedAIChat';\n\n// Mock the hooks and utilities\nvi.mock('@/hooks/use-toast', () => ({\n  useToast: () => ({\n    toast: vi.fn()\n  })\n}));\n\nvi.mock('wouter', () => ({\n  useLocation: () => ['/test', vi.fn()]\n}));\n\nvi.mock('@/lib/session', () => ({\n  getSessionId: () => 'test-session-id'\n}));\n\n// Mock fetch for API calls\nglobal.fetch = vi.fn();\n\nconst mockFetch = global.fetch as any;\n\ndescribe('UnifiedAIChat Component', () => {\n  const defaultProps = {\n    context: {\n      location: 'Miami Beach',\n      checkIn: '2024-06-01',\n      checkOut: '2024-06-03',\n      guests: '4',\n      bedrooms: '2',\n      groupType: 'family' as const\n    },\n    variant: 'modal' as const,\n    onClose: vi.fn(),\n    onNavigate: vi.fn(),\n    onPropertySelect: vi.fn(),\n    onSearchUpdate: vi.fn()\n  };\n\n  beforeEach(() => {\n    vi.clearAllMocks();\n    mockFetch.mockClear();\n  });\n\n  const renderComponent = (props = {}) => {\n    return render(\n      <BrowserRouter>\n        <UnifiedAIChat {...defaultProps} {...props} />\n      </BrowserRouter>\n    );\n  };\n\n  describe('Initial Rendering', () => {\n    it('should render with contextual welcome message', () => {\n      renderComponent();\n      \n      expect(screen.getByText('AI Travel Companion')).toBeInTheDocument();\n      expect(screen.getByText(/Miami Beach/)).toBeInTheDocument();\n      expect(screen.getByText(/June 1, 2024/)).toBeInTheDocument();\n    });\n\n    it('should show context-aware quick actions', () => {\n      renderComponent();\n      \n      expect(screen.getByText('Explore Miami Beach')).toBeInTheDocument();\n      expect(screen.getByText('2-Bedroom Options')).toBeInTheDocument();\n      expect(screen.getByText('Family-Friendly')).toBeInTheDocument();\n    });\n\n    it('should render without welcome message when showWelcome is false', () => {\n      renderComponent({ showWelcome: false });\n      \n      expect(screen.getByText('AI Travel Companion')).toBeInTheDocument();\n      expect(screen.queryByText(/Miami Beach/)).not.toBeInTheDocument();\n    });\n  });\n\n  describe('Message Handling', () => {\n    it('should send user messages to AI service', async () => {\n      const mockResponse = {\n        ok: true,\n        body: {\n          getReader: () => ({\n            read: vi.fn()\n              .mockResolvedValueOnce({\n                done: false,\n                value: new TextEncoder().encode('data: {\"type\": \"text\", \"data\": \"Hello! I can help you find accommodations.\"}\\n\\n')\n              })\n              .mockResolvedValueOnce({\n                done: true,\n                value: undefined\n              })\n          })\n        }\n      };\n\n      mockFetch.mockResolvedValue(mockResponse);\n\n      renderComponent();\n      \n      const input = screen.getByPlaceholderText('Ask me anything about travel...');\n      const sendButton = screen.getByRole('button', { name: /send/i });\n\n      fireEvent.change(input, { target: { value: 'Find me a family resort' } });\n      fireEvent.click(sendButton);\n\n      await waitFor(() => {\n        expect(mockFetch).toHaveBeenCalledWith('/api/chat', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: expect.stringContaining('Find me a family resort')\n        });\n      });\n    });\n\n    it('should handle property recommendations', async () => {\n      const mockProperties = [\n        {\n          id: 1,\n          name: 'Beach Resort',\n          type: 'resort',\n          bedrooms: 2,\n          price: 300,\n          currency: 'USD',\n          rating: 4.5,\n          image: 'resort.jpg',\n          highlights: ['Pool', 'Beach Access'],\n          reasonForRecommendation: 'Perfect for families'\n        }\n      ];\n\n      const mockResponse = {\n        ok: true,\n        body: {\n          getReader: () => ({\n            read: vi.fn()\n              .mockResolvedValueOnce({\n                done: false,\n                value: new TextEncoder().encode(`data: {\"type\": \"properties\", \"data\": ${JSON.stringify(mockProperties)}}\\n\\n`)\n              })\n              .mockResolvedValueOnce({\n                done: true,\n                value: undefined\n              })\n          })\n        }\n      };\n\n      mockFetch.mockResolvedValue(mockResponse);\n\n      renderComponent();\n      \n      const input = screen.getByPlaceholderText('Ask me anything about travel...');\n      fireEvent.change(input, { target: { value: 'Show me resorts' } });\n      fireEvent.click(screen.getByRole('button', { name: /send/i }));\n\n      await waitFor(() => {\n        expect(screen.getByText('Beach Resort')).toBeInTheDocument();\n        expect(screen.getByText('Perfect for families')).toBeInTheDocument();\n        expect(screen.getByText('$300/USD')).toBeInTheDocument();\n      });\n    });\n\n    it('should handle experience recommendations', async () => {\n      const mockExperiences = [\n        {\n          id: 'exp1',\n          name: 'Snorkeling Tour',\n          type: 'activity' as const,\n          description: 'Explore coral reefs',\n          location: 'Miami Beach',\n          duration: '3 hours',\n          priceRange: '$50-80'\n        }\n      ];\n\n      const mockResponse = {\n        ok: true,\n        body: {\n          getReader: () => ({\n            read: vi.fn()\n              .mockResolvedValueOnce({\n                done: false,\n                value: new TextEncoder().encode(`data: {\"type\": \"experience\", \"data\": ${JSON.stringify(mockExperiences)}}\\n\\n`)\n              })\n              .mockResolvedValueOnce({\n                done: true,\n                value: undefined\n              })\n          })\n        }\n      };\n\n      mockFetch.mockResolvedValue(mockResponse);\n\n      renderComponent();\n      \n      const input = screen.getByPlaceholderText('Ask me anything about travel...');\n      fireEvent.change(input, { target: { value: 'What activities are available?' } });\n      fireEvent.click(screen.getByRole('button', { name: /send/i }));\n\n      await waitFor(() => {\n        expect(screen.getByText('Snorkeling Tour')).toBeInTheDocument();\n        expect(screen.getByText('Explore coral reefs')).toBeInTheDocument();\n        expect(screen.getByText('3 hours')).toBeInTheDocument();\n        expect(screen.getByText('$50-80')).toBeInTheDocument();\n      });\n    });\n\n    it('should handle travel insights', async () => {\n      const mockInsights = [\n        {\n          type: 'weather' as const,\n          title: 'Perfect Beach Weather',\n          content: 'Sunny skies and 80°F temperatures expected',\n          importance: 'medium' as const,\n          actionable: true\n        }\n      ];\n\n      const mockResponse = {\n        ok: true,\n        body: {\n          getReader: () => ({\n            read: vi.fn()\n              .mockResolvedValueOnce({\n                done: false,\n                value: new TextEncoder().encode(`data: {\"type\": \"insight\", \"data\": ${JSON.stringify(mockInsights)}}\\n\\n`)\n              })\n              .mockResolvedValueOnce({\n                done: true,\n                value: undefined\n              })\n          })\n        }\n      };\n\n      mockFetch.mockResolvedValue(mockResponse);\n\n      renderComponent();\n      \n      const input = screen.getByPlaceholderText('Ask me anything about travel...');\n      fireEvent.change(input, { target: { value: 'What\\'s the weather like?' } });\n      fireEvent.click(screen.getByRole('button', { name: /send/i }));\n\n      await waitFor(() => {\n        expect(screen.getByText('Perfect Beach Weather')).toBeInTheDocument();\n        expect(screen.getByText('Sunny skies and 80°F temperatures expected')).toBeInTheDocument();\n        expect(screen.getByText('Actionable Tip')).toBeInTheDocument();\n      });\n    });\n  });\n\n  describe('Quick Actions', () => {\n    it('should trigger quick actions when clicked', () => {\n      renderComponent();\n      \n      const exploreAction = screen.getByText('Explore Miami Beach');\n      fireEvent.click(exploreAction);\n\n      const input = screen.getByPlaceholderText('Ask me anything about travel...');\n      expect(input).toHaveValue('Tell me about the best accommodations and experiences in Miami Beach');\n    });\n\n    it('should show bedroom-specific quick actions', () => {\n      renderComponent({\n        context: {\n          ...defaultProps.context,\n          bedrooms: '4'\n        }\n      });\n      \n      expect(screen.getByText('4-Bedroom Options')).toBeInTheDocument();\n    });\n\n    it('should show group-type specific quick actions', () => {\n      renderComponent({\n        context: {\n          ...defaultProps.context,\n          groupType: 'corporate'\n        }\n      });\n      \n      expect(screen.getByText('Business Travel')).toBeInTheDocument();\n    });\n  });\n\n  describe('Property Interactions', () => {\n    it('should call onPropertySelect when property is clicked', async () => {\n      const mockProperties = [\n        {\n          id: 1,\n          name: 'Test Resort',\n          type: 'resort',\n          bedrooms: 2,\n          price: 300,\n          currency: 'USD',\n          rating: 4.5,\n          image: 'resort.jpg',\n          highlights: ['Pool'],\n          reasonForRecommendation: 'Great choice'\n        }\n      ];\n\n      const mockResponse = {\n        ok: true,\n        body: {\n          getReader: () => ({\n            read: vi.fn()\n              .mockResolvedValueOnce({\n                done: false,\n                value: new TextEncoder().encode(`data: {\"type\": \"properties\", \"data\": ${JSON.stringify(mockProperties)}}\\n\\n`)\n              })\n              .mockResolvedValueOnce({\n                done: true,\n                value: undefined\n              })\n          })\n        }\n      };\n\n      mockFetch.mockResolvedValue(mockResponse);\n\n      renderComponent();\n      \n      const input = screen.getByPlaceholderText('Ask me anything about travel...');\n      fireEvent.change(input, { target: { value: 'Show properties' } });\n      fireEvent.click(screen.getByRole('button', { name: /send/i }));\n\n      await waitFor(() => {\n        const propertyCard = screen.getByText('Test Resort').closest('div');\n        fireEvent.click(propertyCard!);\n        \n        expect(defaultProps.onPropertySelect).toHaveBeenCalledWith(mockProperties[0]);\n      });\n    });\n  });\n\n  describe('Error Handling', () => {\n    it('should handle API errors gracefully', async () => {\n      mockFetch.mockRejectedValue(new Error('Network error'));\n\n      renderComponent();\n      \n      const input = screen.getByPlaceholderText('Ask me anything about travel...');\n      fireEvent.change(input, { target: { value: 'Test message' } });\n      fireEvent.click(screen.getByRole('button', { name: /send/i }));\n\n      await waitFor(() => {\n        expect(screen.getByText(/encountered an error/)).toBeInTheDocument();\n      });\n    });\n\n    it('should handle malformed streaming responses', async () => {\n      const mockResponse = {\n        ok: true,\n        body: {\n          getReader: () => ({\n            read: vi.fn()\n              .mockResolvedValueOnce({\n                done: false,\n                value: new TextEncoder().encode('data: invalid json\\n\\n')\n              })\n              .mockResolvedValueOnce({\n                done: true,\n                value: undefined\n              })\n          })\n        }\n      };\n\n      mockFetch.mockResolvedValue(mockResponse);\n\n      renderComponent();\n      \n      const input = screen.getByPlaceholderText('Ask me anything about travel...');\n      fireEvent.change(input, { target: { value: 'Test message' } });\n      fireEvent.click(screen.getByRole('button', { name: /send/i }));\n\n      // Should not crash and should handle gracefully\n      await waitFor(() => {\n        expect(screen.getByPlaceholderText('Ask me anything about travel...')).toBeInTheDocument();\n      });\n    });\n  });\n\n  describe('Accessibility', () => {\n    it('should be keyboard navigable', () => {\n      renderComponent();\n      \n      const input = screen.getByPlaceholderText('Ask me anything about travel...');\n      input.focus();\n      \n      expect(document.activeElement).toBe(input);\n      \n      fireEvent.keyDown(input, { key: 'Tab' });\n      expect(document.activeElement).toBe(screen.getByRole('button', { name: /send/i }));\n    });\n\n    it('should have proper ARIA labels', () => {\n      renderComponent();\n      \n      expect(screen.getByRole('textbox')).toHaveAttribute('placeholder', 'Ask me anything about travel...');\n      expect(screen.getByRole('button', { name: /send/i })).toBeInTheDocument();\n    });\n  });\n\n  describe('Responsive Design', () => {\n    it('should render correctly in different variants', () => {\n      const { rerender } = renderComponent({ variant: 'floating' });\n      expect(screen.getByText('AI Travel Companion')).toBeInTheDocument();\n      \n      rerender(\n        <BrowserRouter>\n          <UnifiedAIChat {...defaultProps} variant=\"embedded\" />\n        </BrowserRouter>\n      );\n      expect(screen.getByText('AI Travel Companion')).toBeInTheDocument();\n      \n      rerender(\n        <BrowserRouter>\n          <UnifiedAIChat {...defaultProps} variant=\"sidebar\" />\n        </BrowserRouter>\n      );\n      expect(screen.getByText('AI Travel Companion')).toBeInTheDocument();\n    });\n  });\n});\n"}