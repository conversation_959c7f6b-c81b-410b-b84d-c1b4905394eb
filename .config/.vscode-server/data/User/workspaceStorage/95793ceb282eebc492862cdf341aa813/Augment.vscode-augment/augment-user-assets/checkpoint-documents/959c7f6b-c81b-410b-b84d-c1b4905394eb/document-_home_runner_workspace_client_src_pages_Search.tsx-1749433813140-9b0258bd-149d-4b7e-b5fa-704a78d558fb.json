{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Search.tsx"}, "originalCode": "import { useState, useRef } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { \n  MessageCircle, \n  Search as SearchIcon, \n  Star,\n  MapPin\n} from \"lucide-react\";\nimport UnifiedAIChat from \"@/components/UnifiedAIChat\";\nimport SearchForm from \"@/components/SearchForm\";\nimport FeaturedProperties from \"@/components/FeaturedProperties\";\nimport { useLocation } from \"wouter\";\nimport { SearchFormValues } from \"@/components/SearchForm\";\nimport { Card } from \"@/components/ui/card\";\nimport { format } from \"date-fns\";\nimport { cn } from \"@/lib/utils\";\nimport { useSearchHistory } from \"@/hooks/use-search-history\";\nimport RecentSearches from \"@/components/RecentSearches\";\nimport Map from \"@/components/Map\";\n\nexport default function Search() {\n  const [showAiChat, setShowAiChat] = useState(false);\n  const [_, setLocation] = useLocation();\n  const searchFormRef = useRef<{ submit: () => void }>(null);\n  const [searchContext, setSearchContext] = useState({\n    location: null as string | null,\n    checkIn: null as string | null,\n    checkOut: null as string | null,\n    guests: null as string | null,\n    rooms: null as string | null\n  });\n\n  const { \n    searchHistory, \n    addToHistory, \n    clearHistory, \n    repeatSearch \n  } = useSearchHistory();\n\n  const handleSearchSubmit = (params: SearchFormValues) => {\n    const searchParams = new URLSearchParams({\n      lat: params.lat?.toString() || '',\n      lng: params.lng?.toString() || '',\n      locationName: params.locationName || '',\n      checkIn: params.checkIn || '',\n      checkOut: params.checkOut || '',\n      guests: params.guests?.toString() || '',\n      rooms: params.rooms?.toString() || ''\n    });\n\n    // Add to search history\n    addToHistory({\n      locationName: params.locationName || '',\n      lat: params.lat || 0,\n      lng: params.lng || 0,\n      checkIn: params.checkIn || '',\n      checkOut: params.checkOut || '',\n      guests: params.guests || 2,\n      rooms: params.rooms || 1\n    });\n\n    setLocation(`/results?${searchParams.toString()}`);\n  };\n\n  const handleFormUpdate = (data: SearchFormValues) => {\n    setSearchContext({\n      location: data.locationName || null,\n      checkIn: data.checkIn || null,\n      checkOut: data.checkOut || null,\n      guests: data.guests?.toString() || null,\n      rooms: data.rooms?.toString() || null\n    });\n  };\n\n  const getInitialMessage = () => {\n    // First check search history\n    if (searchHistory.length > 0) {\n      const lastSearch = searchHistory[0];\n      return `Hi! I see you recently searched for ${lastSearch.locationName}. Would you like to explore similar destinations or find something different?`;\n    }\n\n    if (!searchContext.location && !searchContext.checkIn && !searchContext.guests) {\n      return \"Hi! I'd like help finding a place to stay. Can you help me explore my options?\";\n    }\n\n    let message = \"Hi! I'm looking for\";\n    if (searchContext.location) {\n      message += ` a place to stay in ${searchContext.location}`;\n    }\n    if (searchContext.checkIn && searchContext.checkOut) {\n      message += ` from ${format(new Date(searchContext.checkIn), 'PPP')} to ${format(new Date(searchContext.checkOut), 'PPP')}`;\n    }\n    if (searchContext.guests) {\n      const guestCount = parseInt(searchContext.guests);\n      message += ` for ${guestCount} ${guestCount > 1 ? 'people' : 'person'}`;\n    }\n    message += \". Can you help me find the perfect place?\";\n    return message;\n  };\n\n  const handleOpenChat = () => {\n    console.log('🎯 Plan with AI button clicked!');\n    \n    if (showAiChat) {\n      console.log('🚫 Chat already open, ignoring click');\n      return;\n    }\n    \n    // Clear relevant localStorage items for a fresh start\n    localStorage.removeItem('chatHistory');\n    localStorage.removeItem('conversationState'); // Keep if you want to persist some AI state across sessions\n    localStorage.removeItem('ai_chat_trigger');\n    localStorage.removeItem('ai_chat_trigger_processed');\n    localStorage.removeItem('ai_chat_initial_user_message'); // Clear any previous one\n    \n    console.log('🧹 Cleared existing localStorage data for AI chat trigger');\n    \n    const messageContent = getInitialMessage();\n    \n    // Validate the message content (already good)\n    const finalMessageContent = (messageContent && messageContent.trim()) ? \n      messageContent.trim() : \n      \"Hi! I'd like help finding a place to stay. Can you help me explore my options?\";\n    \n    console.log('💬 Setting initial user message for AI Chat:', finalMessageContent);\n    localStorage.setItem('ai_chat_initial_user_message', finalMessageContent);\n    localStorage.setItem('ai_chat_trigger', 'true');\n    console.log('🚀 Set trigger flag and initial message for AiChat to pick up');\n    \n    // Show the chat\n    // Small delay might not be strictly necessary anymore but doesn't hurt\n    setTimeout(() => {\n      setShowAiChat(true);\n      console.log('✨ Chat modal should now be visible');\n    }, 50);\n  };\n\n  const handleCloseChat = () => {\n    setShowAiChat(false);\n    // Clean up chat-related data from localStorage\n    localStorage.removeItem('chatHistory');\n    localStorage.removeItem('ai_chat_trigger');\n    localStorage.removeItem('ai_chat_trigger_processed');\n    // Don't remove conversationState to preserve preferences between sessions\n  };\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section with Background */}\n      <div className=\"relative min-h-[90vh] flex items-center\">\n        {/* Background Image with Overlay */}\n        <div className=\"absolute inset-0 z-0\">\n          <div \n            className=\"absolute inset-0 bg-cover bg-center\"\n            style={{\n              backgroundImage: `url('/images/hero-travel.jpg')`,\n              backgroundPosition: 'center 40%'\n            }}\n          />\n          <div className=\"absolute inset-0 bg-gradient-to-b from-black/50 via-black/30 to-blue-900/40 backdrop-blur-[2px]\" />\n        </div>\n\n        {/* Content */}\n        <div className=\"container mx-auto px-4 py-12 relative z-10\">\n          <div className=\"max-w-4xl mx-auto text-center space-y-6\">\n            <h1 className=\"text-4xl font-bold tracking-tight text-white sm:text-6xl drop-shadow-md\">\n              Discover your ideal stay\n            </h1>\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-white/20\"></div>\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 text-white/90 bg-transparent\">with</span>\n              </div>\n            </div>\n            <h1 className={cn(\n              \"text-3xl font-bold tracking-tight sm:text-5xl\",\n              \"bg-clip-text text-transparent bg-gradient-to-r from-blue-200 via-blue-300 to-blue-400\",\n              \"drop-shadow-[0_2px_15px_rgba(59,130,246,0.7)]\",\n              \"drop-shadow-[0_0px_30px_rgba(147,197,253,0.5)]\",\n              \"drop-shadow-[0_0px_70px_rgba(59,130,246,0.4)]\",\n              \"filter brightness-150\",\n              \"relative\"\n            )}>\n              <span className=\"absolute inset-0 bg-gradient-to-r from-blue-400/20 to-blue-600/20 blur-2xl\" />\n              AI-Powered Intelligence\n            </h1>\n            <p className=\"mt-4 text-xl text-white/90 max-w-2xl mx-auto drop-shadow-md\">\n              RoomLamAI is your travel agent, curating perfect options, tailored just for you.\n            </p>\n\n            <Card className={cn(\n              \"mt-12 p-8 shadow-2xl\",\n              \"bg-white/95 backdrop-blur-md\",\n              \"border border-white/20\",\n              \"animate-fade-in-up\"\n            )}>\n              <div className=\"space-y-8\">\n                <div className=\"w-full\">\n                  <SearchForm \n                    onSubmit={handleSearchSubmit} \n                    formRef={searchFormRef}\n                    onChange={handleFormUpdate}\n                  />\n                </div>\n\n                {searchHistory.length > 0 && (\n                  <div className=\"border-t pt-6\">\n                    <RecentSearches\n                      searches={searchHistory}\n                      onSelect={repeatSearch}\n                      onClear={clearHistory}\n                      variant=\"compact\"\n                    />\n                  </div>\n                )}\n\n                <div className=\"flex flex-col md:flex-row justify-center items-center gap-4 md:gap-8\">\n                  <Button\n                    size=\"lg\"\n                    className={cn(\n                      \"bg-blue-600 hover:bg-blue-700 text-white\",\n                      \"px-8 py-6 text-lg rounded-full\",\n                      \"shadow-lg hover:shadow-blue-500/30\",\n                      \"transition-all duration-200 ease-in-out\",\n                      \"transform hover:scale-105\",\n                      \"flex items-center justify-center gap-3\"\n                    )}\n                    onClick={() => searchFormRef.current?.submit()}\n                  >\n                    <SearchIcon className=\"w-5 h-5\" />\n                    <span>Search Properties</span>\n                  </Button>\n\n                  <div className=\"relative hidden md:block\">\n                    <div className=\"absolute inset-y-0 left-1/2 -translate-x-1/2 w-px bg-gray-200\"></div>\n                    <div className=\"absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2\">\n                      <span className=\"px-2 text-gray-500 bg-white text-sm\">or</span>\n                    </div>\n                  </div>\n\n                  <Button\n                    size=\"lg\"\n                    className={cn(\n                      \"bg-blue-600 hover:bg-blue-700 text-white\",\n                      \"px-8 py-6 text-lg rounded-full\",\n                      \"shadow-lg hover:shadow-blue-500/30\",\n                      \"transition-all duration-200 ease-in-out\",\n                      \"transform hover:scale-105\",\n                      \"flex items-center justify-center gap-3\"\n                    )}\n                    onClick={handleOpenChat}\n                  >\n                    <MessageCircle className=\"w-5 h-5\" />\n                    <span>Plan with AI</span>\n                    <span className=\"text-sm text-blue-200/90\">• AI-Powered</span>\n                  </Button>\n                </div>\n              </div>\n            </Card>\n          </div>\n        </div>\n      </div>\n\n      {/* Recent Searches Section */}\n      {searchHistory.length > 0 && (\n        <div className=\"bg-white py-12\">\n          <div className=\"container mx-auto px-4\">\n            <div className=\"max-w-5xl mx-auto\">\n              <h2 className=\"text-2xl font-semibold mb-6\">Recent Searches</h2>\n              <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n                {searchHistory.slice(0, 3).map((search, index) => (\n                  <Card \n                    key={index}\n                    className=\"relative overflow-hidden hover:shadow-lg transition-shadow cursor-pointer\"\n                    onClick={() => repeatSearch(search)}\n                  >\n                    <div className=\"h-36\">\n                      <Map\n                        properties={[]}\n                        center={{ lat: search.lat, lng: search.lng }}\n                        zoom={12}\n                        className=\"w-full h-full\"\n                      />\n                    </div>\n                    <div className=\"p-4\">\n                      <div className=\"flex items-start gap-3\">\n                        <MapPin className=\"h-4 w-4 mt-1 shrink-0 text-primary\" />\n                        <div>\n                          <div className=\"font-medium\">{search.locationName}</div>\n                          <div className=\"text-sm text-muted-foreground mt-1\">\n                            {new Date(search.checkIn).toLocaleDateString()} - {new Date(search.checkOut).toLocaleDateString()}\n                          </div>\n                          <div className=\"text-sm text-muted-foreground\">\n                            {search.guests} guests, {search.rooms} {search.rooms === 1 ? 'room' : 'rooms'}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </Card>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* How it Works Section */}\n      <div className=\"bg-gradient-to-b from-gray-100 to-white py-24\">\n        <div className=\"container mx-auto px-4\">\n          <h2 className=\"text-3xl font-semibold text-center mb-12 text-gray-800\">\n            How RoomLama Works For You\n          </h2>\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <Card className=\"p-6 text-center hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-white\">\n              <div className=\"bg-blue-50 w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4\">\n                <MessageCircle className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <h3 className=\"font-semibold mb-2 text-gray-800\">Tell Us Your Preferences</h3>\n              <p className=\"text-gray-600\">Share your travel plans and preferences with our AI assistant</p>\n            </Card>\n            <Card className=\"p-6 text-center hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-white\">\n              <div className=\"bg-blue-50 w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4\">\n                <SearchIcon className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <h3 className=\"font-semibold mb-2 text-gray-800\">Smart Recommendations</h3>\n              <p className=\"text-gray-600\">Get personalized suggestions based on your needs</p>\n            </Card>\n            <Card className=\"p-6 text-center hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-white\">\n              <div className=\"bg-blue-50 w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4\">\n                <Star className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <h3 className=\"font-semibold mb-2 text-gray-800\">Book with Confidence</h3>\n              <p className=\"text-gray-600\">Choose from curated options that match your criteria</p>\n            </Card>\n          </div>\n        </div>\n      </div>\n\n      {/* Featured Properties Section */}\n      <div className=\"bg-gradient-to-b from-white via-gray-50 to-gray-100 py-24\">\n        <div className=\"container mx-auto px-4\">\n          <h2 className=\"text-2xl font-semibold text-center mb-8 text-gray-800\">\n            Featured Properties\n          </h2>\n          <FeaturedProperties />\n        </div>\n      </div>\n\n      {showAiChat && (\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n          <div className=\"relative w-full max-w-4xl h-[80vh]\">\n            <Button\n              variant=\"outline\"\n              className=\"absolute -top-12 right-0 bg-white hover:bg-gray-50 shadow-lg\"\n              onClick={handleCloseChat}\n            >\n              <X className=\"w-4 h-4 mr-2\" />\n              Close Chat\n            </Button>\n            <UnifiedAIChat\n              context={{\n                ...searchContext,\n                // Pass search history as part of context\n                searchHistory: searchHistory.slice(0, 3)\n              }}\n              variant=\"embedded\"\n              onClose={handleCloseChat}\n              onNavigate={(path) => setLocation(path)}\n              onSearchUpdate={(params) => {\n                setSearchContext(prev => ({ ...prev, ...params }));\n              }}\n            />\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}", "modifiedCode": "import { useState, useRef } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport {\n  MessageCircle,\n  Search as SearchIcon,\n  Star,\n  MapPin,\n  X\n} from \"lucide-react\";\nimport UnifiedAIChat from \"@/components/UnifiedAIChat\";\nimport SearchForm from \"@/components/SearchForm\";\nimport FeaturedProperties from \"@/components/FeaturedProperties\";\nimport { useLocation } from \"wouter\";\nimport { SearchFormValues } from \"@/components/SearchForm\";\nimport { Card } from \"@/components/ui/card\";\nimport { format } from \"date-fns\";\nimport { cn } from \"@/lib/utils\";\nimport { useSearchHistory } from \"@/hooks/use-search-history\";\nimport RecentSearches from \"@/components/RecentSearches\";\nimport Map from \"@/components/Map\";\n\nexport default function Search() {\n  const [showAiChat, setShowAiChat] = useState(false);\n  const [_, setLocation] = useLocation();\n  const searchFormRef = useRef<{ submit: () => void }>(null);\n  const [searchContext, setSearchContext] = useState({\n    location: null as string | null,\n    checkIn: null as string | null,\n    checkOut: null as string | null,\n    guests: null as string | null,\n    rooms: null as string | null\n  });\n\n  const { \n    searchHistory, \n    addToHistory, \n    clearHistory, \n    repeatSearch \n  } = useSearchHistory();\n\n  const handleSearchSubmit = (params: SearchFormValues) => {\n    const searchParams = new URLSearchParams({\n      lat: params.lat?.toString() || '',\n      lng: params.lng?.toString() || '',\n      locationName: params.locationName || '',\n      checkIn: params.checkIn || '',\n      checkOut: params.checkOut || '',\n      guests: params.guests?.toString() || '',\n      rooms: params.rooms?.toString() || ''\n    });\n\n    // Add to search history\n    addToHistory({\n      locationName: params.locationName || '',\n      lat: params.lat || 0,\n      lng: params.lng || 0,\n      checkIn: params.checkIn || '',\n      checkOut: params.checkOut || '',\n      guests: params.guests || 2,\n      rooms: params.rooms || 1\n    });\n\n    setLocation(`/results?${searchParams.toString()}`);\n  };\n\n  const handleFormUpdate = (data: SearchFormValues) => {\n    setSearchContext({\n      location: data.locationName || null,\n      checkIn: data.checkIn || null,\n      checkOut: data.checkOut || null,\n      guests: data.guests?.toString() || null,\n      rooms: data.rooms?.toString() || null\n    });\n  };\n\n  const getInitialMessage = () => {\n    // First check search history\n    if (searchHistory.length > 0) {\n      const lastSearch = searchHistory[0];\n      return `Hi! I see you recently searched for ${lastSearch.locationName}. Would you like to explore similar destinations or find something different?`;\n    }\n\n    if (!searchContext.location && !searchContext.checkIn && !searchContext.guests) {\n      return \"Hi! I'd like help finding a place to stay. Can you help me explore my options?\";\n    }\n\n    let message = \"Hi! I'm looking for\";\n    if (searchContext.location) {\n      message += ` a place to stay in ${searchContext.location}`;\n    }\n    if (searchContext.checkIn && searchContext.checkOut) {\n      message += ` from ${format(new Date(searchContext.checkIn), 'PPP')} to ${format(new Date(searchContext.checkOut), 'PPP')}`;\n    }\n    if (searchContext.guests) {\n      const guestCount = parseInt(searchContext.guests);\n      message += ` for ${guestCount} ${guestCount > 1 ? 'people' : 'person'}`;\n    }\n    message += \". Can you help me find the perfect place?\";\n    return message;\n  };\n\n  const handleOpenChat = () => {\n    console.log('🎯 Plan with AI button clicked!');\n    \n    if (showAiChat) {\n      console.log('🚫 Chat already open, ignoring click');\n      return;\n    }\n    \n    // Clear relevant localStorage items for a fresh start\n    localStorage.removeItem('chatHistory');\n    localStorage.removeItem('conversationState'); // Keep if you want to persist some AI state across sessions\n    localStorage.removeItem('ai_chat_trigger');\n    localStorage.removeItem('ai_chat_trigger_processed');\n    localStorage.removeItem('ai_chat_initial_user_message'); // Clear any previous one\n    \n    console.log('🧹 Cleared existing localStorage data for AI chat trigger');\n    \n    const messageContent = getInitialMessage();\n    \n    // Validate the message content (already good)\n    const finalMessageContent = (messageContent && messageContent.trim()) ? \n      messageContent.trim() : \n      \"Hi! I'd like help finding a place to stay. Can you help me explore my options?\";\n    \n    console.log('💬 Setting initial user message for AI Chat:', finalMessageContent);\n    localStorage.setItem('ai_chat_initial_user_message', finalMessageContent);\n    localStorage.setItem('ai_chat_trigger', 'true');\n    console.log('🚀 Set trigger flag and initial message for AiChat to pick up');\n    \n    // Show the chat\n    // Small delay might not be strictly necessary anymore but doesn't hurt\n    setTimeout(() => {\n      setShowAiChat(true);\n      console.log('✨ Chat modal should now be visible');\n    }, 50);\n  };\n\n  const handleCloseChat = () => {\n    setShowAiChat(false);\n    // Clean up chat-related data from localStorage\n    localStorage.removeItem('chatHistory');\n    localStorage.removeItem('ai_chat_trigger');\n    localStorage.removeItem('ai_chat_trigger_processed');\n    // Don't remove conversationState to preserve preferences between sessions\n  };\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section with Background */}\n      <div className=\"relative min-h-[90vh] flex items-center\">\n        {/* Background Image with Overlay */}\n        <div className=\"absolute inset-0 z-0\">\n          <div \n            className=\"absolute inset-0 bg-cover bg-center\"\n            style={{\n              backgroundImage: `url('/images/hero-travel.jpg')`,\n              backgroundPosition: 'center 40%'\n            }}\n          />\n          <div className=\"absolute inset-0 bg-gradient-to-b from-black/50 via-black/30 to-blue-900/40 backdrop-blur-[2px]\" />\n        </div>\n\n        {/* Content */}\n        <div className=\"container mx-auto px-4 py-12 relative z-10\">\n          <div className=\"max-w-4xl mx-auto text-center space-y-6\">\n            <h1 className=\"text-4xl font-bold tracking-tight text-white sm:text-6xl drop-shadow-md\">\n              Discover your ideal stay\n            </h1>\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-white/20\"></div>\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 text-white/90 bg-transparent\">with</span>\n              </div>\n            </div>\n            <h1 className={cn(\n              \"text-3xl font-bold tracking-tight sm:text-5xl\",\n              \"bg-clip-text text-transparent bg-gradient-to-r from-blue-200 via-blue-300 to-blue-400\",\n              \"drop-shadow-[0_2px_15px_rgba(59,130,246,0.7)]\",\n              \"drop-shadow-[0_0px_30px_rgba(147,197,253,0.5)]\",\n              \"drop-shadow-[0_0px_70px_rgba(59,130,246,0.4)]\",\n              \"filter brightness-150\",\n              \"relative\"\n            )}>\n              <span className=\"absolute inset-0 bg-gradient-to-r from-blue-400/20 to-blue-600/20 blur-2xl\" />\n              AI-Powered Intelligence\n            </h1>\n            <p className=\"mt-4 text-xl text-white/90 max-w-2xl mx-auto drop-shadow-md\">\n              RoomLamAI is your travel agent, curating perfect options, tailored just for you.\n            </p>\n\n            <Card className={cn(\n              \"mt-12 p-8 shadow-2xl\",\n              \"bg-white/95 backdrop-blur-md\",\n              \"border border-white/20\",\n              \"animate-fade-in-up\"\n            )}>\n              <div className=\"space-y-8\">\n                <div className=\"w-full\">\n                  <SearchForm \n                    onSubmit={handleSearchSubmit} \n                    formRef={searchFormRef}\n                    onChange={handleFormUpdate}\n                  />\n                </div>\n\n                {searchHistory.length > 0 && (\n                  <div className=\"border-t pt-6\">\n                    <RecentSearches\n                      searches={searchHistory}\n                      onSelect={repeatSearch}\n                      onClear={clearHistory}\n                      variant=\"compact\"\n                    />\n                  </div>\n                )}\n\n                <div className=\"flex flex-col md:flex-row justify-center items-center gap-4 md:gap-8\">\n                  <Button\n                    size=\"lg\"\n                    className={cn(\n                      \"bg-blue-600 hover:bg-blue-700 text-white\",\n                      \"px-8 py-6 text-lg rounded-full\",\n                      \"shadow-lg hover:shadow-blue-500/30\",\n                      \"transition-all duration-200 ease-in-out\",\n                      \"transform hover:scale-105\",\n                      \"flex items-center justify-center gap-3\"\n                    )}\n                    onClick={() => searchFormRef.current?.submit()}\n                  >\n                    <SearchIcon className=\"w-5 h-5\" />\n                    <span>Search Properties</span>\n                  </Button>\n\n                  <div className=\"relative hidden md:block\">\n                    <div className=\"absolute inset-y-0 left-1/2 -translate-x-1/2 w-px bg-gray-200\"></div>\n                    <div className=\"absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2\">\n                      <span className=\"px-2 text-gray-500 bg-white text-sm\">or</span>\n                    </div>\n                  </div>\n\n                  <Button\n                    size=\"lg\"\n                    className={cn(\n                      \"bg-blue-600 hover:bg-blue-700 text-white\",\n                      \"px-8 py-6 text-lg rounded-full\",\n                      \"shadow-lg hover:shadow-blue-500/30\",\n                      \"transition-all duration-200 ease-in-out\",\n                      \"transform hover:scale-105\",\n                      \"flex items-center justify-center gap-3\"\n                    )}\n                    onClick={handleOpenChat}\n                  >\n                    <MessageCircle className=\"w-5 h-5\" />\n                    <span>Plan with AI</span>\n                    <span className=\"text-sm text-blue-200/90\">• AI-Powered</span>\n                  </Button>\n                </div>\n              </div>\n            </Card>\n          </div>\n        </div>\n      </div>\n\n      {/* Recent Searches Section */}\n      {searchHistory.length > 0 && (\n        <div className=\"bg-white py-12\">\n          <div className=\"container mx-auto px-4\">\n            <div className=\"max-w-5xl mx-auto\">\n              <h2 className=\"text-2xl font-semibold mb-6\">Recent Searches</h2>\n              <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n                {searchHistory.slice(0, 3).map((search, index) => (\n                  <Card \n                    key={index}\n                    className=\"relative overflow-hidden hover:shadow-lg transition-shadow cursor-pointer\"\n                    onClick={() => repeatSearch(search)}\n                  >\n                    <div className=\"h-36\">\n                      <Map\n                        properties={[]}\n                        center={{ lat: search.lat, lng: search.lng }}\n                        zoom={12}\n                        className=\"w-full h-full\"\n                      />\n                    </div>\n                    <div className=\"p-4\">\n                      <div className=\"flex items-start gap-3\">\n                        <MapPin className=\"h-4 w-4 mt-1 shrink-0 text-primary\" />\n                        <div>\n                          <div className=\"font-medium\">{search.locationName}</div>\n                          <div className=\"text-sm text-muted-foreground mt-1\">\n                            {new Date(search.checkIn).toLocaleDateString()} - {new Date(search.checkOut).toLocaleDateString()}\n                          </div>\n                          <div className=\"text-sm text-muted-foreground\">\n                            {search.guests} guests, {search.rooms} {search.rooms === 1 ? 'room' : 'rooms'}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </Card>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* How it Works Section */}\n      <div className=\"bg-gradient-to-b from-gray-100 to-white py-24\">\n        <div className=\"container mx-auto px-4\">\n          <h2 className=\"text-3xl font-semibold text-center mb-12 text-gray-800\">\n            How RoomLama Works For You\n          </h2>\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <Card className=\"p-6 text-center hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-white\">\n              <div className=\"bg-blue-50 w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4\">\n                <MessageCircle className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <h3 className=\"font-semibold mb-2 text-gray-800\">Tell Us Your Preferences</h3>\n              <p className=\"text-gray-600\">Share your travel plans and preferences with our AI assistant</p>\n            </Card>\n            <Card className=\"p-6 text-center hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-white\">\n              <div className=\"bg-blue-50 w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4\">\n                <SearchIcon className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <h3 className=\"font-semibold mb-2 text-gray-800\">Smart Recommendations</h3>\n              <p className=\"text-gray-600\">Get personalized suggestions based on your needs</p>\n            </Card>\n            <Card className=\"p-6 text-center hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-white\">\n              <div className=\"bg-blue-50 w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4\">\n                <Star className=\"w-8 h-8 text-blue-600\" />\n              </div>\n              <h3 className=\"font-semibold mb-2 text-gray-800\">Book with Confidence</h3>\n              <p className=\"text-gray-600\">Choose from curated options that match your criteria</p>\n            </Card>\n          </div>\n        </div>\n      </div>\n\n      {/* Featured Properties Section */}\n      <div className=\"bg-gradient-to-b from-white via-gray-50 to-gray-100 py-24\">\n        <div className=\"container mx-auto px-4\">\n          <h2 className=\"text-2xl font-semibold text-center mb-8 text-gray-800\">\n            Featured Properties\n          </h2>\n          <FeaturedProperties />\n        </div>\n      </div>\n\n      {showAiChat && (\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n          <div className=\"relative w-full max-w-4xl h-[80vh]\">\n            <Button\n              variant=\"outline\"\n              className=\"absolute -top-12 right-0 bg-white hover:bg-gray-50 shadow-lg\"\n              onClick={handleCloseChat}\n            >\n              <X className=\"w-4 h-4 mr-2\" />\n              Close Chat\n            </Button>\n            <UnifiedAIChat\n              context={{\n                ...searchContext,\n                // Pass search history as part of context\n                searchHistory: searchHistory.slice(0, 3)\n              }}\n              variant=\"embedded\"\n              onClose={handleCloseChat}\n              onNavigate={(path) => setLocation(path)}\n              onSearchUpdate={(params) => {\n                setSearchContext(prev => ({ ...prev, ...params }));\n              }}\n            />\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}"}