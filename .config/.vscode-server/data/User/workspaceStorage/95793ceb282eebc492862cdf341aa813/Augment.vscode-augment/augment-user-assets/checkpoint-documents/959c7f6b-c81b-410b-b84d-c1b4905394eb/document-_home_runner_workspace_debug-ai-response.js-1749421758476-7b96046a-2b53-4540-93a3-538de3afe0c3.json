{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "debug-ai-response.js"}, "modifiedCode": "// Debug script to see what the AI is actually returning\nimport fetch from 'node-fetch';\n\nasync function debugAIResponse() {\n  console.log('🔍 Debugging AI Response Generation...\\n');\n\n  try {\n    const response = await fetch('http://localhost:5000/api/chat', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        message: 'I want to visit Paris, France',\n        context: {},\n        sessionId: 'debug-session',\n        extractLocation: true,\n        enhancedMode: true\n      }),\n    });\n\n    if (!response.ok) {\n      console.log(`❌ HTTP Error: ${response.status}`);\n      return;\n    }\n\n    console.log('📡 Raw AI Response Stream:');\n    console.log('=' .repeat(50));\n\n    const reader = response.body.getReader();\n    const decoder = new TextDecoder();\n    let buffer = '';\n    let chunkCount = 0;\n\n    while (true) {\n      const { done, value } = await reader.read();\n      if (done) break;\n\n      buffer += decoder.decode(value, { stream: true });\n      const lines = buffer.split('\\n');\n      buffer = lines.pop() || '';\n\n      for (const line of lines) {\n        if (line.startsWith('data: ')) {\n          const data = line.slice(6);\n          if (data === '[DONE]') {\n            console.log('🏁 Stream ended');\n            continue;\n          }\n\n          chunkCount++;\n          console.log(`\\n📦 Chunk ${chunkCount}:`);\n          console.log(`Raw: ${data}`);\n\n          try {\n            const parsed = JSON.parse(data);\n            console.log(`Type: ${parsed.type}`);\n            \n            if (parsed.type === 'text') {\n              console.log(`Text: \"${parsed.data}\"`);\n            } else if (parsed.type === 'action') {\n              console.log(`Action Type: ${parsed.data.type}`);\n              console.log(`Action Label: ${parsed.data.label}`);\n              console.log(`Action Data:`, JSON.stringify(parsed.data.data, null, 2));\n            } else if (parsed.type === 'location') {\n              console.log(`Location:`, JSON.stringify(parsed.data, null, 2));\n            } else {\n              console.log(`Other Data:`, JSON.stringify(parsed.data, null, 2));\n            }\n          } catch (error) {\n            console.log(`❌ Parse Error: ${error.message}`);\n          }\n        }\n      }\n    }\n\n    console.log('\\n' + '=' .repeat(50));\n    console.log(`✅ Debug complete. Total chunks: ${chunkCount}`);\n\n  } catch (error) {\n    console.log(`❌ Error: ${error.message}`);\n  }\n}\n\ndebugAIResponse().catch(console.error);\n"}