{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/index.css"}, "originalCode": "@import './styles/animations.css';\n@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n@layer base {\n  :root {\n    --background: 0 0% 100%;\n    --foreground: 222.2 84% 4.9%;\n    --card: 0 0% 100%;\n    --card-foreground: 222.2 84% 4.9%;\n    --popover: 0 0% 100%;\n    --popover-foreground: 222.2 84% 4.9%;\n    --primary: 221.2 83.2% 53.3%;\n    --primary-foreground: 210 40% 98%;\n    --secondary: 210 40% 96.1%;\n    --secondary-foreground: 222.2 47.4% 11.2%;\n    --muted: 210 40% 96.1%;\n    --muted-foreground: 215.4 16.3% 46.9%;\n    --accent: 210 40% 96.1%;\n    --accent-foreground: 222.2 47.4% 11.2%;\n    --destructive: 0 84.2% 60.2%;\n    --destructive-foreground: 210 40% 98%;\n    --border: 214.3 31.8% 91.4%;\n    --input: 214.3 31.8% 91.4%;\n    --ring: 221.2 83.2% 53.3%;\n    --radius: 0.5rem;\n  }\n\n  .dark {\n    --background: 222.2 84% 4.9%;\n    --foreground: 210 40% 98%;\n    --card: 222.2 84% 4.9%;\n    --card-foreground: 210 40% 98%;\n    --popover: 222.2 84% 4.9%;\n    --popover-foreground: 210 40% 98%;\n    --primary: 217.2 91.2% 59.8%;\n    --primary-foreground: 222.2 47.4% 11.2%;\n    --secondary: 217.2 32.6% 17.5%;\n    --secondary-foreground: 210 40% 98%;\n    --muted: 217.2 32.6% 17.5%;\n    --muted-foreground: 215 20.2% 65.1%;\n    --accent: 217.2 32.6% 17.5%;\n    --accent-foreground: 210 40% 98%;\n    --destructive: 0 62.8% 30.6%;\n    --destructive-foreground: 210 40% 98%;\n    --border: 217.2 32.6% 17.5%;\n    --input: 217.2 32.6% 17.5%;\n    --ring: 224.3 76.3% 48%;\n  }\n\n  * {\n    @apply border-border;\n  }\n\n  body {\n    @apply font-sans antialiased bg-background text-foreground;\n  }\n}\n\n/* Google Places Autocomplete Styles */\n.pac-container {\n  background-color: hsl(var(--background));\n  border: 1px solid hsl(var(--border));\n  border-radius: 0.5rem;\n  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  margin-top: 0.5rem;\n  padding: 0.5rem;\n  z-index: 9999;\n}\n\n.pac-item {\n  padding: 0.5rem;\n  cursor: pointer;\n  border: none;\n  color: hsl(var(--foreground));\n  font-family: var(--font-sans);\n}\n\n.pac-item:hover {\n  background-color: hsl(var(--accent));\n}\n\n.pac-item-query {\n  color: hsl(var(--foreground));\n  font-size: 0.875rem;\n}\n\n.pac-matched {\n  color: hsl(var(--primary));\n  font-weight: 500;\n}\n\n.pac-icon {\n  display: none;\n}", "modifiedCode": "@import './styles/animations.css';\n@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n@layer base {\n  :root {\n    --background: 0 0% 100%;\n    --foreground: 222.2 84% 4.9%;\n    --card: 0 0% 100%;\n    --card-foreground: 222.2 84% 4.9%;\n    --popover: 0 0% 100%;\n    --popover-foreground: 222.2 84% 4.9%;\n    --primary: 221.2 83.2% 53.3%;\n    --primary-foreground: 210 40% 98%;\n    --secondary: 210 40% 96.1%;\n    --secondary-foreground: 222.2 47.4% 11.2%;\n    --muted: 210 40% 96.1%;\n    --muted-foreground: 215.4 16.3% 46.9%;\n    --accent: 210 40% 96.1%;\n    --accent-foreground: 222.2 47.4% 11.2%;\n    --destructive: 0 84.2% 60.2%;\n    --destructive-foreground: 210 40% 98%;\n    --border: 214.3 31.8% 91.4%;\n    --input: 214.3 31.8% 91.4%;\n    --ring: 221.2 83.2% 53.3%;\n    --radius: 0.5rem;\n  }\n\n  .dark {\n    --background: 222.2 84% 4.9%;\n    --foreground: 210 40% 98%;\n    --card: 222.2 84% 4.9%;\n    --card-foreground: 210 40% 98%;\n    --popover: 222.2 84% 4.9%;\n    --popover-foreground: 210 40% 98%;\n    --primary: 217.2 91.2% 59.8%;\n    --primary-foreground: 222.2 47.4% 11.2%;\n    --secondary: 217.2 32.6% 17.5%;\n    --secondary-foreground: 210 40% 98%;\n    --muted: 217.2 32.6% 17.5%;\n    --muted-foreground: 215 20.2% 65.1%;\n    --accent: 217.2 32.6% 17.5%;\n    --accent-foreground: 210 40% 98%;\n    --destructive: 0 62.8% 30.6%;\n    --destructive-foreground: 210 40% 98%;\n    --border: 217.2 32.6% 17.5%;\n    --input: 217.2 32.6% 17.5%;\n    --ring: 224.3 76.3% 48%;\n  }\n\n  * {\n    @apply border-border;\n  }\n\n  body {\n    @apply font-sans antialiased bg-background text-foreground;\n  }\n}\n\n/* Google Places Autocomplete Styles */\n.pac-container {\n  background-color: hsl(var(--background));\n  border: 1px solid hsl(var(--border));\n  border-radius: 0.5rem;\n  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  margin-top: 0.5rem;\n  padding: 0.5rem;\n  z-index: 9999;\n}\n\n.pac-item {\n  padding: 0.5rem;\n  cursor: pointer;\n  border: none;\n  color: hsl(var(--foreground));\n  font-family: var(--font-sans);\n}\n\n.pac-item:hover {\n  background-color: hsl(var(--accent));\n}\n\n.pac-item-query {\n  color: hsl(var(--foreground));\n  font-size: 0.875rem;\n}\n\n.pac-matched {\n  color: hsl(var(--primary));\n  font-weight: 500;\n}\n\n.pac-icon {\n  display: none;\n}\n\n/* Text wrapping utilities for chat content */\n@layer utilities {\n  .overflow-wrap-anywhere {\n    overflow-wrap: anywhere;\n    word-break: break-word;\n    hyphens: auto;\n  }\n\n  .break-words {\n    word-break: break-word;\n    overflow-wrap: break-word;\n  }\n\n  /* Ensure modal content doesn't overflow */\n  .modal-content {\n    max-width: 100%;\n    overflow: hidden;\n  }\n\n  .modal-content * {\n    max-width: 100%;\n    box-sizing: border-box;\n  }\n}"}