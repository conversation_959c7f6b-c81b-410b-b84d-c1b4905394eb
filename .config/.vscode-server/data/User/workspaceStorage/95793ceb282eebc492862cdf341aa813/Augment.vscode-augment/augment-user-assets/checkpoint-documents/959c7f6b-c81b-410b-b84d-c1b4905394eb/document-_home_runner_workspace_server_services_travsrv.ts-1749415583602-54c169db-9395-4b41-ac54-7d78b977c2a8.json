{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/travsrv.ts"}, "originalCode": "import type { Property } from \"@db/schema.js\";\nimport { log } from \"../vite.js\";\nimport { db } from \"@db/index.js\";\nimport { properties } from \"@db/schema.js\";\nimport { eq } from \"drizzle-orm\";\n\nconst BASE_URL = \"https://api.travsrv.com/api\";\nconst AUTH_HEADER = \"Basic bHluY2h0ZXN0OmFybjEyMzQ1\";\n// const SITE_ID = \"30268\";\nconst SITE_ID = \"30992\";\nconst MAX_RETRIES = 2;\nconst RETRY_DELAY = 1000;\n\ninterface TravSrvSearchParams {\n  latitude: number;\n  longitude: number;\n  inDate: string;\n  outDate: string;\n  adults?: number;\n  children?: number;\n  rooms?: number;\n  currency?: string;\n  radius?: number; // radius in kilometers\n}\n\ninterface TravSrvRoom {\n  \"@Code\": string;\n  \"@Description\": string;\n  \"@Rate\": string;\n  \"@Currency\": string;\n  \"@RoomTypeCode\": string;\n  \"@BedTypeCode\": string;\n  \"@MaxOccupancy\": string;\n  \"@AvailableQuantity\": string;\n  Total?: {\n    \"@Amount\": string;\n    \"@Currency\": string;\n  };\n}\n\ninterface TravSrvRatePlan {\n  \"@Code\": string;\n  \"@Description\": string;\n  \"@CommissionStatus\": string;\n  Room: TravSrvRoom | TravSrvRoom[];\n}\n\ninterface TravSrvHotel {\n  \"@HotelID\": string;\n  \"@Name\": string;\n  \"@LocationDescription\": string;\n  \"@City\": string;\n  \"@State\": string;\n  \"@CountryCode\": string;\n  \"@Address1\": string;\n  \"@Latitude\": string;\n  \"@Longitude\": string;\n  \"@PropertyType\": string;\n  \"@PriceClass\": string;\n  \"@ImageThumbnail\": string;\n  \"@Images\"?: string;\n  \"@PropertyImages\"?: string;\n  \"@TripAdvisorRating\"?: string;\n  \"@TripAdvisorReviewCount\"?: string;\n  \"@RatingImageUrl\"?: string;\n  \"@PropertyLink\"?: string;\n  \"@HotelInfo\"?: string;\n  \"@Postal\"?: string;\n  \"@PercentMatch\"?: string;\n}\n\ninterface RoomImage {\n  url: string;\n  caption?: string;\n  displayOrder?: number;\n  roomTypeCode: string;\n  category?: string;\n}\n\ninterface Room {\n  code: string;\n  description: string;\n  rate: number;\n  currency: string;\n  roomTypeCode: string;\n  bedTypeCode: string;\n  restrictedRate: boolean;\n  refundable: boolean;\n  maxOccupancy: number;\n  availableQuantity: number;\n  totalAmount: number;\n  totalDiscount: number;\n  totalComparableRetailDiscount: number;\n  retailDiscountPercent: number;\n  totalCurrency: string;\n  rateCode?: string;\n  rateDescription?: string;\n  cancellationPolicy?: string;\n  guaranteePolicy?: string;\n  depositPolicy?: string;\n  includedServices?: string[];\n  amenities?: string[];\n  viewType?: string;\n  smokingPreference?: string;\n  bedCount?: number;\n  bedType?: string;\n  roomSize?: string;\n  floorLevel?: string;\n  accessibility?: string;\n  images?: RoomImage[];\n  promotions?: Array<{\n    code: string;\n    description: string;\n    discountType: string;\n    discountValue: number;\n    startDate?: string;\n    endDate?: string;\n  }>;\n  taxes?: Array<{\n    type: string;\n    amount: number;\n    currency: string;\n    included: boolean;\n  }>;\n  fees?: Array<{\n    type: string;\n    amount: number;\n    currency: string;\n    included: boolean;\n  }>;\n}\n\ninterface RatePlan {\n  code: string;\n  description: string;\n  commissionStatus: string;\n  rooms: { [key: string]: Room };\n  type?: string;\n  category?: string;\n  mealPlan?: string;\n  packageInclusions?: string[];\n  restrictions?: {\n    minStay?: number;\n    maxStay?: number;\n    closedToArrival?: boolean;\n    closedToDeparture?: boolean;\n    advancePurchaseDays?: number;\n    stayThrough?: string;\n    blackoutDates?: string[];\n  };\n  cancellationPolicies?: Array<{\n    deadline: string;\n    amount: number;\n    currency: string;\n    type: string;\n  }>;\n  guaranteePolicy?: {\n    type: string;\n    required: boolean;\n    deadline?: string;\n  };\n  depositPolicy?: {\n    type: string;\n    amount: number;\n    currency: string;\n    deadline?: string;\n  };\n  paymentTypes?: string[];\n  promotionalCode?: string;\n  rateComments?: string[];\n  commissionAmount?: number;\n  commissionCurrency?: string;\n  markupAmount?: number;\n  markupCurrency?: string;\n}\n\ninterface AvailabilityResponse {\n  ratePlans: { [key: string]: RatePlan };\n}\n\nfunction generateRequestId(): string {\n  return `travsrv-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n}\n\nfunction logApiOperation(requestId: string, stage: string, data: any) {\n  const timestamp = new Date().toISOString();\n  log(\n    `[TravSrv][${timestamp}][${requestId}][${stage.toUpperCase()}] ${JSON.stringify(data, null, 2)}`,\n  );\n}\n\nasync function delay(ms: number): Promise<void> {\n  return new Promise((resolve) => setTimeout(resolve, ms));\n}\n\nasync function fetchWithRetry(\n  url: string,\n  options: RequestInit,\n  requestId: string,\n  retryCount = 0,\n): Promise<Response> {\n  try {\n    const response = await fetch(url, options);\n\n    // Only check response.ok if we actually got a response\n    if (!response || !response.ok) {\n      if (\n        retryCount < MAX_RETRIES &&\n        (response?.status === 429 || response?.status >= 500)\n      ) {\n        logApiOperation(requestId, \"debug\", {\n          message: `Retrying request after error (attempt ${retryCount + 1}/${MAX_RETRIES})`,\n          status: response?.status,\n          statusText: response?.statusText,\n          url,\n          method: options.method || \"GET\",\n          headers: {\n            ...options.headers,\n            Authorization: \"[REDACTED]\",\n          },\n        });\n\n        await delay(RETRY_DELAY * Math.pow(2, retryCount));\n        return fetchWithRetry(url, options, requestId, retryCount + 1);\n      }\n\n      const errorDetails = {\n        status: response?.status,\n        statusText: response?.statusText,\n        url,\n        method: options.method || \"GET\",\n        retryCount,\n        responseBody: await response?.text(),\n      };\n      console.debug(\"API Error Details:\", errorDetails);\n      throw new Error(`API Error: ${response?.status} ${response?.statusText}`);\n    }\n\n    return response;\n  } catch (error) {\n    if (retryCount < MAX_RETRIES) {\n      logApiOperation(requestId, \"debug\", {\n        message: `Network error, retrying (attempt ${retryCount + 1}/${MAX_RETRIES})`,\n        error: error instanceof Error ? error.message : \"Unknown error\",\n        url,\n        method: options.method || \"GET\",\n        headers: {\n          ...options.headers,\n          Authorization: \"[REDACTED]\",\n        },\n      });\n\n      await delay(RETRY_DELAY * Math.pow(2, retryCount));\n      return fetchWithRetry(url, options, requestId, retryCount + 1);\n    }\n    // Always throw 'API Error' for consistency with test expectations\n    throw new Error(\"API Error\");\n  }\n}\n\nasync function cacheProperty(property: Property): Promise<void> {\n  try {\n    const propertyData = {\n      externalId: property.externalId || property.id?.toString(),\n      name: property.name,\n      description: property.description,\n      latitude: Number(property.latitude),\n      longitude: Number(property.longitude),\n      address: property.address,\n      city: property.city,\n      state: property.state,\n      country: property.country,\n      rating: property.rating ? Number(property.rating) : null,\n      reviewCount: property.reviewCount || 0,\n      basePrice: Number(property.basePrice),\n      currency: property.currency || 'USD',\n      propertyType: property.propertyType || 'hotel',\n      // Fix: Store arrays directly in JSONB fields, don't double-stringify\n      amenities: Array.isArray(property.amenities) ? property.amenities : [],\n      images: Array.isArray(property.images) ? property.images : [],\n      source: 'api',\n      updatedAt: new Date()\n    };\n\n    await db\n      .insert(properties)\n      .values(propertyData)\n      .onConflictDoUpdate({\n        target: [properties.externalId],\n        set: {\n          ...propertyData,\n          updatedAt: new Date()\n        }\n      });\n\n    log(`✅ Property cached successfully: ${property.name} (ID: ${property.externalId || property.id})`);\n  } catch (error) {\n    log(`❌ Property caching failed for ${property.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    // Don't throw - allow operation to continue\n  }\n}\n\nexport async function searchTravSrvProperties(\n  params: TravSrvSearchParams,\n): Promise<Property[]> {\n  const requestId = generateRequestId();\n  logApiOperation(requestId, \"start\", { params });\n\n  try {\n    const queryParams = new URLSearchParams({\n      type: \"availability\",\n      siteid: SITE_ID,\n      rooms: (params.rooms || 1).toString(),\n      adults: (params.adults || 1).toString(),\n      children: (params.children || 0).toString(),\n      candidateSearch: \"true\",\n      _type: \"json\",\n      currency: params.currency || \"USD\",\n      sortType: \"dealpercent\",\n      latitude: params.latitude.toString(),\n      longitude: params.longitude.toString(),\n      inDate: params.inDate,\n      outDate: params.outDate,\n      maxResults: \"100\",\n      ...(params.radius && { radius: params.radius.toString() }), // Add radius if provided\n    });\n\n    const requestUrl = `${BASE_URL}/hotel?${queryParams.toString()}`;\n\n    logApiOperation(requestId, \"request\", {\n      url: requestUrl,\n      method: \"GET\",\n      params,\n    });\n\n    const response = await fetchWithRetry(\n      requestUrl,\n      {\n        headers: {\n          Authorization: AUTH_HEADER,\n          \"Accept-version\": \"2\",\n        },\n      },\n      requestId,\n    );\n\n    const data = await response.json();\n\n    if (!data.ArnResponse?.Availability?.HotelAvailability?.Hotel) {\n      logApiOperation(requestId, \"no_results\", {\n        message: \"No hotels found in response\",\n        responseData: data,\n      });\n      return [];\n    }\n\n    logApiOperation(requestId, \"response\", {\n      url: requestUrl,\n      method: \"GET\",\n      data: JSON.stringify(data).substring(0, 1000) + \"...\",\n    });\n\n    const hotels = Array.isArray(\n      data.ArnResponse.Availability.HotelAvailability.Hotel,\n    )\n      ? data.ArnResponse.Availability.HotelAvailability.Hotel\n      : [data.ArnResponse.Availability.HotelAvailability.Hotel];\n\n    // Log the first hotel's data to check available image fields\n    if (hotels.length > 0) {\n      logApiOperation(requestId, \"hotel_data\", {\n        hotelId: hotels[0][\"@HotelID\"],\n        imageFields: Object.keys(hotels[0]).filter((key) =>\n          key.toLowerCase().includes(\"image\"),\n        ),\n        imageData: {\n          thumbnail: hotels[0][\"@ImageThumbnail\"],\n          images: hotels[0][\"Images\"],\n          propertyImages: hotels[0][\"PropertyImages\"],\n          allImages: hotels[0][\"AllImages\"],\n          imageUrls: hotels[0][\"ImageUrls\"],\n        },\n      });\n    }\n\n    const properties = hotels.map((hotel: TravSrvHotel): Property => {\n      // Extract the numeric part of PriceClass and convert to dollars with 2 decimal places\n      const priceMatch = hotel[\"@PriceClass\"].match(/\\d+/);\n      const basePrice = priceMatch\n        ? Number(priceMatch[0]).toFixed(2)\n        : \"300.00\";\n\n      // Handle rating: ensure it's between 0 and 5 with one decimal place\n      let rating: string | null = null;\n      if (hotel[\"@TripAdvisorRating\"]) {\n        const ratingNum = parseFloat(hotel[\"@TripAdvisorRating\"]);\n        rating = (ratingNum <= 0 ? 0 : Math.min(5, ratingNum)).toFixed(1);\n      }\n\n      // Default amenities based on property type and price class\n      const defaultAmenities = [\n        \"WiFi\",\n        \"TV\",\n        hotel[\"@PriceClass\"].includes(\"3\") ||\n        hotel[\"@PriceClass\"].includes(\"4\") ||\n        hotel[\"@PriceClass\"].includes(\"5\")\n          ? \"Air Conditioning\"\n          : null,\n        hotel[\"@PriceClass\"].includes(\"4\") || hotel[\"@PriceClass\"].includes(\"5\")\n          ? \"Breakfast\"\n          : null,\n        hotel[\"@PropertyType\"].toLowerCase() === \"resort\" ? \"Pool\" : null,\n        hotel[\"@PriceClass\"].includes(\"4\") || hotel[\"@PriceClass\"].includes(\"5\")\n          ? \"Gym\"\n          : null,\n        hotel[\"@PriceClass\"].includes(\"3\") ||\n        hotel[\"@PriceClass\"].includes(\"4\") ||\n        hotel[\"@PriceClass\"].includes(\"5\")\n          ? \"Parking\"\n          : null,\n      ].filter(Boolean) as string[];\n\n      return {\n        id: parseInt(hotel[\"@HotelID\"]),\n        externalId: hotel[\"@HotelID\"],\n        name: hotel[\"@Name\"],\n        description:\n          hotel[\"@LocationDescription\"] ||\n          `Located in ${hotel[\"@City\"]}${hotel[\"@State\"] ? `, ${hotel[\"@State\"]}` : \"\"}. ${\n            hotel[\"@TripAdvisorRating\"]\n              ? `Rated ${hotel[\"@TripAdvisorRating\"]}/5 with ${hotel[\"@TripAdvisorReviewCount\"]} reviews.`\n              : \"\"\n          }`,\n        propertyType: hotel[\"@PropertyType\"].toLowerCase(),\n        address: hotel[\"@Address1\"],\n        city: hotel[\"@City\"],\n        state: hotel[\"@State\"] || null,\n        country: hotel[\"@CountryCode\"],\n        latitude: hotel[\"@Latitude\"] ? parseFloat(hotel[\"@Latitude\"]) : 0,\n        longitude: hotel[\"@Longitude\"] ? parseFloat(hotel[\"@Longitude\"]) : 0,\n        basePrice: hotel[\"@PriceClass\"] ? parseFloat(hotel[\"@PriceClass\"]) : 0,\n        rating: rating ? parseFloat(rating) : null,\n        reviewCount: hotel[\"@TripAdvisorReviewCount\"]\n          ? parseInt(hotel[\"@TripAdvisorReviewCount\"])\n          : null,\n        images: [\n          hotel[\"@ImageThumbnail\"],\n          ...(hotel[\"@Images\"]?.split(\",\").map((img: string) => img.trim()) ||\n            []),\n          ...(hotel[\"@PropertyImages\"]\n            ?.split(\",\")\n            .map((img: string) => img.trim()) || []),\n        ].filter(Boolean),\n        amenities: defaultAmenities,\n        currency: \"USD\",\n        source: \"travsrv\",\n        discount: 0,\n        featured: false,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n    });\n\n    // Cache all properties (re-enabled with fixed implementation)\n    await Promise.all(\n      properties.map((property: Property) => cacheProperty(property))\n    );\n\n    return properties;\n  } catch (error) {\n    logApiOperation(requestId, \"error\", {\n      message: \"Candidate search failed\",\n      error: error instanceof Error ? error.message : \"Unknown error\",\n    });\n    throw error;\n  }\n}\n\nexport async function getPropertyAvailability(\n  hotelId: string,\n  params: Omit<TravSrvSearchParams, \"latitude\" | \"longitude\">,\n  bestRateOnly: boolean = true\n): Promise<AvailabilityResponse | null> {\n  const requestId = generateRequestId();\n\n  try {\n    // Build query params for API requests\n    const queryParams = new URLSearchParams({\n      type: \"availability\",\n      siteid: SITE_ID,\n      rooms: (params.rooms || 1).toString(),\n      adults: (params.adults || 1).toString(),\n      children: (params.children || 0).toString(),\n      _type: \"json\",\n      currency: params.currency || \"USD\",\n      hotelIds: hotelId,\n      inDate: params.inDate || new Date().toISOString().split(\"T\")[0],\n      outDate:\n        params.outDate ||\n        new Date(Date.now() + 86400000).toISOString().split(\"T\")[0],\n    });\n\n    // Add bestRateSearch parameter when only the best rate is needed\n    if (bestRateOnly) {\n      queryParams.append(\"bestRateSearch\", \"true\");\n    }\n\n    // Fetch availability data\n    const availabilityUrl = `${BASE_URL}/hotel?${queryParams.toString()}`;\n    logApiOperation(requestId, \"debug\", {\n      message: \"Fetching availability data\",\n      url: availabilityUrl,\n      propertyId: hotelId,\n      params: Object.fromEntries(queryParams),\n    });\n\n    const availabilityResponse = await fetchWithRetry(\n      availabilityUrl,\n      {\n        headers: {\n          Authorization: AUTH_HEADER,\n          \"Accept-version\": \"2\",\n          Accept: \"application/json\",\n        },\n      },\n      requestId,\n    );\n\n    const availabilityData = await availabilityResponse.json();\n    const hotelAvailability =\n      availabilityData.ArnResponse?.Availability?.HotelAvailability;\n    const availableHotel = Array.isArray(hotelAvailability?.Hotel)\n      ? hotelAvailability.Hotel[0]\n      : hotelAvailability?.Hotel;\n\n    if (!availableHotel) {\n      return null;\n    }\n\n    // Process rate plans and rooms\n    const ratePlans: { [key: string]: RatePlan } = {};\n    const rawRatePlans = Array.isArray(availableHotel.RatePlan)\n      ? availableHotel.RatePlan\n      : availableHotel.RatePlan\n        ? [availableHotel.RatePlan]\n        : [];\n\n    for (const plan of rawRatePlans) {\n      const rooms: { [key: string]: Room } = {};\n      const rawRooms = Array.isArray(plan.Room) ? plan.Room : [plan.Room];\n\n      for (const room of rawRooms) {\n        rooms[room[\"@Code\"]] = {\n          code: room[\"@Code\"],\n          description: room[\"@Description\"] || \"Standard Room\",\n          rate: parseFloat(room[\"@Rate\"]),\n          currency: room[\"@Currency\"],\n          roomTypeCode: room[\"@RoomTypeCode\"],\n          bedTypeCode: room[\"@BedTypeCode\"],\n          restrictedRate: room[\"@RestrictedRate\"] === \"true\",\n          refundable: room[\"@Refundable\"] === \"true\",\n          maxOccupancy: parseInt(room[\"@MaxOccupancy\"]) || 2,\n          availableQuantity: parseInt(room[\"@MaximumBookable\"]) || 0,\n          totalAmount: room.Total?.[\"@Amount\"]\n            ? parseFloat(room.Total[\"@Amount\"])\n            : 0,\n          totalDiscount: room.Total?.[\"@Discount\"]\n            ? parseFloat(room.Total[\"@Discount\"])\n            : 0,\n          totalComparableRetailDiscount: room.Total?.[\n            \"@ComparableRetailDiscount\"\n          ]\n            ? parseFloat(room.Total[\"@ComparableRetailDiscount\"])\n            : 0,\n          retailDiscountPercent: room.Total?.[\"@RetailDiscountPercent\"]\n            ? parseFloat(room.Total[\"@RetailDiscountPercent\"])\n            : 0,\n          totalCurrency: room.Total?.[\"@Currency\"] || room[\"@Currency\"],\n          // Add any additional fields present in the response\n          rateCode: room[\"@RateCode\"],\n          rateDescription: room[\"@RateDescription\"],\n          cancellationPolicy: room[\"@CancellationPolicy\"],\n          guaranteePolicy: room[\"@GuaranteePolicy\"],\n          depositPolicy: room[\"@DepositPolicy\"],\n          includedServices: room[\"@IncludedServices\"]\n            ?.split(\",\")\n            .map((s: string) => s.trim()),\n          amenities: room[\"@Amenities\"]\n            ?.split(\",\")\n            .map((s: string) => s.trim()),\n          viewType: room[\"@ViewType\"],\n          smokingPreference: room[\"@SmokingPreference\"],\n          bedCount: room[\"@BedCount\"] ? parseInt(room[\"@BedCount\"]) : undefined,\n          bedType: room[\"@BedType\"],\n          roomSize: room[\"@RoomSize\"],\n          floorLevel: room[\"@FloorLevel\"],\n          accessibility: room[\"@Accessibility\"],\n          images: room[\"@Images\"]?.split(\",\").map((s: string) => ({\n            url: s.trim(),\n            roomTypeCode: room[\"@RoomTypeCode\"],\n            category: \"room\",\n          })),\n          promotions: room[\"Promotions\"]?.map((promo: any) => ({\n            code: promo[\"@Code\"],\n            description: promo[\"@Description\"],\n            discountType: promo[\"@DiscountType\"],\n            discountValue: parseFloat(promo[\"@DiscountValue\"]),\n            startDate: promo[\"@StartDate\"],\n            endDate: promo[\"@EndDate\"],\n          })),\n          taxes: room[\"Taxes\"]?.map((tax: any) => ({\n            type: tax[\"@Type\"],\n            amount: parseFloat(tax[\"@Amount\"]),\n            currency: tax[\"@Currency\"],\n            included: tax[\"@Included\"] === \"true\",\n          })),\n          fees: room[\"Fees\"]?.map((fee: any) => ({\n            type: fee[\"@Type\"],\n            amount: parseFloat(fee[\"@Amount\"]),\n            currency: fee[\"@Currency\"],\n            included: fee[\"@Included\"] === \"true\",\n          })),\n        };\n      }\n\n      ratePlans[plan[\"@Code\"]] = {\n        code: plan[\"@Code\"],\n        description: plan[\"@Description\"],\n        commissionStatus: plan[\"@CommissionStatus\"],\n        rooms,\n      };\n    }\n\n    return { ratePlans };\n  } catch (error) {\n    logApiOperation(requestId, \"error\", {\n      message: \"Failed to fetch availability\",\n      error: error instanceof Error ? error.message : \"Unknown error\",\n      propertyId: hotelId,\n    });\n    throw error;\n  }\n}\n", "modifiedCode": "import type { Property } from \"../db/schema.js\";\nimport { log } from \"../vite.js\";\nimport { db } from \"../db/index.js\";\nimport { properties } from \"../db/schema.js\";\nimport { eq } from \"drizzle-orm\";\n\nconst BASE_URL = \"https://api.travsrv.com/api\";\nconst AUTH_HEADER = \"Basic bHluY2h0ZXN0OmFybjEyMzQ1\";\n// const SITE_ID = \"30268\";\nconst SITE_ID = \"30992\";\nconst MAX_RETRIES = 2;\nconst RETRY_DELAY = 1000;\n\ninterface TravSrvSearchParams {\n  latitude: number;\n  longitude: number;\n  inDate: string;\n  outDate: string;\n  adults?: number;\n  children?: number;\n  rooms?: number;\n  currency?: string;\n  radius?: number; // radius in kilometers\n}\n\ninterface TravSrvRoom {\n  \"@Code\": string;\n  \"@Description\": string;\n  \"@Rate\": string;\n  \"@Currency\": string;\n  \"@RoomTypeCode\": string;\n  \"@BedTypeCode\": string;\n  \"@MaxOccupancy\": string;\n  \"@AvailableQuantity\": string;\n  Total?: {\n    \"@Amount\": string;\n    \"@Currency\": string;\n  };\n}\n\ninterface TravSrvRatePlan {\n  \"@Code\": string;\n  \"@Description\": string;\n  \"@CommissionStatus\": string;\n  Room: TravSrvRoom | TravSrvRoom[];\n}\n\ninterface TravSrvHotel {\n  \"@HotelID\": string;\n  \"@Name\": string;\n  \"@LocationDescription\": string;\n  \"@City\": string;\n  \"@State\": string;\n  \"@CountryCode\": string;\n  \"@Address1\": string;\n  \"@Latitude\": string;\n  \"@Longitude\": string;\n  \"@PropertyType\": string;\n  \"@PriceClass\": string;\n  \"@ImageThumbnail\": string;\n  \"@Images\"?: string;\n  \"@PropertyImages\"?: string;\n  \"@TripAdvisorRating\"?: string;\n  \"@TripAdvisorReviewCount\"?: string;\n  \"@RatingImageUrl\"?: string;\n  \"@PropertyLink\"?: string;\n  \"@HotelInfo\"?: string;\n  \"@Postal\"?: string;\n  \"@PercentMatch\"?: string;\n}\n\ninterface RoomImage {\n  url: string;\n  caption?: string;\n  displayOrder?: number;\n  roomTypeCode: string;\n  category?: string;\n}\n\ninterface Room {\n  code: string;\n  description: string;\n  rate: number;\n  currency: string;\n  roomTypeCode: string;\n  bedTypeCode: string;\n  restrictedRate: boolean;\n  refundable: boolean;\n  maxOccupancy: number;\n  availableQuantity: number;\n  totalAmount: number;\n  totalDiscount: number;\n  totalComparableRetailDiscount: number;\n  retailDiscountPercent: number;\n  totalCurrency: string;\n  rateCode?: string;\n  rateDescription?: string;\n  cancellationPolicy?: string;\n  guaranteePolicy?: string;\n  depositPolicy?: string;\n  includedServices?: string[];\n  amenities?: string[];\n  viewType?: string;\n  smokingPreference?: string;\n  bedCount?: number;\n  bedType?: string;\n  roomSize?: string;\n  floorLevel?: string;\n  accessibility?: string;\n  images?: RoomImage[];\n  promotions?: Array<{\n    code: string;\n    description: string;\n    discountType: string;\n    discountValue: number;\n    startDate?: string;\n    endDate?: string;\n  }>;\n  taxes?: Array<{\n    type: string;\n    amount: number;\n    currency: string;\n    included: boolean;\n  }>;\n  fees?: Array<{\n    type: string;\n    amount: number;\n    currency: string;\n    included: boolean;\n  }>;\n}\n\ninterface RatePlan {\n  code: string;\n  description: string;\n  commissionStatus: string;\n  rooms: { [key: string]: Room };\n  type?: string;\n  category?: string;\n  mealPlan?: string;\n  packageInclusions?: string[];\n  restrictions?: {\n    minStay?: number;\n    maxStay?: number;\n    closedToArrival?: boolean;\n    closedToDeparture?: boolean;\n    advancePurchaseDays?: number;\n    stayThrough?: string;\n    blackoutDates?: string[];\n  };\n  cancellationPolicies?: Array<{\n    deadline: string;\n    amount: number;\n    currency: string;\n    type: string;\n  }>;\n  guaranteePolicy?: {\n    type: string;\n    required: boolean;\n    deadline?: string;\n  };\n  depositPolicy?: {\n    type: string;\n    amount: number;\n    currency: string;\n    deadline?: string;\n  };\n  paymentTypes?: string[];\n  promotionalCode?: string;\n  rateComments?: string[];\n  commissionAmount?: number;\n  commissionCurrency?: string;\n  markupAmount?: number;\n  markupCurrency?: string;\n}\n\ninterface AvailabilityResponse {\n  ratePlans: { [key: string]: RatePlan };\n}\n\nfunction generateRequestId(): string {\n  return `travsrv-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n}\n\nfunction logApiOperation(requestId: string, stage: string, data: any) {\n  const timestamp = new Date().toISOString();\n  log(\n    `[TravSrv][${timestamp}][${requestId}][${stage.toUpperCase()}] ${JSON.stringify(data, null, 2)}`,\n  );\n}\n\nasync function delay(ms: number): Promise<void> {\n  return new Promise((resolve) => setTimeout(resolve, ms));\n}\n\nasync function fetchWithRetry(\n  url: string,\n  options: RequestInit,\n  requestId: string,\n  retryCount = 0,\n): Promise<Response> {\n  try {\n    const response = await fetch(url, options);\n\n    // Only check response.ok if we actually got a response\n    if (!response || !response.ok) {\n      if (\n        retryCount < MAX_RETRIES &&\n        (response?.status === 429 || response?.status >= 500)\n      ) {\n        logApiOperation(requestId, \"debug\", {\n          message: `Retrying request after error (attempt ${retryCount + 1}/${MAX_RETRIES})`,\n          status: response?.status,\n          statusText: response?.statusText,\n          url,\n          method: options.method || \"GET\",\n          headers: {\n            ...options.headers,\n            Authorization: \"[REDACTED]\",\n          },\n        });\n\n        await delay(RETRY_DELAY * Math.pow(2, retryCount));\n        return fetchWithRetry(url, options, requestId, retryCount + 1);\n      }\n\n      const errorDetails = {\n        status: response?.status,\n        statusText: response?.statusText,\n        url,\n        method: options.method || \"GET\",\n        retryCount,\n        responseBody: await response?.text(),\n      };\n      console.debug(\"API Error Details:\", errorDetails);\n      throw new Error(`API Error: ${response?.status} ${response?.statusText}`);\n    }\n\n    return response;\n  } catch (error) {\n    if (retryCount < MAX_RETRIES) {\n      logApiOperation(requestId, \"debug\", {\n        message: `Network error, retrying (attempt ${retryCount + 1}/${MAX_RETRIES})`,\n        error: error instanceof Error ? error.message : \"Unknown error\",\n        url,\n        method: options.method || \"GET\",\n        headers: {\n          ...options.headers,\n          Authorization: \"[REDACTED]\",\n        },\n      });\n\n      await delay(RETRY_DELAY * Math.pow(2, retryCount));\n      return fetchWithRetry(url, options, requestId, retryCount + 1);\n    }\n    // Always throw 'API Error' for consistency with test expectations\n    throw new Error(\"API Error\");\n  }\n}\n\nasync function cacheProperty(property: Property): Promise<void> {\n  try {\n    const propertyData = {\n      externalId: property.externalId || property.id?.toString(),\n      name: property.name,\n      description: property.description,\n      latitude: Number(property.latitude),\n      longitude: Number(property.longitude),\n      address: property.address,\n      city: property.city,\n      state: property.state,\n      country: property.country,\n      rating: property.rating ? Number(property.rating) : null,\n      reviewCount: property.reviewCount || 0,\n      basePrice: Number(property.basePrice),\n      currency: property.currency || 'USD',\n      propertyType: property.propertyType || 'hotel',\n      // Fix: Store arrays directly in JSONB fields, don't double-stringify\n      amenities: Array.isArray(property.amenities) ? property.amenities : [],\n      images: Array.isArray(property.images) ? property.images : [],\n      source: 'api',\n      updatedAt: new Date()\n    };\n\n    await db\n      .insert(properties)\n      .values(propertyData)\n      .onConflictDoUpdate({\n        target: [properties.externalId],\n        set: {\n          ...propertyData,\n          updatedAt: new Date()\n        }\n      });\n\n    log(`✅ Property cached successfully: ${property.name} (ID: ${property.externalId || property.id})`);\n  } catch (error) {\n    log(`❌ Property caching failed for ${property.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);\n    // Don't throw - allow operation to continue\n  }\n}\n\nexport async function searchTravSrvProperties(\n  params: TravSrvSearchParams,\n): Promise<Property[]> {\n  const requestId = generateRequestId();\n  logApiOperation(requestId, \"start\", { params });\n\n  try {\n    const queryParams = new URLSearchParams({\n      type: \"availability\",\n      siteid: SITE_ID,\n      rooms: (params.rooms || 1).toString(),\n      adults: (params.adults || 1).toString(),\n      children: (params.children || 0).toString(),\n      candidateSearch: \"true\",\n      _type: \"json\",\n      currency: params.currency || \"USD\",\n      sortType: \"dealpercent\",\n      latitude: params.latitude.toString(),\n      longitude: params.longitude.toString(),\n      inDate: params.inDate,\n      outDate: params.outDate,\n      maxResults: \"100\",\n      ...(params.radius && { radius: params.radius.toString() }), // Add radius if provided\n    });\n\n    const requestUrl = `${BASE_URL}/hotel?${queryParams.toString()}`;\n\n    logApiOperation(requestId, \"request\", {\n      url: requestUrl,\n      method: \"GET\",\n      params,\n    });\n\n    const response = await fetchWithRetry(\n      requestUrl,\n      {\n        headers: {\n          Authorization: AUTH_HEADER,\n          \"Accept-version\": \"2\",\n        },\n      },\n      requestId,\n    );\n\n    const data = await response.json();\n\n    if (!data.ArnResponse?.Availability?.HotelAvailability?.Hotel) {\n      logApiOperation(requestId, \"no_results\", {\n        message: \"No hotels found in response\",\n        responseData: data,\n      });\n      return [];\n    }\n\n    logApiOperation(requestId, \"response\", {\n      url: requestUrl,\n      method: \"GET\",\n      data: JSON.stringify(data).substring(0, 1000) + \"...\",\n    });\n\n    const hotels = Array.isArray(\n      data.ArnResponse.Availability.HotelAvailability.Hotel,\n    )\n      ? data.ArnResponse.Availability.HotelAvailability.Hotel\n      : [data.ArnResponse.Availability.HotelAvailability.Hotel];\n\n    // Log the first hotel's data to check available image fields\n    if (hotels.length > 0) {\n      logApiOperation(requestId, \"hotel_data\", {\n        hotelId: hotels[0][\"@HotelID\"],\n        imageFields: Object.keys(hotels[0]).filter((key) =>\n          key.toLowerCase().includes(\"image\"),\n        ),\n        imageData: {\n          thumbnail: hotels[0][\"@ImageThumbnail\"],\n          images: hotels[0][\"Images\"],\n          propertyImages: hotels[0][\"PropertyImages\"],\n          allImages: hotels[0][\"AllImages\"],\n          imageUrls: hotels[0][\"ImageUrls\"],\n        },\n      });\n    }\n\n    const properties = hotels.map((hotel: TravSrvHotel): Property => {\n      // Extract the numeric part of PriceClass and convert to dollars with 2 decimal places\n      const priceMatch = hotel[\"@PriceClass\"].match(/\\d+/);\n      const basePrice = priceMatch\n        ? Number(priceMatch[0]).toFixed(2)\n        : \"300.00\";\n\n      // Handle rating: ensure it's between 0 and 5 with one decimal place\n      let rating: string | null = null;\n      if (hotel[\"@TripAdvisorRating\"]) {\n        const ratingNum = parseFloat(hotel[\"@TripAdvisorRating\"]);\n        rating = (ratingNum <= 0 ? 0 : Math.min(5, ratingNum)).toFixed(1);\n      }\n\n      // Default amenities based on property type and price class\n      const defaultAmenities = [\n        \"WiFi\",\n        \"TV\",\n        hotel[\"@PriceClass\"].includes(\"3\") ||\n        hotel[\"@PriceClass\"].includes(\"4\") ||\n        hotel[\"@PriceClass\"].includes(\"5\")\n          ? \"Air Conditioning\"\n          : null,\n        hotel[\"@PriceClass\"].includes(\"4\") || hotel[\"@PriceClass\"].includes(\"5\")\n          ? \"Breakfast\"\n          : null,\n        hotel[\"@PropertyType\"].toLowerCase() === \"resort\" ? \"Pool\" : null,\n        hotel[\"@PriceClass\"].includes(\"4\") || hotel[\"@PriceClass\"].includes(\"5\")\n          ? \"Gym\"\n          : null,\n        hotel[\"@PriceClass\"].includes(\"3\") ||\n        hotel[\"@PriceClass\"].includes(\"4\") ||\n        hotel[\"@PriceClass\"].includes(\"5\")\n          ? \"Parking\"\n          : null,\n      ].filter(Boolean) as string[];\n\n      return {\n        id: parseInt(hotel[\"@HotelID\"]),\n        externalId: hotel[\"@HotelID\"],\n        name: hotel[\"@Name\"],\n        description:\n          hotel[\"@LocationDescription\"] ||\n          `Located in ${hotel[\"@City\"]}${hotel[\"@State\"] ? `, ${hotel[\"@State\"]}` : \"\"}. ${\n            hotel[\"@TripAdvisorRating\"]\n              ? `Rated ${hotel[\"@TripAdvisorRating\"]}/5 with ${hotel[\"@TripAdvisorReviewCount\"]} reviews.`\n              : \"\"\n          }`,\n        propertyType: hotel[\"@PropertyType\"].toLowerCase(),\n        address: hotel[\"@Address1\"],\n        city: hotel[\"@City\"],\n        state: hotel[\"@State\"] || null,\n        country: hotel[\"@CountryCode\"],\n        latitude: hotel[\"@Latitude\"] ? parseFloat(hotel[\"@Latitude\"]) : 0,\n        longitude: hotel[\"@Longitude\"] ? parseFloat(hotel[\"@Longitude\"]) : 0,\n        basePrice: hotel[\"@PriceClass\"] ? parseFloat(hotel[\"@PriceClass\"]) : 0,\n        rating: rating ? parseFloat(rating) : null,\n        reviewCount: hotel[\"@TripAdvisorReviewCount\"]\n          ? parseInt(hotel[\"@TripAdvisorReviewCount\"])\n          : null,\n        images: [\n          hotel[\"@ImageThumbnail\"],\n          ...(hotel[\"@Images\"]?.split(\",\").map((img: string) => img.trim()) ||\n            []),\n          ...(hotel[\"@PropertyImages\"]\n            ?.split(\",\")\n            .map((img: string) => img.trim()) || []),\n        ].filter(Boolean),\n        amenities: defaultAmenities,\n        currency: \"USD\",\n        source: \"travsrv\",\n        discount: 0,\n        featured: false,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n    });\n\n    // Cache all properties (re-enabled with fixed implementation)\n    await Promise.all(\n      properties.map((property: Property) => cacheProperty(property))\n    );\n\n    return properties;\n  } catch (error) {\n    logApiOperation(requestId, \"error\", {\n      message: \"Candidate search failed\",\n      error: error instanceof Error ? error.message : \"Unknown error\",\n    });\n    throw error;\n  }\n}\n\nexport async function getPropertyAvailability(\n  hotelId: string,\n  params: Omit<TravSrvSearchParams, \"latitude\" | \"longitude\">,\n  bestRateOnly: boolean = true\n): Promise<AvailabilityResponse | null> {\n  const requestId = generateRequestId();\n\n  try {\n    // Build query params for API requests\n    const queryParams = new URLSearchParams({\n      type: \"availability\",\n      siteid: SITE_ID,\n      rooms: (params.rooms || 1).toString(),\n      adults: (params.adults || 1).toString(),\n      children: (params.children || 0).toString(),\n      _type: \"json\",\n      currency: params.currency || \"USD\",\n      hotelIds: hotelId,\n      inDate: params.inDate || new Date().toISOString().split(\"T\")[0],\n      outDate:\n        params.outDate ||\n        new Date(Date.now() + 86400000).toISOString().split(\"T\")[0],\n    });\n\n    // Add bestRateSearch parameter when only the best rate is needed\n    if (bestRateOnly) {\n      queryParams.append(\"bestRateSearch\", \"true\");\n    }\n\n    // Fetch availability data\n    const availabilityUrl = `${BASE_URL}/hotel?${queryParams.toString()}`;\n    logApiOperation(requestId, \"debug\", {\n      message: \"Fetching availability data\",\n      url: availabilityUrl,\n      propertyId: hotelId,\n      params: Object.fromEntries(queryParams),\n    });\n\n    const availabilityResponse = await fetchWithRetry(\n      availabilityUrl,\n      {\n        headers: {\n          Authorization: AUTH_HEADER,\n          \"Accept-version\": \"2\",\n          Accept: \"application/json\",\n        },\n      },\n      requestId,\n    );\n\n    const availabilityData = await availabilityResponse.json();\n    const hotelAvailability =\n      availabilityData.ArnResponse?.Availability?.HotelAvailability;\n    const availableHotel = Array.isArray(hotelAvailability?.Hotel)\n      ? hotelAvailability.Hotel[0]\n      : hotelAvailability?.Hotel;\n\n    if (!availableHotel) {\n      return null;\n    }\n\n    // Process rate plans and rooms\n    const ratePlans: { [key: string]: RatePlan } = {};\n    const rawRatePlans = Array.isArray(availableHotel.RatePlan)\n      ? availableHotel.RatePlan\n      : availableHotel.RatePlan\n        ? [availableHotel.RatePlan]\n        : [];\n\n    for (const plan of rawRatePlans) {\n      const rooms: { [key: string]: Room } = {};\n      const rawRooms = Array.isArray(plan.Room) ? plan.Room : [plan.Room];\n\n      for (const room of rawRooms) {\n        rooms[room[\"@Code\"]] = {\n          code: room[\"@Code\"],\n          description: room[\"@Description\"] || \"Standard Room\",\n          rate: parseFloat(room[\"@Rate\"]),\n          currency: room[\"@Currency\"],\n          roomTypeCode: room[\"@RoomTypeCode\"],\n          bedTypeCode: room[\"@BedTypeCode\"],\n          restrictedRate: room[\"@RestrictedRate\"] === \"true\",\n          refundable: room[\"@Refundable\"] === \"true\",\n          maxOccupancy: parseInt(room[\"@MaxOccupancy\"]) || 2,\n          availableQuantity: parseInt(room[\"@MaximumBookable\"]) || 0,\n          totalAmount: room.Total?.[\"@Amount\"]\n            ? parseFloat(room.Total[\"@Amount\"])\n            : 0,\n          totalDiscount: room.Total?.[\"@Discount\"]\n            ? parseFloat(room.Total[\"@Discount\"])\n            : 0,\n          totalComparableRetailDiscount: room.Total?.[\n            \"@ComparableRetailDiscount\"\n          ]\n            ? parseFloat(room.Total[\"@ComparableRetailDiscount\"])\n            : 0,\n          retailDiscountPercent: room.Total?.[\"@RetailDiscountPercent\"]\n            ? parseFloat(room.Total[\"@RetailDiscountPercent\"])\n            : 0,\n          totalCurrency: room.Total?.[\"@Currency\"] || room[\"@Currency\"],\n          // Add any additional fields present in the response\n          rateCode: room[\"@RateCode\"],\n          rateDescription: room[\"@RateDescription\"],\n          cancellationPolicy: room[\"@CancellationPolicy\"],\n          guaranteePolicy: room[\"@GuaranteePolicy\"],\n          depositPolicy: room[\"@DepositPolicy\"],\n          includedServices: room[\"@IncludedServices\"]\n            ?.split(\",\")\n            .map((s: string) => s.trim()),\n          amenities: room[\"@Amenities\"]\n            ?.split(\",\")\n            .map((s: string) => s.trim()),\n          viewType: room[\"@ViewType\"],\n          smokingPreference: room[\"@SmokingPreference\"],\n          bedCount: room[\"@BedCount\"] ? parseInt(room[\"@BedCount\"]) : undefined,\n          bedType: room[\"@BedType\"],\n          roomSize: room[\"@RoomSize\"],\n          floorLevel: room[\"@FloorLevel\"],\n          accessibility: room[\"@Accessibility\"],\n          images: room[\"@Images\"]?.split(\",\").map((s: string) => ({\n            url: s.trim(),\n            roomTypeCode: room[\"@RoomTypeCode\"],\n            category: \"room\",\n          })),\n          promotions: room[\"Promotions\"]?.map((promo: any) => ({\n            code: promo[\"@Code\"],\n            description: promo[\"@Description\"],\n            discountType: promo[\"@DiscountType\"],\n            discountValue: parseFloat(promo[\"@DiscountValue\"]),\n            startDate: promo[\"@StartDate\"],\n            endDate: promo[\"@EndDate\"],\n          })),\n          taxes: room[\"Taxes\"]?.map((tax: any) => ({\n            type: tax[\"@Type\"],\n            amount: parseFloat(tax[\"@Amount\"]),\n            currency: tax[\"@Currency\"],\n            included: tax[\"@Included\"] === \"true\",\n          })),\n          fees: room[\"Fees\"]?.map((fee: any) => ({\n            type: fee[\"@Type\"],\n            amount: parseFloat(fee[\"@Amount\"]),\n            currency: fee[\"@Currency\"],\n            included: fee[\"@Included\"] === \"true\",\n          })),\n        };\n      }\n\n      ratePlans[plan[\"@Code\"]] = {\n        code: plan[\"@Code\"],\n        description: plan[\"@Description\"],\n        commissionStatus: plan[\"@CommissionStatus\"],\n        rooms,\n      };\n    }\n\n    return { ratePlans };\n  } catch (error) {\n    logApiOperation(requestId, \"error\", {\n      message: \"Failed to fetch availability\",\n      error: error instanceof Error ? error.message : \"Unknown error\",\n      propertyId: hotelId,\n    });\n    throw error;\n  }\n}\n"}