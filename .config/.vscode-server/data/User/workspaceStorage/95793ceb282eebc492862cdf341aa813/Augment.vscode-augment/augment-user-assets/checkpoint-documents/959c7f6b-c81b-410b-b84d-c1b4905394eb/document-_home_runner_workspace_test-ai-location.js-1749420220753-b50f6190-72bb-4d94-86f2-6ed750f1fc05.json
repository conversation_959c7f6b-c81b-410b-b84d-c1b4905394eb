{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "test-ai-location.js"}, "modifiedCode": "// Test script to verify enhanced AI location parsing\nconst fetch = require('node-fetch');\n\nasync function testLocationParsing() {\n  console.log('🧪 Testing Enhanced AI Location Parsing...\\n');\n\n  const testQueries = [\n    'I want to visit Paris, France',\n    'Show me destinations in Tokyo',\n    'Find me places to stay in New York City',\n    'I need accommodations in Miami Beach',\n    'Tell me about travel to San Francisco'\n  ];\n\n  for (const query of testQueries) {\n    console.log(`📝 Testing: \"${query}\"`);\n    \n    try {\n      const response = await fetch('http://localhost:5000/api/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: query,\n          context: {},\n          sessionId: 'test-session',\n          extractLocation: true,\n          enhancedMode: true\n        }),\n      });\n\n      if (!response.ok) {\n        console.log(`❌ HTTP Error: ${response.status}`);\n        continue;\n      }\n\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder();\n      let buffer = '';\n      let foundLocation = false;\n\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) break;\n\n        buffer += decoder.decode(value, { stream: true });\n        const lines = buffer.split('\\n');\n        buffer = lines.pop() || '';\n\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            const data = line.slice(6);\n            if (data === '[DONE]') continue;\n\n            try {\n              const parsed = JSON.parse(data);\n              \n              if (parsed.type === 'action' && parsed.data.type === 'location') {\n                console.log(`✅ Found ACTION:LOCATION - ${parsed.data.data.name} (${parsed.data.data.lat}, ${parsed.data.data.lng})`);\n                foundLocation = true;\n              } else if (parsed.type === 'location') {\n                console.log(`✅ Found LOCATION - ${parsed.data.name} (${parsed.data.lat}, ${parsed.data.lng})`);\n                foundLocation = true;\n              }\n            } catch (error) {\n              // Ignore parsing errors for this test\n            }\n          }\n        }\n      }\n\n      if (!foundLocation) {\n        console.log('⚠️  No location data found in response');\n      }\n\n    } catch (error) {\n      console.log(`❌ Error: ${error.message}`);\n    }\n\n    console.log('---\\n');\n  }\n\n  console.log('🎉 Test completed!');\n}\n\ntestLocationParsing().catch(console.error);\n"}