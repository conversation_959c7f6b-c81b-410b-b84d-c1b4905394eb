{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/critical-functionality.test.ts"}, "originalCode": "// Simple functionality test without complex dependencies\ndescribe('Critical Functionality Tests', () => {\n  it('should validate multi-bedroom service exists', () => {\n    // Test that the service file exists and can be imported\n    expect(true).toBe(true); // Placeholder test\n  });\n\n  it('should validate property caching fix', () => {\n    // Test that property caching logic is fixed\n    expect(true).toBe(true); // Placeholder test\n  });\n\n  it('should validate AI chat components are unified', () => {\n    // Test that old components are removed and new one exists\n    expect(true).toBe(true); // Placeholder test\n  });\n});\n\n\n", "modifiedCode": "import { describe, it, expect } from '@jest/globals';\nimport fs from 'fs';\nimport path from 'path';\n\ndescribe('Critical Functionality Tests', () => {\n  describe('File Structure Validation', () => {\n    it('should have multi-bedroom service file', () => {\n      const servicePath = path.join(__dirname, '../server/services/multiBedroomService.ts');\n      expect(fs.existsSync(servicePath)).toBe(true);\n    });\n\n    it('should have unified AI chat component', () => {\n      const componentPath = path.join(__dirname, '../client/src/components/UnifiedAIChat.tsx');\n      expect(fs.existsSync(componentPath)).toBe(true);\n    });\n\n    it('should have removed old AI chat components', () => {\n      const oldComponents = [\n        '../client/src/components/AiChatEnhanced.tsx',\n        '../client/src/components/EnhancedAIChat.tsx',\n        '../client/src/components/AiChat.tsx.bak'\n      ];\n\n      oldComponents.forEach(componentPath => {\n        const fullPath = path.join(__dirname, componentPath);\n        expect(fs.existsSync(fullPath)).toBe(false);\n      });\n    });\n  });\n\n  describe('Property Caching Fix Validation', () => {\n    it('should have fixed property caching implementation', () => {\n      const travsrvPath = path.join(__dirname, '../server/services/travsrv.ts');\n      const content = fs.readFileSync(travsrvPath, 'utf8');\n\n      // Check that caching is re-enabled\n      expect(content).toContain('await Promise.all(');\n      expect(content).toContain('cacheProperty(property)');\n\n      // Check that the fix for JSONB fields is present\n      expect(content).toContain('amenities: Array.isArray(property.amenities) ? property.amenities : []');\n      expect(content).toContain('images: Array.isArray(property.images) ? property.images : []');\n    });\n  });\n\n  describe('Multi-Bedroom API Route', () => {\n    it('should have multi-bedroom API route registered', () => {\n      const routesPath = path.join(__dirname, '../server/routes.ts');\n      const content = fs.readFileSync(routesPath, 'utf8');\n\n      expect(content).toContain('/api/properties/multi-bedroom');\n      expect(content).toContain('searchMultiBedroomAccommodations');\n    });\n  });\n\n  describe('Component Integration', () => {\n    it('should have updated Search page to use UnifiedAIChat', () => {\n      const searchPath = path.join(__dirname, '../client/src/pages/Search.tsx');\n      const content = fs.readFileSync(searchPath, 'utf8');\n\n      expect(content).toContain('UnifiedAIChat');\n      expect(content).not.toContain('AiTravelCompanion');\n    });\n\n    it('should have updated Results page to use UnifiedAIChat', () => {\n      const resultsPath = path.join(__dirname, '../client/src/pages/Results.tsx');\n      const content = fs.readFileSync(resultsPath, 'utf8');\n\n      expect(content).toContain('UnifiedAIChat');\n      expect(content).not.toContain('AiTravelCompanion');\n    });\n  });\n\n  describe('Service Integration', () => {\n    it('should have proper imports in multi-bedroom service', () => {\n      const servicePath = path.join(__dirname, '../server/services/multiBedroomService.ts');\n      const content = fs.readFileSync(servicePath, 'utf8');\n\n      expect(content).toContain(\"import { searchTravSrvProperties, getPropertyAvailability } from './travsrv.js'\");\n      expect(content).toContain(\"import { geocodeLocation } from './geocodingService.js'\");\n    });\n  });\n\n  describe('TypeScript Fixes', () => {\n    it('should have fixed critical TypeScript errors', () => {\n      const paymentServicePath = path.join(__dirname, '../server/services/paymentService.ts');\n      const content = fs.readFileSync(paymentServicePath, 'utf8');\n\n      // Check Stripe API version fix\n      expect(content).toContain(\"apiVersion: '2024-12-18.acacia'\");\n    });\n  });\n});\n\n\n"}