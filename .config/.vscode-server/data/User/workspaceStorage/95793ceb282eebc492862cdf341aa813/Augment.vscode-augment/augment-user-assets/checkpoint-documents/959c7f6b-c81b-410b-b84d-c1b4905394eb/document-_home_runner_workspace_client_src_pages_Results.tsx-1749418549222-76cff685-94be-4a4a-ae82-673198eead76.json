{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Results.tsx"}, "originalCode": "import { useLocation } from \"wouter\";\nimport { useInfiniteQuery, useQueries, useQueryClient } from \"@tanstack/react-query\";\nimport { useInView } from \"framer-motion\";\nimport { useRef, useEffect, useState, useMemo, useCallback } from \"react\";\nimport PropertyCard, { PropertyCardProps } from \"../components/PropertyCard.jsx\";\nimport Map, { MapProps } from \"../components/Map.jsx\";\nimport { Card, CardContent } from \"../components/ui/card.jsx\";\nimport { Skeleton } from \"../components/ui/skeleton.jsx\";\nimport { AlertCircle, Loader2, Map as MapIcon, X, Bot, Star, Hotel, DollarSign, SlidersHorizontal } from \"lucide-react\";\nimport { PropertyLoadingCard } from \"../components/ui/loading-card\";\nimport { Input } from \"../components/ui/input.jsx\";\nimport { Slider } from \"../components/ui/slider.jsx\";\nimport { Label } from \"../components/ui/label.jsx\";\nimport SearchForm from \"../components/SearchForm.jsx\";\nimport { Property, PropertyType, PropertyWithRates, PropertyImage, Room } from \"../types/schema.js\";\nimport { Rate } from \"../types/rate.js\";\nimport { Button } from \"../components/ui/button.jsx\";\nimport { Checkbox } from \"../components/ui/checkbox.jsx\";\nimport { ScrollArea } from \"../components/ui/scroll-area.jsx\";\nimport styled from '@emotion/styled';\nimport { Dialog, DialogContent, DialogTitle, DialogDescription } from \"../components/ui/dialog.jsx\";\nimport { cn } from \"../lib/utils.js\";\nimport CompareDrawer from \"../components/CompareDrawer.jsx\";\nimport { VisuallyHidden } from \"@/components/ui/visually-hidden\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport UnifiedAIChat from \"@/components/UnifiedAIChat\";\nimport { Sheet, SheetContent, SheetTrigger } from \"@/components/ui/sheet\";\nimport { getSessionId } from \"@/lib/session\";\nimport InsightsPanel from \"@/components/InsightsPanel\";\n\ninterface SearchResponse {\n  properties: Property[];\n  total: number;\n  currentPage: number;\n  totalPages: number;\n  explanation?: string;\n  searchId: string;\n  timing: {\n    total: number;\n  };\n}\n\ninterface RoomRate {\n  rate: number;\n  totalAmount?: number;\n  originalAmount?: number;\n  currency: string;\n  Total?: {\n    '@Amount': string;\n    '@Discount'?: string;\n    '@IncludesBookingFee': string;\n  };\n  totalDiscount?: number;\n  retailDiscountPercent?: number;\n  totalComparableRetailDiscount?: number;\n}\n\ninterface RatePlan {\n  code: string;\n  description: string;\n  rooms: {\n    [key: string]: RoomRate;\n  };\n}\n\ninterface AvailabilityResponse {\n  ratePlans: {\n    [key: string]: RatePlan;\n  };\n}\n\ninterface SearchParams {\n  lat: number;\n  lng: number;\n  locationName: string;\n  checkIn: string;\n  checkOut: string;\n  guests: number;\n  rooms: number;\n  radius?: number;\n}\n\n// Custom hook for property search\nfunction usePropertySearch(searchParams: SearchParams) {\n  return useInfiniteQuery<SearchResponse>({\n    queryKey: ['propertySearch', searchParams],\n    queryFn: async ({ pageParam = 1 }) => {\n      // Validate required parameters\n      if (!searchParams.lat || !searchParams.lng || !searchParams.checkIn || !searchParams.checkOut) {\n        throw new Error('Missing required search parameters: lat, lng, checkIn, and checkOut are required');\n      }\n\n      const params = new URLSearchParams({\n        lat: String(searchParams.lat),\n        lng: String(searchParams.lng),\n        locationName: searchParams.locationName || '',\n        checkIn: searchParams.checkIn,\n        checkOut: searchParams.checkOut,\n        guests: searchParams.guests.toString(),\n        rooms: searchParams.rooms.toString(),\n        page: String(pageParam),\n        pageSize: '10',\n        radius: searchParams.radius?.toString() || '10'\n      });\n\n      console.log('Fetching properties with params:', Object.fromEntries(params.entries()));\n\n      try {\n        const response = await fetch(`/api/properties/search?${params}`);\n        const responseText = await response.text();\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch properties: ${response.status} ${response.statusText}\\n${responseText}`);\n        }\n\n        try {\n          return JSON.parse(responseText);\n        } catch (e) {\n          console.error('Failed to parse response:', e);\n          throw new Error('Invalid response format from server');\n        }\n      } catch (error) {\n        console.error('Search error:', error);\n        throw error;\n      }\n    },\n    getNextPageParam: (lastPage) =>\n      lastPage.currentPage < lastPage.totalPages ? lastPage.currentPage + 1 : undefined,\n    initialPageParam: 1,\n  });\n}\n\n// Add new interfaces for filter options\nconst PROPERTY_TYPES = [\n  { id: 'hotel', label: 'Hotel' },\n  { id: 'resort', label: 'Resort' },\n  { id: 'apartment', label: 'Apartment' },\n  { id: 'villa', label: 'Villa' },\n  { id: 'guesthouse', label: 'Guesthouse' }\n];\n\nconst AMENITIES = [\n  { id: 'pool', label: 'Swimming Pool' },\n  { id: 'wifi', label: 'Free WiFi' },\n  { id: 'parking', label: 'Parking' },\n  { id: 'restaurant', label: 'Restaurant' },\n  { id: 'fitness', label: 'Fitness Center' },\n  { id: 'spa', label: 'Spa' },\n  { id: 'beach', label: 'Beach Access' },\n  { id: 'breakfast', label: 'Free Breakfast' },\n  { id: 'bar', label: 'Bar/Lounge' },\n  { id: 'aircon', label: 'Air Conditioning' }\n];\n\n// Update the Map component interface to include onMapMoved\ninterface MapComponentProps extends MapProps {\n  onMapMoved?: (center: { lat: number; lng: number }, radius: number) => void;\n  onMoveEnd?: (center: { lat: number; lng: number }, radius: number) => void;\n  radius?: number;\n}\n\nconst ResultsContainer = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 24px;\n`;\n\nconst MapViewButton = styled.button`\n  position: fixed;\n  bottom: 24px;\n  right: 24px;\n  background: #1976d2;\n  color: white;\n  border: none;\n  border-radius: 24px;\n  padding: 12px 24px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  cursor: pointer;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.2);\n\n  &:hover {\n    background: #1565c0;\n  }\n`;\n\nconst FilterDrawer = () => {\n  const [nameFilter, setNameFilter] = useState(\"\");\n  const [minRating, setMinRating] = useState(0);\n  const [maxPrice, setMaxPrice] = useState(1000);\n  const [selectedPropertyTypes, setSelectedPropertyTypes] = useState<string[]>([]);\n  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);\n  const [selectedBedrooms, setSelectedBedrooms] = useState<number | null>(null);\n\n  const handleNameFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setNameFilter(e.target.value);\n  };\n\n  const handleRatingChange = ([value]: [number]) => {\n    setMinRating(value);\n  };\n\n  const handlePriceChange = ([value]: [number]) => {\n    setMaxPrice(value);\n  };\n\n  const handlePropertyTypeChange = (checked: boolean | \"indeterminate\", typeId: string) => {\n    setSelectedPropertyTypes(prev =>\n      checked === true\n        ? [...prev, typeId]\n        : prev.filter(t => t !== typeId)\n    );\n  };\n\n  const handleAmenityChange = (checked: boolean | \"indeterminate\", amenityId: string) => {\n    setSelectedAmenities(prev =>\n      checked === true\n        ? [...prev, amenityId]\n        : prev.filter(a => a !== amenityId)\n    );\n  };\n\n  return (\n    <Sheet>\n      <SheetTrigger asChild>\n        <Button variant=\"outline\" size=\"sm\" className=\"md:hidden\">\n          <SlidersHorizontal className=\"mr-2 h-4 w-4\" />\n          Filters\n        </Button>\n      </SheetTrigger>\n      <SheetContent side=\"bottom\" className=\"h-[80vh]\">\n        <div className=\"space-y-6 overflow-y-auto\">\n          <div className=\"space-y-4\">\n            <div>\n              <Label>Hotel Name</Label>\n              <Input\n                placeholder=\"Search hotels...\"\n                value={nameFilter}\n                onChange={handleNameFilterChange}\n              />\n            </div>\n\n            <div>\n              <Label>Minimum Rating</Label>\n              <Slider\n                value={[minRating]}\n                onValueChange={handleRatingChange}\n                min={0}\n                max={5}\n                step={0.5}\n              />\n              <span className=\"text-sm text-muted-foreground\">{minRating} stars</span>\n            </div>\n\n            <div>\n              <Label>Maximum Price</Label>\n              <Slider\n                value={[maxPrice]}\n                onValueChange={handlePriceChange}\n                min={0}\n                max={1000}\n                step={50}\n              />\n              <span className=\"text-sm text-muted-foreground\">${maxPrice}</span>\n            </div>\n\n            <div>\n              <Label>Minimum Bedrooms</Label>\n              <div className=\"flex gap-2 mt-2\">\n                {[1, 2, 3, 4, 5].map(count => (\n                  <Button\n                    key={count}\n                    variant={selectedBedrooms === count ? \"default\" : \"outline\"}\n                    size=\"sm\"\n                    onClick={() => setSelectedBedrooms(selectedBedrooms === count ? null : count)}\n                    className=\"flex-1\"\n                  >\n                    {count}+\n                  </Button>\n                ))}\n              </div>\n              {selectedBedrooms && (\n                <span className=\"text-sm text-muted-foreground mt-1 block\">\n                  {selectedBedrooms}+ bedrooms\n                </span>\n              )}\n            </div>\n          </div>\n\n          <div>\n            <Label className=\"text-base\">Property Type</Label>\n            <ScrollArea className=\"h-[200px]\">\n              <div className=\"space-y-2\">\n                {PROPERTY_TYPES.map(type => (\n                  <div key={type.id} className=\"flex items-center space-x-2\">\n                    <Checkbox\n                      id={`type-${type.id}`}\n                      checked={selectedPropertyTypes.includes(type.id)}\n                      onCheckedChange={(checked) => handlePropertyTypeChange(checked as boolean, type.id)}\n                    />\n                    <label\n                      htmlFor={`type-${type.id}`}\n                      className=\"text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n                    >\n                      {type.label}\n                    </label>\n                  </div>\n                ))}\n              </div>\n            </ScrollArea>\n          </div>\n\n          <div>\n            <Label className=\"text-base\">Amenities</Label>\n            <ScrollArea className=\"h-[200px]\">\n              <div className=\"space-y-2\">\n                {AMENITIES.map(amenity => (\n                  <div key={amenity.id} className=\"flex items-center space-x-2\">\n                    <Checkbox\n                      id={`amenity-${amenity.id}`}\n                      checked={selectedAmenities.includes(amenity.id)}\n                      onCheckedChange={(checked) => handleAmenityChange(checked as boolean, amenity.id)}\n                    />\n                    <label\n                      htmlFor={`amenity-${amenity.id}`}\n                      className=\"text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n                    >\n                      {amenity.label}\n                    </label>\n                  </div>\n                ))}\n              </div>\n            </ScrollArea>\n          </div>\n        </div>\n      </SheetContent>\n    </Sheet>\n  );\n};\n\n// Cast Map so TS allows extra optional props like radius\nconst MapComponent = Map as unknown as React.FC<MapComponentProps>;\n\nexport default function Results() {\n  const [location, setLocation] = useLocation();\n  const { toast } = useToast();\n\n  // Parse search params from URL\n  const searchParams = useMemo(() => {\n    try {\n      const params = new URLSearchParams(window.location.search);\n\n      const lat = params.get('lat');\n      const lng = params.get('lng');\n      const checkIn = params.get('checkIn');\n      const checkOut = params.get('checkOut');\n      const locationName = params.get('locationName');\n      const recommended = params.get('recommended');\n\n      // Parse numeric values\n      const parsedLat = lat ? parseFloat(lat) : NaN;\n      const parsedLng = lng ? parseFloat(lng) : NaN;\n\n      // Validate required location parameters\n      if (!lat || isNaN(parsedLat)) {\n        return null;\n      }\n      if (!lng || isNaN(parsedLng)) {\n        return null;\n      }\n      if (!locationName) {\n        return null;\n      }\n\n      // Provide default dates if missing or empty\n      const today = new Date();\n      const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);\n      const dayAfterTomorrow = new Date(today.getTime() + 2 * 24 * 60 * 60 * 1000);\n      \n      const defaultCheckIn = tomorrow.toISOString().split('T')[0];\n      const defaultCheckOut = dayAfterTomorrow.toISOString().split('T')[0];\n      \n      const finalCheckIn = (checkIn && checkIn.trim()) ? checkIn : defaultCheckIn;\n      const finalCheckOut = (checkOut && checkOut.trim()) ? checkOut : defaultCheckOut;\n\n      // All validations passed, create the params object\n      const validatedParams: SearchParams = {\n        lat: parsedLat,\n        lng: parsedLng,\n        locationName: locationName,\n        checkIn: finalCheckIn,\n        checkOut: finalCheckOut,\n        guests: Math.max(1, parseInt(params.get('guests') || '2', 10)),\n        rooms: Math.max(1, parseInt(params.get('rooms') || '1', 10)),\n        radius: Math.max(1, parseInt(params.get('radius') || '10', 10))\n      };\n\n      return validatedParams;\n    } catch (error) {\n      return null;\n    }\n  }, []);\n\n  // Don't proceed with search if parameters are invalid\n  if (!searchParams) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <div className=\"container mx-auto px-4 py-8\">\n          <Card className=\"p-6\">\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center gap-2 text-destructive\">\n                <AlertCircle className=\"h-5 w-4\" />\n                <h2 className=\"text-lg font-semibold\">Invalid Search Parameters</h2>\n              </div>\n              <p>Please check your search parameters and try again.</p>\n              <Button onClick={() => window.location.href = '/'} variant=\"outline\">\n                Return to Search\n              </Button>\n            </div>\n          </Card>\n        </div>\n      </div>\n    );\n  }\n\n  // Create a stable query object for usePropertySearch\n  const queryParams = useMemo(() => ({\n    lat: Number(searchParams.lat),\n    lng: Number(searchParams.lng),\n    locationName: searchParams.locationName,\n    checkIn: searchParams.checkIn,\n    checkOut: searchParams.checkOut,\n    guests: Number(searchParams.guests),\n    rooms: Number(searchParams.rooms),\n    radius: Number(searchParams.radius)\n  }), [searchParams]);\n\n  // Initialize search query after validating parameters\n  const searchQuery = usePropertySearch(queryParams);\n\n  const queryClient = useQueryClient();\n\n  // Initialize all state hooks at the top level\n  const [properties, setProperties] = useState<Property[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [mapCenter, setMapCenter] = useState<{ lat: number; lng: number; } | undefined>(undefined);\n  const [searchRadius, setSearchRadius] = useState<number>(0);\n  const [isMapFullscreen, setIsMapFullscreen] = useState(false);\n  const [pendingMapCenter, setPendingMapCenter] = useState<{ lat: number; lng: number; } | undefined>(undefined);\n  const [pendingRadius, setPendingRadius] = useState<number>(0);\n  const [nameFilter, setNameFilter] = useState(\"\");\n  const [minRating, setMinRating] = useState(0);\n  const [maxPrice, setMaxPrice] = useState(1000);\n  const [selectedPropertyTypes, setSelectedPropertyTypes] = useState<string[]>([]);\n  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);\n  const [selectedBedrooms, setSelectedBedrooms] = useState<number | null>(null);\n  const [mapViewOpen, setMapViewOpen] = useState(false);\n  const [selectedPropertyId, setSelectedPropertyId] = useState<number | null>(null);\n  const [isMapVisible, setIsMapVisible] = useState(false);\n  const [compareProperties, setCompareProperties] = useState<PropertyWithRates[]>([]);\n  const [isCompareOpen, setIsCompareOpen] = useState(false);\n  const [showAiChat, setShowAiChat] = useState(false);\n  const [mapFilters, setMapFilters] = useState<{\n    priceRange: [number, number];\n    propertyTypes: PropertyType[];\n  }>({\n    priceRange: [0, 1000],\n    propertyTypes: []\n  });\n\n  // Initialize refs at the top level\n  const loadMoreRef = useRef<HTMLDivElement>(null);\n  const isInView = useInView(loadMoreRef);\n\n  // Unified session ID shared across app\n  const sessionId = useMemo(() => getSessionId(), []);\n\n  // Record property view to server context\n  const recordPropertyView = useCallback(async (propertyId: number) => {\n    try {\n      await fetch('/api/context/property-view', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ sessionId, propertyId })\n      });\n    } catch (error) {\n      console.error('Failed to record property view:', error);\n    }\n  }, [sessionId]);\n\n  // Record comparison to server context\n  const recordComparison = useCallback(async (propertyId: number, isAdding: boolean) => {\n    try {\n      await fetch('/api/context/comparison', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ sessionId, propertyId, action: isAdding ? 'add' : 'remove' })\n      });\n    } catch (error) {\n      console.error('Failed to record comparison:', error);\n    }\n  }, [sessionId]);\n\n  // Add handler for comparison\n  const handleCompareToggle = (property: PropertyWithRates) => {\n    setCompareProperties(prev => {\n      const exists = prev.some(p => p.id === property.id);\n      const isAdding = !exists;\n      \n      // Record comparison action to server context\n      recordComparison(property.id, isAdding);\n      \n      if (exists) {\n        return prev.filter(p => p.id !== property.id);\n      }\n      if (prev.length >= 3) {\n        return prev;\n      }\n      return [...prev, property];\n    });\n  };\n\n  // Get all properties from the search results\n  const allProperties = searchQuery.data?.pages.flatMap(page => page.properties) || [];\n\n  // Calculate number of nights\n  const checkInDate = searchParams.checkIn ? new Date(searchParams.checkIn) : undefined;\n  const checkOutDate = searchParams.checkOut ? new Date(searchParams.checkOut) : undefined;\n  const nights = useMemo(() => {\n    const checkInParam = searchParams.checkIn;\n    const checkOutParam = searchParams.checkOut;\n    if (!checkInParam || !checkOutParam) return 0;\n\n    const checkIn = new Date(checkInParam);\n    const checkOut = new Date(checkOutParam);\n    return Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));\n  }, [searchParams.checkIn, searchParams.checkOut]);\n\n  // Update availability queries to use searchQueryParams\n  const availabilityQueries = useQueries({\n    queries: allProperties.map(property => ({\n      queryKey: ['availability', property.id, searchParams],\n      queryFn: async (): Promise<AvailabilityResponse> => {\n        const response = await fetch(\n          `/api/properties/${property.id}/availability?` +\n          `checkIn=${searchParams.checkIn}&` +\n          `checkOut=${searchParams.checkOut}&` +\n          `guests=${searchParams.guests}&` +\n          `rooms=${searchParams.rooms}`\n        );\n\n        if (!response.ok) {\n          throw new Error('Failed to fetch availability');\n        }\n\n        return response.json();\n      },\n      staleTime: 1000 * 60 * 5, // Consider data fresh for 5 minutes\n      retry: 1,\n      enabled: !!property.id\n    }))\n  });\n\n  // Create details queries\n  const detailsQueries = useQueries({\n    queries: allProperties.map(property => ({\n      queryKey: ['propertyDetails', property.id],\n      queryFn: async () => {\n        const response = await fetch(`/api/properties/${property.id}/content`);\n        if (!response.ok) {\n          throw new Error('Failed to fetch property details');\n        }\n        return response.json();\n      },\n      staleTime: 1000 * 60 * 30, // Consider data fresh for 30 minutes\n      retry: 1,\n      enabled: !!property.id\n    }))\n  });\n\n  // Combine properties with their rates and details\n  const propertiesWithRates: PropertyWithRates[] = useMemo(() => {\n    return allProperties.map((property, index) => {\n      const availabilityQuery = availabilityQueries[index];\n      const detailsQuery = detailsQueries[index];\n\n      // Process rates from first rate plan only\n      let rateInfo = undefined;\n      if (availabilityQuery.data?.ratePlans) {\n        const firstPlan = Object.values(availabilityQuery.data.ratePlans)[0];\n\n        if (firstPlan) {\n          const firstRoom = Object.values(firstPlan.rooms)[0] as Room;\n\n          if (firstRoom) {\n            // Extract values from the Total object if it exists\n            // Parse the base rate first\n            const baseRate = typeof firstRoom.rate === 'number'\n              ? firstRoom.rate\n              : parseFloat(String(firstRoom.rate || '0'));\n\n            // Get the Total object with all required fields\n            const total = {\n              '@Amount': String(firstRoom.Total?.['@Amount'] ?? firstRoom.totalAmount ?? baseRate ?? '0'),\n              '@Discount': String(firstRoom.Total?.['@Discount'] ?? firstRoom.totalDiscount ?? '0'),\n              '@RetailDiscountPercent': String(firstRoom.Total?.['@RetailDiscountPercent'] ?? firstRoom.retailDiscountPercent ?? '0'),\n              '@Currency': String(firstRoom.Total?.['@Currency'] ?? firstRoom.currency ?? 'USD'),\n              '@IncludesBookingFee': String(firstRoom.Total?.['@IncludesBookingFee'] ?? 'false'),\n              '@ComparableRetailDiscount': String(firstRoom.Total?.['@ComparableRetailDiscount'] ?? firstRoom.totalComparableRetailDiscount ?? '0')\n            };\n\n            // Parse all numeric values\n            const totalAmount = parseFloat(total['@Amount']);\n            const discountAmount = parseFloat(total['@Discount']);\n            const originalAmount = totalAmount + discountAmount;\n            const retailDiscountPercent = Math.ceil(parseFloat(total['@RetailDiscountPercent']) || ((discountAmount / originalAmount) * 100));\n\n            rateInfo = {\n              code: firstPlan.code || '',\n              description: firstPlan.description || '',\n              rate: baseRate,\n              totalAmount: totalAmount || baseRate, // Fallback to base rate if total amount is 0\n              originalAmount: originalAmount || baseRate, // Fallback to base rate if original amount is 0\n              discountAmount,\n              discountPercent: retailDiscountPercent,\n              currency: firstRoom.currency || total['@Currency'],\n              roomTypeCode: firstRoom.roomTypeCode || '',\n              bedTypeCode: firstRoom.bedTypeCode || '',\n              restrictedRate: firstRoom.restrictedRate || false,\n              refundable: firstRoom.refundable || true,\n              maxOccupancy: firstRoom.maxOccupancy || 2,\n              availableQuantity: firstRoom.availableQuantity || 1,\n              rateCode: firstRoom.rateCode || firstPlan.code || '',\n              rateDescription: firstRoom.rateDescription || firstPlan.description || '',\n              cancellationPolicy: firstRoom.cancellationPolicy || '',\n              guaranteePolicy: firstRoom.guaranteePolicy || '',\n              depositPolicy: firstRoom.depositPolicy || '',\n              includedServices: firstRoom.includedServices || [],\n              promotions: firstRoom.promotions || [],\n              taxes: firstRoom.taxes || [],\n              fees: firstRoom.fees || []\n            } satisfies Rate;\n          }\n        }\n      } else {\n        console.warn('No rate plans available for property:', {\n          propertyId: property.id,\n          availabilityQuery: availabilityQuery.data\n        });\n      }\n\n      return {\n        ...property,\n        rates: rateInfo ? [rateInfo] : undefined,\n        isLoadingRates: availabilityQuery.isLoading,\n        rateError: availabilityQuery.error ? 'Failed to load rates' : undefined,\n        propertyDetails: {\n          images: detailsQuery.data?.images,\n          isLoading: detailsQuery.isLoading,\n          error: detailsQuery.isError\n        }\n      };\n    });\n  }, [allProperties, availabilityQueries, detailsQueries, nights]);\n\n  // Sort properties by discount percentage\n  const sortedProperties = useMemo(() => {\n    return [...propertiesWithRates].sort((a, b) => {\n      // If rates are still loading, keep original order\n      if (a.isLoadingRates || b.isLoadingRates) return 0;\n\n      // If there's an error loading rates, move to the end\n      if (a.rateError && !b.rateError) return 1;\n      if (!a.rateError && b.rateError) return -1;\n\n      // Get discount percentages, defaulting to 0 if not available\n      const aDiscount = a.rates?.[0]?.discountPercent ?? 0;\n      const bDiscount = b.rates?.[0]?.discountPercent ?? 0;\n\n      // Sort by highest discount first\n      if (bDiscount !== aDiscount) {\n        return bDiscount - aDiscount;\n      }\n\n      // If discounts are equal, sort by total amount\n      const aTotal = a.rates?.[0]?.totalAmount ?? Number.MAX_VALUE;\n      const bTotal = b.rates?.[0]?.totalAmount ?? Number.MAX_VALUE;\n      return aTotal - bTotal;\n    });\n  }, [propertiesWithRates]);\n\n  // Update filtered properties to include new filters\n  const filteredProperties = sortedProperties.filter(property => {\n    const matchesName = property.name.toLowerCase().includes(nameFilter.toLowerCase());\n    const matchesRating = property.rating ? parseFloat(String(property.rating)) >= minRating : minRating === 0;\n    const matchesPrice = property.rates ?\n      property.rates[0].totalAmount <= maxPrice * nights :\n      (typeof property.basePrice === 'number' ? property.basePrice / 100 : 0) <= maxPrice;\n\n    const matchesPropertyType = selectedPropertyTypes.length === 0 ||\n      selectedPropertyTypes.includes(property.type?.toLowerCase() || '');\n\n    const matchesAmenities = selectedAmenities.length === 0 ||\n      selectedAmenities.every(amenity =>\n        property.amenities?.map(a => a.toLowerCase()).includes(amenity.toLowerCase())\n      );\n\n    // Multi-bedroom filter - analyze property description for bedroom count\n    const matchesBedrooms = selectedBedrooms === null || (() => {\n      const description = (property.description || '').toLowerCase();\n      const name = (property.name || '').toLowerCase();\n\n      // Extract bedroom count from description and name\n      const bedroomMatches = [\n        ...description.matchAll(/(\\d+)[\\s-]*bedroom/g),\n        ...name.matchAll(/(\\d+)[\\s-]*bedroom/g),\n        ...description.matchAll(/(\\d+)[\\s-]*bed[\\s,]/g),\n        ...name.matchAll(/(\\d+)[\\s-]*bed[\\s,]/g)\n      ];\n\n      if (bedroomMatches.length > 0) {\n        const maxBedrooms = Math.max(...bedroomMatches.map((match: any) => parseInt(match[1])));\n        return maxBedrooms >= selectedBedrooms;\n      }\n\n      // If no bedroom info found, assume 1 bedroom for hotels, 2+ for vacation rentals\n      const propertyType = (property.type || '').toLowerCase();\n      const assumedBedrooms = propertyType.includes('apartment') || propertyType.includes('villa') || propertyType.includes('house') ? 2 : 1;\n      return assumedBedrooms >= selectedBedrooms;\n    })();\n\n    return matchesName && matchesRating && matchesPrice && matchesPropertyType && matchesAmenities && matchesBedrooms;\n  });\n\n  // Update infinite scroll logic\n  useEffect(() => {\n    if (isInView && searchQuery.hasNextPage && !searchQuery.isFetchingNextPage) {\n      searchQuery.fetchNextPage();\n    }\n  }, [isInView, searchQuery.hasNextPage, searchQuery.isFetchingNextPage, searchQuery]);\n\n  // Get the total properties count from the first page\n  const totalProperties = searchQuery.data?.pages[0]?.total || 0;\n\n  // Event handlers with proper types\n  const handleNameFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setNameFilter(e.target.value);\n  };\n\n  const handleRatingChange = ([value]: [number]) => {\n    setMinRating(value);\n  };\n\n  const handlePriceChange = ([value]: [number]) => {\n    setMaxPrice(value);\n  };\n\n  // Quick action buttons\n  const handleQuickAction = (action: 'luxury' | 'family' | 'budget') => {\n    switch (action) {\n      case 'luxury':\n        setSelectedPropertyTypes(['hotel', 'resort']);\n        setSelectedAmenities(['pool', 'spa']);\n        handleRatingChange([4]);\n        handlePriceChange([1000]);\n        break;\n      case 'family':\n        setSelectedPropertyTypes(['hotel', 'resort', 'apartment']);\n        setSelectedAmenities(['wifi', 'parking', 'breakfast']);\n        handleRatingChange([3.5]);\n        handlePriceChange([300]);\n        break;\n      case 'budget':\n        setSelectedPropertyTypes(['hotel', 'guesthouse', 'apartment']);\n        setSelectedAmenities(['wifi']);\n        handleRatingChange([3]);\n        handlePriceChange([150]);\n        break;\n    }\n  };\n\n  // Update handleMapMoved to just store the pending values\n  const handleMapMoved = (center: { lat: number; lng: number }, radius: number) => {\n    setPendingMapCenter(center);\n    setPendingRadius(radius);\n\n    // Notify backend so AI context is updated\n    fetch('/api/context/map-move', {\n      method: 'POST',\n      headers: { 'Content-Type': 'application/json' },\n      body: JSON.stringify({ sessionId, center, radius })\n    }).catch(() => {/* non-blocking */});\n\n    // Force React Query to refetch with new parameters by invalidating key\n    queryClient.invalidateQueries({ queryKey: ['propertySearch'] });\n  };\n\n  // Update handleSearchThisArea to use the pending values\n  const handleSearchThisArea = async () => {\n    if (!pendingMapCenter) return;\n\n    // Update the search parameters with the new location\n    const newSearchParams = new URLSearchParams(window.location.search);\n    newSearchParams.set('lat', pendingMapCenter.lat.toString());\n    newSearchParams.set('lng', pendingMapCenter.lng.toString());\n    // Convert radius from meters to kilometers and round to 2 decimal places\n    const radiusInKm = (pendingRadius / 1000).toFixed(2);\n    newSearchParams.set('radius', radiusInKm);\n\n    // Update the URL without triggering a page reload\n    window.history.replaceState({}, '', `?${newSearchParams.toString()}`);\n\n    // Update the actual map center and radius state\n    setMapCenter(pendingMapCenter);\n    setSearchRadius(pendingRadius);\n\n    // Create new search params object\n    const newParams: SearchParams = {\n      ...searchParams,\n      lat: pendingMapCenter.lat,\n      lng: pendingMapCenter.lng,\n      radius: parseFloat(radiusInKm)\n    };\n\n    // Refetch with new parameters\n    await searchQuery.refetch();\n  };\n\n  // Update the checkbox event handlers with proper types\n  const handlePropertyTypeChange = (checked: boolean | \"indeterminate\", typeId: string) => {\n    setSelectedPropertyTypes(prev =>\n      checked === true\n        ? [...prev, typeId]\n        : prev.filter(t => t !== typeId)\n    );\n  };\n\n  const handleAmenityChange = (checked: boolean | \"indeterminate\", amenityId: string) => {\n    setSelectedAmenities(prev =>\n      checked === true\n        ? [...prev, amenityId]\n        : prev.filter(a => a !== amenityId)\n    );\n  };\n\n  // Update the dialog handling\n  const openSearchModal = () => {\n    const dialog = document.getElementById('search-form-modal') as HTMLDialogElement;\n    if (dialog) dialog.showModal();\n  };\n\n  const closeSearchModal = () => {\n    const dialog = document.getElementById('search-form-modal') as HTMLDialogElement;\n    if (dialog) dialog.close();\n  };\n\n  const handlePropertySelect = (propertyId: number) => {\n    const property = filteredProperties.find(p => p.id === propertyId);\n    if (property) {\n      // Scroll the sidebar to the selected property\n      const propertyElement = document.getElementById(`property-${propertyId}`);\n      if (propertyElement) {\n        propertyElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\n      }\n\n      // Highlight the property in the list\n      setSelectedPropertyId(propertyId);\n    }\n  };\n\n  // Add filter handler\n  const handleMapFiltersChange = (filters: {\n    priceRange: [number, number];\n    propertyTypes: PropertyType[];\n  }) => {\n    setMapFilters(filters);\n\n    // Push filters to backend context for AI awareness\n    fetch('/api/context/filters', {\n      method: 'POST',\n      headers: { 'Content-Type': 'application/json' },\n      body: JSON.stringify({ sessionId, filters })\n    }).catch(() => {});\n  };\n\n  return (\n    <ResultsContainer>\n      {/* Minimal Search Bar */}\n      <div className=\"sticky top-0 z-40 bg-background/95 backdrop-blur-sm border-b shadow-sm\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"h-14 flex items-center justify-between gap-4\">\n            <div className=\"flex items-center gap-3 text-sm overflow-x-auto no-scrollbar\">\n              <span className=\"font-medium truncate max-w-[150px] md:max-w-[200px]\">\n                {searchParams.locationName}\n              </span>\n              <span className=\"text-muted-foreground/60 hidden sm:inline\">•</span>\n              <span className=\"text-muted-foreground whitespace-nowrap\">\n                {checkInDate?.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - {checkOutDate?.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}\n              </span>\n              <span className=\"text-muted-foreground/60 hidden sm:inline\">•</span>\n              <span className=\"text-muted-foreground hidden sm:inline\">{searchParams.guests} guests, {searchParams.rooms} {searchParams.rooms === 1 ? 'room' : 'rooms'}</span>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <FilterDrawer />\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setIsMapVisible(true)}\n                className=\"flex items-center gap-2 shadow-sm hover:shadow-md transition-shadow\"\n              >\n                <MapIcon className=\"h-4 w-4\" />\n                <span className=\"hidden sm:inline\">View map</span>\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-[280px,1fr] gap-6\">\n          {/* Desktop Filters */}\n          <div className=\"hidden md:block space-y-6\">\n            <Card className=\"p-4 sticky top-[calc(3.5rem+1px)]\">\n              <h2 className=\"text-lg font-semibold mb-4\">Filters</h2>\n              <div className=\"space-y-6\">\n                <div className=\"space-y-4\">\n                  <div>\n                    <Label>Hotel Name</Label>\n                    <Input\n                      placeholder=\"Search hotels...\"\n                      value={nameFilter}\n                      onChange={handleNameFilterChange}\n                    />\n                  </div>\n\n                  <div>\n                    <Label>Minimum Rating</Label>\n                    <Slider\n                      value={[minRating]}\n                      onValueChange={handleRatingChange}\n                      min={0}\n                      max={5}\n                      step={0.5}\n                    />\n                    <span className=\"text-sm text-muted-foreground\">{minRating} stars</span>\n                  </div>\n\n                  <div>\n                    <Label>Maximum Price</Label>\n                    <Slider\n                      value={[maxPrice]}\n                      onValueChange={handlePriceChange}\n                      min={0}\n                      max={1000}\n                      step={50}\n                    />\n                    <span className=\"text-sm text-muted-foreground\">${maxPrice}</span>\n                  </div>\n\n                  <div>\n                    <Label>Minimum Bedrooms</Label>\n                    <div className=\"flex gap-2 mt-2\">\n                      {[1, 2, 3, 4, 5].map(count => (\n                        <Button\n                          key={count}\n                          variant={selectedBedrooms === count ? \"default\" : \"outline\"}\n                          size=\"sm\"\n                          onClick={() => setSelectedBedrooms(selectedBedrooms === count ? null : count)}\n                          className=\"flex-1\"\n                        >\n                          {count}+\n                        </Button>\n                      ))}\n                    </div>\n                    {selectedBedrooms && (\n                      <span className=\"text-sm text-muted-foreground mt-1 block\">\n                        {selectedBedrooms}+ bedrooms\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                {/* Property Type filter */}\n                <div>\n                  <Label className=\"text-base\">Property Type</Label>\n                  <ScrollArea className=\"h-[200px] pr-4\">\n                    <div className=\"space-y-2\">\n                      {PROPERTY_TYPES.map(type => (\n                        <div key={type.id} className=\"flex items-center space-x-2\">\n                          <Checkbox\n                            id={`type-${type.id}`}\n                            checked={selectedPropertyTypes.includes(type.id)}\n                            onCheckedChange={(checked) => handlePropertyTypeChange(checked as boolean, type.id)}\n                          />\n                          <label\n                            htmlFor={`type-${type.id}`}\n                            className=\"text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n                          >\n                            {type.label}\n                          </label>\n                        </div>\n                      ))}\n                    </div>\n                  </ScrollArea>\n                </div>\n\n                {/* Amenities filter */}\n                <div>\n                  <Label className=\"text-base\">Amenities</Label>\n                  <ScrollArea className=\"h-[200px] pr-4\">\n                    <div className=\"space-y-2\">\n                      {AMENITIES.map(amenity => (\n                        <div key={amenity.id} className=\"flex items-center space-x-2\">\n                          <Checkbox\n                            id={`amenity-${amenity.id}`}\n                            checked={selectedAmenities.includes(amenity.id)}\n                            onCheckedChange={(checked) => handleAmenityChange(checked as boolean, amenity.id)}\n                          />\n                          <label\n                            htmlFor={`amenity-${amenity.id}`}\n                            className=\"text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n                          >\n                            {amenity.label}\n                          </label>\n                        </div>\n                      ))}\n                    </div>\n                  </ScrollArea>\n                </div>\n              </div>\n            </Card>\n          </div>\n\n          {/* Property List */}\n          <div className=\"flex-1\">\n            {searchQuery.isError && (\n              <Card className=\"mb-4 p-4 border-destructive\">\n                <div className=\"flex items-center gap-2 text-destructive\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <div className=\"font-medium\">Error loading properties</div>\n                </div>\n                <p className=\"mt-1 text-sm text-muted-foreground\">\n                  {searchQuery.error instanceof Error \n                    ? searchQuery.error.message \n                    : 'An unexpected error occurred'\n                  }\n                </p>\n              </Card>\n            )}\n            <div className=\"flex justify-between items-center\">\n              {searchQuery.isLoading ? (\n                <h1 className=\"text-2xl font-bold\">\n                  Loading...\n                </h1>\n              ) : searchQuery.error ? (\n                <div className=\"text-destructive flex items-center gap-2\">\n                  <AlertCircle className=\"h-5 w-5\" />\n                  <span>Error loading properties</span>\n                </div>\n              ) : (\n                <h1 className=\"text-2xl font-bold\">\n                  {filteredProperties.length} {filteredProperties.length === 1 ? 'property' : 'properties'} found\n                </h1>\n              )}\n            </div>\n\n            {/* Quick Action Buttons */}\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mb-6\">\n              <Button\n                variant=\"outline\"\n                className=\"flex items-center gap-2 h-auto py-4 px-6\"\n                onClick={() => handleQuickAction('luxury')}\n              >\n                <Star className=\"w-5 h-5 text-yellow-500\" />\n                <div className=\"text-left\">\n                  <div className=\"font-semibold\">Luxury Stays</div>\n                  <div className=\"text-sm text-muted-foreground\">4+ stars with pool & spa</div>\n                </div>\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                className=\"flex items-center gap-2 h-auto py-4 px-6\"\n                onClick={() => handleQuickAction('family')}\n              >\n                <Hotel className=\"w-5 h-5 text-blue-500\" />\n                <div className=\"text-left\">\n                  <div className=\"font-semibold\">Family-Friendly</div>\n                  <div className=\"text-sm text-muted-foreground\">With breakfast & parking</div>\n                </div>\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                className=\"flex items-center gap-2 h-auto py-4 px-6\"\n                onClick={() => handleQuickAction('budget')}\n              >\n                <DollarSign className=\"w-5 h-5 text-green-500\" />\n                <div className=\"text-left\">\n                  <div className=\"font-semibold\">Budget-Friendly</div>\n                  <div className=\"text-sm text-muted-foreground\">Best value stays</div>\n                </div>\n              </Button>\n            </div>\n\n            {searchQuery.isLoading ? (\n              // Show loading cards while properties are loading\n              <div className=\"grid gap-6\">\n                {Array.from({ length: 3 }).map((_, index) => (\n                  <PropertyLoadingCard key={`loading-${index}`} />\n                ))}\n              </div>\n            ) : filteredProperties.length > 0 ? (\n              <div className=\"grid gap-6\">\n                {filteredProperties.map((property) => {\n                  // Find the availability query for this property by matching index\n                  const propertyIndex = allProperties.findIndex(p => p.id === property.id);\n                  const availabilityQuery = propertyIndex !== -1 ? availabilityQueries[propertyIndex] : null;\n                  \n                  // Check if the availability data is loading\n                  const isAvailabilityLoading = availabilityQuery?.isLoading || false;\n                  \n                  // Determine if there's an error with availability\n                  const availabilityError = availabilityQuery?.error\n                    ? (availabilityQuery.error as Error).message\n                    : undefined;\n                  \n                  return (\n                    <Card\n                      key={property.id}\n                      id={`property-${property.id}`}\n                      className={cn(\n                        \"cursor-pointer hover:shadow-md transition-shadow\",\n                        property.highlighted && \"ring-2 ring-primary\",\n                        selectedPropertyId === property.id && \"ring-2 ring-primary bg-primary/5\"\n                      )}\n                    >\n                      <PropertyCard\n                        property={property}\n                        rates={property.rates}\n                        isLoading={isAvailabilityLoading}\n                        error={availabilityError}\n                        highlighted={(property.rates?.[0]?.discountPercent ?? 0) > 0}\n                        onCompareToggle={() => handleCompareToggle(property)}\n                        isComparing={compareProperties.some(p => p.id === property.id)}\n                        checkIn={new Date(searchParams.checkIn)}\n                        checkOut={new Date(searchParams.checkOut)}\n                        guests={searchParams.guests.toString()}\n                        rooms={searchParams.rooms.toString()}\n                        onClick={() => {\n                          console.log('Results page onClick handler called for property:', property.id);\n                          // Record property view in session context\n                          recordPropertyView(property.id);\n                          // Navigate to property details\n                          const targetUrl = `/property/${property.id}?${new URLSearchParams({\n                            checkIn: searchParams.checkIn,\n                            checkOut: searchParams.checkOut,\n                            guests: searchParams.guests.toString(),\n                            rooms: searchParams.rooms.toString()\n                          }).toString()}`;\n                          console.log('Results page navigating to:', targetUrl);\n                          setLocation(targetUrl);\n                        }}\n                      />\n                    </Card>\n                  );\n                })}\n              </div>\n            ) : !searchQuery.isLoading && (\n              <Card className=\"p-6\">\n                <div className=\"text-center text-muted-foreground\">\n                  No properties found matching your criteria\n                </div>\n              </Card>\n            )}\n\n            {/* Load More Section */}\n            {searchQuery.hasNextPage && (\n              <div ref={loadMoreRef} className=\"py-4 text-center\">\n                {searchQuery.isFetchingNextPage ? (\n                  <Loader2 className=\"h-6 w-6 animate-spin mx-auto\" />\n                ) : (\n                  <Button\n                    variant=\"outline\"\n                    onClick={() => searchQuery.fetchNextPage()}\n                    disabled={!searchQuery.hasNextPage || searchQuery.isFetchingNextPage}\n                  >\n                    Load More\n                  </Button>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Search Form Modal */}\n      <dialog id=\"search-form-modal\" className=\"modal fixed inset-0 bg-background/95 p-4\">\n        <div className=\"bg-white rounded-lg shadow-2xl w-full max-w-3xl relative\" style={{ zIndex: 9999 }}>\n          <div className=\"p-6\">\n            <div className=\"flex justify-between items-center mb-4\">\n              <h2 className=\"text-2xl font-semibold\">Modify Search</h2>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={closeSearchModal}\n                className=\"absolute top-2 right-2\"\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                  <path d=\"M18 6L6 18M6 6l12 12\" />\n                </svg>\n              </Button>\n            </div>\n            <div className=\"relative\" style={{ zIndex: 10000 }}>\n              <SearchForm onSubmit={closeSearchModal} />\n            </div>\n          </div>\n        </div>\n      </dialog>\n\n      {/* Mobile Map View */}\n      <Dialog open={isMapVisible} onOpenChange={setIsMapVisible}>\n        <DialogContent className=\"sm:max-w-[100vw] h-[100vh] p-0\">\n          <VisuallyHidden>\n            <DialogTitle>Map View</DialogTitle>\n            <DialogDescription>Interactive map showing property locations and details</DialogDescription>\n          </VisuallyHidden>\n          <div className=\"relative h-full\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"absolute top-2 right-2 z-10\"\n              onClick={() => setIsMapVisible(false)}\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n            <div className=\"space-y-4\">\n              <MapComponent\n                center={mapCenter}\n                radius={searchRadius}\n                onMoveEnd={handleMapMoved}\n                properties={filteredProperties}\n              />\n\n              {/* Smart AI insights below the map */}\n              <InsightsPanel\n                locationName={searchParams.locationName}\n                lat={mapCenter?.lat ?? searchParams.lat}\n                lng={mapCenter?.lng ?? searchParams.lng}\n                dateRange={{ checkIn: searchParams.checkIn, checkOut: searchParams.checkOut }}\n              />\n            </div>\n          </div>\n        </DialogContent>\n      </Dialog>\n\n      {/* Compare Properties Drawer */}\n      <CompareDrawer\n        open={isCompareOpen}\n        onClose={() => setIsCompareOpen(false)}\n        properties={compareProperties}\n        onRemove={(id) => {\n          setCompareProperties(prev => prev.filter(p => p.id !== id));\n        }}\n      />\n\n      {/* Mobile AI Chat */}\n      <Button\n        variant=\"default\"\n        size=\"icon\"\n        className=\"fixed bottom-4 right-4 h-12 w-12 rounded-full shadow-lg md:hidden\"\n        onClick={() => setShowAiChat(true)}\n      >\n        <Bot className=\"h-6 w-6\" />\n      </Button>\n\n      <Sheet open={showAiChat} onOpenChange={setShowAiChat}>\n        <SheetContent side=\"bottom\" className=\"h-[80vh]\">\n          <UnifiedAIChat\n            context={{\n              location: new URLSearchParams(window.location.search).get('locationName') || undefined,\n              checkIn: new URLSearchParams(window.location.search).get('checkIn') || undefined,\n              checkOut: new URLSearchParams(window.location.search).get('checkOut') || undefined,\n              guests: new URLSearchParams(window.location.search).get('guests') || undefined,\n              bedrooms: new URLSearchParams(window.location.search).get('bedrooms') || undefined,\n              properties: properties || undefined\n            }}\n            variant=\"embedded\"\n            onClose={() => setShowAiChat(false)}\n            onPropertySelect={(property) => {\n              setSelectedPropertyId(property.id);\n              setShowAiChat(false);\n            }}\n          />\n        </SheetContent>\n      </Sheet>\n    </ResultsContainer>\n  );\n}", "modifiedCode": "import { useLocation } from \"wouter\";\nimport { useInfiniteQuery, useQueries, useQueryClient } from \"@tanstack/react-query\";\nimport { useInView } from \"framer-motion\";\nimport { useRef, useEffect, useState, useMemo, useCallback } from \"react\";\nimport PropertyCard, { PropertyCardProps } from \"../components/PropertyCard.jsx\";\nimport Map, { MapProps } from \"../components/Map.jsx\";\nimport { Card, CardContent } from \"../components/ui/card.jsx\";\nimport { Skeleton } from \"../components/ui/skeleton.jsx\";\nimport { AlertCircle, Loader2, Map as MapIcon, X, Bot, Star, Hotel, DollarSign, SlidersHorizontal } from \"lucide-react\";\nimport { PropertyLoadingCard } from \"../components/ui/loading-card\";\nimport { Input } from \"../components/ui/input.jsx\";\nimport { Slider } from \"../components/ui/slider.jsx\";\nimport { Label } from \"../components/ui/label.jsx\";\nimport SearchForm from \"../components/SearchForm.jsx\";\nimport { Property, PropertyType, PropertyWithRates, PropertyImage, Room } from \"../types/schema.js\";\nimport { Rate } from \"../types/rate.js\";\nimport { Button } from \"../components/ui/button.jsx\";\nimport { Checkbox } from \"../components/ui/checkbox.jsx\";\nimport { ScrollArea } from \"../components/ui/scroll-area.jsx\";\nimport styled from '@emotion/styled';\nimport { Dialog, DialogContent, DialogTitle, DialogDescription } from \"../components/ui/dialog.jsx\";\nimport { cn } from \"../lib/utils.js\";\nimport CompareDrawer from \"../components/CompareDrawer.jsx\";\nimport { VisuallyHidden } from \"@/components/ui/visually-hidden\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport UnifiedAIChat from \"@/components/UnifiedAIChat\";\nimport { Sheet, SheetContent, SheetTrigger } from \"@/components/ui/sheet\";\nimport { getSessionId } from \"@/lib/session\";\nimport InsightsPanel from \"@/components/InsightsPanel\";\n\ninterface SearchResponse {\n  properties: Property[];\n  total: number;\n  currentPage: number;\n  totalPages: number;\n  explanation?: string;\n  searchId: string;\n  timing: {\n    total: number;\n  };\n}\n\ninterface RoomRate {\n  rate: number;\n  totalAmount?: number;\n  originalAmount?: number;\n  currency: string;\n  Total?: {\n    '@Amount': string;\n    '@Discount'?: string;\n    '@IncludesBookingFee': string;\n  };\n  totalDiscount?: number;\n  retailDiscountPercent?: number;\n  totalComparableRetailDiscount?: number;\n}\n\ninterface RatePlan {\n  code: string;\n  description: string;\n  rooms: {\n    [key: string]: RoomRate;\n  };\n}\n\ninterface AvailabilityResponse {\n  ratePlans: {\n    [key: string]: RatePlan;\n  };\n}\n\ninterface SearchParams {\n  lat: number;\n  lng: number;\n  locationName: string;\n  checkIn: string;\n  checkOut: string;\n  guests: number;\n  rooms: number;\n  radius?: number;\n}\n\n// Custom hook for property search\nfunction usePropertySearch(searchParams: SearchParams) {\n  return useInfiniteQuery<SearchResponse>({\n    queryKey: ['propertySearch', searchParams],\n    queryFn: async ({ pageParam = 1 }) => {\n      // Validate required parameters\n      if (!searchParams.lat || !searchParams.lng || !searchParams.checkIn || !searchParams.checkOut) {\n        throw new Error('Missing required search parameters: lat, lng, checkIn, and checkOut are required');\n      }\n\n      const params = new URLSearchParams({\n        lat: String(searchParams.lat),\n        lng: String(searchParams.lng),\n        locationName: searchParams.locationName || '',\n        checkIn: searchParams.checkIn,\n        checkOut: searchParams.checkOut,\n        guests: searchParams.guests.toString(),\n        rooms: searchParams.rooms.toString(),\n        page: String(pageParam),\n        pageSize: '10',\n        radius: searchParams.radius?.toString() || '10'\n      });\n\n      console.log('Fetching properties with params:', Object.fromEntries(params.entries()));\n\n      try {\n        const response = await fetch(`/api/properties/search?${params}`);\n        const responseText = await response.text();\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch properties: ${response.status} ${response.statusText}\\n${responseText}`);\n        }\n\n        try {\n          return JSON.parse(responseText);\n        } catch (e) {\n          console.error('Failed to parse response:', e);\n          throw new Error('Invalid response format from server');\n        }\n      } catch (error) {\n        console.error('Search error:', error);\n        throw error;\n      }\n    },\n    getNextPageParam: (lastPage) =>\n      lastPage.currentPage < lastPage.totalPages ? lastPage.currentPage + 1 : undefined,\n    initialPageParam: 1,\n  });\n}\n\n// Add new interfaces for filter options\nconst PROPERTY_TYPES = [\n  { id: 'hotel', label: 'Hotel' },\n  { id: 'resort', label: 'Resort' },\n  { id: 'apartment', label: 'Apartment' },\n  { id: 'villa', label: 'Villa' },\n  { id: 'guesthouse', label: 'Guesthouse' }\n];\n\nconst AMENITIES = [\n  { id: 'pool', label: 'Swimming Pool' },\n  { id: 'wifi', label: 'Free WiFi' },\n  { id: 'parking', label: 'Parking' },\n  { id: 'restaurant', label: 'Restaurant' },\n  { id: 'fitness', label: 'Fitness Center' },\n  { id: 'spa', label: 'Spa' },\n  { id: 'beach', label: 'Beach Access' },\n  { id: 'breakfast', label: 'Free Breakfast' },\n  { id: 'bar', label: 'Bar/Lounge' },\n  { id: 'aircon', label: 'Air Conditioning' }\n];\n\n// Update the Map component interface to include onMapMoved\ninterface MapComponentProps extends MapProps {\n  onMapMoved?: (center: { lat: number; lng: number }, radius: number) => void;\n  onMoveEnd?: (center: { lat: number; lng: number }, radius: number) => void;\n  radius?: number;\n}\n\nconst ResultsContainer = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 24px;\n`;\n\nconst MapViewButton = styled.button`\n  position: fixed;\n  bottom: 24px;\n  right: 24px;\n  background: #1976d2;\n  color: white;\n  border: none;\n  border-radius: 24px;\n  padding: 12px 24px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  cursor: pointer;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.2);\n\n  &:hover {\n    background: #1565c0;\n  }\n`;\n\nconst FilterDrawer = () => {\n  const [nameFilter, setNameFilter] = useState(\"\");\n  const [minRating, setMinRating] = useState(0);\n  const [maxPrice, setMaxPrice] = useState(1000);\n  const [selectedPropertyTypes, setSelectedPropertyTypes] = useState<string[]>([]);\n  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);\n  const [selectedBedrooms, setSelectedBedrooms] = useState<number | null>(null);\n\n  const handleNameFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setNameFilter(e.target.value);\n  };\n\n  const handleRatingChange = ([value]: [number]) => {\n    setMinRating(value);\n  };\n\n  const handlePriceChange = ([value]: [number]) => {\n    setMaxPrice(value);\n  };\n\n  const handlePropertyTypeChange = (checked: boolean | \"indeterminate\", typeId: string) => {\n    setSelectedPropertyTypes(prev =>\n      checked === true\n        ? [...prev, typeId]\n        : prev.filter(t => t !== typeId)\n    );\n  };\n\n  const handleAmenityChange = (checked: boolean | \"indeterminate\", amenityId: string) => {\n    setSelectedAmenities(prev =>\n      checked === true\n        ? [...prev, amenityId]\n        : prev.filter(a => a !== amenityId)\n    );\n  };\n\n  return (\n    <Sheet>\n      <SheetTrigger asChild>\n        <Button variant=\"outline\" size=\"sm\" className=\"md:hidden\">\n          <SlidersHorizontal className=\"mr-2 h-4 w-4\" />\n          Filters\n        </Button>\n      </SheetTrigger>\n      <SheetContent side=\"bottom\" className=\"h-[80vh]\">\n        <div className=\"space-y-6 overflow-y-auto\">\n          <div className=\"space-y-4\">\n            <div>\n              <Label>Hotel Name</Label>\n              <Input\n                placeholder=\"Search hotels...\"\n                value={nameFilter}\n                onChange={handleNameFilterChange}\n              />\n            </div>\n\n            <div>\n              <Label>Minimum Rating</Label>\n              <Slider\n                value={[minRating]}\n                onValueChange={handleRatingChange}\n                min={0}\n                max={5}\n                step={0.5}\n              />\n              <span className=\"text-sm text-muted-foreground\">{minRating} stars</span>\n            </div>\n\n            <div>\n              <Label>Maximum Price</Label>\n              <Slider\n                value={[maxPrice]}\n                onValueChange={handlePriceChange}\n                min={0}\n                max={1000}\n                step={50}\n              />\n              <span className=\"text-sm text-muted-foreground\">${maxPrice}</span>\n            </div>\n\n            <div>\n              <Label>Minimum Bedrooms</Label>\n              <div className=\"flex gap-2 mt-2\">\n                {[1, 2, 3, 4, 5].map(count => (\n                  <Button\n                    key={count}\n                    variant={selectedBedrooms === count ? \"default\" : \"outline\"}\n                    size=\"sm\"\n                    onClick={() => setSelectedBedrooms(selectedBedrooms === count ? null : count)}\n                    className=\"flex-1\"\n                  >\n                    {count}+\n                  </Button>\n                ))}\n              </div>\n              {selectedBedrooms && (\n                <span className=\"text-sm text-muted-foreground mt-1 block\">\n                  {selectedBedrooms}+ bedrooms\n                </span>\n              )}\n            </div>\n          </div>\n\n          <div>\n            <Label className=\"text-base\">Property Type</Label>\n            <ScrollArea className=\"h-[200px]\">\n              <div className=\"space-y-2\">\n                {PROPERTY_TYPES.map(type => (\n                  <div key={type.id} className=\"flex items-center space-x-2\">\n                    <Checkbox\n                      id={`type-${type.id}`}\n                      checked={selectedPropertyTypes.includes(type.id)}\n                      onCheckedChange={(checked) => handlePropertyTypeChange(checked as boolean, type.id)}\n                    />\n                    <label\n                      htmlFor={`type-${type.id}`}\n                      className=\"text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n                    >\n                      {type.label}\n                    </label>\n                  </div>\n                ))}\n              </div>\n            </ScrollArea>\n          </div>\n\n          <div>\n            <Label className=\"text-base\">Amenities</Label>\n            <ScrollArea className=\"h-[200px]\">\n              <div className=\"space-y-2\">\n                {AMENITIES.map(amenity => (\n                  <div key={amenity.id} className=\"flex items-center space-x-2\">\n                    <Checkbox\n                      id={`amenity-${amenity.id}`}\n                      checked={selectedAmenities.includes(amenity.id)}\n                      onCheckedChange={(checked) => handleAmenityChange(checked as boolean, amenity.id)}\n                    />\n                    <label\n                      htmlFor={`amenity-${amenity.id}`}\n                      className=\"text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n                    >\n                      {amenity.label}\n                    </label>\n                  </div>\n                ))}\n              </div>\n            </ScrollArea>\n          </div>\n        </div>\n      </SheetContent>\n    </Sheet>\n  );\n};\n\n// Cast Map so TS allows extra optional props like radius\nconst MapComponent = Map as unknown as React.FC<MapComponentProps>;\n\nexport default function Results() {\n  const [location, setLocation] = useLocation();\n  const { toast } = useToast();\n\n  // Parse search params from URL\n  const searchParams = useMemo(() => {\n    try {\n      const params = new URLSearchParams(window.location.search);\n\n      const lat = params.get('lat');\n      const lng = params.get('lng');\n      const checkIn = params.get('checkIn');\n      const checkOut = params.get('checkOut');\n      const locationName = params.get('locationName');\n      const recommended = params.get('recommended');\n\n      // Parse numeric values\n      const parsedLat = lat ? parseFloat(lat) : NaN;\n      const parsedLng = lng ? parseFloat(lng) : NaN;\n\n      // Validate required location parameters\n      if (!lat || isNaN(parsedLat)) {\n        return null;\n      }\n      if (!lng || isNaN(parsedLng)) {\n        return null;\n      }\n      if (!locationName) {\n        return null;\n      }\n\n      // Provide default dates if missing or empty\n      const today = new Date();\n      const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);\n      const dayAfterTomorrow = new Date(today.getTime() + 2 * 24 * 60 * 60 * 1000);\n      \n      const defaultCheckIn = tomorrow.toISOString().split('T')[0];\n      const defaultCheckOut = dayAfterTomorrow.toISOString().split('T')[0];\n      \n      const finalCheckIn = (checkIn && checkIn.trim()) ? checkIn : defaultCheckIn;\n      const finalCheckOut = (checkOut && checkOut.trim()) ? checkOut : defaultCheckOut;\n\n      // All validations passed, create the params object\n      const validatedParams: SearchParams = {\n        lat: parsedLat,\n        lng: parsedLng,\n        locationName: locationName,\n        checkIn: finalCheckIn,\n        checkOut: finalCheckOut,\n        guests: Math.max(1, parseInt(params.get('guests') || '2', 10)),\n        rooms: Math.max(1, parseInt(params.get('rooms') || '1', 10)),\n        radius: Math.max(1, parseInt(params.get('radius') || '10', 10))\n      };\n\n      return validatedParams;\n    } catch (error) {\n      return null;\n    }\n  }, []);\n\n  // Don't proceed with search if parameters are invalid\n  if (!searchParams) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <div className=\"container mx-auto px-4 py-8\">\n          <Card className=\"p-6\">\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center gap-2 text-destructive\">\n                <AlertCircle className=\"h-5 w-4\" />\n                <h2 className=\"text-lg font-semibold\">Invalid Search Parameters</h2>\n              </div>\n              <p>Please check your search parameters and try again.</p>\n              <Button onClick={() => window.location.href = '/'} variant=\"outline\">\n                Return to Search\n              </Button>\n            </div>\n          </Card>\n        </div>\n      </div>\n    );\n  }\n\n  // Create a stable query object for usePropertySearch\n  const queryParams = useMemo(() => ({\n    lat: Number(searchParams.lat),\n    lng: Number(searchParams.lng),\n    locationName: searchParams.locationName,\n    checkIn: searchParams.checkIn,\n    checkOut: searchParams.checkOut,\n    guests: Number(searchParams.guests),\n    rooms: Number(searchParams.rooms),\n    radius: Number(searchParams.radius)\n  }), [searchParams]);\n\n  // Initialize search query after validating parameters\n  const searchQuery = usePropertySearch(queryParams);\n\n  const queryClient = useQueryClient();\n\n  // Initialize all state hooks at the top level\n  const [properties, setProperties] = useState<Property[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [mapCenter, setMapCenter] = useState<{ lat: number; lng: number; } | undefined>(undefined);\n  const [searchRadius, setSearchRadius] = useState<number>(0);\n  const [isMapFullscreen, setIsMapFullscreen] = useState(false);\n  const [pendingMapCenter, setPendingMapCenter] = useState<{ lat: number; lng: number; } | undefined>(undefined);\n  const [pendingRadius, setPendingRadius] = useState<number>(0);\n  const [nameFilter, setNameFilter] = useState(\"\");\n  const [minRating, setMinRating] = useState(0);\n  const [maxPrice, setMaxPrice] = useState(1000);\n  const [selectedPropertyTypes, setSelectedPropertyTypes] = useState<string[]>([]);\n  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);\n  const [selectedBedrooms, setSelectedBedrooms] = useState<number | null>(null);\n  const [mapViewOpen, setMapViewOpen] = useState(false);\n  const [selectedPropertyId, setSelectedPropertyId] = useState<number | null>(null);\n  const [isMapVisible, setIsMapVisible] = useState(false);\n  const [compareProperties, setCompareProperties] = useState<PropertyWithRates[]>([]);\n  const [isCompareOpen, setIsCompareOpen] = useState(false);\n  const [showAiChat, setShowAiChat] = useState(false);\n  const [mapFilters, setMapFilters] = useState<{\n    priceRange: [number, number];\n    propertyTypes: PropertyType[];\n  }>({\n    priceRange: [0, 1000],\n    propertyTypes: []\n  });\n\n  // Initialize refs at the top level\n  const loadMoreRef = useRef<HTMLDivElement>(null);\n  const isInView = useInView(loadMoreRef);\n\n  // Unified session ID shared across app\n  const sessionId = useMemo(() => getSessionId(), []);\n\n  // Record property view to server context\n  const recordPropertyView = useCallback(async (propertyId: number) => {\n    try {\n      await fetch('/api/context/property-view', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ sessionId, propertyId })\n      });\n    } catch (error) {\n      console.error('Failed to record property view:', error);\n    }\n  }, [sessionId]);\n\n  // Record comparison to server context\n  const recordComparison = useCallback(async (propertyId: number, isAdding: boolean) => {\n    try {\n      await fetch('/api/context/comparison', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ sessionId, propertyId, action: isAdding ? 'add' : 'remove' })\n      });\n    } catch (error) {\n      console.error('Failed to record comparison:', error);\n    }\n  }, [sessionId]);\n\n  // Add handler for comparison\n  const handleCompareToggle = (property: PropertyWithRates) => {\n    setCompareProperties(prev => {\n      const exists = prev.some(p => p.id === property.id);\n      const isAdding = !exists;\n      \n      // Record comparison action to server context\n      recordComparison(property.id, isAdding);\n      \n      if (exists) {\n        return prev.filter(p => p.id !== property.id);\n      }\n      if (prev.length >= 3) {\n        return prev;\n      }\n      return [...prev, property];\n    });\n  };\n\n  // Get all properties from the search results\n  const allProperties = searchQuery.data?.pages.flatMap(page => page.properties) || [];\n\n  // Calculate number of nights\n  const checkInDate = searchParams.checkIn ? new Date(searchParams.checkIn) : undefined;\n  const checkOutDate = searchParams.checkOut ? new Date(searchParams.checkOut) : undefined;\n  const nights = useMemo(() => {\n    const checkInParam = searchParams.checkIn;\n    const checkOutParam = searchParams.checkOut;\n    if (!checkInParam || !checkOutParam) return 0;\n\n    const checkIn = new Date(checkInParam);\n    const checkOut = new Date(checkOutParam);\n    return Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));\n  }, [searchParams.checkIn, searchParams.checkOut]);\n\n  // Update availability queries to use searchQueryParams\n  const availabilityQueries = useQueries({\n    queries: allProperties.map(property => ({\n      queryKey: ['availability', property.id, searchParams],\n      queryFn: async (): Promise<AvailabilityResponse> => {\n        const response = await fetch(\n          `/api/properties/${property.id}/availability?` +\n          `checkIn=${searchParams.checkIn}&` +\n          `checkOut=${searchParams.checkOut}&` +\n          `guests=${searchParams.guests}&` +\n          `rooms=${searchParams.rooms}`\n        );\n\n        if (!response.ok) {\n          throw new Error('Failed to fetch availability');\n        }\n\n        return response.json();\n      },\n      staleTime: 1000 * 60 * 5, // Consider data fresh for 5 minutes\n      retry: 1,\n      enabled: !!property.id\n    }))\n  });\n\n  // Create details queries\n  const detailsQueries = useQueries({\n    queries: allProperties.map(property => ({\n      queryKey: ['propertyDetails', property.id],\n      queryFn: async () => {\n        const response = await fetch(`/api/properties/${property.id}/content`);\n        if (!response.ok) {\n          throw new Error('Failed to fetch property details');\n        }\n        return response.json();\n      },\n      staleTime: 1000 * 60 * 30, // Consider data fresh for 30 minutes\n      retry: 1,\n      enabled: !!property.id\n    }))\n  });\n\n  // Combine properties with their rates and details\n  const propertiesWithRates: PropertyWithRates[] = useMemo(() => {\n    return allProperties.map((property, index) => {\n      const availabilityQuery = availabilityQueries[index];\n      const detailsQuery = detailsQueries[index];\n\n      // Process rates from first rate plan only\n      let rateInfo = undefined;\n      if (availabilityQuery.data?.ratePlans) {\n        const firstPlan = Object.values(availabilityQuery.data.ratePlans)[0];\n\n        if (firstPlan) {\n          const firstRoom = Object.values(firstPlan.rooms)[0] as Room;\n\n          if (firstRoom) {\n            // Extract values from the Total object if it exists\n            // Parse the base rate first\n            const baseRate = typeof firstRoom.rate === 'number'\n              ? firstRoom.rate\n              : parseFloat(String(firstRoom.rate || '0'));\n\n            // Get the Total object with all required fields\n            const total = {\n              '@Amount': String(firstRoom.Total?.['@Amount'] ?? firstRoom.totalAmount ?? baseRate ?? '0'),\n              '@Discount': String(firstRoom.Total?.['@Discount'] ?? firstRoom.totalDiscount ?? '0'),\n              '@RetailDiscountPercent': String(firstRoom.Total?.['@RetailDiscountPercent'] ?? firstRoom.retailDiscountPercent ?? '0'),\n              '@Currency': String(firstRoom.Total?.['@Currency'] ?? firstRoom.currency ?? 'USD'),\n              '@IncludesBookingFee': String(firstRoom.Total?.['@IncludesBookingFee'] ?? 'false'),\n              '@ComparableRetailDiscount': String(firstRoom.Total?.['@ComparableRetailDiscount'] ?? firstRoom.totalComparableRetailDiscount ?? '0')\n            };\n\n            // Parse all numeric values\n            const totalAmount = parseFloat(total['@Amount']);\n            const discountAmount = parseFloat(total['@Discount']);\n            const originalAmount = totalAmount + discountAmount;\n            const retailDiscountPercent = Math.ceil(parseFloat(total['@RetailDiscountPercent']) || ((discountAmount / originalAmount) * 100));\n\n            rateInfo = {\n              code: firstPlan.code || '',\n              description: firstPlan.description || '',\n              rate: baseRate,\n              totalAmount: totalAmount || baseRate, // Fallback to base rate if total amount is 0\n              originalAmount: originalAmount || baseRate, // Fallback to base rate if original amount is 0\n              discountAmount,\n              discountPercent: retailDiscountPercent,\n              currency: firstRoom.currency || total['@Currency'],\n              roomTypeCode: firstRoom.roomTypeCode || '',\n              bedTypeCode: firstRoom.bedTypeCode || '',\n              restrictedRate: firstRoom.restrictedRate || false,\n              refundable: firstRoom.refundable || true,\n              maxOccupancy: firstRoom.maxOccupancy || 2,\n              availableQuantity: firstRoom.availableQuantity || 1,\n              rateCode: firstRoom.rateCode || firstPlan.code || '',\n              rateDescription: firstRoom.rateDescription || firstPlan.description || '',\n              cancellationPolicy: firstRoom.cancellationPolicy || '',\n              guaranteePolicy: firstRoom.guaranteePolicy || '',\n              depositPolicy: firstRoom.depositPolicy || '',\n              includedServices: firstRoom.includedServices || [],\n              promotions: firstRoom.promotions || [],\n              taxes: firstRoom.taxes || [],\n              fees: firstRoom.fees || []\n            } satisfies Rate;\n          }\n        }\n      } else {\n        console.warn('No rate plans available for property:', {\n          propertyId: property.id,\n          availabilityQuery: availabilityQuery.data\n        });\n      }\n\n      return {\n        ...property,\n        rates: rateInfo ? [rateInfo] : undefined,\n        isLoadingRates: availabilityQuery.isLoading,\n        rateError: availabilityQuery.error ? 'Failed to load rates' : undefined,\n        propertyDetails: {\n          images: detailsQuery.data?.images,\n          isLoading: detailsQuery.isLoading,\n          error: detailsQuery.isError\n        }\n      };\n    });\n  }, [allProperties, availabilityQueries, detailsQueries, nights]);\n\n  // Sort properties by discount percentage\n  const sortedProperties = useMemo(() => {\n    return [...propertiesWithRates].sort((a, b) => {\n      // If rates are still loading, keep original order\n      if (a.isLoadingRates || b.isLoadingRates) return 0;\n\n      // If there's an error loading rates, move to the end\n      if (a.rateError && !b.rateError) return 1;\n      if (!a.rateError && b.rateError) return -1;\n\n      // Get discount percentages, defaulting to 0 if not available\n      const aDiscount = a.rates?.[0]?.discountPercent ?? 0;\n      const bDiscount = b.rates?.[0]?.discountPercent ?? 0;\n\n      // Sort by highest discount first\n      if (bDiscount !== aDiscount) {\n        return bDiscount - aDiscount;\n      }\n\n      // If discounts are equal, sort by total amount\n      const aTotal = a.rates?.[0]?.totalAmount ?? Number.MAX_VALUE;\n      const bTotal = b.rates?.[0]?.totalAmount ?? Number.MAX_VALUE;\n      return aTotal - bTotal;\n    });\n  }, [propertiesWithRates]);\n\n  // Update filtered properties to include new filters\n  const filteredProperties = sortedProperties.filter(property => {\n    const matchesName = property.name.toLowerCase().includes(nameFilter.toLowerCase());\n    const matchesRating = property.rating ? parseFloat(String(property.rating)) >= minRating : minRating === 0;\n    const matchesPrice = property.rates ?\n      property.rates[0].totalAmount <= maxPrice * nights :\n      (typeof property.basePrice === 'number' ? property.basePrice / 100 : 0) <= maxPrice;\n\n    const matchesPropertyType = selectedPropertyTypes.length === 0 ||\n      selectedPropertyTypes.includes(property.type?.toLowerCase() || '');\n\n    const matchesAmenities = selectedAmenities.length === 0 ||\n      selectedAmenities.every(amenity =>\n        property.amenities?.map(a => a.toLowerCase()).includes(amenity.toLowerCase())\n      );\n\n    // Multi-bedroom filter - analyze property description for bedroom count\n    const matchesBedrooms = selectedBedrooms === null || (() => {\n      const description = (property.description || '').toLowerCase();\n      const name = (property.name || '').toLowerCase();\n\n      // Extract bedroom count from description and name\n      const bedroomMatches = [\n        ...description.matchAll(/(\\d+)[\\s-]*bedroom/g),\n        ...name.matchAll(/(\\d+)[\\s-]*bedroom/g),\n        ...description.matchAll(/(\\d+)[\\s-]*bed[\\s,]/g),\n        ...name.matchAll(/(\\d+)[\\s-]*bed[\\s,]/g)\n      ];\n\n      if (bedroomMatches.length > 0) {\n        const maxBedrooms = Math.max(...bedroomMatches.map((match: any) => parseInt(match[1])));\n        return maxBedrooms >= selectedBedrooms;\n      }\n\n      // If no bedroom info found, assume 1 bedroom for hotels, 2+ for vacation rentals\n      const propertyType = (property.type || '').toLowerCase();\n      const assumedBedrooms = propertyType.includes('apartment') || propertyType.includes('villa') || propertyType.includes('house') ? 2 : 1;\n      return assumedBedrooms >= selectedBedrooms;\n    })();\n\n    return matchesName && matchesRating && matchesPrice && matchesPropertyType && matchesAmenities && matchesBedrooms;\n  });\n\n  // Update infinite scroll logic\n  useEffect(() => {\n    if (isInView && searchQuery.hasNextPage && !searchQuery.isFetchingNextPage) {\n      searchQuery.fetchNextPage();\n    }\n  }, [isInView, searchQuery.hasNextPage, searchQuery.isFetchingNextPage, searchQuery]);\n\n  // Get the total properties count from the first page\n  const totalProperties = searchQuery.data?.pages[0]?.total || 0;\n\n  // Event handlers with proper types\n  const handleNameFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setNameFilter(e.target.value);\n  };\n\n  const handleRatingChange = ([value]: [number]) => {\n    setMinRating(value);\n  };\n\n  const handlePriceChange = ([value]: [number]) => {\n    setMaxPrice(value);\n  };\n\n  // Quick action buttons\n  const handleQuickAction = (action: 'luxury' | 'family' | 'budget') => {\n    switch (action) {\n      case 'luxury':\n        setSelectedPropertyTypes(['hotel', 'resort']);\n        setSelectedAmenities(['pool', 'spa']);\n        handleRatingChange([4]);\n        handlePriceChange([1000]);\n        break;\n      case 'family':\n        setSelectedPropertyTypes(['hotel', 'resort', 'apartment']);\n        setSelectedAmenities(['wifi', 'parking', 'breakfast']);\n        setSelectedBedrooms(2); // Family-friendly with 2+ bedrooms\n        handleRatingChange([3.5]);\n        handlePriceChange([300]);\n        break;\n      case 'budget':\n        setSelectedPropertyTypes(['hotel', 'guesthouse', 'apartment']);\n        setSelectedAmenities(['wifi']);\n        handleRatingChange([3]);\n        handlePriceChange([150]);\n        break;\n    }\n  };\n\n  // Update handleMapMoved to just store the pending values\n  const handleMapMoved = (center: { lat: number; lng: number }, radius: number) => {\n    setPendingMapCenter(center);\n    setPendingRadius(radius);\n\n    // Notify backend so AI context is updated\n    fetch('/api/context/map-move', {\n      method: 'POST',\n      headers: { 'Content-Type': 'application/json' },\n      body: JSON.stringify({ sessionId, center, radius })\n    }).catch(() => {/* non-blocking */});\n\n    // Force React Query to refetch with new parameters by invalidating key\n    queryClient.invalidateQueries({ queryKey: ['propertySearch'] });\n  };\n\n  // Update handleSearchThisArea to use the pending values\n  const handleSearchThisArea = async () => {\n    if (!pendingMapCenter) return;\n\n    // Update the search parameters with the new location\n    const newSearchParams = new URLSearchParams(window.location.search);\n    newSearchParams.set('lat', pendingMapCenter.lat.toString());\n    newSearchParams.set('lng', pendingMapCenter.lng.toString());\n    // Convert radius from meters to kilometers and round to 2 decimal places\n    const radiusInKm = (pendingRadius / 1000).toFixed(2);\n    newSearchParams.set('radius', radiusInKm);\n\n    // Update the URL without triggering a page reload\n    window.history.replaceState({}, '', `?${newSearchParams.toString()}`);\n\n    // Update the actual map center and radius state\n    setMapCenter(pendingMapCenter);\n    setSearchRadius(pendingRadius);\n\n    // Create new search params object\n    const newParams: SearchParams = {\n      ...searchParams,\n      lat: pendingMapCenter.lat,\n      lng: pendingMapCenter.lng,\n      radius: parseFloat(radiusInKm)\n    };\n\n    // Refetch with new parameters\n    await searchQuery.refetch();\n  };\n\n  // Update the checkbox event handlers with proper types\n  const handlePropertyTypeChange = (checked: boolean | \"indeterminate\", typeId: string) => {\n    setSelectedPropertyTypes(prev =>\n      checked === true\n        ? [...prev, typeId]\n        : prev.filter(t => t !== typeId)\n    );\n  };\n\n  const handleAmenityChange = (checked: boolean | \"indeterminate\", amenityId: string) => {\n    setSelectedAmenities(prev =>\n      checked === true\n        ? [...prev, amenityId]\n        : prev.filter(a => a !== amenityId)\n    );\n  };\n\n  // Update the dialog handling\n  const openSearchModal = () => {\n    const dialog = document.getElementById('search-form-modal') as HTMLDialogElement;\n    if (dialog) dialog.showModal();\n  };\n\n  const closeSearchModal = () => {\n    const dialog = document.getElementById('search-form-modal') as HTMLDialogElement;\n    if (dialog) dialog.close();\n  };\n\n  const handlePropertySelect = (propertyId: number) => {\n    const property = filteredProperties.find(p => p.id === propertyId);\n    if (property) {\n      // Scroll the sidebar to the selected property\n      const propertyElement = document.getElementById(`property-${propertyId}`);\n      if (propertyElement) {\n        propertyElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\n      }\n\n      // Highlight the property in the list\n      setSelectedPropertyId(propertyId);\n    }\n  };\n\n  // Add filter handler\n  const handleMapFiltersChange = (filters: {\n    priceRange: [number, number];\n    propertyTypes: PropertyType[];\n  }) => {\n    setMapFilters(filters);\n\n    // Push filters to backend context for AI awareness\n    fetch('/api/context/filters', {\n      method: 'POST',\n      headers: { 'Content-Type': 'application/json' },\n      body: JSON.stringify({ sessionId, filters })\n    }).catch(() => {});\n  };\n\n  return (\n    <ResultsContainer>\n      {/* Minimal Search Bar */}\n      <div className=\"sticky top-0 z-40 bg-background/95 backdrop-blur-sm border-b shadow-sm\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"h-14 flex items-center justify-between gap-4\">\n            <div className=\"flex items-center gap-3 text-sm overflow-x-auto no-scrollbar\">\n              <span className=\"font-medium truncate max-w-[150px] md:max-w-[200px]\">\n                {searchParams.locationName}\n              </span>\n              <span className=\"text-muted-foreground/60 hidden sm:inline\">•</span>\n              <span className=\"text-muted-foreground whitespace-nowrap\">\n                {checkInDate?.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - {checkOutDate?.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}\n              </span>\n              <span className=\"text-muted-foreground/60 hidden sm:inline\">•</span>\n              <span className=\"text-muted-foreground hidden sm:inline\">{searchParams.guests} guests, {searchParams.rooms} {searchParams.rooms === 1 ? 'room' : 'rooms'}</span>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <FilterDrawer />\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setIsMapVisible(true)}\n                className=\"flex items-center gap-2 shadow-sm hover:shadow-md transition-shadow\"\n              >\n                <MapIcon className=\"h-4 w-4\" />\n                <span className=\"hidden sm:inline\">View map</span>\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-[280px,1fr] gap-6\">\n          {/* Desktop Filters */}\n          <div className=\"hidden md:block space-y-6\">\n            <Card className=\"p-4 sticky top-[calc(3.5rem+1px)]\">\n              <h2 className=\"text-lg font-semibold mb-4\">Filters</h2>\n              <div className=\"space-y-6\">\n                <div className=\"space-y-4\">\n                  <div>\n                    <Label>Hotel Name</Label>\n                    <Input\n                      placeholder=\"Search hotels...\"\n                      value={nameFilter}\n                      onChange={handleNameFilterChange}\n                    />\n                  </div>\n\n                  <div>\n                    <Label>Minimum Rating</Label>\n                    <Slider\n                      value={[minRating]}\n                      onValueChange={handleRatingChange}\n                      min={0}\n                      max={5}\n                      step={0.5}\n                    />\n                    <span className=\"text-sm text-muted-foreground\">{minRating} stars</span>\n                  </div>\n\n                  <div>\n                    <Label>Maximum Price</Label>\n                    <Slider\n                      value={[maxPrice]}\n                      onValueChange={handlePriceChange}\n                      min={0}\n                      max={1000}\n                      step={50}\n                    />\n                    <span className=\"text-sm text-muted-foreground\">${maxPrice}</span>\n                  </div>\n\n                  <div>\n                    <Label>Minimum Bedrooms</Label>\n                    <div className=\"flex gap-2 mt-2\">\n                      {[1, 2, 3, 4, 5].map(count => (\n                        <Button\n                          key={count}\n                          variant={selectedBedrooms === count ? \"default\" : \"outline\"}\n                          size=\"sm\"\n                          onClick={() => setSelectedBedrooms(selectedBedrooms === count ? null : count)}\n                          className=\"flex-1\"\n                        >\n                          {count}+\n                        </Button>\n                      ))}\n                    </div>\n                    {selectedBedrooms && (\n                      <span className=\"text-sm text-muted-foreground mt-1 block\">\n                        {selectedBedrooms}+ bedrooms\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                {/* Property Type filter */}\n                <div>\n                  <Label className=\"text-base\">Property Type</Label>\n                  <ScrollArea className=\"h-[200px] pr-4\">\n                    <div className=\"space-y-2\">\n                      {PROPERTY_TYPES.map(type => (\n                        <div key={type.id} className=\"flex items-center space-x-2\">\n                          <Checkbox\n                            id={`type-${type.id}`}\n                            checked={selectedPropertyTypes.includes(type.id)}\n                            onCheckedChange={(checked) => handlePropertyTypeChange(checked as boolean, type.id)}\n                          />\n                          <label\n                            htmlFor={`type-${type.id}`}\n                            className=\"text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n                          >\n                            {type.label}\n                          </label>\n                        </div>\n                      ))}\n                    </div>\n                  </ScrollArea>\n                </div>\n\n                {/* Amenities filter */}\n                <div>\n                  <Label className=\"text-base\">Amenities</Label>\n                  <ScrollArea className=\"h-[200px] pr-4\">\n                    <div className=\"space-y-2\">\n                      {AMENITIES.map(amenity => (\n                        <div key={amenity.id} className=\"flex items-center space-x-2\">\n                          <Checkbox\n                            id={`amenity-${amenity.id}`}\n                            checked={selectedAmenities.includes(amenity.id)}\n                            onCheckedChange={(checked) => handleAmenityChange(checked as boolean, amenity.id)}\n                          />\n                          <label\n                            htmlFor={`amenity-${amenity.id}`}\n                            className=\"text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n                          >\n                            {amenity.label}\n                          </label>\n                        </div>\n                      ))}\n                    </div>\n                  </ScrollArea>\n                </div>\n              </div>\n            </Card>\n          </div>\n\n          {/* Property List */}\n          <div className=\"flex-1\">\n            {searchQuery.isError && (\n              <Card className=\"mb-4 p-4 border-destructive\">\n                <div className=\"flex items-center gap-2 text-destructive\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <div className=\"font-medium\">Error loading properties</div>\n                </div>\n                <p className=\"mt-1 text-sm text-muted-foreground\">\n                  {searchQuery.error instanceof Error \n                    ? searchQuery.error.message \n                    : 'An unexpected error occurred'\n                  }\n                </p>\n              </Card>\n            )}\n            <div className=\"flex justify-between items-center\">\n              {searchQuery.isLoading ? (\n                <h1 className=\"text-2xl font-bold\">\n                  Loading...\n                </h1>\n              ) : searchQuery.error ? (\n                <div className=\"text-destructive flex items-center gap-2\">\n                  <AlertCircle className=\"h-5 w-5\" />\n                  <span>Error loading properties</span>\n                </div>\n              ) : (\n                <h1 className=\"text-2xl font-bold\">\n                  {filteredProperties.length} {filteredProperties.length === 1 ? 'property' : 'properties'} found\n                </h1>\n              )}\n            </div>\n\n            {/* Quick Action Buttons */}\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mb-6\">\n              <Button\n                variant=\"outline\"\n                className=\"flex items-center gap-2 h-auto py-4 px-6\"\n                onClick={() => handleQuickAction('luxury')}\n              >\n                <Star className=\"w-5 h-5 text-yellow-500\" />\n                <div className=\"text-left\">\n                  <div className=\"font-semibold\">Luxury Stays</div>\n                  <div className=\"text-sm text-muted-foreground\">4+ stars with pool & spa</div>\n                </div>\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                className=\"flex items-center gap-2 h-auto py-4 px-6\"\n                onClick={() => handleQuickAction('family')}\n              >\n                <Hotel className=\"w-5 h-5 text-blue-500\" />\n                <div className=\"text-left\">\n                  <div className=\"font-semibold\">Family-Friendly</div>\n                  <div className=\"text-sm text-muted-foreground\">With breakfast & parking</div>\n                </div>\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                className=\"flex items-center gap-2 h-auto py-4 px-6\"\n                onClick={() => handleQuickAction('budget')}\n              >\n                <DollarSign className=\"w-5 h-5 text-green-500\" />\n                <div className=\"text-left\">\n                  <div className=\"font-semibold\">Budget-Friendly</div>\n                  <div className=\"text-sm text-muted-foreground\">Best value stays</div>\n                </div>\n              </Button>\n            </div>\n\n            {searchQuery.isLoading ? (\n              // Show loading cards while properties are loading\n              <div className=\"grid gap-6\">\n                {Array.from({ length: 3 }).map((_, index) => (\n                  <PropertyLoadingCard key={`loading-${index}`} />\n                ))}\n              </div>\n            ) : filteredProperties.length > 0 ? (\n              <div className=\"grid gap-6\">\n                {filteredProperties.map((property) => {\n                  // Find the availability query for this property by matching index\n                  const propertyIndex = allProperties.findIndex(p => p.id === property.id);\n                  const availabilityQuery = propertyIndex !== -1 ? availabilityQueries[propertyIndex] : null;\n                  \n                  // Check if the availability data is loading\n                  const isAvailabilityLoading = availabilityQuery?.isLoading || false;\n                  \n                  // Determine if there's an error with availability\n                  const availabilityError = availabilityQuery?.error\n                    ? (availabilityQuery.error as Error).message\n                    : undefined;\n                  \n                  return (\n                    <Card\n                      key={property.id}\n                      id={`property-${property.id}`}\n                      className={cn(\n                        \"cursor-pointer hover:shadow-md transition-shadow\",\n                        property.highlighted && \"ring-2 ring-primary\",\n                        selectedPropertyId === property.id && \"ring-2 ring-primary bg-primary/5\"\n                      )}\n                    >\n                      <PropertyCard\n                        property={property}\n                        rates={property.rates}\n                        isLoading={isAvailabilityLoading}\n                        error={availabilityError}\n                        highlighted={(property.rates?.[0]?.discountPercent ?? 0) > 0}\n                        onCompareToggle={() => handleCompareToggle(property)}\n                        isComparing={compareProperties.some(p => p.id === property.id)}\n                        checkIn={new Date(searchParams.checkIn)}\n                        checkOut={new Date(searchParams.checkOut)}\n                        guests={searchParams.guests.toString()}\n                        rooms={searchParams.rooms.toString()}\n                        onClick={() => {\n                          console.log('Results page onClick handler called for property:', property.id);\n                          // Record property view in session context\n                          recordPropertyView(property.id);\n                          // Navigate to property details\n                          const targetUrl = `/property/${property.id}?${new URLSearchParams({\n                            checkIn: searchParams.checkIn,\n                            checkOut: searchParams.checkOut,\n                            guests: searchParams.guests.toString(),\n                            rooms: searchParams.rooms.toString()\n                          }).toString()}`;\n                          console.log('Results page navigating to:', targetUrl);\n                          setLocation(targetUrl);\n                        }}\n                      />\n                    </Card>\n                  );\n                })}\n              </div>\n            ) : !searchQuery.isLoading && (\n              <Card className=\"p-6\">\n                <div className=\"text-center text-muted-foreground\">\n                  No properties found matching your criteria\n                </div>\n              </Card>\n            )}\n\n            {/* Load More Section */}\n            {searchQuery.hasNextPage && (\n              <div ref={loadMoreRef} className=\"py-4 text-center\">\n                {searchQuery.isFetchingNextPage ? (\n                  <Loader2 className=\"h-6 w-6 animate-spin mx-auto\" />\n                ) : (\n                  <Button\n                    variant=\"outline\"\n                    onClick={() => searchQuery.fetchNextPage()}\n                    disabled={!searchQuery.hasNextPage || searchQuery.isFetchingNextPage}\n                  >\n                    Load More\n                  </Button>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Search Form Modal */}\n      <dialog id=\"search-form-modal\" className=\"modal fixed inset-0 bg-background/95 p-4\">\n        <div className=\"bg-white rounded-lg shadow-2xl w-full max-w-3xl relative\" style={{ zIndex: 9999 }}>\n          <div className=\"p-6\">\n            <div className=\"flex justify-between items-center mb-4\">\n              <h2 className=\"text-2xl font-semibold\">Modify Search</h2>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={closeSearchModal}\n                className=\"absolute top-2 right-2\"\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                  <path d=\"M18 6L6 18M6 6l12 12\" />\n                </svg>\n              </Button>\n            </div>\n            <div className=\"relative\" style={{ zIndex: 10000 }}>\n              <SearchForm onSubmit={closeSearchModal} />\n            </div>\n          </div>\n        </div>\n      </dialog>\n\n      {/* Mobile Map View */}\n      <Dialog open={isMapVisible} onOpenChange={setIsMapVisible}>\n        <DialogContent className=\"sm:max-w-[100vw] h-[100vh] p-0\">\n          <VisuallyHidden>\n            <DialogTitle>Map View</DialogTitle>\n            <DialogDescription>Interactive map showing property locations and details</DialogDescription>\n          </VisuallyHidden>\n          <div className=\"relative h-full\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"absolute top-2 right-2 z-10\"\n              onClick={() => setIsMapVisible(false)}\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n            <div className=\"space-y-4\">\n              <MapComponent\n                center={mapCenter}\n                radius={searchRadius}\n                onMoveEnd={handleMapMoved}\n                properties={filteredProperties}\n              />\n\n              {/* Smart AI insights below the map */}\n              <InsightsPanel\n                locationName={searchParams.locationName}\n                lat={mapCenter?.lat ?? searchParams.lat}\n                lng={mapCenter?.lng ?? searchParams.lng}\n                dateRange={{ checkIn: searchParams.checkIn, checkOut: searchParams.checkOut }}\n              />\n            </div>\n          </div>\n        </DialogContent>\n      </Dialog>\n\n      {/* Compare Properties Drawer */}\n      <CompareDrawer\n        open={isCompareOpen}\n        onClose={() => setIsCompareOpen(false)}\n        properties={compareProperties}\n        onRemove={(id) => {\n          setCompareProperties(prev => prev.filter(p => p.id !== id));\n        }}\n      />\n\n      {/* Mobile AI Chat */}\n      <Button\n        variant=\"default\"\n        size=\"icon\"\n        className=\"fixed bottom-4 right-4 h-12 w-12 rounded-full shadow-lg md:hidden\"\n        onClick={() => setShowAiChat(true)}\n      >\n        <Bot className=\"h-6 w-6\" />\n      </Button>\n\n      <Sheet open={showAiChat} onOpenChange={setShowAiChat}>\n        <SheetContent side=\"bottom\" className=\"h-[80vh]\">\n          <UnifiedAIChat\n            context={{\n              location: new URLSearchParams(window.location.search).get('locationName') || undefined,\n              checkIn: new URLSearchParams(window.location.search).get('checkIn') || undefined,\n              checkOut: new URLSearchParams(window.location.search).get('checkOut') || undefined,\n              guests: new URLSearchParams(window.location.search).get('guests') || undefined,\n              bedrooms: new URLSearchParams(window.location.search).get('bedrooms') || undefined,\n              properties: properties || undefined\n            }}\n            variant=\"embedded\"\n            onClose={() => setShowAiChat(false)}\n            onPropertySelect={(property) => {\n              setSelectedPropertyId(property.id);\n              setShowAiChat(false);\n            }}\n          />\n        </SheetContent>\n      </Sheet>\n    </ResultsContainer>\n  );\n}"}