{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/paymentService.ts"}, "modifiedCode": "import Stripe from 'stripe';\nimport { db } from '../../db/index.js';\nimport { reservations, users } from '../../db/schema.js';\nimport { eq } from 'drizzle-orm';\nimport logger from '../utils/logger.js';\n\nconst stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {\n  apiVersion: '2024-06-20',\n});\n\nexport interface PaymentIntentData {\n  amount: number;\n  currency: string;\n  reservationId: number;\n  userId: number;\n  propertyId: number;\n  metadata?: Record<string, string>;\n}\n\nexport interface BookingConfirmation {\n  reservationId: number;\n  confirmationCode: string;\n  paymentIntentId: string;\n  status: 'confirmed' | 'pending' | 'failed';\n  totalAmount: number;\n  currency: string;\n}\n\n/**\n * Create a payment intent for a reservation\n */\nexport async function createPaymentIntent(data: PaymentIntentData): Promise<Stripe.PaymentIntent> {\n  try {\n    // Get user and reservation details\n    const [user, reservation] = await Promise.all([\n      db.query.users.findFirst({ where: eq(users.id, data.userId) }),\n      db.query.reservations.findFirst({ where: eq(reservations.id, data.reservationId) })\n    ]);\n\n    if (!user || !reservation) {\n      throw new Error('User or reservation not found');\n    }\n\n    const paymentIntent = await stripe.paymentIntents.create({\n      amount: Math.round(data.amount * 100), // Convert to cents\n      currency: data.currency.toLowerCase(),\n      customer: user.email, // You might want to create Stripe customers\n      metadata: {\n        reservationId: data.reservationId.toString(),\n        userId: data.userId.toString(),\n        propertyId: data.propertyId.toString(),\n        ...data.metadata\n      },\n      description: `Hotel reservation for ${user.email}`,\n    });\n\n    // Update reservation with payment intent ID\n    await db\n      .update(reservations)\n      .set({ \n        paymentIntentId: paymentIntent.id,\n        updatedAt: new Date()\n      })\n      .where(eq(reservations.id, data.reservationId));\n\n    logger.info('Payment intent created', {\n      paymentIntentId: paymentIntent.id,\n      reservationId: data.reservationId,\n      amount: data.amount\n    });\n\n    return paymentIntent;\n  } catch (error) {\n    logger.error('Failed to create payment intent', {\n      error: error instanceof Error ? error.message : 'Unknown error',\n      reservationId: data.reservationId\n    });\n    throw error;\n  }\n}\n\n/**\n * Confirm a booking after successful payment\n */\nexport async function confirmBooking(paymentIntentId: string): Promise<BookingConfirmation> {\n  try {\n    // Retrieve payment intent from Stripe\n    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);\n    \n    if (paymentIntent.status !== 'succeeded') {\n      throw new Error(`Payment not successful. Status: ${paymentIntent.status}`);\n    }\n\n    const reservationId = parseInt(paymentIntent.metadata.reservationId);\n    \n    // Generate confirmation code\n    const confirmationCode = generateConfirmationCode();\n    \n    // Update reservation status\n    await db\n      .update(reservations)\n      .set({\n        status: 'confirmed',\n        confirmationCode,\n        updatedAt: new Date()\n      })\n      .where(eq(reservations.id, reservationId));\n\n    logger.info('Booking confirmed', {\n      reservationId,\n      confirmationCode,\n      paymentIntentId\n    });\n\n    return {\n      reservationId,\n      confirmationCode,\n      paymentIntentId,\n      status: 'confirmed',\n      totalAmount: paymentIntent.amount / 100,\n      currency: paymentIntent.currency.toUpperCase()\n    };\n  } catch (error) {\n    logger.error('Failed to confirm booking', {\n      error: error instanceof Error ? error.message : 'Unknown error',\n      paymentIntentId\n    });\n    throw error;\n  }\n}\n\n/**\n * Handle webhook events from Stripe\n */\nexport async function handleStripeWebhook(event: Stripe.Event): Promise<void> {\n  try {\n    switch (event.type) {\n      case 'payment_intent.succeeded':\n        const paymentIntent = event.data.object as Stripe.PaymentIntent;\n        await confirmBooking(paymentIntent.id);\n        break;\n      \n      case 'payment_intent.payment_failed':\n        const failedPayment = event.data.object as Stripe.PaymentIntent;\n        await handleFailedPayment(failedPayment.id);\n        break;\n      \n      default:\n        logger.info('Unhandled webhook event', { type: event.type });\n    }\n  } catch (error) {\n    logger.error('Webhook handling failed', {\n      error: error instanceof Error ? error.message : 'Unknown error',\n      eventType: event.type\n    });\n    throw error;\n  }\n}\n\n/**\n * Handle failed payment\n */\nasync function handleFailedPayment(paymentIntentId: string): Promise<void> {\n  try {\n    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);\n    const reservationId = parseInt(paymentIntent.metadata.reservationId);\n    \n    await db\n      .update(reservations)\n      .set({\n        status: 'cancelled',\n        updatedAt: new Date()\n      })\n      .where(eq(reservations.id, reservationId));\n\n    logger.info('Reservation cancelled due to failed payment', {\n      reservationId,\n      paymentIntentId\n    });\n  } catch (error) {\n    logger.error('Failed to handle payment failure', {\n      error: error instanceof Error ? error.message : 'Unknown error',\n      paymentIntentId\n    });\n  }\n}\n\n/**\n * Generate a unique confirmation code\n */\nfunction generateConfirmationCode(): string {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n  let result = '';\n  for (let i = 0; i < 8; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n}\n\n/**\n * Get booking details by confirmation code\n */\nexport async function getBookingByConfirmation(confirmationCode: string) {\n  try {\n    const booking = await db.query.reservations.findFirst({\n      where: eq(reservations.confirmationCode, confirmationCode),\n      with: {\n        property: true,\n        user: {\n          columns: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true\n          }\n        }\n      }\n    });\n\n    return booking;\n  } catch (error) {\n    logger.error('Failed to get booking by confirmation', {\n      error: error instanceof Error ? error.message : 'Unknown error',\n      confirmationCode\n    });\n    throw error;\n  }\n}\n"}