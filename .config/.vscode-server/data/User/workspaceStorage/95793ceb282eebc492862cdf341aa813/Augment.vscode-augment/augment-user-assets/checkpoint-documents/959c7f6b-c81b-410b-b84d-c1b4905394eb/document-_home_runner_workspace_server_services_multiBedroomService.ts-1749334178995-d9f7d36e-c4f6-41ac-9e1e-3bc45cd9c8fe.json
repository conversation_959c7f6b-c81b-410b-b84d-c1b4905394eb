{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/multiBedroomService.ts"}, "modifiedCode": "import { searchTravSrvProperties, getPropertyAvailability } from './travsrv.js';\nimport { geocodeLocation } from './geocodingService.js';\nimport { db } from '../../db/index.js';\nimport { properties } from '../../db/schema.js';\nimport { eq, and, gte, lte, sql } from 'drizzle-orm';\nimport logger from '../utils/logger.js';\n\nexport interface MultiBedroomSearchParams {\n  location: string;\n  checkIn: string;\n  checkOut: string;\n  adults: number;\n  children?: number;\n  bedrooms: number;\n  minBedrooms?: number;\n  maxBedrooms?: number;\n  propertyTypes?: string[];\n  amenities?: string[];\n  priceRange?: {\n    min?: number;\n    max?: number;\n  };\n  groupType?: 'family' | 'friends' | 'corporate' | 'wedding' | 'reunion' | 'multi_generational';\n  specialRequests?: string[];\n}\n\nexport interface MultiBedroomProperty {\n  id: number;\n  name: string;\n  description: string;\n  location: {\n    address: string;\n    city: string;\n    state: string;\n    country: string;\n    coordinates: { lat: number; lng: number };\n  };\n  accommodations: {\n    bedrooms: number;\n    bathrooms: number;\n    maxOccupancy: number;\n    bedConfiguration: string[];\n    livingSpaces: string[];\n  };\n  amenities: {\n    kitchen: boolean;\n    laundry: boolean;\n    parking: boolean;\n    wifi: boolean;\n    pool: boolean;\n    hotTub: boolean;\n    bbq: boolean;\n    gameRoom: boolean;\n    familyFriendly: string[];\n    accessibility: string[];\n  };\n  pricing: {\n    basePrice: number;\n    currency: string;\n    totalPrice: number;\n    pricePerNight: number;\n    fees: Array<{\n      type: string;\n      amount: number;\n      description: string;\n    }>;\n  };\n  images: string[];\n  rating: number;\n  reviewCount: number;\n  propertyType: string;\n  highlights: string[];\n  nearbyAttractions: Array<{\n    name: string;\n    distance: string;\n    type: string;\n  }>;\n  suitabilityScore: number;\n  recommendations: {\n    bestFor: string[];\n    considerations: string[];\n    localTips: string[];\n  };\n}\n\n/**\n * Search for multi-bedroom accommodations with intelligent filtering\n */\nexport async function searchMultiBedroomAccommodations(\n  params: MultiBedroomSearchParams\n): Promise<MultiBedroomProperty[]> {\n  try {\n    logger.info('Starting multi-bedroom accommodation search', {\n      location: params.location,\n      bedrooms: params.bedrooms,\n      groupType: params.groupType,\n      checkIn: params.checkIn,\n      checkOut: params.checkOut\n    });\n\n    // Geocode the location\n    const locationData = await geocodeLocation(params.location);\n    if (!locationData) {\n      throw new Error(`Unable to find location: ${params.location}`);\n    }\n\n    // Search for properties using the travel service\n    const baseProperties = await searchTravSrvProperties({\n      latitude: locationData.lat,\n      longitude: locationData.lng,\n      inDate: params.checkIn,\n      outDate: params.checkOut,\n      adults: params.adults,\n      children: params.children || 0,\n      rooms: Math.max(params.bedrooms, 1),\n      radius: 25 // Wider search for vacation rentals\n    });\n\n    // Enhance properties with multi-bedroom specific data\n    const enhancedProperties = await Promise.all(\n      baseProperties.map(property => enhancePropertyForMultiBedroom(property, params))\n    );\n\n    // Filter and score properties based on multi-bedroom criteria\n    const filteredProperties = enhancedProperties\n      .filter(property => property !== null)\n      .filter(property => meetsBedrooomRequirements(property!, params))\n      .map(property => calculateSuitabilityScore(property!, params))\n      .sort((a, b) => b.suitabilityScore - a.suitabilityScore);\n\n    logger.info('Multi-bedroom search completed', {\n      totalFound: baseProperties.length,\n      afterFiltering: filteredProperties.length,\n      location: params.location\n    });\n\n    return filteredProperties;\n\n  } catch (error) {\n    logger.error('Multi-bedroom search failed', {\n      error: error instanceof Error ? error.message : 'Unknown error',\n      params\n    });\n    throw error;\n  }\n}\n\n/**\n * Enhance a basic property with multi-bedroom specific information\n */\nasync function enhancePropertyForMultiBedroom(\n  property: any,\n  params: MultiBedroomSearchParams\n): Promise<MultiBedroomProperty | null> {\n  try {\n    // Get detailed availability and room information\n    const availability = await getPropertyAvailability(\n      property.externalId || property.id.toString(),\n      {\n        inDate: params.checkIn,\n        outDate: params.checkOut,\n        adults: params.adults,\n        children: params.children || 0,\n        rooms: params.bedrooms\n      }\n    );\n\n    // Analyze property type and amenities to determine bedroom count\n    const bedroomInfo = analyzeBedroomConfiguration(property, availability);\n    \n    if (!bedroomInfo || bedroomInfo.bedrooms < params.minBedrooms || 0) {\n      return null;\n    }\n\n    // Generate intelligent recommendations based on group type\n    const recommendations = generateGroupRecommendations(property, params.groupType);\n\n    // Find nearby attractions\n    const nearbyAttractions = await findNearbyAttractions(\n      property.latitude,\n      property.longitude,\n      params.groupType\n    );\n\n    const enhancedProperty: MultiBedroomProperty = {\n      id: property.id,\n      name: property.name,\n      description: property.description,\n      location: {\n        address: property.address,\n        city: property.city,\n        state: property.state,\n        country: property.country,\n        coordinates: {\n          lat: property.latitude,\n          lng: property.longitude\n        }\n      },\n      accommodations: {\n        bedrooms: bedroomInfo.bedrooms,\n        bathrooms: bedroomInfo.bathrooms,\n        maxOccupancy: bedroomInfo.maxOccupancy,\n        bedConfiguration: bedroomInfo.bedConfiguration,\n        livingSpaces: bedroomInfo.livingSpaces\n      },\n      amenities: analyzeAmenities(property),\n      pricing: calculatePricing(property, availability, params),\n      images: Array.isArray(property.images) ? property.images : [],\n      rating: property.rating || 0,\n      reviewCount: property.reviewCount || 0,\n      propertyType: property.propertyType || 'vacation_rental',\n      highlights: generateHighlights(property, bedroomInfo, params),\n      nearbyAttractions,\n      suitabilityScore: 0, // Will be calculated later\n      recommendations\n    };\n\n    return enhancedProperty;\n\n  } catch (error) {\n    logger.error('Failed to enhance property for multi-bedroom', {\n      propertyId: property.id,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    });\n    return null;\n  }\n}\n\n/**\n * Analyze property to determine bedroom configuration\n */\nfunction analyzeBedroomConfiguration(property: any, availability: any) {\n  // Extract bedroom information from property description and amenities\n  const description = (property.description || '').toLowerCase();\n  const amenities = property.amenities || [];\n  \n  // Look for bedroom indicators in description\n  const bedroomMatches = description.match(/(\\d+)\\s*bedroom/g);\n  const bathroomMatches = description.match(/(\\d+)\\s*bathroom/g);\n  \n  let bedrooms = 1;\n  let bathrooms = 1;\n  \n  if (bedroomMatches) {\n    bedrooms = Math.max(...bedroomMatches.map(match => \n      parseInt(match.match(/(\\d+)/)?.[1] || '1')\n    ));\n  }\n  \n  if (bathroomMatches) {\n    bathrooms = Math.max(...bathroomMatches.map(match => \n      parseInt(match.match(/(\\d+)/)?.[1] || '1')\n    ));\n  }\n\n  // Analyze room types from availability data\n  const bedConfiguration: string[] = [];\n  const livingSpaces: string[] = [];\n  \n  if (availability?.ratePlans) {\n    Object.values(availability.ratePlans).forEach((ratePlan: any) => {\n      Object.values(ratePlan.rooms || {}).forEach((room: any) => {\n        if (room.description) {\n          const roomDesc = room.description.toLowerCase();\n          if (roomDesc.includes('king')) bedConfiguration.push('King Bed');\n          if (roomDesc.includes('queen')) bedConfiguration.push('Queen Bed');\n          if (roomDesc.includes('twin')) bedConfiguration.push('Twin Beds');\n          if (roomDesc.includes('sofa')) livingSpaces.push('Sofa Bed');\n          if (roomDesc.includes('living')) livingSpaces.push('Living Room');\n          if (roomDesc.includes('kitchen')) livingSpaces.push('Full Kitchen');\n        }\n      });\n    });\n  }\n\n  // Default configurations based on property type\n  if (bedConfiguration.length === 0) {\n    for (let i = 0; i < bedrooms; i++) {\n      bedConfiguration.push(i === 0 ? 'King Bed' : 'Queen Bed');\n    }\n  }\n\n  if (livingSpaces.length === 0) {\n    livingSpaces.push('Living Room');\n    if (amenities.some((a: string) => a.toLowerCase().includes('kitchen'))) {\n      livingSpaces.push('Full Kitchen');\n    }\n  }\n\n  return {\n    bedrooms,\n    bathrooms,\n    maxOccupancy: bedrooms * 2 + livingSpaces.filter(s => s.includes('Sofa')).length * 2,\n    bedConfiguration,\n    livingSpaces\n  };\n}\n\n/**\n * Analyze property amenities for multi-bedroom suitability\n */\nfunction analyzeAmenities(property: any) {\n  const amenities = property.amenities || [];\n  const description = (property.description || '').toLowerCase();\n  \n  return {\n    kitchen: amenities.some((a: string) => \n      a.toLowerCase().includes('kitchen') || a.toLowerCase().includes('kitchenette')\n    ) || description.includes('kitchen'),\n    laundry: amenities.some((a: string) => \n      a.toLowerCase().includes('laundry') || a.toLowerCase().includes('washer')\n    ) || description.includes('laundry'),\n    parking: amenities.some((a: string) => \n      a.toLowerCase().includes('parking') || a.toLowerCase().includes('garage')\n    ) || description.includes('parking'),\n    wifi: amenities.some((a: string) => \n      a.toLowerCase().includes('wifi') || a.toLowerCase().includes('internet')\n    ) || description.includes('wifi'),\n    pool: amenities.some((a: string) => \n      a.toLowerCase().includes('pool')\n    ) || description.includes('pool'),\n    hotTub: amenities.some((a: string) => \n      a.toLowerCase().includes('hot tub') || a.toLowerCase().includes('jacuzzi')\n    ) || description.includes('hot tub'),\n    bbq: amenities.some((a: string) => \n      a.toLowerCase().includes('bbq') || a.toLowerCase().includes('grill')\n    ) || description.includes('grill'),\n    gameRoom: amenities.some((a: string) => \n      a.toLowerCase().includes('game') || a.toLowerCase().includes('recreation')\n    ) || description.includes('game room'),\n    familyFriendly: amenities.filter((a: string) => \n      a.toLowerCase().includes('family') || \n      a.toLowerCase().includes('child') || \n      a.toLowerCase().includes('kid')\n    ),\n    accessibility: amenities.filter((a: string) => \n      a.toLowerCase().includes('accessible') || \n      a.toLowerCase().includes('wheelchair') || \n      a.toLowerCase().includes('mobility')\n    )\n  };\n}\n\n/**\n * Calculate pricing for multi-bedroom stay\n */\nfunction calculatePricing(property: any, availability: any, params: MultiBedroomSearchParams) {\n  const basePrice = property.basePrice || 0;\n  const nights = Math.ceil(\n    (new Date(params.checkOut).getTime() - new Date(params.checkIn).getTime()) / (1000 * 60 * 60 * 24)\n  );\n  \n  let totalPrice = basePrice * nights;\n  const fees: Array<{ type: string; amount: number; description: string }> = [];\n  \n  // Add typical vacation rental fees\n  if (property.propertyType === 'vacation_rental' || property.propertyType === 'apartment') {\n    const cleaningFee = Math.round(basePrice * 0.15);\n    fees.push({\n      type: 'cleaning',\n      amount: cleaningFee,\n      description: 'One-time cleaning fee'\n    });\n    totalPrice += cleaningFee;\n    \n    const serviceFee = Math.round(totalPrice * 0.12);\n    fees.push({\n      type: 'service',\n      amount: serviceFee,\n      description: 'Service fee'\n    });\n    totalPrice += serviceFee;\n  }\n  \n  return {\n    basePrice,\n    currency: property.currency || 'USD',\n    totalPrice,\n    pricePerNight: basePrice,\n    fees\n  };\n}\n\n/**\n * Check if property meets bedroom requirements\n */\nfunction meetsBedrooomRequirements(property: MultiBedroomProperty, params: MultiBedroomSearchParams): boolean {\n  const bedrooms = property.accommodations.bedrooms;\n  \n  if (bedrooms < params.bedrooms) return false;\n  if (params.minBedrooms && bedrooms < params.minBedrooms) return false;\n  if (params.maxBedrooms && bedrooms > params.maxBedrooms) return false;\n  \n  // Check occupancy\n  const totalGuests = params.adults + (params.children || 0);\n  if (property.accommodations.maxOccupancy < totalGuests) return false;\n  \n  return true;\n}\n\n/**\n * Calculate suitability score based on group type and requirements\n */\nfunction calculateSuitabilityScore(property: MultiBedroomProperty, params: MultiBedroomSearchParams): MultiBedroomProperty {\n  let score = 50; // Base score\n  \n  // Bedroom match bonus\n  if (property.accommodations.bedrooms === params.bedrooms) {\n    score += 20;\n  } else if (property.accommodations.bedrooms > params.bedrooms) {\n    score += 10;\n  }\n  \n  // Group type specific scoring\n  switch (params.groupType) {\n    case 'family':\n      if (property.amenities.familyFriendly.length > 0) score += 15;\n      if (property.amenities.kitchen) score += 10;\n      if (property.amenities.laundry) score += 10;\n      if (property.amenities.pool) score += 10;\n      break;\n      \n    case 'friends':\n      if (property.amenities.gameRoom) score += 15;\n      if (property.amenities.pool) score += 10;\n      if (property.amenities.bbq) score += 10;\n      if (property.amenities.hotTub) score += 10;\n      break;\n      \n    case 'corporate':\n      if (property.amenities.wifi) score += 15;\n      if (property.amenities.parking) score += 10;\n      if (property.location.city.toLowerCase().includes('downtown')) score += 10;\n      break;\n      \n    case 'multi_generational':\n      if (property.accommodations.bedrooms >= 3) score += 15;\n      if (property.amenities.accessibility.length > 0) score += 15;\n      if (property.amenities.kitchen) score += 10;\n      if (property.amenities.laundry) score += 10;\n      break;\n  }\n  \n  // Rating bonus\n  score += (property.rating || 0) * 5;\n  \n  property.suitabilityScore = Math.min(100, Math.max(0, score));\n  return property;\n}\n\n/**\n * Generate group-specific recommendations\n */\nfunction generateGroupRecommendations(property: any, groupType?: string) {\n  const bestFor: string[] = [];\n  const considerations: string[] = [];\n  const localTips: string[] = [];\n  \n  switch (groupType) {\n    case 'family':\n      bestFor.push('Family vacations with children');\n      bestFor.push('Multi-generational trips');\n      considerations.push('Check for child-safe amenities');\n      considerations.push('Verify kitchen facilities for meal prep');\n      localTips.push('Look for nearby family attractions');\n      break;\n      \n    case 'friends':\n      bestFor.push('Group getaways and reunions');\n      bestFor.push('Celebration trips');\n      considerations.push('Ensure adequate common spaces');\n      considerations.push('Check noise policies');\n      localTips.push('Explore local nightlife and activities');\n      break;\n      \n    case 'corporate':\n      bestFor.push('Team retreats and meetings');\n      bestFor.push('Extended business stays');\n      considerations.push('Verify reliable WiFi and workspace');\n      considerations.push('Check proximity to business districts');\n      localTips.push('Consider transportation to meeting venues');\n      break;\n  }\n  \n  return { bestFor, considerations, localTips };\n}\n\n/**\n * Find nearby attractions based on group type\n */\nasync function findNearbyAttractions(lat: number, lng: number, groupType?: string) {\n  // This would integrate with the geocoding service to find nearby places\n  // For now, return mock data based on group type\n  const attractions = [];\n  \n  switch (groupType) {\n    case 'family':\n      attractions.push(\n        { name: 'Family Theme Park', distance: '2.5 miles', type: 'entertainment' },\n        { name: 'Children\\'s Museum', distance: '1.8 miles', type: 'educational' },\n        { name: 'Beach Access', distance: '0.5 miles', type: 'recreation' }\n      );\n      break;\n      \n    case 'friends':\n      attractions.push(\n        { name: 'Downtown Entertainment District', distance: '3.2 miles', type: 'nightlife' },\n        { name: 'Adventure Sports Center', distance: '4.1 miles', type: 'recreation' },\n        { name: 'Local Brewery Tour', distance: '2.8 miles', type: 'dining' }\n      );\n      break;\n      \n    default:\n      attractions.push(\n        { name: 'City Center', distance: '2.0 miles', type: 'shopping' },\n        { name: 'Local Restaurant District', distance: '1.5 miles', type: 'dining' },\n        { name: 'Cultural Attractions', distance: '3.0 miles', type: 'cultural' }\n      );\n  }\n  \n  return attractions;\n}\n\n/**\n * Generate property highlights for multi-bedroom stays\n */\nfunction generateHighlights(property: any, bedroomInfo: any, params: MultiBedroomSearchParams): string[] {\n  const highlights: string[] = [];\n  \n  highlights.push(`${bedroomInfo.bedrooms} bedrooms, ${bedroomInfo.bathrooms} bathrooms`);\n  highlights.push(`Sleeps up to ${bedroomInfo.maxOccupancy} guests`);\n  \n  if (bedroomInfo.livingSpaces.includes('Full Kitchen')) {\n    highlights.push('Full kitchen for group meals');\n  }\n  \n  if (property.amenities?.some((a: string) => a.toLowerCase().includes('pool'))) {\n    highlights.push('Private or shared pool access');\n  }\n  \n  if (property.amenities?.some((a: string) => a.toLowerCase().includes('parking'))) {\n    highlights.push('Parking available');\n  }\n  \n  if (property.rating && property.rating >= 4.5) {\n    highlights.push('Highly rated by guests');\n  }\n  \n  return highlights;\n}\n"}