{"/home/<USER>/workspace/.config/.vscode-server/data/logs/20250607T214721/exthost2/Augment.vscode-augment/Augment.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250607T214721/exthost2/Augment.vscode-augment/Augment.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-global-state/fuzzyFsFilesIndex.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-global-state/fuzzyFsFilesIndex.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/vscode.lock": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/vscode.lock"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/agent-edit-shard-storage-manifest.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/agent-edit-shard-storage-manifest.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/task-storage/manifest/manifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/task-storage/manifest/manifest"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/task-storage/tasks/e4555c40-1a0b-4630-b594-daf497c34799": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/task-storage/tasks/e4555c40-1a0b-4630-b594-daf497c34799"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/task-storage/tasks/6ec9c24f-dced-4a74-95a5-5835b1fa6490": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/task-storage/tasks/6ec9c24f-dced-4a74-95a5-5835b1fa6490"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/task-storage/tasks/e5fb3da8-167a-4fb6-8e8e-0840ecac57c6": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/task-storage/tasks/e5fb3da8-167a-4fb6-8e8e-0840ecac57c6"}, "/home/<USER>/workspace/postcss.config.js": {"rootPath": "/home/<USER>/workspace", "relPath": "postcss.config.js"}, "/home/<USER>/workspace/replit.nix": {"rootPath": "/home/<USER>/workspace", "relPath": "replit.nix"}, "/home/<USER>/workspace/theme.json": {"rootPath": "/home/<USER>/workspace", "relPath": "theme.json"}, "/home/<USER>/workspace/client/src/hooks/use-mobile.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/use-mobile.tsx"}, "/home/<USER>/workspace/client/src/hooks/use-toast.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/use-toast.ts"}, "/home/<USER>/workspace/client/src/components/ui/accordion.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/accordion.tsx"}, "/home/<USER>/workspace/client/src/components/ui/alert-dialog.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/alert-dialog.tsx"}, "/home/<USER>/workspace/client/src/components/ui/alert.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/alert.tsx"}, "/home/<USER>/workspace/client/src/components/ui/aspect-ratio.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/aspect-ratio.tsx"}, "/home/<USER>/workspace/client/src/components/ui/breadcrumb.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/breadcrumb.tsx"}, "/home/<USER>/workspace/client/src/components/ui/button.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/button.tsx"}, "/home/<USER>/workspace/client/src/components/ui/calendar.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/calendar.tsx"}, "/home/<USER>/workspace/client/src/components/ui/card.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/card.tsx"}, "/home/<USER>/workspace/client/src/components/ui/carousel.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/carousel.tsx"}, "/home/<USER>/workspace/client/src/components/ui/chart.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/chart.tsx"}, "/home/<USER>/workspace/client/src/components/ui/checkbox.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/checkbox.tsx"}, "/home/<USER>/workspace/client/src/components/ui/collapsible.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/collapsible.tsx"}, "/home/<USER>/workspace/client/src/components/ui/context-menu.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/context-menu.tsx"}, "/home/<USER>/workspace/client/src/components/ui/drawer.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/drawer.tsx"}, "/home/<USER>/workspace/client/src/components/ui/dropdown-menu.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/dropdown-menu.tsx"}, "/home/<USER>/workspace/client/src/components/ui/form.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/form.tsx"}, "/home/<USER>/workspace/client/src/components/ui/hover-card.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/hover-card.tsx"}, "/home/<USER>/workspace/client/src/components/ui/input-otp.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/input-otp.tsx"}, "/home/<USER>/workspace/client/src/components/ui/input.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/input.tsx"}, "/home/<USER>/workspace/client/src/components/ui/label.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/label.tsx"}, "/home/<USER>/workspace/client/src/components/ui/menubar.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/menubar.tsx"}, "/home/<USER>/workspace/client/src/components/ui/navigation-menu.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/navigation-menu.tsx"}, "/home/<USER>/workspace/client/src/components/ui/pagination.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/pagination.tsx"}, "/home/<USER>/workspace/client/src/components/ui/popover.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/popover.tsx"}, "/home/<USER>/workspace/client/src/components/ui/progress.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/progress.tsx"}, "/home/<USER>/workspace/client/src/components/ui/radio-group.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/radio-group.tsx"}, "/home/<USER>/workspace/client/src/components/ui/resizable.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/resizable.tsx"}, "/home/<USER>/workspace/client/src/components/ui/scroll-area.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/scroll-area.tsx"}, "/home/<USER>/workspace/client/src/components/ui/select.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/select.tsx"}, "/home/<USER>/workspace/client/src/components/ui/separator.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/separator.tsx"}, "/home/<USER>/workspace/client/src/components/ui/sheet.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/sheet.tsx"}, "/home/<USER>/workspace/client/src/components/ui/sidebar.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/sidebar.tsx"}, "/home/<USER>/workspace/client/src/components/ui/skeleton.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/skeleton.tsx"}, "/home/<USER>/workspace/client/src/components/ui/slider.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/slider.tsx"}, "/home/<USER>/workspace/client/src/components/ui/switch.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/switch.tsx"}, "/home/<USER>/workspace/client/src/components/ui/table.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/table.tsx"}, "/home/<USER>/workspace/client/src/components/ui/tabs.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/tabs.tsx"}, "/home/<USER>/workspace/client/src/components/ui/toast.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/toast.tsx"}, "/home/<USER>/workspace/client/src/components/ui/toaster.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/toaster.tsx"}, "/home/<USER>/workspace/client/src/components/ui/toggle-group.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/toggle-group.tsx"}, "/home/<USER>/workspace/client/src/components/ui/toggle.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/toggle.tsx"}, "/home/<USER>/workspace/client/src/components/ui/tooltip.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/tooltip.tsx"}, "/home/<USER>/workspace/.local/state/replit/agent/.latest.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/.latest.json"}, "/home/<USER>/workspace/.local/state/replit/agent/rapid_build_success": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/rapid_build_success"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/.vsixmanifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/.vsixmanifest"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/CHANGELOG.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/CHANGELOG.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/activitybar.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/activitybar.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/panel-icon-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/panel-icon-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/panel-icon-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/panel-icon-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/bg-next-edit-applied-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/bg-next-edit-applied-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/bg-next-edit-applied-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/bg-next-edit-applied-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/bg-next-edit-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/bg-next-edit-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/bg-next-edit-gray-hook.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/bg-next-edit-gray-hook.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/bg-next-edit-gray-line.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/bg-next-edit-gray-line.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/bg-next-edit-inactive-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/bg-next-edit-inactive-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/bg-next-edit-inactive-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/bg-next-edit-inactive-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/bg-next-edit-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/bg-next-edit-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/left-dark-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/left-dark-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/left-dark-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/left-dark-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/left-light-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/left-light-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/left-light-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/left-light-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-addition-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-addition-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-addition-inbetween-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-addition-inbetween-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-addition-inbetween-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-addition-inbetween-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-addition-inbetween-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-addition-inbetween-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-addition-inbetween-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-addition-inbetween-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-addition-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-addition-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-addition-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-addition-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-addition-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-addition-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-applied-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-applied-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-applied-inbetween-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-applied-inbetween-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-applied-inbetween-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-applied-inbetween-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-applied-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-applied-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-available-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-available-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-available-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-available-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-change-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-change-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-change-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-change-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-change-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-change-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-change-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-change-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-deletion-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-deletion-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-deletion-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-deletion-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-deletion-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-deletion-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-deletion-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-deletion-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-loading-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-loading-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-loading-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-loading-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-rejected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-rejected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-rejected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-rejected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-unavailable-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-unavailable-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-unavailable-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-unavailable-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-update-complete-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-update-complete-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-update-complete-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-update-complete-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-update-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-update-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-update-disabled-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-update-disabled-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-update-disabled-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-update-disabled-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-update-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-update-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-update-loading-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-update-loading-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-update-loading-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/nextedit-update-loading-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/right-dark-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/right-dark-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/right-dark-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/right-dark-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/right-light-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/right-light-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/right-light-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/next-edit/right-light-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/a.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/a.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/alt.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/alt.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/b.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/b.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/backspace.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/backspace.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/c.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/c.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/command.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/command.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/control.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/control.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/ctrl.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/ctrl.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/d.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/d.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/delete.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/delete.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/e.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/e.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/escape.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/escape.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/f.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/f.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/g.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/g.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/h.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/h.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/i.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/i.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/j.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/j.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/k.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/k.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/l.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/l.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/m.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/m.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/meta.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/meta.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/n.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/n.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/o.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/o.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/option.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/option.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/p.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/p.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/q.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/q.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/r.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/r.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/return.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/return.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/s.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/s.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/semicolon.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/semicolon.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/shift.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/shift.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/t.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/t.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/tab.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/tab.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/u.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/u.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/v.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/v.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/w.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/w.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/win.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/win.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/x.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/x.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/y.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/y.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/z.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/light/z.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/a.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/a.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/alt.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/alt.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/b.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/b.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/backspace.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/backspace.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/c.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/c.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/command.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/command.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/control.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/control.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/ctrl.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/ctrl.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/d.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/d.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/delete.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/delete.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/e.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/e.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/escape.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/escape.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/f.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/f.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/g.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/g.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/h.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/h.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/i.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/i.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/j.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/j.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/k.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/k.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/l.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/l.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/m.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/m.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/meta.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/meta.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/n.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/n.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/o.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/o.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/option.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/option.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/p.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/p.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/q.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/q.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/r.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/r.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/return.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/return.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/s.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/s.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/semicolon.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/semicolon.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/shift.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/shift.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/t.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/t.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/tab.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/tab.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/u.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/u.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/v.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/v.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/w.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/w.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/win.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/win.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/x.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/x.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/y.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/y.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/z.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/media/keyboard/dark/z.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/autofix.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/autofix.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/diff-view.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/diff-view.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/history.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/history.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/main-panel.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/main-panel.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/memories.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/memories.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/next-edit-suggestions.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/next-edit-suggestions.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/preference.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/preference.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/remote-agent-diff.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/remote-agent-diff.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/remote-agent-home.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/remote-agent-home.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/rules.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/rules.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/settings.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/settings.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/AugmentMessage-Bix3vTO2.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/AugmentMessage-Bix3vTO2.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/BaseButton-C1unVd78.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/BaseButton-C1unVd78.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/BaseButton-DvMdfQ3F.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/BaseButton-DvMdfQ3F.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ButtonAugment-CNK8zC8i.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ButtonAugment-CNK8zC8i.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ButtonAugment-PWVCCDdn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ButtonAugment-PWVCCDdn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/CardAugment-BAo8Ti0V.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/CardAugment-BAo8Ti0V.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/CardAugment-D2Hs6UjS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/CardAugment-D2Hs6UjS.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/Content-D0WttAzY.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/Content-D0WttAzY.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/Content-xvE836E_.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/Content-xvE836E_.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/Drawer-BphJwrBH.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/Drawer-BphJwrBH.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/Drawer-DwFbLE28.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/Drawer-DwFbLE28.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/Filespan-DeFTcAEj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/Filespan-DeFTcAEj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/Filespan-tclW2Ian.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/Filespan-tclW2Ian.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/IconButtonAugment-BTu-iglL.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/IconButtonAugment-BTu-iglL.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/IconButtonAugment-D_WR2I26.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/IconButtonAugment-D_WR2I26.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/IconFilePath-BjUJjRTr.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/IconFilePath-BjUJjRTr.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/IconFilePath-CiKel2Kp.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/IconFilePath-CiKel2Kp.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/Keybindings-BFFBoxX3.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/Keybindings-BFFBoxX3.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/Keybindings-C19Zl3Ej.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/Keybindings-C19Zl3Ej.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/LanguageIcon-CA8dtZ_C.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/LanguageIcon-CA8dtZ_C.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/LanguageIcon-D78BqCXT.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/LanguageIcon-D78BqCXT.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/MarkdownEditor-qBL2ZDYL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/MarkdownEditor-qBL2ZDYL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/MarkdownEditor-zNvUkrOp.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/MarkdownEditor-zNvUkrOp.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/MaterialIcon-BO_oU5T3.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/MaterialIcon-BO_oU5T3.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/MaterialIcon-D8Nb6HkU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/MaterialIcon-D8Nb6HkU.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/MessageList-jcql_8od.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/MessageList-jcql_8od.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/MessageList-k9jn7mxj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/MessageList-k9jn7mxj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/NextEditSuggestions-BUxBPIaq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/NextEditSuggestions-BUxBPIaq.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/NextEditSuggestions-C1kwmzU5.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/NextEditSuggestions-C1kwmzU5.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/OpenFileButton-BO1gXf_-.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/OpenFileButton-BO1gXf_-.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/OpenFileButton-_9PQ-to3.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/OpenFileButton-_9PQ-to3.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/RemoteAgentRetry-CgKZWHFz.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/RemoteAgentRetry-CgKZWHFz.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/RemoteAgentRetry-wzAOG5IN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/RemoteAgentRetry-wzAOG5IN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/RemoteAgentSetup-B1m5qM5w.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/RemoteAgentSetup-B1m5qM5w.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/RemoteAgentSetup-CDmI6naJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/RemoteAgentSetup-CDmI6naJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/RulesDropdown-BtxZUbSP.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/RulesDropdown-BtxZUbSP.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/SpinnerAugment-DnPofOlT.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/SpinnerAugment-DnPofOlT.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/SpinnerAugment-JC8TPhVf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/SpinnerAugment-JC8TPhVf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/StatusIndicator-BiyeFzqm.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/StatusIndicator-BiyeFzqm.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/StatusIndicator-D-yOSWp9.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/StatusIndicator-D-yOSWp9.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/TextAreaAugment-BYCw5p2g.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/TextAreaAugment-BYCw5p2g.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/TextAreaAugment-J75lFxU7.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/TextAreaAugment-J75lFxU7.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/TextTooltipAugment-BIMZ5dVo.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/TextTooltipAugment-BIMZ5dVo.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/TextTooltipAugment-WghC7pXE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/TextTooltipAugment-WghC7pXE.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/VSCodeCodicon-DVaocTud.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/VSCodeCodicon-DVaocTud.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/VSCodeCodicon-zeLUoeQd.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/VSCodeCodicon-zeLUoeQd.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/VirtualizedMessageList-B0lMHs3x.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/VirtualizedMessageList-B0lMHs3x.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/VirtualizedMessageList-DhWSVHYH.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/VirtualizedMessageList-DhWSVHYH.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/_basePickBy-DdaYhufB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/_basePickBy-DdaYhufB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/_baseUniq-kgSnk0sw.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/_baseUniq-kgSnk0sw.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/abap-BrlRCFwh.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/abap-BrlRCFwh.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/abap-CRCWOmpq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/abap-CRCWOmpq.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/apex-BE2Kqs_0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/apex-BE2Kqs_0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/apex-DFVco9Dq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/apex-DFVco9Dq.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/arc-D_9aAG2g.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/arc-D_9aAG2g.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/architectureDiagram-UYN6MBPD-PDEbz-7E.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/architectureDiagram-UYN6MBPD-PDEbz-7E.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/arrow-up-right-from-square-q_X4-2Am.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/arrow-up-right-from-square-q_X4-2Am.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/augment-logo-E1jEbeRV.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/augment-logo-E1jEbeRV.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/autofix-CX5hiNi4.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/autofix-CX5hiNi4.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/autofix-CrkuFWqJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/autofix-CrkuFWqJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/autofix-state-d-ymFdyn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/autofix-state-d-ymFdyn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/await_block-C0teov-5.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/await_block-C0teov-5.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/azcli-1IWB1ccx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/azcli-1IWB1ccx.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/azcli-CBeeoD2V.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/azcli-CBeeoD2V.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/bat-CtWuqYvB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/bat-CtWuqYvB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/bat-DPkNLes8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/bat-DPkNLes8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/bicep-BZbtZWRn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/bicep-BZbtZWRn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/bicep-C6yweCii.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/bicep-C6yweCii.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/blockDiagram-ZHA2E4KO-BCSulqYl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/blockDiagram-ZHA2E4KO-BCSulqYl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/c4Diagram-6F5ED5ID-D7X9FWHn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/c4Diagram-6F5ED5ID-D7X9FWHn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/cameligo-CGrWLZr3.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/cameligo-CGrWLZr3.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/cameligo-hfF0gFWA.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/cameligo-hfF0gFWA.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/channel-5BTvuuaT.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/channel-5BTvuuaT.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/chat-flags-model-S_DCcmJS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/chat-flags-model-S_DCcmJS.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/chat-types-NgqNgjwU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/chat-types-NgqNgjwU.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/chevron-down-B-gSyyd4.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/chevron-down-B-gSyyd4.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/chunk-5HRBRIJM-CzTE5zxy.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/chunk-5HRBRIJM-CzTE5zxy.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/chunk-7U56Z5CX-DdZ5lMmr.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/chunk-7U56Z5CX-DdZ5lMmr.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/chunk-ASOPGD6M-C3n_BuUC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/chunk-ASOPGD6M-C3n_BuUC.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/chunk-KFBOBJHC-DoMju-MD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/chunk-KFBOBJHC-DoMju-MD.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/chunk-T2TOU4HS-Cy5IYnAV.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/chunk-T2TOU4HS-Cy5IYnAV.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/chunk-TMUBEWPD-C5l0nSLF.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/chunk-TMUBEWPD-C5l0nSLF.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/classDiagram-LNE6IOMH-yXZDpWPp.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/classDiagram-LNE6IOMH-yXZDpWPp.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/classDiagram-v2-MQ7JQ4JX-yXZDpWPp.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/classDiagram-v2-MQ7JQ4JX-yXZDpWPp.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/clojure-BhAVYYK7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/clojure-BhAVYYK7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/clojure-D9WOWImG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/clojure-D9WOWImG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/clone-DIOJOjB9.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/clone-DIOJOjB9.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/coffee-B7EJu28W.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/coffee-B7EJu28W.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/coffee-D3gVwdtb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/coffee-D3gVwdtb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/cpp-B6k-yq-r.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/cpp-B6k-yq-r.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/cpp-DghbrAFl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/cpp-DghbrAFl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/csharp-1bC6NAu3.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/csharp-1bC6NAu3.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/csharp-BoL64M5l.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/csharp-BoL64M5l.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/csp-C46ZqvIl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/csp-C46ZqvIl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/csp-ZI2qu8Le.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/csp-ZI2qu8Le.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/css-BkD51DMU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/css-BkD51DMU.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/css-DQU6DXDx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/css-DQU6DXDx.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/cssMode-Cf0wo1J6.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/cssMode-Cf0wo1J6.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/cssMode-CnVX4sPg.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/cssMode-CnVX4sPg.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/cypher-D84EuPTj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/cypher-D84EuPTj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/cypher-DQ3GyGCv.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/cypher-DQ3GyGCv.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/cytoscape.esm-B0yNE0-9.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/cytoscape.esm-B0yNE0-9.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/dagre-4EVJKHTY-vIba3ZXE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/dagre-4EVJKHTY-vIba3ZXE.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/dart-CQal6Qht.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/dart-CQal6Qht.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/dart-D8lhlL1r.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/dart-D8lhlL1r.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/design-system-init-Bf1-mlh4.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/design-system-init-Bf1-mlh4.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/design-system-init-CRmW_T8r.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/design-system-init-CRmW_T8r.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/design-system-init-CajyFoaO.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/design-system-init-CajyFoaO.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/diagram-QW4FP2JN-C0zT9SrI.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/diagram-QW4FP2JN-C0zT9SrI.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/diff-utils-Bh9TYMbV.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/diff-utils-Bh9TYMbV.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/diff-utils-DB7Uu6wb.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/diff-utils-DB7Uu6wb.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/diff-view-CAV2jFBX.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/diff-view-CAV2jFBX.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/diff-view-DwmCBV60.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/diff-view-DwmCBV60.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/dockerfile-CuMHdPl5.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/dockerfile-CuMHdPl5.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/dockerfile-DLk6rpji.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/dockerfile-DLk6rpji.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ecl-BO6FnfXk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ecl-BO6FnfXk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ecl-DrG4DZS2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ecl-DrG4DZS2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/elixir-BRjLKONM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/elixir-BRjLKONM.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/elixir-nOQiPlLZ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/elixir-nOQiPlLZ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ellipsis-ce3_p-Q7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ellipsis-ce3_p-Q7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/erDiagram-6RL3IURR-Ckp7O8sf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/erDiagram-6RL3IURR-Ckp7O8sf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/exclamation-triangle-DfKf7sb_.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/exclamation-triangle-DfKf7sb_.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/expand-C7dSG_GJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/expand-C7dSG_GJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/file-paths-BcSg4gks.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/file-paths-BcSg4gks.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/file-reader-B5sFoklF.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/file-reader-B5sFoklF.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/file-reader-B7W_DzJn.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/file-reader-B7W_DzJn.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/flow9-Cac8vKd7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/flow9-Cac8vKd7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/flow9-DFOiqFq1.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/flow9-DFOiqFq1.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/flowDiagram-7ASYPVHJ-6PrNFSDY.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/flowDiagram-7ASYPVHJ-6PrNFSDY.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/folder-DRaPdzNV.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/folder-DRaPdzNV.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/folder-IxOJUoWR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/folder-IxOJUoWR.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/folder-opened-bSDyFrZo.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/folder-opened-bSDyFrZo.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/freemarker2-DOiDZsWD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/freemarker2-DOiDZsWD.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/freemarker2-DoNuTueB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/freemarker2-DoNuTueB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/fsharp-BpBzFqoi.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/fsharp-BpBzFqoi.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/fsharp-fd1GTHhf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/fsharp-fd1GTHhf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ganttDiagram-NTVNEXSI-CBVBM3iX.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ganttDiagram-NTVNEXSI-CBVBM3iX.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/gitGraph-YCYPL57B-DXh_24JE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/gitGraph-YCYPL57B-DXh_24JE.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/gitGraphDiagram-NRZ2UAAF-Ch7eoP9u.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/gitGraphDiagram-NRZ2UAAF-Ch7eoP9u.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/github-C1jNNmyr.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/github-C1jNNmyr.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/globals-D0QH3NT1.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/globals-D0QH3NT1.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/go-CHYgS3dC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/go-CHYgS3dC.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/go-O9LJTZXk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/go-O9LJTZXk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/graph-DP5w6F5Q.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/graph-DP5w6F5Q.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/graphql-LQdxqEYJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/graphql-LQdxqEYJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/graphql-csByOneL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/graphql-csByOneL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/handlebars-CNQw3EXp.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/handlebars-CNQw3EXp.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/handlebars-SegiTBbt.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/handlebars-SegiTBbt.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/hcl-CVzGlmMO.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/hcl-CVzGlmMO.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/hcl-DxDQ3s82.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/hcl-DxDQ3s82.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/history-BJ24T2yD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/history-BJ24T2yD.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/history-DLzhBtGZ.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/history-DLzhBtGZ.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/html-CHnKu8C4.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/html-CHnKu8C4.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/html-gnlaprsJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/html-gnlaprsJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/htmlMode-CIRhgpF_.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/htmlMode-CIRhgpF_.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/htmlMode-DkXYjxNb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/htmlMode-DkXYjxNb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/index-9HWdRmiB.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/index-9HWdRmiB.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/index-CW7fyhvB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/index-CW7fyhvB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/index-DiI90jLk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/index-DiI90jLk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/index-McRKs1sU.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/index-McRKs1sU.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/index-Yat2JVWz.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/index-Yat2JVWz.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/index-eY12-hdZ.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/index-eY12-hdZ.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/index-yERhhNs7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/index-yERhhNs7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/infoDiagram-A4XQUW5V-CYJpMW6Y.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/infoDiagram-A4XQUW5V-CYJpMW6Y.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ini-BvajGCUy.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ini-BvajGCUy.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ini-COn9E3gi.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ini-COn9E3gi.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/init-g68aIKmP.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/init-g68aIKmP.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/isObjectLike-BA2QYXi-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/isObjectLike-BA2QYXi-.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/java-DBwYS35M.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/java-DBwYS35M.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/java-SYsfObOQ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/java-SYsfObOQ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/javascript-BrEubtUq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/javascript-BrEubtUq.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/javascript-nb2UY8k5.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/javascript-nb2UY8k5.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/journeyDiagram-G5WM74LC-hzTtjCWc.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/journeyDiagram-G5WM74LC-hzTtjCWc.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/jsonMode-B6_b0_sN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/jsonMode-B6_b0_sN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/jsonMode-l8Gs1TaB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/jsonMode-l8Gs1TaB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/julia-ClS8lr_N.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/julia-ClS8lr_N.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/julia-DQXNmw_w.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/julia-DQXNmw_w.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/kanban-definition-QRCXZQQD-DvoXGatg.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/kanban-definition-QRCXZQQD-DvoXGatg.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/katex-BAVf198l.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/katex-BAVf198l.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/keypress-DD1aQVr0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/keypress-DD1aQVr0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/kotlin-CUUhw8ZM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/kotlin-CUUhw8ZM.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/kotlin-qQ0MG-9I.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/kotlin-qQ0MG-9I.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/layer-group-DMm-CNlu.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/layer-group-DMm-CNlu.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/layer-group-Df_FYENN.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/layer-group-Df_FYENN.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/layout-BQSDSE65.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/layout-BQSDSE65.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/less-CW-yd8b8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/less-CW-yd8b8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/less-GGFNNJHn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/less-GGFNNJHn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/lexon-Canl7DCW.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/lexon-Canl7DCW.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/lexon-DwtVlf1I.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/lexon-DwtVlf1I.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/linear-kNLpc4xp.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/linear-kNLpc4xp.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/liquid-CMxlMAZJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/liquid-CMxlMAZJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/liquid-DOEm5dbE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/liquid-DOEm5dbE.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/lodash-l00D6itj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/lodash-l00D6itj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/lua-BdjVVLHC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/lua-BdjVVLHC.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/lua-D28Ae8-K.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/lua-D28Ae8-K.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/m3-B3V054Zg.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/m3-B3V054Zg.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/m3-Bu4mmWhs.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/m3-Bu4mmWhs.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/magnifying-glass-D5YyJVGd.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/magnifying-glass-D5YyJVGd.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/main-panel-DPyc9-tp.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/main-panel-DPyc9-tp.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/markdown-9NNSJ0ww.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/markdown-9NNSJ0ww.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/markdown-B811l8j2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/markdown-B811l8j2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/mcp-logo-TxRUeZhS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/mcp-logo-TxRUeZhS.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/mdx-BaH_mmJD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/mdx-BaH_mmJD.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/mdx-CW_e0Iyn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/mdx-CW_e0Iyn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/memories-CMFr_HGh.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/memories-CMFr_HGh.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/memories-drAAvers.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/memories-drAAvers.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/mindmap-definition-GWI6TPTV-CYLpqLZz.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/mindmap-definition-GWI6TPTV-CYLpqLZz.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/mips-CdjsipkG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/mips-CdjsipkG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/mips-Cu7FWeYr.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/mips-Cu7FWeYr.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/msdax-CYqgjx_P.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/msdax-CYqgjx_P.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/msdax-DBX3bZkL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/msdax-DBX3bZkL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/mysql-BHd6q0vd.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/mysql-BHd6q0vd.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/mysql-CMGNIvT0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/mysql-CMGNIvT0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/next-edit-suggestions-BsQxVkbC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/next-edit-suggestions-BsQxVkbC.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/next-edit-suggestions-CKieFv1T.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/next-edit-suggestions-CKieFv1T.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/next-edit-suggestions-UoxOVKUP.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/next-edit-suggestions-UoxOVKUP.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/next-edit-types-904A5ehg.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/next-edit-types-904A5ehg.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/objective-c-BdAIHrxl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/objective-c-BdAIHrxl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/objective-c-DCIC4Ga8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/objective-c-DCIC4Ga8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/open-in-new-window-CkR7J3XO.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/open-in-new-window-CkR7J3XO.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ordinal-_rw2EY4v.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ordinal-_rw2EY4v.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pascal-BhNW15KB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pascal-BhNW15KB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pascal-DVjYFmSU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pascal-DVjYFmSU.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pascaligo-5jv8CcQD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pascaligo-5jv8CcQD.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pascaligo-LOm9cWIk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pascaligo-LOm9cWIk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pen-to-square-3TLxExQu.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pen-to-square-3TLxExQu.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pen-to-square-Dvw-pMXw.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pen-to-square-Dvw-pMXw.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/perl-DlYyT36c.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/perl-DlYyT36c.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/perl-UpK8AUhB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/perl-UpK8AUhB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pgsql-Dy0bjov7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pgsql-Dy0bjov7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pgsql-cWj3SLw2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pgsql-cWj3SLw2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/php-120yhfDK.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/php-120yhfDK.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/php-C92L-r_Y.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/php-C92L-r_Y.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pieDiagram-YF2LJOPJ-_AIpHX6b.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pieDiagram-YF2LJOPJ-_AIpHX6b.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pla-B-trYkKT.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pla-B-trYkKT.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pla-CjnFlu4u.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pla-CjnFlu4u.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/postiats-CQpG440k.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/postiats-CQpG440k.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/postiats-ToQhlN1R.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/postiats-ToQhlN1R.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/powerquery-BLkMU_zt.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/powerquery-BLkMU_zt.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/powerquery-DdJtto1Z.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/powerquery-DdJtto1Z.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/powershell-Bu_VLpJB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/powershell-Bu_VLpJB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/powershell-Cz-ePiwW.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/powershell-Cz-ePiwW.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/preference-B0MRnf4G.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/preference-B0MRnf4G.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/preference-Dn6mpF6J.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/preference-Dn6mpF6J.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/preload-helper-Dv6uf1Os.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/preload-helper-Dv6uf1Os.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/protobuf-BQ74DTcm.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/protobuf-BQ74DTcm.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/protobuf-CZXszgil.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/protobuf-CZXszgil.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pug-8ix3pnNZ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pug-8ix3pnNZ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pug-kFxLfcjb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/pug-kFxLfcjb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/python-CZ67Wo4I.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/python-CZ67Wo4I.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/python-DW6CoqQ2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/python-DW6CoqQ2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/qsharp-YKUDF0Oj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/qsharp-YKUDF0Oj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/qsharp-q7JyzKFN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/qsharp-q7JyzKFN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/quadrantDiagram-OS5C2QUG-BMlNxa5k.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/quadrantDiagram-OS5C2QUG-BMlNxa5k.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/r-BIFz-_sK.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/r-BIFz-_sK.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/r-DShZCeRJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/r-DShZCeRJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/razor-BxlDHIuM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/razor-BxlDHIuM.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/razor-CgmX9w71.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/razor-CgmX9w71.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/redis-CHOsPHWR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/redis-CHOsPHWR.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/redis-DJMpkPfA.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/redis-DJMpkPfA.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/redshift-6xAzNskS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/redshift-6xAzNskS.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/redshift-CBifECDb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/redshift-CBifECDb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/remote-agent-diff-CE-sUffg.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/remote-agent-diff-CE-sUffg.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/remote-agent-diff-CekNHzOv.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/remote-agent-diff-CekNHzOv.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/remote-agent-home-DXGXtJVn.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/remote-agent-home-DXGXtJVn.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/remote-agent-home-HfC6g5QJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/remote-agent-home-HfC6g5QJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/requirementDiagram-MIRIMTAZ-Bn6D7oCT.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/requirementDiagram-MIRIMTAZ-Bn6D7oCT.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/resize-observer-DdAtcrRr.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/resize-observer-DdAtcrRr.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/restructuredtext-CQoPj0uC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/restructuredtext-CQoPj0uC.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/restructuredtext-CghPJEOS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/restructuredtext-CghPJEOS.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ruby-1H8dtvFl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ruby-1H8dtvFl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ruby-CYWGW-b1.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/ruby-CYWGW-b1.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/rules-CRc3OQde.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/rules-CRc3OQde.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/rules-DecO7AHT.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/rules-DecO7AHT.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/rules-parser-D2d7xQ-G.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/rules-parser-D2d7xQ-G.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/rust-APfvjYow.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/rust-APfvjYow.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/rust-DMDD0SHb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/rust-DMDD0SHb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sankeyDiagram-Y46BX6SQ-D6pomnSu.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sankeyDiagram-Y46BX6SQ-D6pomnSu.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sb-BYAiYHFx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sb-BYAiYHFx.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sb-Ddgo-Lel.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sb-Ddgo-Lel.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/scala-Bqvq8jcR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/scala-Bqvq8jcR.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/scala-Bzjcj0lf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/scala-Bzjcj0lf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/scheme-Dhb-2j9p.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/scheme-Dhb-2j9p.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/scheme-Ecrf_Zyn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/scheme-Ecrf_Zyn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/scss-CTwUZ5N7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/scss-CTwUZ5N7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/scss-DuQSCaUL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/scss-DuQSCaUL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sequenceDiagram-G6AWOVSC-l98-LdVo.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sequenceDiagram-G6AWOVSC-l98-LdVo.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/settings-BUgdOl3i.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/settings-BUgdOl3i.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/settings-BVJF1XWf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/settings-BVJF1XWf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/shell-CNhb_Zkf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/shell-CNhb_Zkf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/shell-CsDZo4DB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/shell-CsDZo4DB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/solidity-C4mwTkrB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/solidity-C4mwTkrB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/solidity-CME5AdoB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/solidity-CME5AdoB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sophia-RYC1BQQz.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sophia-RYC1BQQz.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sophia-dWwzI90F.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sophia-dWwzI90F.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sparql-CouE6pZG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sparql-CouE6pZG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sparql-KEyrF7De.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sparql-KEyrF7De.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sql-BV61QDTH.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sql-BV61QDTH.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sql-BdTr02Mf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/sql-BdTr02Mf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/st-BZ7aq21L.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/st-BZ7aq21L.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/st-C7iG7M4S.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/st-C7iG7M4S.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/stateDiagram-MAYHULR4-3NmVhtZP.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/stateDiagram-MAYHULR4-3NmVhtZP.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/stateDiagram-v2-4JROLMXI-CkdPw6bU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/stateDiagram-v2-4JROLMXI-CkdPw6bU.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/swift-D7IUmUK8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/swift-D7IUmUK8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/swift-DqwpnxQL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/swift-DqwpnxQL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/systemverilog-CeZ7LPTL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/systemverilog-CeZ7LPTL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/systemverilog-DgMryOEJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/systemverilog-DgMryOEJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/tcl-Bl2hYPt-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/tcl-Bl2hYPt-.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/tcl-PloMZuKG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/tcl-PloMZuKG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/terminal-BBUsFUTj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/terminal-BBUsFUTj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/timeline-definition-U7ZMHBDA-CQLOK7uO.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/timeline-definition-U7ZMHBDA-CQLOK7uO.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/toggleHighContrast-D4zjdeIP.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/toggleHighContrast-D4zjdeIP.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/tsMode-CQlxoi98.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/tsMode-CQlxoi98.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/tsMode-DM5zRHFn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/tsMode-DM5zRHFn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/twig-BfRIq3la.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/twig-BfRIq3la.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/twig-h6VuAx0U.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/twig-h6VuAx0U.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/types-B5Ac2hek.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/types-B5Ac2hek.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/types-BSMhNRWH.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/types-BSMhNRWH.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/types-Cgd-nZOV.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/types-Cgd-nZOV.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/typescript-DTP-A_Zf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/typescript-DTP-A_Zf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/typescript-Dst5H8CP.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/typescript-Dst5H8CP.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/typespec-5IKh-a8s.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/typespec-5IKh-a8s.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/typespec-DKGjpBXL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/typespec-DKGjpBXL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/vb-BwAE3J76.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/vb-BwAE3J76.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/vb-CS586MRk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/vb-CS586MRk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/wgsl-DCafy-vX.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/wgsl-DCafy-vX.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/wgsl-Du36xR5C.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/wgsl-Du36xR5C.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/xml-C0Ah6gFk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/xml-C0Ah6gFk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/xml-_u1XISHN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/xml-_u1XISHN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/xychartDiagram-6QU3TZC5-9dd63YPu.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/xychartDiagram-6QU3TZC5-9dd63YPu.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/yaml-Bo9nk_ct.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/yaml-Bo9nk_ct.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/yaml-CRGTkk5g.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/common-webviews/assets/yaml-CRGTkk5g.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/terminalSettings.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/terminalSettings.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/LICENSE": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/LICENSE"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/product.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/product.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/bootstrap-fork.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/bootstrap-fork.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/server-cli.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/server-cli.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/CodeTabExpansion.psm1": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/CodeTabExpansion.psm1"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/GitTabExpansion.psm1": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/GitTabExpansion.psm1"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-env.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-env.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-login.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-login.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-profile.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-profile.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-rc.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-rc.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/fish_xdg_data/fish/vendor_conf.d/shellIntegration.fish": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/workbench/contrib/terminal/common/scripts/fish_xdg_data/fish/vendor_conf.d/shellIntegration.fish"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/files/node/watcher/watcherMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/files/node/watcher/watcherMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/base/node/cpuUsage.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/base/node/cpuUsage.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/base/node/ps.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/base/node/ps.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/schemas/jsconfig.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/schemas/jsconfig.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/schemas/tsconfig.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/schemas/tsconfig.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/resources/walkthroughs/create-a-js-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/resources/walkthroughs/create-a-js-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/resources/walkthroughs/debug-and-run.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/resources/walkthroughs/debug-and-run.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/resources/walkthroughs/install-node-js.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/resources/walkthroughs/install-node-js.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/resources/walkthroughs/learn-more.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/typescript-language-features/resources/walkthroughs/learn-more.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/tunnel-forwarding/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/tunnel-forwarding/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/tunnel-forwarding/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/tunnel-forwarding/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/tunnel-forwarding/.vscode/launch.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/tunnel-forwarding/.vscode/launch.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/codicon.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/codicon.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/main.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/main.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/preview-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/preview-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/preview-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/simple-browser/media/preview-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/search-result/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/search-result/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/search-result/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/search-result/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/search-result/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/search-result/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/search-result/syntaxes/searchResult.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/search-result/syntaxes/searchResult.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/references-view/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/references-view/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/references-view/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/references-view/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/references-view/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/references-view/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/php-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/php-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/php-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/php-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/php-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/php-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/npm/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/npm/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/npm/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/npm/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/npm/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/npm/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/npm/images/code.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/npm/images/code.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/ci.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/ci.yml"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/tsconfig.browser.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/tsconfig.browser.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/425.heapsnapshotWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/425.heapsnapshotWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/848.extension.web.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/848.extension.web.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/cpu-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/cpu-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.web.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.web.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/heap-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/heap-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshot-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshot-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshotWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshotWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/SECURITY.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/SECURITY.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/ci.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/ci.yml"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/eslint.config.mjs": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/eslint.config.mjs"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-global-state/fuzzyFsFoldersIndex.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-global-state/fuzzyFsFoldersIndex.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/connect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/connect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/ci.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/ci.yml"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/syntaxes/md-math-fence.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/syntaxes/md-math-fence.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/html-language-features/server/lib/jquery.d.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/html-language-features/server/lib/jquery.d.ts"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/references-view/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/references-view/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/renameWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/renameWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/simple-browser/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/simple-browser/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github-authentication/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github-authentication/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/php-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/php-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github-authentication/media/auth.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github-authentication/media/auth.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/configure.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/configure.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ipynb/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ipynb/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/resources/walkthroughs/learn-more.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/resources/walkthroughs/learn-more.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-untracked.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-untracked.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/microsoft-authentication/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/microsoft-authentication/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/json-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/json-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/resume.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/resume.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/merge-conflict/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/merge-conflict/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/imagePreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/imagePreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/disconnect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/disconnect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/syntaxes/git-commit.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/syntaxes/git-commit.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/server/lib/jquery.d.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/server/lib/jquery.d.ts"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/bin/helpers/browser.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/bin/helpers/browser.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/syntaxes/git-rebase.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/syntaxes/git-rebase.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/emojis.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/emojis.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/server-cli.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/server-cli.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/microsoft-authentication/media/auth.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/microsoft-authentication/media/auth.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/syntaxes/md-math-block.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/syntaxes/md-math-block.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/hash.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/hash.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/bootstrap-fork.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/bootstrap-fork.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/grunt/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/grunt/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/vendor/acorn-loose.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/vendor/acorn-loose.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/imagePreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/imagePreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-deleted.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-deleted.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-conflict.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-conflict.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/stop.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/stop.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/audioPreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/audioPreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/base/node/ps.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/base/node/ps.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/preview-styles/index.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/preview-styles/index.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/configuration-editing/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/configuration-editing/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/simple-browser/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/simple-browser/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/media/highlight.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/media/highlight.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/restart.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/restart.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/connect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/connect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/connect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/connect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-deleted.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-deleted.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/page.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/page.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/out/848.extension.web.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/out/848.extension.web.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/syntaxes/md-math-block.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/syntaxes/md-math-block.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/restart.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/restart.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/page.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/page.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/hash.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/hash.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/languages/git-commit.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/languages/git-commit.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/grunt/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/grunt/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github-authentication/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github-authentication/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-type-changed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-type-changed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/page.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/page.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/terminal-suggest/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/terminal-suggest/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/emmet/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/emmet/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/html-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/html-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/search-result/syntaxes/searchResult.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/search-result/syntaxes/searchResult.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/out/cpu-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/out/cpu-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/syntaxes/md-math.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/syntaxes/md-math.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/media/loading.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/media/loading.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/jake/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/jake/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/notebook-out/katex.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/notebook-out/katex.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/media/markdown.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/media/markdown.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/notebook-out/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/notebook-out/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/preview-styles/index.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/preview-styles/index.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/stop.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/stop.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/open-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/open-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/telemetry.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/telemetry.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/json-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/json-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/grunt/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/grunt/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/diagnosticTool.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/diagnosticTool.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/node.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/node.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/diagnosticTool.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/diagnosticTool.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/merge-conflict/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/merge-conflict/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/service-worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/service-worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/configuration-editing/schemas/attachContainer.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/configuration-editing/schemas/attachContainer.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/open-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/open-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/search-result/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/search-result/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/simple-browser/media/codicon.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/simple-browser/media/codicon.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/media/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/media/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/resources/walkthroughs/install-node-js.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/resources/walkthroughs/install-node-js.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/syntaxes/ignore.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/syntaxes/ignore.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/gulp/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/gulp/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/logo.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/logo.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/php-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/php-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-login.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-login.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/pause.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/pause.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/merge-conflict/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/merge-conflict/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/bin/code-server": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/bin/code-server"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/page.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/page.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/media/videoPreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/media/videoPreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/bin/helpers/check-requirements.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/bin/helpers/check-requirements.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/gulp/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/gulp/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/service-worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/service-worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/pause.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/pause.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/gulp/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/gulp/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/resources/walkthroughs/create-a-js-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/resources/walkthroughs/create-a-js-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/schemas/jsconfig.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/schemas/jsconfig.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-auto-launch/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-auto-launch/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/restart.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/restart.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/php-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/php-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/gulp/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/gulp/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/languages/git-rebase.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/languages/git-rebase.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/bootloader.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/bootloader.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.web.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.web.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ipynb/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ipynb/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/LICENSE": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/LICENSE"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/out/heap-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/out/heap-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/media/loading-hc.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/media/loading-hc.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/languages/ignore.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/languages/ignore.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-server-ready/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-server-ready/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/watchdog.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/watchdog.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-conflict.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-conflict.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/configure.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/configure.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/disconnect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/disconnect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/watchdog.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/watchdog.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/html-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/html-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/diagnosticTool.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/diagnosticTool.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ipynb/notebook-out/cellAttachmentRenderer.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ipynb/notebook-out/cellAttachmentRenderer.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/vendor/acorn-loose.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/vendor/acorn-loose.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/references-view/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/references-view/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/syntaxes/md-math.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/syntaxes/md-math.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/stop.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/stop.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/syntaxes/md-math-inline.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/syntaxes/md-math-inline.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-rc.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-rc.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/resources/walkthroughs/debug-and-run.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/resources/walkthroughs/debug-and-run.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/platform/files/node/watcher/watcherMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/platform/files/node/watcher/watcherMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/node.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/node.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/highlight.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/highlight.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/resume.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/resume.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/grunt/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/grunt/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-ignored.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-ignored.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/html-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/html-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/merge-conflict/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/merge-conflict/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/node.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/node.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/service-worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/service-worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-untracked.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-untracked.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-modified.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-modified.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-copied.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-copied.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/html-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/html-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/search-result/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/search-result/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/simple-browser/media/main.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/simple-browser/media/main.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-auto-launch/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-auto-launch/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/grunt/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/grunt/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/base/node/cpuUsage.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/base/node/cpuUsage.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/simple-browser/media/preview-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/simple-browser/media/preview-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-added.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-added.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/platform/terminal/node/ptyHostMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/platform/terminal/node/ptyHostMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/out/425.heapsnapshotWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/out/425.heapsnapshotWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-env.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-env.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/npm/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/npm/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/npm/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/npm/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/notebook-out/katex.min.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/notebook-out/katex.min.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/media/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/media/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/preview-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/preview-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/simple-browser/media/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/simple-browser/media/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-renamed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-renamed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/jake/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/jake/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/bootloader.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/bootloader.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/npm/images/code.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/npm/images/code.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/audioPreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/audioPreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ipynb/notebook-out/cellAttachmentRenderer.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ipynb/notebook-out/cellAttachmentRenderer.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/service-worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/service-worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/search-result/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/search-result/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/media/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/media/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/stop-profiling.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/stop-profiling.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/syntaxes/md-math-fence.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/syntaxes/md-math-fence.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github-authentication/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github-authentication/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/pre.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/pre.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/simple-browser/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/simple-browser/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/emmet/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/emmet/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug-companion/SECURITY.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug-companion/SECURITY.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/open-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/open-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-auto-launch/.vscode/launch.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-auto-launch/.vscode/launch.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/disconnect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/disconnect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/node.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/node.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/microsoft-authentication/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/microsoft-authentication/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/media/auth.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/media/auth.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/configuration-editing/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/configuration-editing/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/markdown.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/markdown.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/videoPreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/videoPreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/jake/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/jake/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/npm/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/npm/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/gulp/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/gulp/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-profile.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-profile.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/media/audioPreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/media/audioPreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/configure.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/configure.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/logo.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/logo.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/loading-hc.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/loading-hc.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-added.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-added.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/html-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/open-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/open-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/media/imagePreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/media/imagePreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.fish": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.fish"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github-authentication/media/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github-authentication/media/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/media/preview-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/media/preview-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/media/preview-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/media/preview-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/json-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/json-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/jake/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/jake/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshot-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshot-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/stop.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/light/stop.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/notebook-out/katex.min.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/notebook-out/katex.min.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/microsoft-authentication/media/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/microsoft-authentication/media/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/some-markdown.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/some-markdown.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ipynb/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ipynb/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/media/pre.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/media/pre.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/media/imagePreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/media/imagePreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/markdown.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/markdown.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/renameWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/renameWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/resume.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/resume.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/json-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/json-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/videoPreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/videoPreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/extension-editing/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/extension-editing/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/notebook-out/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/notebook-out/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug-companion/ci.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug-companion/ci.yml"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug-companion/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug-companion/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/extension-editing/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/extension-editing/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/media/audioPreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/media/audioPreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/merge-conflict/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/merge-conflict/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/terminal-suggest/.gitignore": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/terminal-suggest/.gitignore"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ipynb/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ipynb/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/pause.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/pause.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/microsoft-authentication/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-type-changed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-type-changed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git-base/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/jake/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/jake/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/gulp/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/gulp/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/configure.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/configure.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/terminal-suggest/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/terminal-suggest/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/diagnosticTool.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/diagnosticTool.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/media/auth.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github-authentication/media/auth.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/vendor/acorn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/vendor/acorn.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/tunnel-forwarding/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/tunnel-forwarding/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ipynb/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ipynb/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/disconnect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/disconnect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-renamed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-renamed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/product.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/product.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-ignored.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-ignored.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/targets/node/terminateProcess.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/targets/node/terminateProcess.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/out/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug-companion/out/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/vendor/acorn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/vendor/acorn.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-copied.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/light/status-copied.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/resume.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/resume.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug-companion/out/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug-companion/out/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/html-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/html-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/grunt/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/grunt/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/some-markdown.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/some-markdown.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/restart.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/restart.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/terminal-suggest/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/terminal-suggest/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/references-view/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/references-view/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug-companion/eslint.config.mjs": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug-companion/eslint.config.mjs"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/tunnel-forwarding/.vscode/launch.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/tunnel-forwarding/.vscode/launch.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/tsconfig.browser.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/tsconfig.browser.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/schemas/tsconfig.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/schemas/tsconfig.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/media/loading-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/media/loading-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/terminal-suggest/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/terminal-suggest/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug-companion/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug-companion/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/loading-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/loading-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/json-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/json-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/css-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug-companion/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug-companion/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/syntaxes/md-math-inline.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-math/syntaxes/md-math-inline.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug-companion/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug-companion/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/bin/remote-cli/code": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/bin/remote-cli/code"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/simple-browser/media/preview-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/simple-browser/media/preview-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/pause.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/pause.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-modified.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/git/resources/icons/dark/status-modified.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/stop-profiling.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/stop-profiling.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/typescript-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/notebook-out/katex.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/markdown-math/notebook-out/katex.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/json-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/tunnel-forwarding/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/tunnel-forwarding/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/jake/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/jake/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/loading.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/media-preview/media/loading.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/emmet/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/emmet/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/connect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/dark/connect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/microsoft-authentication/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/microsoft-authentication/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/preview-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/markdown-language-features/media/preview-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-server-ready/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/debug-server-ready/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/markdown.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/github/markdown.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/merge-conflict/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/merge-conflict/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/media/videoPreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/media-preview/media/videoPreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.js-debug/resources/light/worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshotWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshotWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ipynb/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/ipynb/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/resources/dark/worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/html-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/html-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/targets/node/terminateProcess.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/extensions/ms-vscode.js-debug/src/targets/node/terminateProcess.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/syntaxes/git-commit.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/syntaxes/git-commit.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/syntaxes/git-rebase.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/syntaxes/git-rebase.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/syntaxes/ignore.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/syntaxes/ignore.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/languages/git-commit.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/languages/git-commit.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/languages/git-rebase.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/languages/git-rebase.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/languages/ignore.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/languages/ignore.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/emojis.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/emojis.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/light/status-added.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/light/status-added.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/light/status-conflict.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/light/status-conflict.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/light/status-copied.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/light/status-copied.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/light/status-deleted.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/light/status-deleted.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/light/status-ignored.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/light/status-ignored.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/light/status-modified.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/light/status-modified.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/light/status-renamed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/light/status-renamed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/light/status-type-changed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/light/status-type-changed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/light/status-untracked.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/light/status-untracked.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/dark/status-added.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/dark/status-added.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/dark/status-conflict.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/dark/status-conflict.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/dark/status-copied.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/dark/status-copied.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/dark/status-deleted.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/dark/status-deleted.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/dark/status-ignored.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/dark/status-ignored.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/dark/status-modified.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/dark/status-modified.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/dark/status-renamed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/dark/status-renamed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/dark/status-type-changed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/dark/status-type-changed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/dark/status-untracked.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/resources/icons/dark/status-untracked.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/extension-editing/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/extension-editing/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/extension-editing/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/extension-editing/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/emmet/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/emmet/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/emmet/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/emmet/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/emmet/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/emmet/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/debug-server-ready/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/debug-server-ready/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/debug-server-ready/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/debug-server-ready/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/debug-auto-launch/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/debug-auto-launch/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/debug-auto-launch/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/debug-auto-launch/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/debug-auto-launch/.vscode/launch.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/debug-auto-launch/.vscode/launch.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/css-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/css-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/css-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/css-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/css-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/css-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/css-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/css-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/css-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/css-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/css-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/css-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/configuration-editing/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/configuration-editing/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/configuration-editing/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/configuration-editing/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/configuration-editing/schemas/attachContainer.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/configuration-editing/schemas/attachContainer.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/bin/code-server": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/bin/code-server"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/bin/remote-cli/code": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/bin/remote-cli/code"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/bin/helpers/browser.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/bin/helpers/browser.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/bin/helpers/check-requirements.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/bin/helpers/check-requirements.sh"}, "/home/<USER>/workspace/.cache/replit/modules.stamp": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules.stamp"}, "/home/<USER>/workspace/.cache/replit/toolchain.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/toolchain.json"}, "/home/<USER>/workspace/.cache/replit/modules/nodejs-20.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/nodejs-20.res"}, "/home/<USER>/workspace/.cache/replit/modules/postgresql-16.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/postgresql-16.res"}, "/home/<USER>/workspace/.cache/replit/modules/replit.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/replit.res"}, "/home/<USER>/workspace/.cache/replit/modules/web.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/web.res"}, "/home/<USER>/workspace/.cache/nix/binary-cache-v6.sqlite-journal": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/nix/binary-cache-v6.sqlite-journal"}, "/home/<USER>/workspace/.breakpoints": {"rootPath": "/home/<USER>/workspace", "relPath": ".breakpoints"}, "/home/<USER>/workspace/.gitignore": {"rootPath": "/home/<USER>/workspace", "relPath": ".giti<PERSON>re"}, "/home/<USER>/workspace/.replit": {"rootPath": "/home/<USER>/workspace", "relPath": ".replit"}, "/home/<USER>/workspace/AI_CHAT_FIXES.md": {"rootPath": "/home/<USER>/workspace", "relPath": "AI_CHAT_FIXES.md"}, "/home/<USER>/workspace/AI_CONSOLIDATION_PLAN.md": {"rootPath": "/home/<USER>/workspace", "relPath": "AI_CONSOLIDATION_PLAN.md"}, "/home/<USER>/workspace/AUTHENTICATION.md": {"rootPath": "/home/<USER>/workspace", "relPath": "AUTHENTICATION.md"}, "/home/<USER>/workspace/ENHANCED_AI_CHAT_IMPROVEMENTS.md": {"rootPath": "/home/<USER>/workspace", "relPath": "ENHANCED_AI_CHAT_IMPROVEMENTS.md"}, "/home/<USER>/workspace/REPLIT_AI_INTEGRATION_GUIDE.md": {"rootPath": "/home/<USER>/workspace", "relPath": "REPLIT_AI_INTEGRATION_GUIDE.md"}, "/home/<USER>/workspace/SOLUTION_SUMMARY.md": {"rootPath": "/home/<USER>/workspace", "relPath": "SOLUTION_SUMMARY.md"}, "/home/<USER>/workspace/TESTING.md": {"rootPath": "/home/<USER>/workspace", "relPath": "TESTING.md"}, "/home/<USER>/workspace/TESTING_GUIDE.md": {"rootPath": "/home/<USER>/workspace", "relPath": "TESTING_GUIDE.md"}, "/home/<USER>/workspace/dev-output.log": {"rootPath": "/home/<USER>/workspace", "relPath": "dev-output.log"}, "/home/<USER>/workspace/drizzle.config.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "drizzle.config.ts"}, "/home/<USER>/workspace/jest.config.functional.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "jest.config.functional.ts"}, "/home/<USER>/workspace/jest.config.react.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "jest.config.react.ts"}, "/home/<USER>/workspace/jest.config.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "jest.config.ts"}, "/home/<USER>/workspace/jest.config.unit.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "jest.config.unit.ts"}, "/home/<USER>/workspace/jest.setup.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "jest.setup.ts"}, "/home/<USER>/workspace/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": "package.json"}, "/home/<USER>/workspace/run-tests.sh": {"rootPath": "/home/<USER>/workspace", "relPath": "run-tests.sh"}, "/home/<USER>/workspace/server.log": {"rootPath": "/home/<USER>/workspace", "relPath": "server.log"}, "/home/<USER>/workspace/tailwind.config.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "tailwind.config.ts"}, "/home/<USER>/workspace/test.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test.js"}, "/home/<USER>/workspace/trigger-refresh.js": {"rootPath": "/home/<USER>/workspace", "relPath": "trigger-refresh.js"}, "/home/<USER>/workspace/tsconfig.json": {"rootPath": "/home/<USER>/workspace", "relPath": "tsconfig.json"}, "/home/<USER>/workspace/vite.config.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "vite.config.ts"}, "/home/<USER>/workspace/tests/check-db.js": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/check-db.js"}, "/home/<USER>/workspace/tests/debug-test.js": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/debug-test.js"}, "/home/<USER>/workspace/tests/test-ai-chat.js": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/test-ai-chat.js"}, "/home/<USER>/workspace/tests/test-api.js": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/test-api.js"}, "/home/<USER>/workspace/tests/test-enhanced-formatting.js": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/test-enhanced-formatting.js"}, "/home/<USER>/workspace/tests/test-image-conversion.js": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/test-image-conversion.js"}, "/home/<USER>/workspace/tests/test-streaming-response.js": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/test-streaming-response.js"}, "/home/<USER>/workspace/test-logs/2025-04-19_18-20-53/functional-tests.log": {"rootPath": "/home/<USER>/workspace", "relPath": "test-logs/2025-04-19_18-20-53/functional-tests.log"}, "/home/<USER>/workspace/test-logs/2025-04-19_18-20-53/react-component-tests.log": {"rootPath": "/home/<USER>/workspace", "relPath": "test-logs/2025-04-19_18-20-53/react-component-tests.log"}, "/home/<USER>/workspace/test-logs/2025-04-19_18-20-53/test_results.log": {"rootPath": "/home/<USER>/workspace", "relPath": "test-logs/2025-04-19_18-20-53/test_results.log"}, "/home/<USER>/workspace/test-logs/2025-04-19_18-20-53/unit-tests.log": {"rootPath": "/home/<USER>/workspace", "relPath": "test-logs/2025-04-19_18-20-53/unit-tests.log"}, "/home/<USER>/workspace/test-logs/2025-04-19_18-16-26/test_results.log": {"rootPath": "/home/<USER>/workspace", "relPath": "test-logs/2025-04-19_18-16-26/test_results.log"}, "/home/<USER>/workspace/test-logs/2025-04-19_18-16-26/unit-tests.log": {"rootPath": "/home/<USER>/workspace", "relPath": "test-logs/2025-04-19_18-16-26/unit-tests.log"}, "/home/<USER>/workspace/test-logs/2025-04-19_18-12-22/functional-tests.log": {"rootPath": "/home/<USER>/workspace", "relPath": "test-logs/2025-04-19_18-12-22/functional-tests.log"}, "/home/<USER>/workspace/test-logs/2025-04-19_18-12-22/react-component-tests.log": {"rootPath": "/home/<USER>/workspace", "relPath": "test-logs/2025-04-19_18-12-22/react-component-tests.log"}, "/home/<USER>/workspace/test-logs/2025-04-19_18-12-22/test_results.log": {"rootPath": "/home/<USER>/workspace", "relPath": "test-logs/2025-04-19_18-12-22/test_results.log"}, "/home/<USER>/workspace/test-logs/2025-04-19_18-12-22/unit-tests.log": {"rootPath": "/home/<USER>/workspace", "relPath": "test-logs/2025-04-19_18-12-22/unit-tests.log"}, "/home/<USER>/workspace/test-logs/2025-04-19_12-51-19/test_results.log": {"rootPath": "/home/<USER>/workspace", "relPath": "test-logs/2025-04-19_12-51-19/test_results.log"}, "/home/<USER>/workspace/test-logs/2025-04-19_12-51-19/unit-tests.log": {"rootPath": "/home/<USER>/workspace", "relPath": "test-logs/2025-04-19_12-51-19/unit-tests.log"}, "/home/<USER>/workspace/src/pages/PropertyPage.jsx": {"rootPath": "/home/<USER>/workspace", "relPath": "src/pages/PropertyPage.jsx"}, "/home/<USER>/workspace/server/auth.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/auth.ts"}, "/home/<USER>/workspace/server/debug-location-test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/debug-location-test.ts"}, "/home/<USER>/workspace/server/index.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/index.ts"}, "/home/<USER>/workspace/server/location-detection-fix.js": {"rootPath": "/home/<USER>/workspace", "relPath": "server/location-detection-fix.js"}, "/home/<USER>/workspace/server/location-detection-test.js": {"rootPath": "/home/<USER>/workspace", "relPath": "server/location-detection-test.js"}, "/home/<USER>/workspace/server/location-detection-test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/location-detection-test.ts"}, "/home/<USER>/workspace/server/location-test.js": {"rootPath": "/home/<USER>/workspace", "relPath": "server/location-test.js"}, "/home/<USER>/workspace/server/real-chat-test.js": {"rootPath": "/home/<USER>/workspace", "relPath": "server/real-chat-test.js"}, "/home/<USER>/workspace/server/routes.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes.ts"}, "/home/<USER>/workspace/server/run-ai-tests.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/run-ai-tests.ts"}, "/home/<USER>/workspace/server/simple-location-test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/simple-location-test.ts"}, "/home/<USER>/workspace/server/simple-test.js": {"rootPath": "/home/<USER>/workspace", "relPath": "server/simple-test.js"}, "/home/<USER>/workspace/server/test-chat.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/test-chat.ts"}, "/home/<USER>/workspace/server/test-location-chat.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/test-location-chat.ts"}, "/home/<USER>/workspace/server/vite.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/vite.ts"}, "/home/<USER>/workspace/server/utils/logger.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/utils/logger.ts"}, "/home/<USER>/workspace/server/utils/streamParser.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/utils/streamParser.ts"}, "/home/<USER>/workspace/server/services/analyticsService.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/analyticsService.ts"}, "/home/<USER>/workspace/server/services/authService.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/authService.ts"}, "/home/<USER>/workspace/server/services/chatTestingService.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/chatTestingService.ts"}, "/home/<USER>/workspace/server/services/contextService.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/contextService.ts"}, "/home/<USER>/workspace/server/services/enhancedContextService.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/enhancedContextService.ts"}, "/home/<USER>/workspace/server/services/enhancedLocationService.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/enhancedLocationService.ts"}, "/home/<USER>/workspace/server/services/expedia.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/expedia.ts"}, "/home/<USER>/workspace/server/services/openai.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/openai.ts"}, "/home/<USER>/workspace/server/services/stripe.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/stripe.ts"}, "/home/<USER>/workspace/server/services/travsrv.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/travsrv.ts"}, "/home/<USER>/workspace/server/services/__tests__/travsrv.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/__tests__/travsrv.test.ts"}, "/home/<USER>/workspace/server/services/__tests__/mockData/hotelResponse.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/__tests__/mockData/hotelResponse.ts"}, "/home/<USER>/workspace/server/middleware/analytics.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/middleware/analytics.ts"}, "/home/<USER>/workspace/server/middleware/auth.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/middleware/auth.ts"}, "/home/<USER>/workspace/server/jobs/propertyRefresh.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/jobs/propertyRefresh.ts"}, "/home/<USER>/workspace/server/functional-tests/chat-location-test.js": {"rootPath": "/home/<USER>/workspace", "relPath": "server/functional-tests/chat-location-test.js"}, "/home/<USER>/workspace/server/functional-tests/location-check.js": {"rootPath": "/home/<USER>/workspace", "relPath": "server/functional-tests/location-check.js"}, "/home/<USER>/workspace/server/functional-tests/location-validation.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/functional-tests/location-validation.ts"}, "/home/<USER>/workspace/server/controllers/adminController.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/controllers/adminController.ts"}, "/home/<USER>/workspace/server/controllers/analyticsController.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/controllers/analyticsController.ts"}, "/home/<USER>/workspace/server/controllers/authController.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/controllers/authController.ts"}, "/home/<USER>/workspace/server/controllers/profileController.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/controllers/profileController.ts"}, "/home/<USER>/workspace/server/controllers/testController.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/controllers/testController.ts"}, "/home/<USER>/workspace/server/controllers/testRunnerController.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/controllers/testRunnerController.ts"}, "/home/<USER>/workspace/server/__tests__/test-utils.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/__tests__/test-utils.ts"}, "/home/<USER>/workspace/server/__tests__/unit/chat-api.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/__tests__/unit/chat-api.test.ts"}, "/home/<USER>/workspace/server/__tests__/unit/openai.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/__tests__/unit/openai.test.ts"}, "/home/<USER>/workspace/server/__tests__/unit/stream-parser-enhanced.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/__tests__/unit/stream-parser-enhanced.test.ts"}, "/home/<USER>/workspace/server/__tests__/unit/stream-parser.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/__tests__/unit/stream-parser.test.ts"}, "/home/<USER>/workspace/server/__tests__/unit/test-utils.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/__tests__/unit/test-utils.test.ts"}, "/home/<USER>/workspace/server/__tests__/functional/ai-location-detection.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/__tests__/functional/ai-location-detection.test.ts"}, "/home/<USER>/workspace/server/__tests__/functional/ai-property-recommendations.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/__tests__/functional/ai-property-recommendations.test.ts"}, "/home/<USER>/workspace/server/__tests__/functional/auth-system.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/__tests__/functional/auth-system.test.ts"}, "/home/<USER>/workspace/server/__tests__/functional/booking-process.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/__tests__/functional/booking-process.test.ts"}, "/home/<USER>/workspace/server/__tests__/functional/chat-api.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/__tests__/functional/chat-api.test.ts"}, "/home/<USER>/workspace/server/__tests__/functional/chat-location.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/__tests__/functional/chat-location.test.ts"}, "/home/<USER>/workspace/server/__tests__/functional/search-functionality.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/__tests__/functional/search-functionality.test.ts"}, "/home/<USER>/workspace/migrations/0000_add_last_updated.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/0000_add_last_updated.sql"}, "/home/<USER>/workspace/migrations/0001_add_extensions.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/0001_add_extensions.sql"}, "/home/<USER>/workspace/migrations/001_initial_schema.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/001_initial_schema.sql"}, "/home/<USER>/workspace/migrations/002_ensure_auth_tables.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/002_ensure_auth_tables.sql"}, "/home/<USER>/workspace/migrations/003_analytics_and_user_profile.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/003_analytics_and_user_profile.sql"}, "/home/<USER>/workspace/migrations/004_fix_amenities_jsonb.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/004_fix_amenities_jsonb.sql"}, "/home/<USER>/workspace/migrations/005_add_unique_external_id.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/005_add_unique_external_id.sql"}, "/home/<USER>/workspace/migrations/006_make_rating_nullable.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/006_make_rating_nullable.sql"}, "/home/<USER>/workspace/migrations/007_repopulate_images_amenities.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/007_repopulate_images_amenities.sql"}, "/home/<USER>/workspace/migrations/008_force_refresh_empty_images.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/008_force_refresh_empty_images.sql"}, "/home/<USER>/workspace/migrations/add_currency_to_properties.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/add_currency_to_properties.sql"}, "/home/<USER>/workspace/db/index.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "db/index.ts"}, "/home/<USER>/workspace/client/src/main.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/main.tsx"}, "/home/<USER>/workspace/client/src/utils/amenityUtils.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/utils/amenityUtils.ts"}, "/home/<USER>/workspace/client/src/types/featured.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/types/featured.ts"}, "/home/<USER>/workspace/client/src/types/rate.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/types/rate.ts"}, "/home/<USER>/workspace/client/src/types/schema.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/types/schema.ts"}, "/home/<USER>/workspace/client/src/styles/animations.css": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/styles/animations.css"}, "/home/<USER>/workspace/client/src/pages/.gitkeep": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/.gitkeep"}, "/home/<USER>/workspace/client/src/pages/AIDemo.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/AIDemo.tsx"}, "/home/<USER>/workspace/client/src/pages/Admin.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Admin.tsx"}, "/home/<USER>/workspace/client/src/pages/Booking.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Booking.tsx"}, "/home/<USER>/workspace/client/src/pages/EnhancedAIChatDemo.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/EnhancedAIChatDemo.tsx"}, "/home/<USER>/workspace/client/src/pages/ProfilePage.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/ProfilePage.tsx"}, "/home/<USER>/workspace/client/src/pages/PropertyDetails.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/PropertyDetails.tsx"}, "/home/<USER>/workspace/client/src/pages/PropertyDetails_backup.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/PropertyDetails_backup.tsx"}, "/home/<USER>/workspace/client/src/pages/Reservation.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Reservation.tsx"}, "/home/<USER>/workspace/client/src/pages/Reservations.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Reservations.tsx"}, "/home/<USER>/workspace/client/src/pages/Results.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Results.tsx"}, "/home/<USER>/workspace/client/src/pages/Search.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Search.tsx"}, "/home/<USER>/workspace/client/src/pages/auth-page.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/auth-page.tsx"}, "/home/<USER>/workspace/client/src/pages/admin/Analytics.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/admin/Analytics.tsx"}, "/home/<USER>/workspace/client/src/pages/admin/Dashboard.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/admin/Dashboard.tsx"}, "/home/<USER>/workspace/client/src/pages/admin/PromoCodes.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/admin/PromoCodes.tsx"}, "/home/<USER>/workspace/client/src/pages/admin/Users.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/admin/Users.tsx"}, "/home/<USER>/workspace/client/src/lib/api.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/api.ts"}, "/home/<USER>/workspace/client/src/lib/googleMaps.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/googleMaps.ts"}, "/home/<USER>/workspace/client/src/lib/protected-route.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/protected-route.tsx"}, "/home/<USER>/workspace/client/src/lib/queryClient.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/queryClient.ts"}, "/home/<USER>/workspace/client/src/lib/session.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/session.ts"}, "/home/<USER>/workspace/client/src/lib/utils.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/utils.ts"}, "/home/<USER>/workspace/client/src/hooks/use-auth.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/use-auth.tsx"}, "/home/<USER>/workspace/client/src/hooks/use-google-places.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/use-google-places.ts"}, "/home/<USER>/workspace/client/src/hooks/use-search-history.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/use-search-history.tsx"}, "/home/<USER>/workspace/client/src/hooks/use-test-runner.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/use-test-runner.ts"}, "/home/<USER>/workspace/client/src/hooks/useSavedProperties.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/useSavedProperties.ts"}, "/home/<USER>/workspace/client/src/components/AdminNav.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/AdminNav.tsx"}, "/home/<USER>/workspace/client/src/components/AiChat.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/AiChat.tsx"}, "/home/<USER>/workspace/client/src/components/AiChat.tsx.bak": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/AiChat.tsx.bak"}, "/home/<USER>/workspace/client/src/components/AiChatEnhanced.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/AiChatEnhanced.tsx"}, "/home/<USER>/workspace/client/src/components/AiTravelCompanion.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/AiTravelCompanion.tsx"}, "/home/<USER>/workspace/client/src/components/BookingForm.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/BookingForm.tsx"}, "/home/<USER>/workspace/client/src/components/CompareDrawer.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/CompareDrawer.tsx"}, "/home/<USER>/workspace/client/src/components/EnhancedAIChat.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/EnhancedAIChat.tsx"}, "/home/<USER>/workspace/client/src/components/FeaturedProperties.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/FeaturedProperties.tsx"}, "/home/<USER>/workspace/client/src/components/Footer.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Footer.tsx"}, "/home/<USER>/workspace/client/src/components/Header.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Header.tsx"}, "/home/<USER>/workspace/client/src/components/ImageCarousel.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ImageCarousel.tsx"}, "/home/<USER>/workspace/client/src/components/ImageGalleryModal.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ImageGalleryModal.tsx"}, "/home/<USER>/workspace/client/src/components/InsightsPanel.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/InsightsPanel.tsx"}, "/home/<USER>/workspace/client/src/components/LocationMarker.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/LocationMarker.tsx"}, "/home/<USER>/workspace/client/src/components/Logo.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Logo.tsx"}, "/home/<USER>/workspace/client/src/components/Map.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Map.tsx"}, "/home/<USER>/workspace/client/src/components/MapFilterControl.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/MapFilterControl.tsx"}, "/home/<USER>/workspace/client/src/components/MapLoadingState.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/MapLoadingState.tsx"}, "/home/<USER>/workspace/client/src/components/MapZoomControl.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/MapZoomControl.tsx"}, "/home/<USER>/workspace/client/src/components/PropertyCard.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/PropertyCard.tsx"}, "/home/<USER>/workspace/client/src/components/PropertyMarkerPopup.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/PropertyMarkerPopup.tsx"}, "/home/<USER>/workspace/client/src/components/ProtectedAdminRoute.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ProtectedAdminRoute.tsx"}, "/home/<USER>/workspace/client/src/components/QuickActionButton.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/QuickActionButton.tsx"}, "/home/<USER>/workspace/client/src/components/RateDisplay.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RateDisplay.tsx"}, "/home/<USER>/workspace/client/src/components/RecentSearches.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RecentSearches.tsx"}, "/home/<USER>/workspace/client/src/components/RecommendationCarousel.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RecommendationCarousel.tsx"}, "/home/<USER>/workspace/client/src/components/RoomAccessibility.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RoomAccessibility.tsx"}, "/home/<USER>/workspace/client/src/components/RoomAmenityList.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RoomAmenityList.tsx"}, "/home/<USER>/workspace/client/src/components/RoomAvailabilityStatus.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RoomAvailabilityStatus.tsx"}, "/home/<USER>/workspace/client/src/components/RoomBedTypes.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RoomBedTypes.tsx"}, "/home/<USER>/workspace/client/src/components/RoomBookingSummary.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RoomBookingSummary.tsx"}, "/home/<USER>/workspace/client/src/components/RoomCancellationPolicy.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RoomCancellationPolicy.tsx"}, "/home/<USER>/workspace/client/src/components/RoomGuestReviews.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RoomGuestReviews.tsx"}, "/home/<USER>/workspace/client/src/components/RoomHousekeeping.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RoomHousekeeping.tsx"}, "/home/<USER>/workspace/client/src/components/RoomImages.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RoomImages.tsx"}, "/home/<USER>/workspace/client/src/components/RoomIncludedServices.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RoomIncludedServices.tsx"}, "/home/<USER>/workspace/client/src/components/RoomPackages.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RoomPackages.tsx"}, "/home/<USER>/workspace/client/src/components/RoomPromotions.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RoomPromotions.tsx"}, "/home/<USER>/workspace/client/src/components/RoomRateCalendar.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RoomRateCalendar.tsx"}, "/home/<USER>/workspace/client/src/components/RoomRateDetails.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RoomRateDetails.tsx"}, "/home/<USER>/workspace/client/src/components/RoomRules.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RoomRules.tsx"}, "/home/<USER>/workspace/client/src/components/RoomSpecialRequests.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RoomSpecialRequests.tsx"}, "/home/<USER>/workspace/client/src/components/RoomTaxesAndFees.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/RoomTaxesAndFees.tsx"}, "/home/<USER>/workspace/client/src/components/SearchForm.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/SearchForm.tsx"}, "/home/<USER>/workspace/client/src/components/theme-provider.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/theme-provider.tsx"}, "/home/<USER>/workspace/client/src/components/ui/avatar.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/avatar.tsx"}, "/home/<USER>/workspace/client/src/components/ui/badge.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/badge.tsx"}, "/home/<USER>/workspace/client/src/components/ui/command.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/command.tsx"}, "/home/<USER>/workspace/client/src/components/ui/container.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/container.tsx"}, "/home/<USER>/workspace/client/src/components/ui/date-range-picker.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/date-range-picker.tsx"}, "/home/<USER>/workspace/client/src/components/ui/dialog.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/dialog.tsx"}, "/home/<USER>/workspace/client/src/components/ui/loading-card.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/loading-card.tsx"}, "/home/<USER>/workspace/client/src/components/ui/shimmer-card.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/shimmer-card.tsx"}, "/home/<USER>/workspace/client/src/components/ui/textarea.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/textarea.tsx"}, "/home/<USER>/workspace/client/src/components/ui/visually-hidden.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/visually-hidden.tsx"}, "/home/<USER>/workspace/client/src/components/ui/styled/property-card.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/styled/property-card.tsx"}, "/home/<USER>/workspace/client/src/components/ui/styled/shimmer.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/styled/shimmer.tsx"}, "/home/<USER>/workspace/client/src/__tests__/AiChat.enhanced.test.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/AiChat.enhanced.test.tsx"}, "/home/<USER>/workspace/client/src/__tests__/AiChat.test.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/AiChat.test.tsx"}, "/home/<USER>/workspace/client/src/__tests__/ChatInteractionTestTool.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/ChatInteractionTestTool.tsx"}, "/home/<USER>/workspace/client/src/__tests__/Debug.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/Debug.tsx"}, "/home/<USER>/workspace/client/src/__tests__/LocationDebug.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/LocationDebug.tsx"}, "/home/<USER>/workspace/client/src/__tests__/LocationDebugTool.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/LocationDebugTool.tsx"}, "/home/<USER>/workspace/client/src/__tests__/TestHub.test.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/TestHub.test.tsx"}, "/home/<USER>/workspace/client/src/__tests__/TestLogOutput.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/TestLogOutput.tsx"}, "/home/<USER>/workspace/client/src/__tests__/TestResultsVisualizer.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/TestResultsVisualizer.tsx"}, "/home/<USER>/workspace/client/src/__tests__/TestRunControls.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/TestRunControls.tsx"}, "/home/<USER>/workspace/client/src/__tests__/ai-chat-test.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/ai-chat-test.test.ts"}, "/home/<USER>/workspace/client/src/__tests__/chat-interaction-test.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/chat-interaction-test.test.ts"}, "/home/<USER>/workspace/client/src/__tests__/e2e-chat.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/e2e-chat.test.ts"}, "/home/<USER>/workspace/client/src/__tests__/enhanced-ai-chat.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/enhanced-ai-chat.test.ts"}, "/home/<USER>/workspace/client/src/__tests__/location-test-utility.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/location-test-utility.test.ts"}, "/home/<USER>/workspace/client/src/__tests__/location-test.test.js": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/location-test.test.js"}, "/home/<USER>/workspace/client/src/__tests__/location-test.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/location-test.test.ts"}, "/home/<USER>/workspace/client/src/__tests__/manual-ai-chat-test.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/manual-ai-chat-test.test.ts"}, "/home/<USER>/workspace/client/src/__tests__/quick-chat.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/quick-chat.test.ts"}, "/home/<USER>/workspace/client/src/__tests__/testApi.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/__tests__/testApi.test.ts"}, "/home/<USER>/workspace/attached_assets/candidate results json.txt.json": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/candidate results json.txt.json"}, "/home/<USER>/workspace/attached_assets/hotel property availabilty json.txt.json": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/hotel property availabilty json.txt.json"}, "/home/<USER>/workspace/.vscode/launch.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".vscode/launch.json"}, "/home/<USER>/workspace/.upm/store.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".upm/store.json"}, "/home/<USER>/workspace/.local/state/replit/agent/repl_state.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/repl_state.json"}, "/home/<USER>/workspace/.local/share/gk/repoMapping.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/share/gk/repoMapping.json"}, "/home/<USER>/workspace/.config/.vscode-server/.cli.258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/.cli.258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3.log"}, "/home/<USER>/workspace/.config/.vscode-server/.cli.f1a4fb101478ce6ec82fe9627c43efbf9e98c813.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/.cli.f1a4fb101478ce6ec82fe9627c43efbf9e98c813.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/extensions.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/extensions.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.472.3/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.472.3/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/machineid": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/machineid"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250607T214721/ptyhost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250607T214721/ptyhost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250607T214721/remoteTelemetry.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250607T214721/remoteTelemetry.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250607T214721/remoteagent.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250607T214721/remoteagent.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250607T214721/exthost2/remoteExtHostTelemetry.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250607T214721/exthost2/remoteExtHostTelemetry.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250607T214721/exthost2/remoteexthost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250607T214721/exthost2/remoteexthost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250607T214721/exthost2/vscode.json-language-features/JSON Language Server.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250607T214721/exthost2/vscode.json-language-features/JSON Language Server.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250607T214721/exthost2/vscode.github/GitHub.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250607T214721/exthost2/vscode.github/GitHub.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250607T214721/exthost2/vscode.git/Git.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250607T214721/exthost2/vscode.git/Git.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250607T214721/exthost1/remoteExtHostTelemetry.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250607T214721/exthost1/remoteExtHostTelemetry.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250607T214721/exthost1/remoteexthost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250607T214721/exthost1/remoteexthost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250607T214721/exthost1/vscode.json-language-features/JSON Language Server.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250607T214721/exthost1/vscode.json-language-features/JSON Language Server.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250607T214721/exthost1/vscode.github/GitHub.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250607T214721/exthost1/vscode.github/GitHub.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250607T214721/exthost1/vscode.git/Git.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250607T214721/exthost1/vscode.git/Git.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250601T175958/ptyhost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250601T175958/ptyhost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250601T175958/remoteagent.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250601T175958/remoteagent.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250601T175958/exthost1/remoteexthost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250601T175958/exthost1/remoteexthost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250601T175958/exthost1/vscode.json-language-features/JSON Language Server.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250601T175958/exthost1/vscode.json-language-features/JSON Language Server.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250601T175958/exthost1/vscode.github/GitHub.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250601T175958/exthost1/vscode.github/GitHub.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250601T175958/exthost1/vscode.git/Git.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250601T175958/exthost1/vscode.git/Git.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/Augment-Memories": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/Augment-Memories"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/lru.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/lru.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/log.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/log.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/pid.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/pid.txt"}, "/home/<USER>/workspace/server/services/proactiveIntelligence.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/proactiveIntelligence.ts"}, "/home/<USER>/workspace/server/routes/enhancedChatRoutes.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/enhancedChatRoutes.ts"}, "/home/<USER>/workspace/db/run-migrations.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "db/run-migrations.ts"}, "/home/<USER>/workspace/db/schema.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "db/schema.ts"}, "/home/<USER>/workspace/db/migrations/001_add_rating_column.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "db/migrations/001_add_rating_column.sql"}, "/home/<USER>/workspace/db/migrations/002_add_review_count.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "db/migrations/002_add_review_count.sql"}, "/home/<USER>/workspace/db/migrations/003_add_missing_columns.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "db/migrations/003_add_missing_columns.sql"}, "/home/<USER>/workspace/client/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": "client/index.html"}, "/home/<USER>/workspace/client/src/App.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/App.tsx"}, "/home/<USER>/workspace/client/src/index.css": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/index.css"}, "/home/<USER>/workspace/client/src/utils/animationUtils.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/utils/animationUtils.ts"}, "/home/<USER>/workspace/client/src/utils/imageUtils.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/utils/imageUtils.ts"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/log.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/log.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/pid.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/pid.txt"}, "/home/<USER>/workspace/.cache/typescript/5.7/package-lock.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/typescript/5.7/package-lock.json"}, "/home/<USER>/workspace/.cache/typescript/5.7/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/typescript/5.7/package.json"}, "/home/<USER>/workspace/.cache/typescript/5.6/package-lock.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/typescript/5.6/package-lock.json"}, "/home/<USER>/workspace/.cache/typescript/5.6/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/typescript/5.6/package.json"}, "/home/<USER>/workspace/.cache/typescript/5.5/package-lock.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/typescript/5.5/package-lock.json"}, "/home/<USER>/workspace/.cache/typescript/5.5/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/typescript/5.5/package.json"}, "/home/<USER>/workspace/.cache/replit/nix/dotreplitenv.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/nix/dotreplitenv.json"}, "/home/<USER>/workspace/.cache/replit/nix/env.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/nix/env.json"}, "/home/<USER>/workspace/.cache/replit/env/latest": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/env/latest"}, "/home/<USER>/workspace/.cache/replit/env/latest.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/env/latest.json"}, "/home/<USER>/workspace/.cache/Microsoft/DeveloperTools/deviceid": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/Microsoft/DeveloperTools/deviceid"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/task-storage/tasks/fdae86cd-91fa-4af5-9a62-343ffb7fad2e": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/task-storage/tasks/fdae86cd-91fa-4af5-9a62-343ffb7fad2e"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250607T214721/exthost2/vscode.typescript-language-features/TypeScript.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250607T214721/exthost2/vscode.typescript-language-features/TypeScript.log"}, "/home/<USER>/workspace/server/services/paymentService.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/paymentService.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-959c7f6b-c81b-410b-b84d-c1b4905394eb.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-959c7f6b-c81b-410b-b84d-c1b4905394eb.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_paymentService.ts-1749333455801-c8d8e930-3e5f-458c-b717-a6b3e418186c.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_paymentService.ts-1749333455801-c8d8e930-3e5f-458c-b717-a6b3e418186c.json"}, "/home/<USER>/workspace/server/services/geocodingService.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/geocodingService.ts"}, "/home/<USER>/workspace/client/src/components/UnifiedAIChat.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/UnifiedAIChat.tsx"}, "/home/<USER>/workspace/ULTIMATE_BOOKING_ENGINE_ROADMAP.md": {"rootPath": "/home/<USER>/workspace", "relPath": "ULTIMATE_BOOKING_ENGINE_ROADMAP.md"}, "/home/<USER>/workspace/CRITICAL_FIXES_ACTION_PLAN.md": {"rootPath": "/home/<USER>/workspace", "relPath": "CRITICAL_FIXES_ACTION_PLAN.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/47de4c07/Bel9": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/47de4c07/Bel9"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/47de4c07/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/47de4c07/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/54a1791e/aOP2.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/54a1791e/aOP2.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/54a1791e/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/54a1791e/entries.json"}, "/home/<USER>/workspace/server/services/multiBedroomService.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/services/multiBedroomService.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/xS9D.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/xS9D.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/iARS.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/iARS.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/23SA.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/23SA.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/9gER.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/9gER.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/PH27.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/PH27.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/VU0D.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/VU0D.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/MDJ0.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/MDJ0.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/Yy7d.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/Yy7d.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/mf4Y.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/mf4Y.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/sldI.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/sldI.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/0wpY.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/0wpY.tsx"}, "/home/<USER>/workspace/tests/ai-travel-assistant.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/ai-travel-assistant.test.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250608T202120/ptyhost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250608T202120/ptyhost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250608T202120/remoteTelemetry.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250608T202120/remoteTelemetry.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250608T202120/remoteagent.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250608T202120/remoteagent.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250608T202120/exthost1/remoteExtHostTelemetry.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250608T202120/exthost1/remoteExtHostTelemetry.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250608T202120/exthost1/remoteexthost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250608T202120/exthost1/remoteexthost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250608T202120/exthost1/vscode.github/GitHub.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250608T202120/exthost1/vscode.github/GitHub.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250608T202120/exthost1/vscode.git/Git.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250608T202120/exthost1/vscode.git/Git.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250608T202120/exthost1/Augment.vscode-augment/Augment.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250608T202120/exthost1/Augment.vscode-augment/Augment.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_CRITICAL_FIXES_ACTION_PLAN.md-1749333599209-63b67e11-79a0-43b7-898b-9b005cc39c7a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_CRITICAL_FIXES_ACTION_PLAN.md-1749333599209-63b67e11-79a0-43b7-898b-9b005cc39c7a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_ULTIMATE_BOOKING_ENGINE_ROADMAP.md-1749333557147-75e549bf-3751-4b55-b1c3-ec8536fff861.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_ULTIMATE_BOOKING_ENGINE_ROADMAP.md-1749333557147-75e549bf-3751-4b55-b1c3-ec8536fff861.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749333526238-e3754a6a-ec08-43eb-90a5-004eaae2be82.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749333526238-e3754a6a-ec08-43eb-90a5-004eaae2be82.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334191766-c856f99d-4039-45c2-aad7-0512ceb78487.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334191766-c856f99d-4039-45c2-aad7-0512ceb78487.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334207493-daded031-0ed4-4a34-b0e0-dec02c22b239.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334207493-daded031-0ed4-4a34-b0e0-dec02c22b239.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334222043-3a8a0b04-c34f-4e56-b1b0-b5a50d5896fb.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334222043-3a8a0b04-c34f-4e56-b1b0-b5a50d5896fb.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334237524-23d0ad76-3515-494b-911e-c2e25564f7b4.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334237524-23d0ad76-3515-494b-911e-c2e25564f7b4.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334251796-791f41d2-cdb5-4a3e-9b33-707911fc1b17.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334251796-791f41d2-cdb5-4a3e-9b33-707911fc1b17.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334270414-238aa910-58af-491a-86b0-6fc3f9efcdd3.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334270414-238aa910-58af-491a-86b0-6fc3f9efcdd3.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334283975-92e6d690-6eba-4843-b2ad-b7dbcdac44e7.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334283975-92e6d690-6eba-4843-b2ad-b7dbcdac44e7.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334297014-d1bd7e3b-c968-4a44-9748-86282b9f026d.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334297014-d1bd7e3b-c968-4a44-9748-86282b9f026d.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334318892-149fa7b2-93d0-4254-8200-8beaef1d6c00.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334318892-149fa7b2-93d0-4254-8200-8beaef1d6c00.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334337103-006e6466-74a7-4cd0-80c4-199019d41ece.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749334337103-006e6466-74a7-4cd0-80c4-199019d41ece.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749335331212-73de782a-0527-4d23-aa93-5fac80bf59ff.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749335331212-73de782a-0527-4d23-aa93-5fac80bf59ff.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_geocodingService.ts-1749333482799-a5890507-8697-4495-9cf4-3272fb12bd44.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_geocodingService.ts-1749333482799-a5890507-8697-4495-9cf4-3272fb12bd44.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_multiBedroomService.ts-1749334178995-d9f7d36e-c4f6-41ac-9e1e-3bc45cd9c8fe.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_multiBedroomService.ts-1749334178995-d9f7d36e-c4f6-41ac-9e1e-3bc45cd9c8fe.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_openai.ts-0-cd7c9ba0-6439-4427-b720-a55937c6e27f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_openai.ts-0-cd7c9ba0-6439-4427-b720-a55937c6e27f.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_openai.ts-1749334118915-62ed81db-901a-46e7-a793-b96db87d7f7e.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_openai.ts-1749334118915-62ed81db-901a-46e7-a793-b96db87d7f7e.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_ai-travel-assistant.test.ts-1749335376207-3ab5a4a5-d357-4ae3-b1f6-01bb0ce8abff.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_ai-travel-assistant.test.ts-1749335376207-3ab5a4a5-d357-4ae3-b1f6-01bb0ce8abff.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/CachedProfilesData/__default__profile__/extensions.user.cache": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/CachedProfilesData/__default__profile__/extensions.user.cache"}, "/home/<USER>/workspace/tests/multi-bedroom-service.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/multi-bedroom-service.test.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_multi-bedroom-service.test.ts-1749414629752-ad05e9d9-6ede-4f13-8b9f-d99181d15047.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_multi-bedroom-service.test.ts-1749414629752-ad05e9d9-6ede-4f13-8b9f-d99181d15047.json"}, "/home/<USER>/workspace/tests/unified-ai-chat.test.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/unified-ai-chat.test.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_unified-ai-chat.test.tsx-1749414669269-88f656be-a8ee-42da-872b-9cf196865981.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_unified-ai-chat.test.tsx-1749414669269-88f656be-a8ee-42da-872b-9cf196865981.json"}, "/home/<USER>/workspace/scripts/run-comprehensive-tests.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "scripts/run-comprehensive-tests.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_scripts_run-comprehensive-tests.ts-1749414702094-d7cc2fd2-9bd5-4d5d-a1d3-2a9a65b00101.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_scripts_run-comprehensive-tests.ts-1749414702094-d7cc2fd2-9bd5-4d5d-a1d3-2a9a65b00101.json"}, "/home/<USER>/workspace/ULTIMATE_AI_TRAVEL_ASSISTANT_IMPLEMENTATION.md": {"rootPath": "/home/<USER>/workspace", "relPath": "ULTIMATE_AI_TRAVEL_ASSISTANT_IMPLEMENTATION.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_ULTIMATE_AI_TRAVEL_ASSISTANT_IMPLEMENTATION.md-1749414738200-260473dc-36c9-4c15-8c58-9a3ed5b34f00.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_ULTIMATE_AI_TRAVEL_ASSISTANT_IMPLEMENTATION.md-1749414738200-260473dc-36c9-4c15-8c58-9a3ed5b34f00.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/47de4c07/ehCf": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/47de4c07/ehCf"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5488a806/26Vp.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5488a806/26Vp.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5488a806/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5488a806/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_travsrv.ts-0-31102ad7-a5b2-4e11-a160-cc6e66a06475.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_travsrv.ts-0-31102ad7-a5b2-4e11-a160-cc6e66a06475.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_travsrv.ts-1749415064508-51cdbd83-3757-470f-8cdc-6a3d8bd8b665.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_travsrv.ts-1749415064508-51cdbd83-3757-470f-8cdc-6a3d8bd8b665.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5488a806/wTW3.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5488a806/wTW3.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_travsrv.ts-1749415076444-8fed0a42-dfa6-4476-a2ec-0bca23a0b79e.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_travsrv.ts-1749415076444-8fed0a42-dfa6-4476-a2ec-0bca23a0b79e.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5e12e58f/9KLI.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5e12e58f/9KLI.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5e12e58f/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5e12e58f/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_routes.ts-0-f1b01ca2-1b52-4283-b66c-a78feb6c6092.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_routes.ts-0-f1b01ca2-1b52-4283-b66c-a78feb6c6092.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_routes.ts-1749415104258-65bdbeee-dab9-41ce-b39e-d9bcdbbbeb61.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_routes.ts-1749415104258-65bdbeee-dab9-41ce-b39e-d9bcdbbbeb61.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5e12e58f/5Hfd.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5e12e58f/5Hfd.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_routes.ts-1749415123416-2d962396-46a1-4604-9743-bba2f326dc46.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_routes.ts-1749415123416-2d962396-46a1-4604-9743-bba2f326dc46.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-75c5484b/EhrN.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-75c5484b/EhrN.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-75c5484b/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-75c5484b/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Search.tsx-0-0decaee4-cae3-4859-93ba-73ba7402ced3.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Search.tsx-0-0decaee4-cae3-4859-93ba-73ba7402ced3.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Search.tsx-1749415165801-db553df9-3434-44b1-b515-b0b2d5d4ac94.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Search.tsx-1749415165801-db553df9-3434-44b1-b515-b0b2d5d4ac94.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-75c5484b/k7l4.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-75c5484b/k7l4.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Search.tsx-1749415184745-3248b0d0-72d1-437d-a186-a06d72a426c3.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Search.tsx-1749415184745-3248b0d0-72d1-437d-a186-a06d72a426c3.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3f165b01/oWNG.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3f165b01/oWNG.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3f165b01/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3f165b01/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-0-83a981cf-74d8-4379-b0a9-353883388438.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-0-83a981cf-74d8-4379-b0a9-353883388438.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749415195622-2b06b170-31c4-4e58-87e7-1f0726868f74.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749415195622-2b06b170-31c4-4e58-87e7-1f0726868f74.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3f165b01/Hbya.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3f165b01/Hbya.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749415214524-8c1a335c-659e-4c54-8366-1fc03a637c2e.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749415214524-8c1a335c-659e-4c54-8366-1fc03a637c2e.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5e12e58f/YtVB.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5e12e58f/YtVB.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_routes.ts-1749415239994-d9255cb2-b536-4ee5-914a-400333fbe006.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_routes.ts-1749415239994-d9255cb2-b536-4ee5-914a-400333fbe006.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5e12e58f/n5IM.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5e12e58f/n5IM.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_routes.ts-1749415253528-71b8d419-ba54-44d4-a5d3-7c638ccefd29.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_routes.ts-1749415253528-71b8d419-ba54-44d4-a5d3-7c638ccefd29.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiChat.tsx.bak-0-d6bcdb90-30d3-4f4d-98a8-540bc1a8cd13.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiChat.tsx.bak-0-d6bcdb90-30d3-4f4d-98a8-540bc1a8cd13.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiChat.tsx.bak-1749415265486-9486f569-941c-473e-a1ee-8d5d2be6d625.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiChat.tsx.bak-1749415265486-9486f569-941c-473e-a1ee-8d5d2be6d625.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiChat.tsx.bak-1749415265847-1f80b425-acc6-4526-912f-5382118b2fc3.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiChat.tsx.bak-1749415265847-1f80b425-acc6-4526-912f-5382118b2fc3.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiChatEnhanced.tsx-0-e6da9cf8-fd2a-43e4-b052-a3b2db97cb1b.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiChatEnhanced.tsx-0-e6da9cf8-fd2a-43e4-b052-a3b2db97cb1b.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiChatEnhanced.tsx-1749415266300-edd898df-0d63-4c7a-b038-c247ee233a21.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiChatEnhanced.tsx-1749415266300-edd898df-0d63-4c7a-b038-c247ee233a21.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_EnhancedAIChat.tsx-0-64c4c4ff-753c-4dfb-8190-65fcba791673.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_EnhancedAIChat.tsx-0-64c4c4ff-753c-4dfb-8190-65fcba791673.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_EnhancedAIChat.tsx-1749415266719-0932af4b-8850-478b-9e30-7b2ea076917b.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_EnhancedAIChat.tsx-1749415266719-0932af4b-8850-478b-9e30-7b2ea076917b.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/58f9e426/SsK4.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/58f9e426/SsK4.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/58f9e426/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/58f9e426/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_EnhancedAIChatDemo.tsx-0-48cf669b-57d9-446a-b8ed-4a36872c63dd.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_EnhancedAIChatDemo.tsx-0-48cf669b-57d9-446a-b8ed-4a36872c63dd.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_EnhancedAIChatDemo.tsx-1749415276952-1bb3948d-bbe1-4fe1-884c-14aa6c6a535f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_EnhancedAIChatDemo.tsx-1749415276952-1bb3948d-bbe1-4fe1-884c-14aa6c6a535f.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/58f9e426/Mr26.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/58f9e426/Mr26.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_EnhancedAIChatDemo.tsx-1749415293809-cb390475-7b1e-4d4d-87fd-c9ac9b65491a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_EnhancedAIChatDemo.tsx-1749415293809-cb390475-7b1e-4d4d-87fd-c9ac9b65491a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3d2b5d68/K73h.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3d2b5d68/K73h.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3d2b5d68/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3d2b5d68/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_AIDemo.tsx-0-b1ad659f-325e-4b81-a8f1-01fe1a8ee83d.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_AIDemo.tsx-0-b1ad659f-325e-4b81-a8f1-01fe1a8ee83d.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_AIDemo.tsx-1749415305639-46e3a880-0629-45b0-84c7-d91d984f1f9c.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_AIDemo.tsx-1749415305639-46e3a880-0629-45b0-84c7-d91d984f1f9c.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3d2b5d68/uamJ.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3d2b5d68/uamJ.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_AIDemo.tsx-1749415324621-51954503-effb-4341-9df0-1aa52b7b3889.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_AIDemo.tsx-1749415324621-51954503-effb-4341-9df0-1aa52b7b3889.json"}, "/home/<USER>/workspace/tests/critical-functionality.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/critical-functionality.test.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_critical-functionality.test.ts-1749415359278-45f0a8e0-6605-48d7-9f7e-537ad2dcfe30.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_critical-functionality.test.ts-1749415359278-45f0a8e0-6605-48d7-9f7e-537ad2dcfe30.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7d8434b8/1Bab.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7d8434b8/1Bab.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7d8434b8/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7d8434b8/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_package.json-0-e36331ca-fcc0-4f3e-aeda-82c0f647bead.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_package.json-0-e36331ca-fcc0-4f3e-aeda-82c0f647bead.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_package.json-1749415380284-615f3767-89ee-4555-83c6-fd328fb6fda4.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_package.json-1749415380284-615f3767-89ee-4555-83c6-fd328fb6fda4.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/738d4b21/mZBM.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/738d4b21/mZBM.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/738d4b21/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/738d4b21/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_paymentService.ts-1749415462122-471efbd3-b6d1-4eab-ab2c-4fea5dd51d7b.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_paymentService.ts-1749415462122-471efbd3-b6d1-4eab-ab2c-4fea5dd51d7b.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-59b04428/LE9J.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-59b04428/LE9J.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-59b04428/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-59b04428/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_multiBedroomService.ts-1749415480541-fabbf0d2-2c48-4de6-b458-5550509d5584.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_multiBedroomService.ts-1749415480541-fabbf0d2-2c48-4de6-b458-5550509d5584.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/DDDC.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/DDDC.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749415492907-080e8917-1a44-4282-bf67-5d3b9f6bb7a5.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749415492907-080e8917-1a44-4282-bf67-5d3b9f6bb7a5.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3f165b01/Ehnm.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3f165b01/Ehnm.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749415505847-1dc06b96-5f23-4a4a-8347-91bafb271ed5.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749415505847-1dc06b96-5f23-4a4a-8347-91bafb271ed5.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5488a806/vgKA.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5488a806/vgKA.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_travsrv.ts-1749415583602-54c169db-9395-4b41-ac54-7d78b977c2a8.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_travsrv.ts-1749415583602-54c169db-9395-4b41-ac54-7d78b977c2a8.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3bc52e10/vkSM.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3bc52e10/vkSM.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3bc52e10/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3bc52e10/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_critical-functionality.test.ts-1749415599021-2dd8b439-e298-4ccf-94d3-cb9f43d7536a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_critical-functionality.test.ts-1749415599021-2dd8b439-e298-4ccf-94d3-cb9f43d7536a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3bc52e10/4eKq.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3bc52e10/4eKq.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_critical-functionality.test.ts-1749415621640-b922b5dd-a6f2-43d3-a759-5e2ba8605914.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_critical-functionality.test.ts-1749415621640-b922b5dd-a6f2-43d3-a759-5e2ba8605914.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/47de4c07/xSxR": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/47de4c07/xSxR"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-59b04428/bCU0.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-59b04428/bCU0.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_multiBedroomService.ts-1749415916487-3c05c501-c950-4ac5-83fd-7d4d6c60a680.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_multiBedroomService.ts-1749415916487-3c05c501-c950-4ac5-83fd-7d4d6c60a680.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3bc52e10/5D01.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3bc52e10/5D01.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_critical-functionality.test.ts-1749415940808-7184ce47-e7d6-46b8-b9ec-54966f5dea65.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_critical-functionality.test.ts-1749415940808-7184ce47-e7d6-46b8-b9ec-54966f5dea65.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3bc52e10/dP97.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3bc52e10/dP97.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_critical-functionality.test.ts-1749415989188-fc9e7111-0e2c-40d4-9fb9-8a51529b5144.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_critical-functionality.test.ts-1749415989188-fc9e7111-0e2c-40d4-9fb9-8a51529b5144.json"}, "/home/<USER>/workspace/IMPLEMENTATION_COMPLETE.md": {"rootPath": "/home/<USER>/workspace", "relPath": "IMPLEMENTATION_COMPLETE.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_IMPLEMENTATION_COMPLETE.md-1749416050515-b695d9bc-b4f8-4c25-8bb2-3740d88b36a7.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_IMPLEMENTATION_COMPLETE.md-1749416050515-b695d9bc-b4f8-4c25-8bb2-3740d88b36a7.json"}, "/home/<USER>/workspace/package-lock.json": {"rootPath": "/home/<USER>/workspace", "relPath": "package-lock.json"}, "/home/<USER>/workspace/.local/state/replit/agent/.agent_state_16dfae4505c84a04da88e2a9c7e5d9075a4b1921.bin": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/.agent_state_16dfae4505c84a04da88e2a9c7e5d9075a4b1921.bin"}, "/home/<USER>/workspace/.local/state/replit/agent/repl_state.bin": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/repl_state.bin"}, "/home/<USER>/workspace/.local/state/replit/agent/.agent_state_15be4c48443c5e028be3d2fcdf762753d65712fd.bin": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/.agent_state_15be4c48443c5e028be3d2fcdf762753d65712fd.bin"}, "/home/<USER>/workspace/.local/state/replit/agent/.agent_state_main.bin": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/.agent_state_main.bin"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-2dd10a47/75H0.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-2dd10a47/75H0.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-2dd10a47/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-2dd10a47/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_SearchForm.tsx-0-ec3f189d-253f-410e-82f3-13f1f4c1383f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_SearchForm.tsx-0-ec3f189d-253f-410e-82f3-13f1f4c1383f.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_SearchForm.tsx-1749418311216-6dee6e35-d86a-4329-ab64-654fbdf3183a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_SearchForm.tsx-1749418311216-6dee6e35-d86a-4329-ab64-654fbdf3183a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-2dd10a47/khy6.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-2dd10a47/khy6.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_SearchForm.tsx-1749418322523-ebec551e-d6a8-4a69-8910-4bea78f7d04e.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_SearchForm.tsx-1749418322523-ebec551e-d6a8-4a69-8910-4bea78f7d04e.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-2dd10a47/DaNG.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-2dd10a47/DaNG.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_SearchForm.tsx-1749418333652-5927e6f6-d676-4cef-9be6-f0ee43052d8e.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_SearchForm.tsx-1749418333652-5927e6f6-d676-4cef-9be6-f0ee43052d8e.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-2dd10a47/twcz.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-2dd10a47/twcz.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_SearchForm.tsx-1749418347744-f13731c5-f8a6-4f73-9e55-8cd0210c2fb6.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_SearchForm.tsx-1749418347744-f13731c5-f8a6-4f73-9e55-8cd0210c2fb6.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3f165b01/dIE6.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3f165b01/dIE6.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418360447-2a433a7e-ed86-4c7e-a130-79511708a11f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418360447-2a433a7e-ed86-4c7e-a130-79511708a11f.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-2dd10a47/YvVz.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-2dd10a47/YvVz.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_SearchForm.tsx-1749418383350-48bb758c-40a0-46b2-8bb5-e37581a5e162.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_SearchForm.tsx-1749418383350-48bb758c-40a0-46b2-8bb5-e37581a5e162.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-2dd10a47/pTIL.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-2dd10a47/pTIL.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_SearchForm.tsx-1749418395264-b01d8dae-2efc-46d4-9993-4b8fe1979e7f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_SearchForm.tsx-1749418395264-b01d8dae-2efc-46d4-9993-4b8fe1979e7f.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-2dd10a47/nLiV.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-2dd10a47/nLiV.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_SearchForm.tsx-1749418408824-7add91af-a425-4bf6-ae39-dd30a3d95d1b.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_SearchForm.tsx-1749418408824-7add91af-a425-4bf6-ae39-dd30a3d95d1b.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-2dd10a47/XT47.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-2dd10a47/XT47.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_SearchForm.tsx-1749418422871-ff729826-4e6e-41e0-b8a7-8e0536646258.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_SearchForm.tsx-1749418422871-ff729826-4e6e-41e0-b8a7-8e0536646258.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3f165b01/1VrG.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3f165b01/1VrG.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418440464-a2d56542-c073-45eb-a368-1c618464a7aa.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418440464-a2d56542-c073-45eb-a368-1c618464a7aa.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3f165b01/LabA.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3f165b01/LabA.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418457895-0c7e84e3-b4ce-40ac-8aa2-640235c33fac.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418457895-0c7e84e3-b4ce-40ac-8aa2-640235c33fac.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3f165b01/ivbU.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3f165b01/ivbU.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418488508-7e1da0c6-567a-42d6-bc26-18757f0036d2.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418488508-7e1da0c6-567a-42d6-bc26-18757f0036d2.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3f165b01/IL1j.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3f165b01/IL1j.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418502328-793c3f5d-6074-49ee-a2ac-d73d1e443731.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418502328-793c3f5d-6074-49ee-a2ac-d73d1e443731.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3f165b01/Sfyc.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3f165b01/Sfyc.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418521177-1df3112d-220d-4457-a034-714f4763ef51.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418521177-1df3112d-220d-4457-a034-714f4763ef51.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3f165b01/phMf.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3f165b01/phMf.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418535146-dfc947c9-a73a-4dd4-a8c1-9e24efad009a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418535146-dfc947c9-a73a-4dd4-a8c1-9e24efad009a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3f165b01/zvSS.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3f165b01/zvSS.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418549222-76cff685-94be-4a4a-ae82-673198eead76.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418549222-76cff685-94be-4a4a-ae82-673198eead76.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3f165b01/pSKh.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3f165b01/pSKh.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418567706-808625ea-2ead-4662-a760-0f935fc17451.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418567706-808625ea-2ead-4662-a760-0f935fc17451.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3f165b01/UMKU.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3f165b01/UMKU.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418581842-2729ea89-a7ad-497a-8668-233bcadfc980.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418581842-2729ea89-a7ad-497a-8668-233bcadfc980.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3f165b01/57Cw.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3f165b01/57Cw.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418594093-04371532-5236-4a02-ac2d-2d01da8b5cb0.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418594093-04371532-5236-4a02-ac2d-2d01da8b5cb0.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3f165b01/b6f7.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3f165b01/b6f7.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418608946-b18ad8e1-379d-4a75-81dd-d433d0c18be3.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_pages_Results.tsx-1749418608946-b18ad8e1-379d-4a75-81dd-d433d0c18be3.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3bc52e10/Y57W.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3bc52e10/Y57W.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_critical-functionality.test.ts-1749418643295-b7d720a1-be13-43ac-8208-07de1c76bb1a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_critical-functionality.test.ts-1749418643295-b7d720a1-be13-43ac-8208-07de1c76bb1a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/47de4c07/aXTs": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/47de4c07/aXTs"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiChat.tsx-0-ca5f4787-630d-46c7-a50e-99da7ed02a18.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiChat.tsx-0-ca5f4787-630d-46c7-a50e-99da7ed02a18.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiChat.tsx-1749419166572-658e7956-4d2f-4796-82bf-3b294fcd22de.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiChat.tsx-1749419166572-658e7956-4d2f-4796-82bf-3b294fcd22de.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiChat.tsx-1749419166912-82b3edec-de86-4bd8-ab11-b1371233d671.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiChat.tsx-1749419166912-82b3edec-de86-4bd8-ab11-b1371233d671.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiTravelCompanion.tsx-0-b235d32c-a695-472b-850c-e522716d4632.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiTravelCompanion.tsx-0-b235d32c-a695-472b-850c-e522716d4632.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiTravelCompanion.tsx-1749419167395-ae7f9f25-78fc-4719-8902-faa3d39b95b5.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_AiTravelCompanion.tsx-1749419167395-ae7f9f25-78fc-4719-8902-faa3d39b95b5.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_routes_enhancedChatRoutes.ts-0-02e0bf3d-0de6-49db-8bbf-6c5c2f436e63.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_routes_enhancedChatRoutes.ts-0-02e0bf3d-0de6-49db-8bbf-6c5c2f436e63.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_routes_enhancedChatRoutes.ts-1749419167806-51431fe6-fc74-43f1-bb8c-ba2ccadfc86f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_routes_enhancedChatRoutes.ts-1749419167806-51431fe6-fc74-43f1-bb8c-ba2ccadfc86f.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_chatTestingService.ts-0-f8a5a9db-d53a-4d94-80d1-e98a8c574b05.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_chatTestingService.ts-0-f8a5a9db-d53a-4d94-80d1-e98a8c574b05.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_chatTestingService.ts-1749419168185-b99300c8-1710-4690-9579-c90d8718f0fa.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_chatTestingService.ts-1749419168185-b99300c8-1710-4690-9579-c90d8718f0fa.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_test-chat.ts-0-13457bb6-ccbe-4082-9a96-c80aeec69a41.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_test-chat.ts-0-13457bb6-ccbe-4082-9a96-c80aeec69a41.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_test-chat.ts-1749419168687-c7b37ec4-83da-4781-abe9-7402bb07595c.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_test-chat.ts-1749419168687-c7b37ec4-83da-4781-abe9-7402bb07595c.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3bc52e10/Iu5f.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3bc52e10/Iu5f.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_critical-functionality.test.ts-1749419192875-d44d5456-2c40-4b9e-8984-456e2b8b80f8.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_critical-functionality.test.ts-1749419192875-d44d5456-2c40-4b9e-8984-456e2b8b80f8.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src___tests___AiChat.test.tsx-0-a29af231-df80-48cd-b2c7-65cfc37b4afe.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src___tests___AiChat.test.tsx-0-a29af231-df80-48cd-b2c7-65cfc37b4afe.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src___tests___AiChat.test.tsx-1749419205722-8e2abc7c-b2c2-4b00-bd23-a2fda799b50a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src___tests___AiChat.test.tsx-1749419205722-8e2abc7c-b2c2-4b00-bd23-a2fda799b50a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src___tests___AiChat.test.tsx-1749419206142-f7cd48ef-37b2-4bec-8360-4fa029c23920.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src___tests___AiChat.test.tsx-1749419206142-f7cd48ef-37b2-4bec-8360-4fa029c23920.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/78B8.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/78B8.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749419223078-2b7d1da2-de21-48c8-8265-28a3edaef754.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749419223078-2b7d1da2-de21-48c8-8265-28a3edaef754.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/9WdO.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/9WdO.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749419245051-d15cd6fb-6a1d-4d67-8e10-d1493fe8fe7f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749419245051-d15cd6fb-6a1d-4d67-8e10-d1493fe8fe7f.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/adzb.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/adzb.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749419274054-661e37bb-07f0-412e-bf61-a017d1fb3197.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749419274054-661e37bb-07f0-412e-bf61-a017d1fb3197.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/g1WK.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/g1WK.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749419292426-6bdaf73b-ce9e-411e-a58c-21f2eedf4014.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749419292426-6bdaf73b-ce9e-411e-a58c-21f2eedf4014.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/5ETQ.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/5ETQ.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749419362163-33c3c5de-4b80-4121-bd5e-12313cd3d1bf.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749419362163-33c3c5de-4b80-4121-bd5e-12313cd3d1bf.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/W23l.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/W23l.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749419387624-3ee942ab-236d-47ee-98e4-29842e8d4179.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749419387624-3ee942ab-236d-47ee-98e4-29842e8d4179.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/47de4c07/3phZ": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/47de4c07/3phZ"}, "/home/<USER>/workspace/tests/user-experience-validation.test.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/user-experience-validation.test.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_user-experience-validation.test.ts-1749419644615-9a185e74-25be-4666-bf8e-41e6d4e97cfc.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_user-experience-validation.test.ts-1749419644615-9a185e74-25be-4666-bf8e-41e6d4e97cfc.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4d24040d/vtgE.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4d24040d/vtgE.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4d24040d/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4d24040d/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_user-experience-validation.test.ts-1749419679585-69fefd92-6fab-47bd-9a3c-ace274b7470f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_user-experience-validation.test.ts-1749419679585-69fefd92-6fab-47bd-9a3c-ace274b7470f.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4d24040d/Cu1X.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4d24040d/Cu1X.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_user-experience-validation.test.ts-1749419723302-8160f085-ce6a-482e-9ca4-b34d384528e9.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_user-experience-validation.test.ts-1749419723302-8160f085-ce6a-482e-9ca4-b34d384528e9.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4d24040d/4E20.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4d24040d/4E20.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_user-experience-validation.test.ts-1749419734565-9f0bb9c5-bbf2-4364-8dd8-63c448fba8d3.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_tests_user-experience-validation.test.ts-1749419734565-9f0bb9c5-bbf2-4364-8dd8-63c448fba8d3.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/47de4c07/jrj5": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/47de4c07/jrj5"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/e93ad388e4ad7bbbc55ef33dcee51d49b0e3b266c41495eae931edc12bdc139b.png": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/e93ad388e4ad7bbbc55ef33dcee51d49b0e3b266c41495eae931edc12bdc139b.png"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/pYwe.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/pYwe.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749420110803-ee7beeb1-1f5b-48ee-b1e7-0b2b5f897bed.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749420110803-ee7beeb1-1f5b-48ee-b1e7-0b2b5f897bed.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/wctH.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/wctH.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749420146161-2372ea70-0c2d-4e16-8b82-f8e76787a56c.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749420146161-2372ea70-0c2d-4e16-8b82-f8e76787a56c.json"}, "/home/<USER>/workspace/test-ai-location.js": {"rootPath": "/home/<USER>/workspace", "relPath": "test-ai-location.js"}, "/home/<USER>/workspace/.cache/typescript/5.8/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/typescript/5.8/package.json"}, "/home/<USER>/workspace/.cache/typescript/5.8/package-lock.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/typescript/5.8/package-lock.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_test-ai-location.js-1749420220753-b50f6190-72bb-4d94-86f2-6ed750f1fc05.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_test-ai-location.js-1749420220753-b50f6190-72bb-4d94-86f2-6ed750f1fc05.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-33dde620/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-33dde620/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_test-ai-location.js-1749420238061-9b16fb70-efd6-430e-b35c-ffdbea8d1207.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_test-ai-location.js-1749420238061-9b16fb70-efd6-430e-b35c-ffdbea8d1207.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-33dde620/OMRX.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-33dde620/OMRX.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/54a1791e/vsmU.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/54a1791e/vsmU.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_openai.ts-1749420302820-abec2d85-697d-4737-bffe-1fdf8ce6ac0b.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_server_services_openai.ts-1749420302820-abec2d85-697d-4737-bffe-1fdf8ce6ac0b.json"}, "/home/<USER>/workspace/test-location-ui.html": {"rootPath": "/home/<USER>/workspace", "relPath": "test-location-ui.html"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250608T202120/exthost1/output_logging_20250608T202125/1-HTML Language Server.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250608T202120/exthost1/output_logging_20250608T202125/1-HTML Language Server.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_test-location-ui.html-1749420399422-af532286-3d24-4581-95f3-9edf23d3b045.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_test-location-ui.html-1749420399422-af532286-3d24-4581-95f3-9edf23d3b045.json"}, "/home/<USER>/workspace/comprehensive-user-test.html": {"rootPath": "/home/<USER>/workspace", "relPath": "comprehensive-user-test.html"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_comprehensive-user-test.html-1749420787036-721ddd9b-9054-4d32-b718-d4a9ccf41f67.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_comprehensive-user-test.html-1749420787036-721ddd9b-9054-4d32-b718-d4a9ccf41f67.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/vaNw.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/vaNw.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749420854241-f2207651-0acb-4358-ad67-ba0d40507342.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749420854241-f2207651-0acb-4358-ad67-ba0d40507342.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/Hb33.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/Hb33.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749420886326-b42d63a1-aab0-4a03-8659-15988e73765c.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749420886326-b42d63a1-aab0-4a03-8659-15988e73765c.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/u3h7.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/u3h7.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749420902585-a7e846d8-f824-4c4f-ad95-282d02986bb2.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749420902585-a7e846d8-f824-4c4f-ad95-282d02986bb2.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/KOLk.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/KOLk.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749420915648-660a3bbd-fff6-4a5c-a2e9-11ad271458ab.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749420915648-660a3bbd-fff6-4a5c-a2e9-11ad271458ab.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/MGQK.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/MGQK.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749420944403-d6b72e25-6ffd-4196-9df7-35e94eb7185a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749420944403-d6b72e25-6ffd-4196-9df7-35e94eb7185a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2e0bac1b/Xtsm.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2e0bac1b/Xtsm.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749420971631-b091516d-f44f-4849-89ed-641dec97a3f1.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_client_src_components_UnifiedAIChat.tsx-1749420971631-b091516d-f44f-4849-89ed-641dec97a3f1.json"}, "/home/<USER>/workspace/final-validation-test.html": {"rootPath": "/home/<USER>/workspace", "relPath": "final-validation-test.html"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_final-validation-test.html-1749421036267-2e9d1f79-e10a-45a6-b9ac-967eba089634.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents/959c7f6b-c81b-410b-b84d-c1b4905394eb/document-_home_runner_workspace_final-validation-test.html-1749421036267-2e9d1f79-e10a-45a6-b9ac-967eba089634.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/47de4c07/DZUa": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/47de4c07/DZUa"}}