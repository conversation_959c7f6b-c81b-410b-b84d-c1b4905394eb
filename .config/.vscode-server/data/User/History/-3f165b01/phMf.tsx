import { useLocation } from "wouter";
import { useInfiniteQuery, useQueries, useQueryClient } from "@tanstack/react-query";
import { useInView } from "framer-motion";
import { useRef, useEffect, useState, useMemo, useCallback } from "react";
import PropertyCard, { PropertyCardProps } from "../components/PropertyCard.jsx";
import Map, { MapProps } from "../components/Map.jsx";
import { Card, CardContent } from "../components/ui/card.jsx";
import { Skeleton } from "../components/ui/skeleton.jsx";
import { AlertCircle, Loader2, Map as MapIcon, X, Bot, Star, Hotel, DollarSign, SlidersHorizontal } from "lucide-react";
import { PropertyLoadingCard } from "../components/ui/loading-card";
import { Input } from "../components/ui/input.jsx";
import { Slider } from "../components/ui/slider.jsx";
import { Label } from "../components/ui/label.jsx";
import SearchForm from "../components/SearchForm.jsx";
import { Property, PropertyType, PropertyWithRates, PropertyImage, Room } from "../types/schema.js";
import { Rate } from "../types/rate.js";
import { Button } from "../components/ui/button.jsx";
import { Checkbox } from "../components/ui/checkbox.jsx";
import { ScrollArea } from "../components/ui/scroll-area.jsx";
import styled from '@emotion/styled';
import { Dialog, DialogContent, DialogTitle, DialogDescription } from "../components/ui/dialog.jsx";
import { cn } from "../lib/utils.js";
import CompareDrawer from "../components/CompareDrawer.jsx";
import { VisuallyHidden } from "@/components/ui/visually-hidden";
import { useToast } from "@/hooks/use-toast";
import UnifiedAIChat from "@/components/UnifiedAIChat";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { getSessionId } from "@/lib/session";
import InsightsPanel from "@/components/InsightsPanel";

interface SearchResponse {
  properties: Property[];
  total: number;
  currentPage: number;
  totalPages: number;
  explanation?: string;
  searchId: string;
  timing: {
    total: number;
  };
}

interface RoomRate {
  rate: number;
  totalAmount?: number;
  originalAmount?: number;
  currency: string;
  Total?: {
    '@Amount': string;
    '@Discount'?: string;
    '@IncludesBookingFee': string;
  };
  totalDiscount?: number;
  retailDiscountPercent?: number;
  totalComparableRetailDiscount?: number;
}

interface RatePlan {
  code: string;
  description: string;
  rooms: {
    [key: string]: RoomRate;
  };
}

interface AvailabilityResponse {
  ratePlans: {
    [key: string]: RatePlan;
  };
}

interface SearchParams {
  lat: number;
  lng: number;
  locationName: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  rooms: number;
  radius?: number;
}

// Custom hook for property search
function usePropertySearch(searchParams: SearchParams) {
  return useInfiniteQuery<SearchResponse>({
    queryKey: ['propertySearch', searchParams],
    queryFn: async ({ pageParam = 1 }) => {
      // Validate required parameters
      if (!searchParams.lat || !searchParams.lng || !searchParams.checkIn || !searchParams.checkOut) {
        throw new Error('Missing required search parameters: lat, lng, checkIn, and checkOut are required');
      }

      const params = new URLSearchParams({
        lat: String(searchParams.lat),
        lng: String(searchParams.lng),
        locationName: searchParams.locationName || '',
        checkIn: searchParams.checkIn,
        checkOut: searchParams.checkOut,
        guests: searchParams.guests.toString(),
        rooms: searchParams.rooms.toString(),
        page: String(pageParam),
        pageSize: '10',
        radius: searchParams.radius?.toString() || '10'
      });

      console.log('Fetching properties with params:', Object.fromEntries(params.entries()));

      try {
        const response = await fetch(`/api/properties/search?${params}`);
        const responseText = await response.text();

        if (!response.ok) {
          throw new Error(`Failed to fetch properties: ${response.status} ${response.statusText}\n${responseText}`);
        }

        try {
          return JSON.parse(responseText);
        } catch (e) {
          console.error('Failed to parse response:', e);
          throw new Error('Invalid response format from server');
        }
      } catch (error) {
        console.error('Search error:', error);
        throw error;
      }
    },
    getNextPageParam: (lastPage) =>
      lastPage.currentPage < lastPage.totalPages ? lastPage.currentPage + 1 : undefined,
    initialPageParam: 1,
  });
}

// Add new interfaces for filter options
const PROPERTY_TYPES = [
  { id: 'hotel', label: 'Hotel' },
  { id: 'resort', label: 'Resort' },
  { id: 'apartment', label: 'Apartment' },
  { id: 'villa', label: 'Villa' },
  { id: 'guesthouse', label: 'Guesthouse' }
];

const AMENITIES = [
  { id: 'pool', label: 'Swimming Pool' },
  { id: 'wifi', label: 'Free WiFi' },
  { id: 'parking', label: 'Parking' },
  { id: 'restaurant', label: 'Restaurant' },
  { id: 'fitness', label: 'Fitness Center' },
  { id: 'spa', label: 'Spa' },
  { id: 'beach', label: 'Beach Access' },
  { id: 'breakfast', label: 'Free Breakfast' },
  { id: 'bar', label: 'Bar/Lounge' },
  { id: 'aircon', label: 'Air Conditioning' }
];

// Update the Map component interface to include onMapMoved
interface MapComponentProps extends MapProps {
  onMapMoved?: (center: { lat: number; lng: number }, radius: number) => void;
  onMoveEnd?: (center: { lat: number; lng: number }, radius: number) => void;
  radius?: number;
}

const ResultsContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
`;

const MapViewButton = styled.button`
  position: fixed;
  bottom: 24px;
  right: 24px;
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 24px;
  padding: 12px 24px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);

  &:hover {
    background: #1565c0;
  }
`;

const FilterDrawer = () => {
  const [nameFilter, setNameFilter] = useState("");
  const [minRating, setMinRating] = useState(0);
  const [maxPrice, setMaxPrice] = useState(1000);
  const [selectedPropertyTypes, setSelectedPropertyTypes] = useState<string[]>([]);
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
  const [selectedBedrooms, setSelectedBedrooms] = useState<number | null>(null);

  const handleNameFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNameFilter(e.target.value);
  };

  const handleRatingChange = ([value]: [number]) => {
    setMinRating(value);
  };

  const handlePriceChange = ([value]: [number]) => {
    setMaxPrice(value);
  };

  const handlePropertyTypeChange = (checked: boolean | "indeterminate", typeId: string) => {
    setSelectedPropertyTypes(prev =>
      checked === true
        ? [...prev, typeId]
        : prev.filter(t => t !== typeId)
    );
  };

  const handleAmenityChange = (checked: boolean | "indeterminate", amenityId: string) => {
    setSelectedAmenities(prev =>
      checked === true
        ? [...prev, amenityId]
        : prev.filter(a => a !== amenityId)
    );
  };

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="outline" size="sm" className="md:hidden">
          <SlidersHorizontal className="mr-2 h-4 w-4" />
          Filters
        </Button>
      </SheetTrigger>
      <SheetContent side="bottom" className="h-[80vh]">
        <div className="space-y-6 overflow-y-auto">
          <div className="space-y-4">
            <div>
              <Label>Hotel Name</Label>
              <Input
                placeholder="Search hotels..."
                value={nameFilter}
                onChange={handleNameFilterChange}
              />
            </div>

            <div>
              <Label>Minimum Rating</Label>
              <Slider
                value={[minRating]}
                onValueChange={handleRatingChange}
                min={0}
                max={5}
                step={0.5}
              />
              <span className="text-sm text-muted-foreground">{minRating} stars</span>
            </div>

            <div>
              <Label>Maximum Price</Label>
              <Slider
                value={[maxPrice]}
                onValueChange={handlePriceChange}
                min={0}
                max={1000}
                step={50}
              />
              <span className="text-sm text-muted-foreground">${maxPrice}</span>
            </div>

            <div>
              <Label>Minimum Bedrooms</Label>
              <div className="flex gap-2 mt-2">
                {[1, 2, 3, 4, 5].map(count => (
                  <Button
                    key={count}
                    variant={selectedBedrooms === count ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedBedrooms(selectedBedrooms === count ? null : count)}
                    className="flex-1"
                  >
                    {count}+
                  </Button>
                ))}
              </div>
              {selectedBedrooms && (
                <span className="text-sm text-muted-foreground mt-1 block">
                  {selectedBedrooms}+ bedrooms
                </span>
              )}
            </div>
          </div>

          <div>
            <Label className="text-base">Property Type</Label>
            <ScrollArea className="h-[200px]">
              <div className="space-y-2">
                {PROPERTY_TYPES.map(type => (
                  <div key={type.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`type-${type.id}`}
                      checked={selectedPropertyTypes.includes(type.id)}
                      onCheckedChange={(checked) => handlePropertyTypeChange(checked as boolean, type.id)}
                    />
                    <label
                      htmlFor={`type-${type.id}`}
                      className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {type.label}
                    </label>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>

          <div>
            <Label className="text-base">Amenities</Label>
            <ScrollArea className="h-[200px]">
              <div className="space-y-2">
                {AMENITIES.map(amenity => (
                  <div key={amenity.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`amenity-${amenity.id}`}
                      checked={selectedAmenities.includes(amenity.id)}
                      onCheckedChange={(checked) => handleAmenityChange(checked as boolean, amenity.id)}
                    />
                    <label
                      htmlFor={`amenity-${amenity.id}`}
                      className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {amenity.label}
                    </label>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

// Cast Map so TS allows extra optional props like radius
const MapComponent = Map as unknown as React.FC<MapComponentProps>;

export default function Results() {
  const [location, setLocation] = useLocation();
  const { toast } = useToast();

  // Parse search params from URL
  const searchParams = useMemo(() => {
    try {
      const params = new URLSearchParams(window.location.search);

      const lat = params.get('lat');
      const lng = params.get('lng');
      const checkIn = params.get('checkIn');
      const checkOut = params.get('checkOut');
      const locationName = params.get('locationName');
      const recommended = params.get('recommended');

      // Parse numeric values
      const parsedLat = lat ? parseFloat(lat) : NaN;
      const parsedLng = lng ? parseFloat(lng) : NaN;

      // Validate required location parameters
      if (!lat || isNaN(parsedLat)) {
        return null;
      }
      if (!lng || isNaN(parsedLng)) {
        return null;
      }
      if (!locationName) {
        return null;
      }

      // Provide default dates if missing or empty
      const today = new Date();
      const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
      const dayAfterTomorrow = new Date(today.getTime() + 2 * 24 * 60 * 60 * 1000);
      
      const defaultCheckIn = tomorrow.toISOString().split('T')[0];
      const defaultCheckOut = dayAfterTomorrow.toISOString().split('T')[0];
      
      const finalCheckIn = (checkIn && checkIn.trim()) ? checkIn : defaultCheckIn;
      const finalCheckOut = (checkOut && checkOut.trim()) ? checkOut : defaultCheckOut;

      // All validations passed, create the params object
      const validatedParams: SearchParams = {
        lat: parsedLat,
        lng: parsedLng,
        locationName: locationName,
        checkIn: finalCheckIn,
        checkOut: finalCheckOut,
        guests: Math.max(1, parseInt(params.get('guests') || '2', 10)),
        rooms: Math.max(1, parseInt(params.get('rooms') || '1', 10)),
        radius: Math.max(1, parseInt(params.get('radius') || '10', 10))
      };

      return validatedParams;
    } catch (error) {
      return null;
    }
  }, []);

  // Don't proceed with search if parameters are invalid
  if (!searchParams) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-8">
          <Card className="p-6">
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-destructive">
                <AlertCircle className="h-5 w-4" />
                <h2 className="text-lg font-semibold">Invalid Search Parameters</h2>
              </div>
              <p>Please check your search parameters and try again.</p>
              <Button onClick={() => window.location.href = '/'} variant="outline">
                Return to Search
              </Button>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  // Create a stable query object for usePropertySearch
  const queryParams = useMemo(() => ({
    lat: Number(searchParams.lat),
    lng: Number(searchParams.lng),
    locationName: searchParams.locationName,
    checkIn: searchParams.checkIn,
    checkOut: searchParams.checkOut,
    guests: Number(searchParams.guests),
    rooms: Number(searchParams.rooms),
    radius: Number(searchParams.radius)
  }), [searchParams]);

  // Initialize search query after validating parameters
  const searchQuery = usePropertySearch(queryParams);

  const queryClient = useQueryClient();

  // Initialize all state hooks at the top level
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [mapCenter, setMapCenter] = useState<{ lat: number; lng: number; } | undefined>(undefined);
  const [searchRadius, setSearchRadius] = useState<number>(0);
  const [isMapFullscreen, setIsMapFullscreen] = useState(false);
  const [pendingMapCenter, setPendingMapCenter] = useState<{ lat: number; lng: number; } | undefined>(undefined);
  const [pendingRadius, setPendingRadius] = useState<number>(0);
  const [nameFilter, setNameFilter] = useState("");
  const [minRating, setMinRating] = useState(0);
  const [maxPrice, setMaxPrice] = useState(1000);
  const [selectedPropertyTypes, setSelectedPropertyTypes] = useState<string[]>([]);
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
  const [selectedBedrooms, setSelectedBedrooms] = useState<number | null>(null);
  const [mapViewOpen, setMapViewOpen] = useState(false);
  const [selectedPropertyId, setSelectedPropertyId] = useState<number | null>(null);
  const [isMapVisible, setIsMapVisible] = useState(false);
  const [compareProperties, setCompareProperties] = useState<PropertyWithRates[]>([]);
  const [isCompareOpen, setIsCompareOpen] = useState(false);
  const [showAiChat, setShowAiChat] = useState(false);
  const [mapFilters, setMapFilters] = useState<{
    priceRange: [number, number];
    propertyTypes: PropertyType[];
  }>({
    priceRange: [0, 1000],
    propertyTypes: []
  });

  // Initialize refs at the top level
  const loadMoreRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(loadMoreRef);

  // Unified session ID shared across app
  const sessionId = useMemo(() => getSessionId(), []);

  // Record property view to server context
  const recordPropertyView = useCallback(async (propertyId: number) => {
    try {
      await fetch('/api/context/property-view', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, propertyId })
      });
    } catch (error) {
      console.error('Failed to record property view:', error);
    }
  }, [sessionId]);

  // Record comparison to server context
  const recordComparison = useCallback(async (propertyId: number, isAdding: boolean) => {
    try {
      await fetch('/api/context/comparison', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, propertyId, action: isAdding ? 'add' : 'remove' })
      });
    } catch (error) {
      console.error('Failed to record comparison:', error);
    }
  }, [sessionId]);

  // Add handler for comparison
  const handleCompareToggle = (property: PropertyWithRates) => {
    setCompareProperties(prev => {
      const exists = prev.some(p => p.id === property.id);
      const isAdding = !exists;
      
      // Record comparison action to server context
      recordComparison(property.id, isAdding);
      
      if (exists) {
        return prev.filter(p => p.id !== property.id);
      }
      if (prev.length >= 3) {
        return prev;
      }
      return [...prev, property];
    });
  };

  // Get all properties from the search results
  const allProperties = searchQuery.data?.pages.flatMap(page => page.properties) || [];

  // Calculate number of nights
  const checkInDate = searchParams.checkIn ? new Date(searchParams.checkIn) : undefined;
  const checkOutDate = searchParams.checkOut ? new Date(searchParams.checkOut) : undefined;
  const nights = useMemo(() => {
    const checkInParam = searchParams.checkIn;
    const checkOutParam = searchParams.checkOut;
    if (!checkInParam || !checkOutParam) return 0;

    const checkIn = new Date(checkInParam);
    const checkOut = new Date(checkOutParam);
    return Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));
  }, [searchParams.checkIn, searchParams.checkOut]);

  // Update availability queries to use searchQueryParams
  const availabilityQueries = useQueries({
    queries: allProperties.map(property => ({
      queryKey: ['availability', property.id, searchParams],
      queryFn: async (): Promise<AvailabilityResponse> => {
        const response = await fetch(
          `/api/properties/${property.id}/availability?` +
          `checkIn=${searchParams.checkIn}&` +
          `checkOut=${searchParams.checkOut}&` +
          `guests=${searchParams.guests}&` +
          `rooms=${searchParams.rooms}`
        );

        if (!response.ok) {
          throw new Error('Failed to fetch availability');
        }

        return response.json();
      },
      staleTime: 1000 * 60 * 5, // Consider data fresh for 5 minutes
      retry: 1,
      enabled: !!property.id
    }))
  });

  // Create details queries
  const detailsQueries = useQueries({
    queries: allProperties.map(property => ({
      queryKey: ['propertyDetails', property.id],
      queryFn: async () => {
        const response = await fetch(`/api/properties/${property.id}/content`);
        if (!response.ok) {
          throw new Error('Failed to fetch property details');
        }
        return response.json();
      },
      staleTime: 1000 * 60 * 30, // Consider data fresh for 30 minutes
      retry: 1,
      enabled: !!property.id
    }))
  });

  // Combine properties with their rates and details
  const propertiesWithRates: PropertyWithRates[] = useMemo(() => {
    return allProperties.map((property, index) => {
      const availabilityQuery = availabilityQueries[index];
      const detailsQuery = detailsQueries[index];

      // Process rates from first rate plan only
      let rateInfo = undefined;
      if (availabilityQuery.data?.ratePlans) {
        const firstPlan = Object.values(availabilityQuery.data.ratePlans)[0];

        if (firstPlan) {
          const firstRoom = Object.values(firstPlan.rooms)[0] as Room;

          if (firstRoom) {
            // Extract values from the Total object if it exists
            // Parse the base rate first
            const baseRate = typeof firstRoom.rate === 'number'
              ? firstRoom.rate
              : parseFloat(String(firstRoom.rate || '0'));

            // Get the Total object with all required fields
            const total = {
              '@Amount': String(firstRoom.Total?.['@Amount'] ?? firstRoom.totalAmount ?? baseRate ?? '0'),
              '@Discount': String(firstRoom.Total?.['@Discount'] ?? firstRoom.totalDiscount ?? '0'),
              '@RetailDiscountPercent': String(firstRoom.Total?.['@RetailDiscountPercent'] ?? firstRoom.retailDiscountPercent ?? '0'),
              '@Currency': String(firstRoom.Total?.['@Currency'] ?? firstRoom.currency ?? 'USD'),
              '@IncludesBookingFee': String(firstRoom.Total?.['@IncludesBookingFee'] ?? 'false'),
              '@ComparableRetailDiscount': String(firstRoom.Total?.['@ComparableRetailDiscount'] ?? firstRoom.totalComparableRetailDiscount ?? '0')
            };

            // Parse all numeric values
            const totalAmount = parseFloat(total['@Amount']);
            const discountAmount = parseFloat(total['@Discount']);
            const originalAmount = totalAmount + discountAmount;
            const retailDiscountPercent = Math.ceil(parseFloat(total['@RetailDiscountPercent']) || ((discountAmount / originalAmount) * 100));

            rateInfo = {
              code: firstPlan.code || '',
              description: firstPlan.description || '',
              rate: baseRate,
              totalAmount: totalAmount || baseRate, // Fallback to base rate if total amount is 0
              originalAmount: originalAmount || baseRate, // Fallback to base rate if original amount is 0
              discountAmount,
              discountPercent: retailDiscountPercent,
              currency: firstRoom.currency || total['@Currency'],
              roomTypeCode: firstRoom.roomTypeCode || '',
              bedTypeCode: firstRoom.bedTypeCode || '',
              restrictedRate: firstRoom.restrictedRate || false,
              refundable: firstRoom.refundable || true,
              maxOccupancy: firstRoom.maxOccupancy || 2,
              availableQuantity: firstRoom.availableQuantity || 1,
              rateCode: firstRoom.rateCode || firstPlan.code || '',
              rateDescription: firstRoom.rateDescription || firstPlan.description || '',
              cancellationPolicy: firstRoom.cancellationPolicy || '',
              guaranteePolicy: firstRoom.guaranteePolicy || '',
              depositPolicy: firstRoom.depositPolicy || '',
              includedServices: firstRoom.includedServices || [],
              promotions: firstRoom.promotions || [],
              taxes: firstRoom.taxes || [],
              fees: firstRoom.fees || []
            } satisfies Rate;
          }
        }
      } else {
        console.warn('No rate plans available for property:', {
          propertyId: property.id,
          availabilityQuery: availabilityQuery.data
        });
      }

      return {
        ...property,
        rates: rateInfo ? [rateInfo] : undefined,
        isLoadingRates: availabilityQuery.isLoading,
        rateError: availabilityQuery.error ? 'Failed to load rates' : undefined,
        propertyDetails: {
          images: detailsQuery.data?.images,
          isLoading: detailsQuery.isLoading,
          error: detailsQuery.isError
        }
      };
    });
  }, [allProperties, availabilityQueries, detailsQueries, nights]);

  // Sort properties by discount percentage
  const sortedProperties = useMemo(() => {
    return [...propertiesWithRates].sort((a, b) => {
      // If rates are still loading, keep original order
      if (a.isLoadingRates || b.isLoadingRates) return 0;

      // If there's an error loading rates, move to the end
      if (a.rateError && !b.rateError) return 1;
      if (!a.rateError && b.rateError) return -1;

      // Get discount percentages, defaulting to 0 if not available
      const aDiscount = a.rates?.[0]?.discountPercent ?? 0;
      const bDiscount = b.rates?.[0]?.discountPercent ?? 0;

      // Sort by highest discount first
      if (bDiscount !== aDiscount) {
        return bDiscount - aDiscount;
      }

      // If discounts are equal, sort by total amount
      const aTotal = a.rates?.[0]?.totalAmount ?? Number.MAX_VALUE;
      const bTotal = b.rates?.[0]?.totalAmount ?? Number.MAX_VALUE;
      return aTotal - bTotal;
    });
  }, [propertiesWithRates]);

  // Update filtered properties to include new filters
  const filteredProperties = sortedProperties.filter(property => {
    const matchesName = property.name.toLowerCase().includes(nameFilter.toLowerCase());
    const matchesRating = property.rating ? parseFloat(String(property.rating)) >= minRating : minRating === 0;
    const matchesPrice = property.rates ?
      property.rates[0].totalAmount <= maxPrice * nights :
      (typeof property.basePrice === 'number' ? property.basePrice / 100 : 0) <= maxPrice;

    const matchesPropertyType = selectedPropertyTypes.length === 0 ||
      selectedPropertyTypes.includes(property.type?.toLowerCase() || '');

    const matchesAmenities = selectedAmenities.length === 0 ||
      selectedAmenities.every(amenity =>
        property.amenities?.map(a => a.toLowerCase()).includes(amenity.toLowerCase())
      );

    // Multi-bedroom filter - analyze property description for bedroom count
    const matchesBedrooms = selectedBedrooms === null || (() => {
      const description = (property.description || '').toLowerCase();
      const name = (property.name || '').toLowerCase();

      // Extract bedroom count from description and name
      const bedroomMatches = [
        ...description.matchAll(/(\d+)[\s-]*bedroom/g),
        ...name.matchAll(/(\d+)[\s-]*bedroom/g),
        ...description.matchAll(/(\d+)[\s-]*bed[\s,]/g),
        ...name.matchAll(/(\d+)[\s-]*bed[\s,]/g)
      ];

      if (bedroomMatches.length > 0) {
        const maxBedrooms = Math.max(...bedroomMatches.map((match: any) => parseInt(match[1])));
        return maxBedrooms >= selectedBedrooms;
      }

      // If no bedroom info found, assume 1 bedroom for hotels, 2+ for vacation rentals
      const propertyType = (property.type || '').toLowerCase();
      const assumedBedrooms = propertyType.includes('apartment') || propertyType.includes('villa') || propertyType.includes('house') ? 2 : 1;
      return assumedBedrooms >= selectedBedrooms;
    })();

    return matchesName && matchesRating && matchesPrice && matchesPropertyType && matchesAmenities && matchesBedrooms;
  });

  // Update infinite scroll logic
  useEffect(() => {
    if (isInView && searchQuery.hasNextPage && !searchQuery.isFetchingNextPage) {
      searchQuery.fetchNextPage();
    }
  }, [isInView, searchQuery.hasNextPage, searchQuery.isFetchingNextPage, searchQuery]);

  // Get the total properties count from the first page
  const totalProperties = searchQuery.data?.pages[0]?.total || 0;

  // Event handlers with proper types
  const handleNameFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNameFilter(e.target.value);
  };

  const handleRatingChange = ([value]: [number]) => {
    setMinRating(value);
  };

  const handlePriceChange = ([value]: [number]) => {
    setMaxPrice(value);
  };

  // Quick action buttons
  const handleQuickAction = (action: 'luxury' | 'family' | 'budget') => {
    switch (action) {
      case 'luxury':
        setSelectedPropertyTypes(['hotel', 'resort']);
        setSelectedAmenities(['pool', 'spa']);
        handleRatingChange([4]);
        handlePriceChange([1000]);
        break;
      case 'family':
        setSelectedPropertyTypes(['hotel', 'resort', 'apartment']);
        setSelectedAmenities(['wifi', 'parking', 'breakfast']);
        handleRatingChange([3.5]);
        handlePriceChange([300]);
        break;
      case 'budget':
        setSelectedPropertyTypes(['hotel', 'guesthouse', 'apartment']);
        setSelectedAmenities(['wifi']);
        handleRatingChange([3]);
        handlePriceChange([150]);
        break;
    }
  };

  // Update handleMapMoved to just store the pending values
  const handleMapMoved = (center: { lat: number; lng: number }, radius: number) => {
    setPendingMapCenter(center);
    setPendingRadius(radius);

    // Notify backend so AI context is updated
    fetch('/api/context/map-move', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ sessionId, center, radius })
    }).catch(() => {/* non-blocking */});

    // Force React Query to refetch with new parameters by invalidating key
    queryClient.invalidateQueries({ queryKey: ['propertySearch'] });
  };

  // Update handleSearchThisArea to use the pending values
  const handleSearchThisArea = async () => {
    if (!pendingMapCenter) return;

    // Update the search parameters with the new location
    const newSearchParams = new URLSearchParams(window.location.search);
    newSearchParams.set('lat', pendingMapCenter.lat.toString());
    newSearchParams.set('lng', pendingMapCenter.lng.toString());
    // Convert radius from meters to kilometers and round to 2 decimal places
    const radiusInKm = (pendingRadius / 1000).toFixed(2);
    newSearchParams.set('radius', radiusInKm);

    // Update the URL without triggering a page reload
    window.history.replaceState({}, '', `?${newSearchParams.toString()}`);

    // Update the actual map center and radius state
    setMapCenter(pendingMapCenter);
    setSearchRadius(pendingRadius);

    // Create new search params object
    const newParams: SearchParams = {
      ...searchParams,
      lat: pendingMapCenter.lat,
      lng: pendingMapCenter.lng,
      radius: parseFloat(radiusInKm)
    };

    // Refetch with new parameters
    await searchQuery.refetch();
  };

  // Update the checkbox event handlers with proper types
  const handlePropertyTypeChange = (checked: boolean | "indeterminate", typeId: string) => {
    setSelectedPropertyTypes(prev =>
      checked === true
        ? [...prev, typeId]
        : prev.filter(t => t !== typeId)
    );
  };

  const handleAmenityChange = (checked: boolean | "indeterminate", amenityId: string) => {
    setSelectedAmenities(prev =>
      checked === true
        ? [...prev, amenityId]
        : prev.filter(a => a !== amenityId)
    );
  };

  // Update the dialog handling
  const openSearchModal = () => {
    const dialog = document.getElementById('search-form-modal') as HTMLDialogElement;
    if (dialog) dialog.showModal();
  };

  const closeSearchModal = () => {
    const dialog = document.getElementById('search-form-modal') as HTMLDialogElement;
    if (dialog) dialog.close();
  };

  const handlePropertySelect = (propertyId: number) => {
    const property = filteredProperties.find(p => p.id === propertyId);
    if (property) {
      // Scroll the sidebar to the selected property
      const propertyElement = document.getElementById(`property-${propertyId}`);
      if (propertyElement) {
        propertyElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }

      // Highlight the property in the list
      setSelectedPropertyId(propertyId);
    }
  };

  // Add filter handler
  const handleMapFiltersChange = (filters: {
    priceRange: [number, number];
    propertyTypes: PropertyType[];
  }) => {
    setMapFilters(filters);

    // Push filters to backend context for AI awareness
    fetch('/api/context/filters', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ sessionId, filters })
    }).catch(() => {});
  };

  return (
    <ResultsContainer>
      {/* Minimal Search Bar */}
      <div className="sticky top-0 z-40 bg-background/95 backdrop-blur-sm border-b shadow-sm">
        <div className="container mx-auto px-4">
          <div className="h-14 flex items-center justify-between gap-4">
            <div className="flex items-center gap-3 text-sm overflow-x-auto no-scrollbar">
              <span className="font-medium truncate max-w-[150px] md:max-w-[200px]">
                {searchParams.locationName}
              </span>
              <span className="text-muted-foreground/60 hidden sm:inline">•</span>
              <span className="text-muted-foreground whitespace-nowrap">
                {checkInDate?.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - {checkOutDate?.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
              </span>
              <span className="text-muted-foreground/60 hidden sm:inline">•</span>
              <span className="text-muted-foreground hidden sm:inline">{searchParams.guests} guests, {searchParams.rooms} {searchParams.rooms === 1 ? 'room' : 'rooms'}</span>
            </div>
            <div className="flex items-center gap-2">
              <FilterDrawer />
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsMapVisible(true)}
                className="flex items-center gap-2 shadow-sm hover:shadow-md transition-shadow"
              >
                <MapIcon className="h-4 w-4" />
                <span className="hidden sm:inline">View map</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-4">
        <div className="grid grid-cols-1 lg:grid-cols-[280px,1fr] gap-6">
          {/* Desktop Filters */}
          <div className="hidden md:block space-y-6">
            <Card className="p-4 sticky top-[calc(3.5rem+1px)]">
              <h2 className="text-lg font-semibold mb-4">Filters</h2>
              <div className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <Label>Hotel Name</Label>
                    <Input
                      placeholder="Search hotels..."
                      value={nameFilter}
                      onChange={handleNameFilterChange}
                    />
                  </div>

                  <div>
                    <Label>Minimum Rating</Label>
                    <Slider
                      value={[minRating]}
                      onValueChange={handleRatingChange}
                      min={0}
                      max={5}
                      step={0.5}
                    />
                    <span className="text-sm text-muted-foreground">{minRating} stars</span>
                  </div>

                  <div>
                    <Label>Maximum Price</Label>
                    <Slider
                      value={[maxPrice]}
                      onValueChange={handlePriceChange}
                      min={0}
                      max={1000}
                      step={50}
                    />
                    <span className="text-sm text-muted-foreground">${maxPrice}</span>
                  </div>

                  <div>
                    <Label>Minimum Bedrooms</Label>
                    <div className="flex gap-2 mt-2">
                      {[1, 2, 3, 4, 5].map(count => (
                        <Button
                          key={count}
                          variant={selectedBedrooms === count ? "default" : "outline"}
                          size="sm"
                          onClick={() => setSelectedBedrooms(selectedBedrooms === count ? null : count)}
                          className="flex-1"
                        >
                          {count}+
                        </Button>
                      ))}
                    </div>
                    {selectedBedrooms && (
                      <span className="text-sm text-muted-foreground mt-1 block">
                        {selectedBedrooms}+ bedrooms
                      </span>
                    )}
                  </div>
                </div>

                {/* Property Type filter */}
                <div>
                  <Label className="text-base">Property Type</Label>
                  <ScrollArea className="h-[200px] pr-4">
                    <div className="space-y-2">
                      {PROPERTY_TYPES.map(type => (
                        <div key={type.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`type-${type.id}`}
                            checked={selectedPropertyTypes.includes(type.id)}
                            onCheckedChange={(checked) => handlePropertyTypeChange(checked as boolean, type.id)}
                          />
                          <label
                            htmlFor={`type-${type.id}`}
                            className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {type.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>

                {/* Amenities filter */}
                <div>
                  <Label className="text-base">Amenities</Label>
                  <ScrollArea className="h-[200px] pr-4">
                    <div className="space-y-2">
                      {AMENITIES.map(amenity => (
                        <div key={amenity.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`amenity-${amenity.id}`}
                            checked={selectedAmenities.includes(amenity.id)}
                            onCheckedChange={(checked) => handleAmenityChange(checked as boolean, amenity.id)}
                          />
                          <label
                            htmlFor={`amenity-${amenity.id}`}
                            className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {amenity.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              </div>
            </Card>
          </div>

          {/* Property List */}
          <div className="flex-1">
            {searchQuery.isError && (
              <Card className="mb-4 p-4 border-destructive">
                <div className="flex items-center gap-2 text-destructive">
                  <AlertCircle className="h-4 w-4" />
                  <div className="font-medium">Error loading properties</div>
                </div>
                <p className="mt-1 text-sm text-muted-foreground">
                  {searchQuery.error instanceof Error 
                    ? searchQuery.error.message 
                    : 'An unexpected error occurred'
                  }
                </p>
              </Card>
            )}
            <div className="flex justify-between items-center">
              {searchQuery.isLoading ? (
                <h1 className="text-2xl font-bold">
                  Loading...
                </h1>
              ) : searchQuery.error ? (
                <div className="text-destructive flex items-center gap-2">
                  <AlertCircle className="h-5 w-5" />
                  <span>Error loading properties</span>
                </div>
              ) : (
                <h1 className="text-2xl font-bold">
                  {filteredProperties.length} {filteredProperties.length === 1 ? 'property' : 'properties'} found
                </h1>
              )}
            </div>

            {/* Quick Action Buttons */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mb-6">
              <Button
                variant="outline"
                className="flex items-center gap-2 h-auto py-4 px-6"
                onClick={() => handleQuickAction('luxury')}
              >
                <Star className="w-5 h-5 text-yellow-500" />
                <div className="text-left">
                  <div className="font-semibold">Luxury Stays</div>
                  <div className="text-sm text-muted-foreground">4+ stars with pool & spa</div>
                </div>
              </Button>

              <Button
                variant="outline"
                className="flex items-center gap-2 h-auto py-4 px-6"
                onClick={() => handleQuickAction('family')}
              >
                <Hotel className="w-5 h-5 text-blue-500" />
                <div className="text-left">
                  <div className="font-semibold">Family-Friendly</div>
                  <div className="text-sm text-muted-foreground">With breakfast & parking</div>
                </div>
              </Button>

              <Button
                variant="outline"
                className="flex items-center gap-2 h-auto py-4 px-6"
                onClick={() => handleQuickAction('budget')}
              >
                <DollarSign className="w-5 h-5 text-green-500" />
                <div className="text-left">
                  <div className="font-semibold">Budget-Friendly</div>
                  <div className="text-sm text-muted-foreground">Best value stays</div>
                </div>
              </Button>
            </div>

            {searchQuery.isLoading ? (
              // Show loading cards while properties are loading
              <div className="grid gap-6">
                {Array.from({ length: 3 }).map((_, index) => (
                  <PropertyLoadingCard key={`loading-${index}`} />
                ))}
              </div>
            ) : filteredProperties.length > 0 ? (
              <div className="grid gap-6">
                {filteredProperties.map((property) => {
                  // Find the availability query for this property by matching index
                  const propertyIndex = allProperties.findIndex(p => p.id === property.id);
                  const availabilityQuery = propertyIndex !== -1 ? availabilityQueries[propertyIndex] : null;
                  
                  // Check if the availability data is loading
                  const isAvailabilityLoading = availabilityQuery?.isLoading || false;
                  
                  // Determine if there's an error with availability
                  const availabilityError = availabilityQuery?.error
                    ? (availabilityQuery.error as Error).message
                    : undefined;
                  
                  return (
                    <Card
                      key={property.id}
                      id={`property-${property.id}`}
                      className={cn(
                        "cursor-pointer hover:shadow-md transition-shadow",
                        property.highlighted && "ring-2 ring-primary",
                        selectedPropertyId === property.id && "ring-2 ring-primary bg-primary/5"
                      )}
                    >
                      <PropertyCard
                        property={property}
                        rates={property.rates}
                        isLoading={isAvailabilityLoading}
                        error={availabilityError}
                        highlighted={(property.rates?.[0]?.discountPercent ?? 0) > 0}
                        onCompareToggle={() => handleCompareToggle(property)}
                        isComparing={compareProperties.some(p => p.id === property.id)}
                        checkIn={new Date(searchParams.checkIn)}
                        checkOut={new Date(searchParams.checkOut)}
                        guests={searchParams.guests.toString()}
                        rooms={searchParams.rooms.toString()}
                        onClick={() => {
                          console.log('Results page onClick handler called for property:', property.id);
                          // Record property view in session context
                          recordPropertyView(property.id);
                          // Navigate to property details
                          const targetUrl = `/property/${property.id}?${new URLSearchParams({
                            checkIn: searchParams.checkIn,
                            checkOut: searchParams.checkOut,
                            guests: searchParams.guests.toString(),
                            rooms: searchParams.rooms.toString()
                          }).toString()}`;
                          console.log('Results page navigating to:', targetUrl);
                          setLocation(targetUrl);
                        }}
                      />
                    </Card>
                  );
                })}
              </div>
            ) : !searchQuery.isLoading && (
              <Card className="p-6">
                <div className="text-center text-muted-foreground">
                  No properties found matching your criteria
                </div>
              </Card>
            )}

            {/* Load More Section */}
            {searchQuery.hasNextPage && (
              <div ref={loadMoreRef} className="py-4 text-center">
                {searchQuery.isFetchingNextPage ? (
                  <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                ) : (
                  <Button
                    variant="outline"
                    onClick={() => searchQuery.fetchNextPage()}
                    disabled={!searchQuery.hasNextPage || searchQuery.isFetchingNextPage}
                  >
                    Load More
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Search Form Modal */}
      <dialog id="search-form-modal" className="modal fixed inset-0 bg-background/95 p-4">
        <div className="bg-white rounded-lg shadow-2xl w-full max-w-3xl relative" style={{ zIndex: 9999 }}>
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-2xl font-semibold">Modify Search</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={closeSearchModal}
                className="absolute top-2 right-2"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M18 6L6 18M6 6l12 12" />
                </svg>
              </Button>
            </div>
            <div className="relative" style={{ zIndex: 10000 }}>
              <SearchForm onSubmit={closeSearchModal} />
            </div>
          </div>
        </div>
      </dialog>

      {/* Mobile Map View */}
      <Dialog open={isMapVisible} onOpenChange={setIsMapVisible}>
        <DialogContent className="sm:max-w-[100vw] h-[100vh] p-0">
          <VisuallyHidden>
            <DialogTitle>Map View</DialogTitle>
            <DialogDescription>Interactive map showing property locations and details</DialogDescription>
          </VisuallyHidden>
          <div className="relative h-full">
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-2 right-2 z-10"
              onClick={() => setIsMapVisible(false)}
            >
              <X className="h-4 w-4" />
            </Button>
            <div className="space-y-4">
              <MapComponent
                center={mapCenter}
                radius={searchRadius}
                onMoveEnd={handleMapMoved}
                properties={filteredProperties}
              />

              {/* Smart AI insights below the map */}
              <InsightsPanel
                locationName={searchParams.locationName}
                lat={mapCenter?.lat ?? searchParams.lat}
                lng={mapCenter?.lng ?? searchParams.lng}
                dateRange={{ checkIn: searchParams.checkIn, checkOut: searchParams.checkOut }}
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Compare Properties Drawer */}
      <CompareDrawer
        open={isCompareOpen}
        onClose={() => setIsCompareOpen(false)}
        properties={compareProperties}
        onRemove={(id) => {
          setCompareProperties(prev => prev.filter(p => p.id !== id));
        }}
      />

      {/* Mobile AI Chat */}
      <Button
        variant="default"
        size="icon"
        className="fixed bottom-4 right-4 h-12 w-12 rounded-full shadow-lg md:hidden"
        onClick={() => setShowAiChat(true)}
      >
        <Bot className="h-6 w-6" />
      </Button>

      <Sheet open={showAiChat} onOpenChange={setShowAiChat}>
        <SheetContent side="bottom" className="h-[80vh]">
          <UnifiedAIChat
            context={{
              location: new URLSearchParams(window.location.search).get('locationName') || undefined,
              checkIn: new URLSearchParams(window.location.search).get('checkIn') || undefined,
              checkOut: new URLSearchParams(window.location.search).get('checkOut') || undefined,
              guests: new URLSearchParams(window.location.search).get('guests') || undefined,
              bedrooms: new URLSearchParams(window.location.search).get('bedrooms') || undefined,
              properties: properties || undefined
            }}
            variant="embedded"
            onClose={() => setShowAiChat(false)}
            onPropertySelect={(property) => {
              setSelectedPropertyId(property.id);
              setShowAiChat(false);
            }}
          />
        </SheetContent>
      </Sheet>
    </ResultsContainer>
  );
}