import React, { useState, useRef, useEffect, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';
import {
  Send,
  MapPin,
  Calendar,
  Users,
  Star,
  Hotel,
  Sparkles,
  Loader2,
  X,
  Maximize2,
  Minimize2,
  Bot,
  User,
  Globe,
  Home,
  Bed,
  Car,
  Utensils,
  Wifi,
  Waves,
  Mountain,
  TreePine,
  Building,
  Heart,
  TrendingUp,
  Clock,
  DollarSign,
  Camera,
  Navigation
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { getSessionId } from '@/lib/session';

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  type?: 'text' | 'location' | 'properties' | 'action' | 'error' | 'experience' | 'insight' | 'recommendation';
  data?: any;
  metadata?: {
    confidence?: number;
    sources?: string[];
    actionable?: boolean;
    priority?: 'low' | 'medium' | 'high';
  };
}

interface PropertyRecommendation {
  id: number;
  name: string;
  type: string;
  bedrooms?: number;
  price: number;
  currency: string;
  rating: number;
  image: string;
  highlights: string[];
  suitabilityScore?: number;
  reasonForRecommendation: string;
}

interface ExperienceRecommendation {
  id: string;
  name: string;
  type: 'attraction' | 'restaurant' | 'activity' | 'event' | 'transportation';
  description: string;
  location: string;
  duration?: string;
  priceRange?: string;
  bestTime?: string;
  bookingRequired?: boolean;
  image?: string;
}

interface TravelInsight {
  type: 'weather' | 'events' | 'pricing' | 'crowds' | 'local_tips' | 'safety';
  title: string;
  content: string;
  importance: 'low' | 'medium' | 'high';
  actionable: boolean;
  icon?: string;
}

interface UnifiedAIChatProps {
  context?: {
    location?: string | null;
    checkIn?: string | null;
    checkOut?: string | null;
    guests?: string | null;
    rooms?: string | null;
    bedrooms?: string | null;
    properties?: any[];
    filters?: any;
    searchHistory?: any[];
    groupType?: 'family' | 'friends' | 'corporate' | 'wedding' | 'reunion' | 'multi_generational';
    travelPurpose?: string;
    budget?: { min?: number; max?: number; currency?: string };
    preferences?: {
      propertyTypes?: string[];
      amenities?: string[];
      accessibility?: string[];
    };
  };
  variant?: 'modal' | 'embedded' | 'floating' | 'sidebar';
  onClose?: () => void;
  onNavigate?: (path: string, params?: any) => void;
  onPropertySelect?: (property: any) => void;
  onSearchUpdate?: (searchParams: any) => void;
  className?: string;
  showWelcome?: boolean;
}

export default function UnifiedAIChat({
  context,
  variant = 'modal',
  onClose,
  onNavigate,
  onPropertySelect,
  onSearchUpdate,
  className,
  showWelcome = true
}: UnifiedAIChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);
  const [sessionId] = useState(() => getSessionId());
  const [_, navigate] = useLocation();
  const { toast } = useToast();
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [conversationContext, setConversationContext] = useState({
    userPreferences: {},
    searchHistory: [],
    currentFocus: null as string | null,
    travelStyle: null as string | null
  });
  const initializationRef = useRef({
    hasInitialized: false,
    hasProcessedInitialMessage: false,
    messageBeingSent: false
  });

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  // Initialize with enhanced welcome message and process any stored initial message
  useEffect(() => {
    if (initializationRef.current.hasInitialized) return;
    initializationRef.current.hasInitialized = true;

    if (showWelcome) {
      // Add enhanced welcome message with context awareness
      const welcomeMessage: Message = {
        id: `system-welcome-${Date.now()}`,
        role: 'system',
        content: generateWelcomeMessage(context),
        timestamp: new Date(),
        type: 'text',
        metadata: {
          confidence: 1.0,
          actionable: true,
          priority: 'high'
        }
      };

      setMessages([welcomeMessage]);
    }

    // Check for stored initial message from "Plan with AI" button
    const storedHistory = localStorage.getItem('chatHistory');
    const triggerFlag = localStorage.getItem('ai_chat_trigger');
    
    if (triggerFlag === 'true' && storedHistory && !initializationRef.current.hasProcessedInitialMessage) {
      try {
        const parsedHistory = JSON.parse(storedHistory);
        const lastMessage = parsedHistory[parsedHistory.length - 1];
        
        if (lastMessage && lastMessage.role === 'user' && !initializationRef.current.messageBeingSent) {
          initializationRef.current.hasProcessedInitialMessage = true;
          initializationRef.current.messageBeingSent = true;
          
          // Clear trigger flag
          localStorage.removeItem('ai_chat_trigger');
          
          // Add user message and send to AI
          const userMessage: Message = {
            id: lastMessage.id || `user-${Date.now()}`,
            role: 'user',
            content: lastMessage.content,
            timestamp: new Date(),
            type: 'text'
          };
          
          setMessages(prev => [...prev, userMessage]);
          sendMessageToAI(lastMessage.content);
        }
      } catch (error) {
        console.error('Failed to process stored message:', error);
      }
    }
  }, [showWelcome, context]);

  // Helper function to generate contextual welcome message
  const generateWelcomeMessage = (context?: any): string => {
    if (context?.location && context?.checkIn) {
      return `🌟 Welcome! I see you're planning a trip to ${context.location} starting ${new Date(context.checkIn).toLocaleDateString()}. I'm your AI travel expert, and I'm excited to help you create an amazing experience!

Whether you need accommodations, local insights, or activity recommendations, I'm here to make your travel planning effortless and enjoyable.`;
    }

    if (context?.bedrooms && parseInt(context.bedrooms) > 1) {
      return `🏡 Welcome! I specialize in finding perfect multi-bedroom accommodations for groups and families. With ${context.bedrooms} bedrooms needed, I'll help you discover amazing properties that bring everyone together comfortably.

Let's find you the ideal place to create lasting memories!`;
    }

    return `✨ Welcome to your AI Travel Companion! I'm not just here to find you accommodations - I'm your personal travel expert who understands what makes trips truly special.

Tell me about your travel dreams, and I'll help bring them to life with personalized recommendations, insider tips, and thoughtful suggestions you won't find anywhere else.`;
  };

  const sendMessageToAI = useCallback(async (messageContent: string) => {
    if (initializationRef.current.messageBeingSent && messageContent !== input) {
      // This is an initial message being processed
    } else {
      setIsLoading(true);
    }

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: messageContent,
          context: {
            ...context,
            conversation: conversationContext,
            userPreferences: conversationContext.userPreferences,
            searchHistory: conversationContext.searchHistory
          },
          sessionId,
          extractLocation: true,
          enhancedMode: true
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      let assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        role: 'assistant',
        content: '',
        timestamp: new Date(),
        type: 'text'
      };

      setMessages(prev => [...prev, assistantMessage]);

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);
              
              if (parsed.type === 'text') {
                assistantMessage.content += parsed.data;
                setMessages(prev => 
                  prev.map(msg => 
                    msg.id === assistantMessage.id 
                      ? { ...msg, content: assistantMessage.content }
                      : msg
                  )
                );
              } else if (parsed.type === 'location') {
                // Handle location response
                handleLocationResponse(parsed.data);
              } else if (parsed.type === 'properties') {
                // Handle properties response
                handlePropertiesResponse(parsed.data);
              }
            } catch (error) {
              console.error('Failed to parse streaming response:', error);
            }
          }
        }
      }

    } catch (error) {
      console.error('Chat error:', error);
      toast({
        title: 'Error',
        description: 'Failed to send message. Please try again.',
        variant: 'destructive',
      });
      
      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        role: 'assistant',
        content: 'I apologize, but I encountered an error. Please try again.',
        timestamp: new Date(),
        type: 'error'
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      initializationRef.current.messageBeingSent = false;
    }
  }, [context, sessionId, toast, input]);

  const handleLocationResponse = (locationData: any) => {
    // Navigate to search results with the detected location
    const searchParams = new URLSearchParams({
      locationName: locationData.name,
      lat: locationData.lat.toString(),
      lng: locationData.lng.toString(),
      checkIn: context?.checkIn || new Date(Date.now() + 86400000).toISOString().split('T')[0],
      checkOut: context?.checkOut || new Date(Date.now() + 2 * 86400000).toISOString().split('T')[0],
      guests: context?.guests || '2',
      rooms: context?.rooms || '1'
    });

    navigate(`/results?${searchParams.toString()}`);
    onClose?.();
  };

  const handlePropertiesResponse = (properties: any[]) => {
    // Add property cards to the chat
    const propertyMessage: Message = {
      id: `properties-${Date.now()}`,
      role: 'assistant',
      content: `I found ${properties.length} properties for you:`,
      timestamp: new Date(),
      type: 'properties',
      data: properties
    };

    setMessages(prev => [...prev, propertyMessage]);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: input.trim(),
      timestamp: new Date(),
      type: 'text'
    };

    setMessages(prev => [...prev, userMessage]);
    sendMessageToAI(input.trim());
    setInput('');
  };

  const handleQuickAction = (action: string) => {
    setInput(action);
    inputRef.current?.focus();
  };

  const containerClasses = cn(
    'flex flex-col',
    {
      'fixed inset-4 z-50 bg-background border rounded-lg shadow-lg': variant === 'modal',
      'w-full h-full': variant === 'embedded',
      'fixed bottom-4 right-4 w-96 h-[500px] z-50 bg-background border rounded-lg shadow-lg': variant === 'floating',
      'fixed inset-2 z-50': variant === 'modal' && isMaximized,
    },
    className
  );

  const quickActions = [
    { icon: <Globe className="w-4 h-4" />, label: 'Explore Destinations', action: 'Show me popular travel destinations' },
    { icon: <Calendar className="w-4 h-4" />, label: 'Plan My Trip', action: 'Help me plan a trip' },
    { icon: <Star className="w-4 h-4" />, label: 'Find Deals', action: 'Find me the best hotel deals' },
    { icon: <Sparkles className="w-4 h-4" />, label: 'Inspire Me', action: 'Inspire me with unique travel ideas' }
  ];

  return (
    <Card className={containerClasses}>
      <CardHeader className="border-b bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center">
              <Bot className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold">AI Travel Companion</h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Your intelligent travel planning assistant
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {variant === 'modal' && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsMaximized(!isMaximized)}
              >
                {isMaximized ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
              </Button>
            )}
            {onClose && (
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="flex-1 p-0">
        <ScrollArea ref={scrollAreaRef} className="h-full p-4">
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  'flex gap-3',
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                )}
              >
                {message.role !== 'user' && (
                  <Avatar className="w-8 h-8">
                    <AvatarFallback>
                      <Bot className="w-4 h-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
                
                <div
                  className={cn(
                    'max-w-[80%] rounded-lg px-4 py-2',
                    message.role === 'user'
                      ? 'bg-primary text-primary-foreground'
                      : message.role === 'system'
                      ? 'bg-muted text-muted-foreground'
                      : 'bg-secondary text-secondary-foreground'
                  )}
                >
                  <p className="text-sm">{message.content}</p>
                  {message.type === 'properties' && message.data && (
                    <div className="mt-2 space-y-2">
                      {message.data.slice(0, 3).map((property: any, index: number) => (
                        <div key={index} className="p-2 border rounded bg-background">
                          <h4 className="font-medium">{property.name}</h4>
                          <p className="text-xs text-muted-foreground">{property.address}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                            <span className="text-xs">{property.rating || 'N/A'}</span>
                            <span className="text-xs font-medium">${property.basePrice}/night</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {message.role === 'user' && (
                  <Avatar className="w-8 h-8">
                    <AvatarFallback>
                      <User className="w-4 h-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}

            {isLoading && (
              <div className="flex gap-3 justify-start">
                <Avatar className="w-8 h-8">
                  <AvatarFallback>
                    <Bot className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
                <div className="bg-secondary text-secondary-foreground rounded-lg px-4 py-2">
                  <div className="flex items-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span className="text-sm">Thinking...</span>
                  </div>
                </div>
              </div>
            )}

            {messages.length === 1 && (
              <div className="grid grid-cols-2 gap-2 mt-4">
                {quickActions.map((action, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    className="h-auto p-3 text-left"
                    onClick={() => handleQuickAction(action.action)}
                  >
                    <div className="flex items-center gap-2">
                      {action.icon}
                      <span className="text-xs">{action.label}</span>
                    </div>
                  </Button>
                ))}
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>

      <CardFooter className="border-t p-4 bg-gray-50 dark:bg-gray-900/50">
        <form onSubmit={handleSubmit} className="flex gap-3 w-full">
          <Input
            ref={inputRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask me anything about travel..."
            className="flex-1"
            disabled={isLoading}
          />
          <Button type="submit" disabled={!input.trim() || isLoading}>
            <Send className="w-4 h-4" />
          </Button>
        </form>
      </CardFooter>
    </Card>
  );
}
