import React, { useState, useRef, useEffect, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';
import {
  Send,
  MapPin,
  Calendar,
  Users,
  Star,
  Hotel,
  Sparkles,
  Loader2,
  X,
  Maximize2,
  Minimize2,
  Bot,
  User,
  Globe,
  Home,
  Bed,
  Car,
  Utensils,
  Wifi,
  Waves,
  Mountain,
  TreePine,
  Building,
  Heart,
  TrendingUp,
  Clock,
  DollarSign,
  Camera,
  Navigation
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { getSessionId } from '@/lib/session';

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  type?: 'text' | 'location' | 'properties' | 'action' | 'error' | 'experience' | 'insight' | 'recommendation';
  data?: any;
  metadata?: {
    confidence?: number;
    sources?: string[];
    actionable?: boolean;
    priority?: 'low' | 'medium' | 'high';
  };
}

interface PropertyRecommendation {
  id: number;
  name: string;
  type: string;
  bedrooms?: number;
  price: number;
  currency: string;
  rating: number;
  image: string;
  highlights: string[];
  suitabilityScore?: number;
  reasonForRecommendation: string;
}

interface ExperienceRecommendation {
  id: string;
  name: string;
  type: 'attraction' | 'restaurant' | 'activity' | 'event' | 'transportation';
  description: string;
  location: string;
  duration?: string;
  priceRange?: string;
  bestTime?: string;
  bookingRequired?: boolean;
  image?: string;
}

interface TravelInsight {
  type: 'weather' | 'events' | 'pricing' | 'crowds' | 'local_tips' | 'safety';
  title: string;
  content: string;
  importance: 'low' | 'medium' | 'high';
  actionable: boolean;
  icon?: string;
}

interface UnifiedAIChatProps {
  context?: {
    location?: string | null;
    checkIn?: string | null;
    checkOut?: string | null;
    guests?: string | null;
    rooms?: string | null;
    bedrooms?: string | null;
    properties?: any[];
    filters?: any;
    searchHistory?: any[];
    groupType?: 'family' | 'friends' | 'corporate' | 'wedding' | 'reunion' | 'multi_generational';
    travelPurpose?: string;
    budget?: { min?: number; max?: number; currency?: string };
    preferences?: {
      propertyTypes?: string[];
      amenities?: string[];
      accessibility?: string[];
    };
  };
  variant?: 'modal' | 'embedded' | 'floating' | 'sidebar';
  onClose?: () => void;
  onNavigate?: (path: string, params?: any) => void;
  onPropertySelect?: (property: any) => void;
  onSearchUpdate?: (searchParams: any) => void;
  className?: string;
  showWelcome?: boolean;
}

export default function UnifiedAIChat({
  context,
  variant = 'modal',
  onClose,
  onNavigate,
  onPropertySelect,
  onSearchUpdate,
  className,
  showWelcome = true
}: UnifiedAIChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);
  const [sessionId] = useState(() => getSessionId());
  const [_, navigate] = useLocation();
  const { toast } = useToast();
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [conversationContext, setConversationContext] = useState({
    userPreferences: {},
    searchHistory: [] as Array<{ location: string; timestamp: Date }>,
    currentFocus: null as string | null,
    travelStyle: null as string | null
  });
  const initializationRef = useRef({
    hasInitialized: false,
    hasProcessedInitialMessage: false,
    messageBeingSent: false
  });

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  // Initialize with enhanced welcome message and process any stored initial message
  useEffect(() => {
    if (initializationRef.current.hasInitialized) return;
    initializationRef.current.hasInitialized = true;

    if (showWelcome) {
      // Add enhanced welcome message with context awareness
      const welcomeMessage: Message = {
        id: `system-welcome-${Date.now()}`,
        role: 'system',
        content: generateWelcomeMessage(context),
        timestamp: new Date(),
        type: 'text',
        metadata: {
          confidence: 1.0,
          actionable: true,
          priority: 'high'
        }
      };

      setMessages([welcomeMessage]);
    }

    // Check for stored initial message from "Plan with AI" button
    const storedHistory = localStorage.getItem('chatHistory');
    const triggerFlag = localStorage.getItem('ai_chat_trigger');
    
    if (triggerFlag === 'true' && storedHistory && !initializationRef.current.hasProcessedInitialMessage) {
      try {
        const parsedHistory = JSON.parse(storedHistory);
        const lastMessage = parsedHistory[parsedHistory.length - 1];
        
        if (lastMessage && lastMessage.role === 'user' && !initializationRef.current.messageBeingSent) {
          initializationRef.current.hasProcessedInitialMessage = true;
          initializationRef.current.messageBeingSent = true;
          
          // Clear trigger flag
          localStorage.removeItem('ai_chat_trigger');
          
          // Add user message and send to AI
          const userMessage: Message = {
            id: lastMessage.id || `user-${Date.now()}`,
            role: 'user',
            content: lastMessage.content,
            timestamp: new Date(),
            type: 'text'
          };
          
          setMessages(prev => [...prev, userMessage]);
          sendMessageToAI(lastMessage.content);
        }
      } catch (error) {
        console.error('Failed to process stored message:', error);
      }
    }
  }, [showWelcome, context]);

  // Helper function to generate contextual welcome message
  const generateWelcomeMessage = (context?: any): string => {
    if (context?.location && context?.checkIn) {
      return `🌟 Welcome! I see you're planning a trip to ${context.location} starting ${new Date(context.checkIn).toLocaleDateString()}. I'm your AI travel expert, and I'm excited to help you create an amazing experience!

Whether you need accommodations, local insights, or activity recommendations, I'm here to make your travel planning effortless and enjoyable.`;
    }

    if (context?.bedrooms && parseInt(context.bedrooms) > 1) {
      return `🏡 Welcome! I specialize in finding perfect multi-bedroom accommodations for groups and families. With ${context.bedrooms} bedrooms needed, I'll help you discover amazing properties that bring everyone together comfortably.

Let's find you the ideal place to create lasting memories!`;
    }

    return `✨ Welcome to your AI Travel Companion! I'm not just here to find you accommodations - I'm your personal travel expert who understands what makes trips truly special.

Tell me about your travel dreams, and I'll help bring them to life with personalized recommendations, insider tips, and thoughtful suggestions you won't find anywhere else.`;
  };

  const sendMessageToAI = useCallback(async (messageContent: string) => {
    if (initializationRef.current.messageBeingSent && messageContent !== input) {
      // This is an initial message being processed
    } else {
      setIsLoading(true);
    }

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: messageContent,
          context: {
            ...context,
            conversation: conversationContext,
            userPreferences: conversationContext.userPreferences,
            searchHistory: conversationContext.searchHistory
          },
          sessionId,
          extractLocation: true,
          enhancedMode: true
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      let assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        role: 'assistant',
        content: '',
        timestamp: new Date(),
        type: 'text'
      };

      setMessages(prev => [...prev, assistantMessage]);

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);
              
              if (parsed.type === 'text') {
                assistantMessage.content += parsed.data;
                setMessages(prev =>
                  prev.map(msg =>
                    msg.id === assistantMessage.id
                      ? { ...msg, content: assistantMessage.content }
                      : msg
                  )
                );
              } else if (parsed.type === 'location') {
                handleLocationResponse(parsed.data);
              } else if (parsed.type === 'properties') {
                handlePropertiesResponse(parsed.data);
              } else if (parsed.type === 'action') {
                handleActionResponse(parsed.data);
              } else if (parsed.type === 'experience') {
                handleExperienceResponse(parsed.data);
              } else if (parsed.type === 'insight') {
                handleInsightResponse(parsed.data);
              }
            } catch (error) {
              console.error('Failed to parse streaming response:', error);
            }
          }
        }
      }

    } catch (error) {
      console.error('Chat error:', error);
      toast({
        title: 'Error',
        description: 'Failed to send message. Please try again.',
        variant: 'destructive',
      });
      
      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        role: 'assistant',
        content: 'I apologize, but I encountered an error. Please try again.',
        timestamp: new Date(),
        type: 'error'
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      initializationRef.current.messageBeingSent = false;
    }
  }, [context, sessionId, toast, input]);

  const handleLocationResponse = (locationData: any) => {
    // Update conversation context
    setConversationContext(prev => ({
      ...prev,
      currentFocus: 'location',
      searchHistory: [...prev.searchHistory, { location: locationData.name, timestamp: new Date() }]
    }));

    // Create an interactive location message with map and action buttons
    const locationMessage: Message = {
      id: `location-${Date.now()}`,
      role: 'assistant',
      content: `🗺️ Perfect! I found **${locationData.name}** for you. Here's what I can help you with:`,
      timestamp: new Date(),
      type: 'location',
      data: locationData,
      metadata: {
        actionable: true,
        priority: 'high',
        geography: {
          coordinates: { lat: locationData.lat, lng: locationData.lng },
          name: locationData.name,
          type: locationData.type || 'destination'
        }
      }
    };

    setMessages(prev => [...prev, locationMessage]);

    // Auto-navigate after showing the location card
    setTimeout(() => {
      const searchParams = new URLSearchParams({
        locationName: locationData.name,
        lat: locationData.lat.toString(),
        lng: locationData.lng.toString(),
        checkIn: context?.checkIn || new Date(Date.now() + 86400000).toISOString().split('T')[0],
        checkOut: context?.checkOut || new Date(Date.now() + 2 * 86400000).toISOString().split('T')[0],
        guests: context?.guests || '2',
        rooms: context?.rooms || '1',
        bedrooms: context?.bedrooms || '1',
        ...(context?.groupType && { groupType: context.groupType })
      });

      if (onNavigate) {
        onNavigate(`/results?${searchParams.toString()}`);
      } else {
        navigate(`/results?${searchParams.toString()}`);
      }

      if (onSearchUpdate) {
        onSearchUpdate({
          location: locationData.name,
          coordinates: { lat: locationData.lat, lng: locationData.lng }
        });
      }

      onClose?.();
    }, 2000); // Give user time to see the location card
  };

  const handlePropertiesResponse = (properties: any[]) => {
    const propertyMessage: Message = {
      id: `properties-${Date.now()}`,
      role: 'assistant',
      content: `I found ${properties.length} perfect accommodations for you:`,
      timestamp: new Date(),
      type: 'properties',
      data: properties,
      metadata: {
        confidence: 0.9,
        actionable: true,
        priority: 'high'
      }
    };

    setMessages(prev => [...prev, propertyMessage]);
  };

  const handleActionResponse = (actionData: any) => {
    const actionMessage: Message = {
      id: `action-${Date.now()}`,
      role: 'assistant',
      content: actionData.label,
      timestamp: new Date(),
      type: 'action',
      data: actionData,
      metadata: {
        actionable: true,
        priority: 'medium'
      }
    };

    setMessages(prev => [...prev, actionMessage]);
  };

  const handleExperienceResponse = (experiences: ExperienceRecommendation[]) => {
    const experienceMessage: Message = {
      id: `experience-${Date.now()}`,
      role: 'assistant',
      content: `Here are some amazing experiences I recommend:`,
      timestamp: new Date(),
      type: 'experience',
      data: experiences,
      metadata: {
        confidence: 0.8,
        actionable: true,
        priority: 'medium'
      }
    };

    setMessages(prev => [...prev, experienceMessage]);
  };

  const handleInsightResponse = (insights: TravelInsight[]) => {
    const insightMessage: Message = {
      id: `insight-${Date.now()}`,
      role: 'assistant',
      content: `Here are some important travel insights:`,
      timestamp: new Date(),
      type: 'insight',
      data: insights,
      metadata: {
        confidence: 0.9,
        actionable: true,
        priority: insights.some(i => i.importance === 'high') ? 'high' : 'medium'
      }
    };

    setMessages(prev => [...prev, insightMessage]);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: input.trim(),
      timestamp: new Date(),
      type: 'text'
    };

    setMessages(prev => [...prev, userMessage]);
    sendMessageToAI(input.trim());
    setInput('');
  };

  const handleQuickAction = (action: string) => {
    setInput(action);
    inputRef.current?.focus();
  };

  const containerClasses = cn(
    'flex flex-col',
    {
      'fixed inset-4 z-50 bg-background border rounded-lg shadow-lg': variant === 'modal',
      'w-full h-full': variant === 'embedded',
      'fixed bottom-4 right-4 w-96 h-[500px] z-50 bg-background border rounded-lg shadow-lg': variant === 'floating',
      'fixed inset-2 z-50': variant === 'modal' && isMaximized,
    },
    className
  );

  // Enhanced quick actions based on context
  const getQuickActions = () => {
    const baseActions = [
      { icon: <Globe className="w-4 h-4" />, label: 'Explore Destinations', action: 'Show me trending travel destinations for this season' },
      { icon: <Home className="w-4 h-4" />, label: 'Multi-Bedroom Stays', action: 'Find me vacation rentals with multiple bedrooms for a group' },
      { icon: <Sparkles className="w-4 h-4" />, label: 'Unique Experiences', action: 'Suggest unique accommodations and local experiences' },
      { icon: <TrendingUp className="w-4 h-4" />, label: 'Best Deals', action: 'Find me the best accommodation deals and travel tips' }
    ];

    // Context-aware actions
    if (context?.location) {
      baseActions.unshift({
        icon: <MapPin className="w-4 h-4" />,
        label: `Explore ${context.location}`,
        action: `Tell me about the best accommodations and experiences in ${context.location}`
      });
    }

    if (context?.bedrooms && parseInt(context.bedrooms) > 1) {
      baseActions.unshift({
        icon: <Bed className="w-4 h-4" />,
        label: `${context.bedrooms}-Bedroom Options`,
        action: `Show me ${context.bedrooms}-bedroom accommodations perfect for my group`
      });
    }

    if (context?.groupType) {
      const groupLabels = {
        family: 'Family-Friendly',
        friends: 'Group Getaway',
        corporate: 'Business Travel',
        wedding: 'Wedding Party',
        reunion: 'Family Reunion',
        multi_generational: 'Multi-Gen Trip'
      };

      baseActions.unshift({
        icon: <Users className="w-4 h-4" />,
        label: groupLabels[context.groupType] || 'Group Travel',
        action: `Help me plan the perfect ${context.groupType} trip with ideal accommodations`
      });
    }

    return baseActions.slice(0, 6); // Limit to 6 actions
  };

  const quickActions = getQuickActions();

  return (
    <Card className={containerClasses}>
      <CardHeader className="border-b bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center">
              <Bot className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold">AI Travel Companion</h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Your intelligent travel planning assistant
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {variant === 'modal' && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsMaximized(!isMaximized)}
              >
                {isMaximized ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
              </Button>
            )}
            {onClose && (
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="flex-1 p-0">
        <ScrollArea ref={scrollAreaRef} className="h-full p-4">
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  'flex gap-3',
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                )}
              >
                {message.role !== 'user' && (
                  <Avatar className="w-8 h-8">
                    <AvatarFallback>
                      <Bot className="w-4 h-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
                
                <div
                  className={cn(
                    'max-w-[85%] rounded-lg px-4 py-3',
                    message.role === 'user'
                      ? 'bg-primary text-primary-foreground'
                      : message.role === 'system'
                      ? 'bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 text-foreground border'
                      : 'bg-secondary text-secondary-foreground'
                  )}
                >
                  <div className="flex items-start gap-2">
                    {message.metadata?.priority === 'high' && (
                      <Sparkles className="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                    )}
                    <div className="flex-1">
                      <p className="text-sm leading-relaxed">{message.content}</p>

                      {/* Enhanced Property Display */}
                      {message.type === 'properties' && message.data && (
                        <div className="mt-3 space-y-3">
                          {message.data.slice(0, 3).map((property: PropertyRecommendation, index: number) => (
                            <div
                              key={index}
                              className="p-3 border rounded-lg bg-background hover:bg-accent/50 transition-colors cursor-pointer"
                              onClick={() => onPropertySelect?.(property)}
                            >
                              <div className="flex items-start gap-3">
                                {property.image && (
                                  <div className="w-16 h-16 rounded-lg bg-muted flex items-center justify-center flex-shrink-0">
                                    <Hotel className="w-6 h-6 text-muted-foreground" />
                                  </div>
                                )}
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-start justify-between gap-2">
                                    <h4 className="font-medium text-sm truncate">{property.name}</h4>
                                    <div className="flex items-center gap-1 flex-shrink-0">
                                      <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                                      <span className="text-xs">{property.rating}</span>
                                    </div>
                                  </div>

                                  {property.bedrooms && (
                                    <div className="flex items-center gap-1 mt-1">
                                      <Bed className="w-3 h-3 text-muted-foreground" />
                                      <span className="text-xs text-muted-foreground">
                                        {property.bedrooms} bedroom{property.bedrooms > 1 ? 's' : ''}
                                      </span>
                                    </div>
                                  )}

                                  <div className="flex items-center justify-between mt-2">
                                    <span className="text-sm font-medium">
                                      ${property.price}/{property.currency === 'USD' ? 'night' : property.currency}
                                    </span>
                                    {property.suitabilityScore && property.suitabilityScore > 80 && (
                                      <Badge variant="secondary" className="text-xs">
                                        Perfect Match
                                      </Badge>
                                    )}
                                  </div>

                                  {property.highlights && property.highlights.length > 0 && (
                                    <div className="flex flex-wrap gap-1 mt-2">
                                      {property.highlights.slice(0, 2).map((highlight, idx) => (
                                        <Badge key={idx} variant="outline" className="text-xs">
                                          {highlight}
                                        </Badge>
                                      ))}
                                    </div>
                                  )}

                                  {property.reasonForRecommendation && (
                                    <p className="text-xs text-muted-foreground mt-2 italic">
                                      "{property.reasonForRecommendation}"
                                    </p>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}

                          {message.data.length > 3 && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="w-full"
                              onClick={() => {
                                // Show all properties
                                const searchParams = new URLSearchParams({
                                  properties: message.data.map((p: any) => p.id).join(',')
                                });
                                navigate(`/results?${searchParams.toString()}`);
                                onClose?.();
                              }}
                            >
                              View All {message.data.length} Properties
                            </Button>
                          )}
                        </div>
                      )}

                      {/* Experience Recommendations */}
                      {message.type === 'experience' && message.data && (
                        <div className="mt-3 space-y-2">
                          {message.data.map((experience: ExperienceRecommendation, index: number) => (
                            <div key={index} className="p-3 border rounded-lg bg-background">
                              <div className="flex items-start gap-3">
                                <div className="w-8 h-8 rounded-full bg-accent flex items-center justify-center flex-shrink-0">
                                  {experience.type === 'restaurant' && <Utensils className="w-4 h-4" />}
                                  {experience.type === 'attraction' && <Camera className="w-4 h-4" />}
                                  {experience.type === 'activity' && <Mountain className="w-4 h-4" />}
                                  {experience.type === 'transportation' && <Car className="w-4 h-4" />}
                                  {experience.type === 'event' && <Calendar className="w-4 h-4" />}
                                </div>
                                <div className="flex-1">
                                  <h5 className="font-medium text-sm">{experience.name}</h5>
                                  <p className="text-xs text-muted-foreground mt-1">{experience.description}</p>
                                  <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                                    {experience.duration && (
                                      <div className="flex items-center gap-1">
                                        <Clock className="w-3 h-3" />
                                        {experience.duration}
                                      </div>
                                    )}
                                    {experience.priceRange && (
                                      <div className="flex items-center gap-1">
                                        <DollarSign className="w-3 h-3" />
                                        {experience.priceRange}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Travel Insights */}
                      {message.type === 'insight' && message.data && (
                        <div className="mt-3 space-y-2">
                          {message.data.map((insight: TravelInsight, index: number) => (
                            <div
                              key={index}
                              className={cn(
                                "p-3 border rounded-lg",
                                insight.importance === 'high' ? 'border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-900/20' :
                                insight.importance === 'medium' ? 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20' :
                                'border-gray-200 bg-gray-50 dark:border-gray-800 dark:bg-gray-900/20'
                              )}
                            >
                              <div className="flex items-start gap-2">
                                <div className={cn(
                                  "w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0",
                                  insight.importance === 'high' ? 'bg-orange-100 dark:bg-orange-900' :
                                  insight.importance === 'medium' ? 'bg-blue-100 dark:bg-blue-900' :
                                  'bg-gray-100 dark:bg-gray-900'
                                )}>
                                  {insight.type === 'weather' && <Waves className="w-3 h-3" />}
                                  {insight.type === 'events' && <Calendar className="w-3 h-3" />}
                                  {insight.type === 'pricing' && <TrendingUp className="w-3 h-3" />}
                                  {insight.type === 'local_tips' && <Navigation className="w-3 h-3" />}
                                  {insight.type === 'safety' && <Heart className="w-3 h-3" />}
                                </div>
                                <div className="flex-1">
                                  <h5 className="font-medium text-sm">{insight.title}</h5>
                                  <p className="text-xs text-muted-foreground mt-1">{insight.content}</p>
                                  {insight.actionable && (
                                    <Badge variant="outline" className="text-xs mt-2">
                                      Actionable Tip
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Action Items */}
                      {message.type === 'action' && message.data && (
                        <div className="mt-3">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              if (message.data.type === 'location') {
                                handleLocationResponse(message.data.data);
                              } else if (message.data.type === 'search') {
                                setInput(message.data.label);
                                inputRef.current?.focus();
                              }
                            }}
                          >
                            {message.data.label}
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {message.role === 'user' && (
                  <Avatar className="w-8 h-8">
                    <AvatarFallback>
                      <User className="w-4 h-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}

            {isLoading && (
              <div className="flex gap-3 justify-start">
                <Avatar className="w-8 h-8">
                  <AvatarFallback>
                    <Bot className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
                <div className="bg-secondary text-secondary-foreground rounded-lg px-4 py-2">
                  <div className="flex items-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span className="text-sm">Thinking...</span>
                  </div>
                </div>
              </div>
            )}

            {messages.length === 1 && (
              <div className="grid grid-cols-2 gap-2 mt-4">
                {quickActions.map((action, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    className="h-auto p-3 text-left"
                    onClick={() => handleQuickAction(action.action)}
                  >
                    <div className="flex items-center gap-2">
                      {action.icon}
                      <span className="text-xs">{action.label}</span>
                    </div>
                  </Button>
                ))}
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>

      <CardFooter className="border-t p-4 bg-gray-50 dark:bg-gray-900/50">
        <form onSubmit={handleSubmit} className="flex gap-3 w-full">
          <Input
            ref={inputRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask me anything about travel..."
            className="flex-1"
            disabled={isLoading}
          />
          <Button type="submit" disabled={!input.trim() || isLoading}>
            <Send className="w-4 h-4" />
          </Button>
        </form>
      </CardFooter>
    </Card>
  );
}
