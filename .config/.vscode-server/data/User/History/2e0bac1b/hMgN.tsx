import React, { useState, useRef, useEffect, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';
import {
  Send,
  MapPin,
  Calendar,
  Users,
  Star,
  Hotel,
  Sparkles,
  Loader2,
  X,
  Maximize2,
  Minimize2,
  Bot,
  User,
  Globe,
  Home,
  Bed,
  Car,
  Utensils,
  Wifi,
  Waves,
  Mountain,
  TreePine,
  Building,
  Heart,
  TrendingUp,
  Clock,
  DollarSign,
  Camera,
  Navigation,
  Search,
  MessageCircle,
  Eye
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { getSessionId } from '@/lib/session';

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  type?: 'text' | 'location' | 'properties' | 'action' | 'error' | 'experience' | 'insight' | 'recommendation';
  data?: any;
  metadata?: {
    confidence?: number;
    sources?: string[];
    actionable?: boolean;
    priority?: 'low' | 'medium' | 'high';
  };
}

interface PropertyRecommendation {
  id: number;
  name: string;
  type: string;
  bedrooms?: number;
  price: number;
  currency: string;
  rating: number;
  image: string;
  highlights: string[];
  suitabilityScore?: number;
  reasonForRecommendation: string;
}

interface ExperienceRecommendation {
  id: string;
  name: string;
  type: 'attraction' | 'restaurant' | 'activity' | 'event' | 'transportation';
  description: string;
  location: string;
  duration?: string;
  priceRange?: string;
  bestTime?: string;
  bookingRequired?: boolean;
  image?: string;
}

interface TravelInsight {
  type: 'weather' | 'events' | 'pricing' | 'crowds' | 'local_tips' | 'safety';
  title: string;
  content: string;
  importance: 'low' | 'medium' | 'high';
  actionable: boolean;
  icon?: string;
}

interface UnifiedAIChatProps {
  context?: {
    location?: string | null;
    checkIn?: string | null;
    checkOut?: string | null;
    guests?: string | null;
    rooms?: string | null;
    bedrooms?: string | null;
    properties?: any[];
    filters?: any;
    searchHistory?: any[];
    groupType?: 'family' | 'friends' | 'corporate' | 'wedding' | 'reunion' | 'multi_generational';
    travelPurpose?: string;
    budget?: { min?: number; max?: number; currency?: string };
    preferences?: {
      propertyTypes?: string[];
      amenities?: string[];
      accessibility?: string[];
    };
  };
  variant?: 'modal' | 'embedded' | 'floating' | 'sidebar';
  onClose?: () => void;
  onNavigate?: (path: string, params?: any) => void;
  onPropertySelect?: (property: any) => void;
  onSearchUpdate?: (searchParams: any) => void;
  className?: string;
  showWelcome?: boolean;
}

// Helper functions for enhanced geography integration
const getTimezoneFromCoordinates = (lat: number, lng: number): string => {
  // Simplified timezone detection based on longitude
  const timezoneOffset = Math.round(lng / 15);
  return `UTC${timezoneOffset >= 0 ? '+' : ''}${timezoneOffset}`;
};

const getRegionFromCoordinates = (lat: number, lng: number): string => {
  // Simplified region detection
  if (lat > 60) return 'Arctic';
  if (lat > 35 && lng > -10 && lng < 40) return 'Europe';
  if (lat > 25 && lat < 50 && lng > -130 && lng < -60) return 'North America';
  if (lat > -35 && lat < 35 && lng > -20 && lng < 50) return 'Africa/Middle East';
  if (lat > -50 && lat < 35 && lng > 60 && lng < 150) return 'Asia';
  if (lat > -50 && lat < -10 && lng > 110 && lng < 180) return 'Australia/Oceania';
  if (lat > -60 && lat < 15 && lng > -90 && lng < -30) return 'South America';
  return 'International';
};

export default function UnifiedAIChat({
  context,
  variant = 'modal',
  onClose,
  onNavigate,
  onPropertySelect,
  onSearchUpdate,
  className,
  showWelcome = true
}: UnifiedAIChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);
  const [sessionId] = useState(() => getSessionId());
  const [_, navigate] = useLocation();
  const { toast } = useToast();
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [conversationContext, setConversationContext] = useState({
    userPreferences: {},
    searchHistory: [] as Array<{ location: string; timestamp: Date }>,
    currentFocus: null as string | null,
    travelStyle: null as string | null,
    lastKnownLocation: null as {
      name: string;
      coordinates: { lat: number; lng: number };
      timestamp: Date
    } | null,
    geographicContext: {
      preferredRegions: [] as string[],
      visitedLocations: [] as string[],
      travelRadius: null as number | null
    }
  });
  const initializationRef = useRef({
    hasInitialized: false,
    hasProcessedInitialMessage: false,
    messageBeingSent: false
  });

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  // Initialize with enhanced welcome message and process any stored initial message
  useEffect(() => {
    if (initializationRef.current.hasInitialized) return;
    initializationRef.current.hasInitialized = true;

    if (showWelcome) {
      // Add enhanced welcome message with context awareness
      const welcomeMessage: Message = {
        id: `system-welcome-${Date.now()}`,
        role: 'system',
        content: generateWelcomeMessage(context),
        timestamp: new Date(),
        type: 'text',
        metadata: {
          confidence: 1.0,
          actionable: true,
          priority: 'high'
        }
      };

      setMessages([welcomeMessage]);
    }

    // Check for stored initial message from "Plan with AI" button
    const storedHistory = localStorage.getItem('chatHistory');
    const triggerFlag = localStorage.getItem('ai_chat_trigger');
    
    if (triggerFlag === 'true' && storedHistory && !initializationRef.current.hasProcessedInitialMessage) {
      try {
        const parsedHistory = JSON.parse(storedHistory);
        const lastMessage = parsedHistory[parsedHistory.length - 1];
        
        if (lastMessage && lastMessage.role === 'user' && !initializationRef.current.messageBeingSent) {
          initializationRef.current.hasProcessedInitialMessage = true;
          initializationRef.current.messageBeingSent = true;
          
          // Clear trigger flag
          localStorage.removeItem('ai_chat_trigger');
          
          // Add user message and send to AI
          const userMessage: Message = {
            id: lastMessage.id || `user-${Date.now()}`,
            role: 'user',
            content: lastMessage.content,
            timestamp: new Date(),
            type: 'text'
          };
          
          setMessages(prev => [...prev, userMessage]);
          sendMessageToAI(lastMessage.content);
        }
      } catch (error) {
        console.error('Failed to process stored message:', error);
      }
    }
  }, [showWelcome, context]);

  // Helper function to generate contextual welcome message
  const generateWelcomeMessage = (context?: any): string => {
    if (context?.location && context?.checkIn) {
      return `🌟 Welcome! I see you're planning a trip to ${context.location} starting ${new Date(context.checkIn).toLocaleDateString()}. I'm your AI travel expert, and I'm excited to help you create an amazing experience!

Whether you need accommodations, local insights, or activity recommendations, I'm here to make your travel planning effortless and enjoyable.`;
    }

    if (context?.bedrooms && parseInt(context.bedrooms) > 1) {
      return `🏡 Welcome! I specialize in finding perfect multi-bedroom accommodations for groups and families. With ${context.bedrooms} bedrooms needed, I'll help you discover amazing properties that bring everyone together comfortably.

Let's find you the ideal place to create lasting memories!`;
    }

    return `🌟 **Your Personal AI Travel Companion** is here!

I'm not just a search tool - I'm your expert travel advisor with deep knowledge of destinations worldwide.

🎯 **I can help you:**
• **🗺️ Discover** amazing destinations with insider knowledge
• **🏨 Find** perfect accommodations for any group size
• **✨ Plan** unforgettable experiences and activities
• **💡 Provide** local insights, weather, and timing tips
• **🎪 Navigate** like a local with expert recommendations

**Ready to plan something amazing? Just tell me your destination or describe your dream trip!**`;
  };

  const sendMessageToAI = useCallback(async (messageContent: string) => {
    if (initializationRef.current.messageBeingSent && messageContent !== input) {
      // This is an initial message being processed
    } else {
      setIsLoading(true);
    }

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: messageContent,
          context: {
            ...context,
            conversation: conversationContext,
            userPreferences: conversationContext.userPreferences,
            searchHistory: conversationContext.searchHistory
          },
          sessionId,
          extractLocation: true,
          enhancedMode: true
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      let assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        role: 'assistant',
        content: '',
        timestamp: new Date(),
        type: 'text'
      };

      setMessages(prev => [...prev, assistantMessage]);

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);
              
              if (parsed.type === 'text') {
                assistantMessage.content += parsed.data;
                setMessages(prev =>
                  prev.map(msg =>
                    msg.id === assistantMessage.id
                      ? { ...msg, content: assistantMessage.content }
                      : msg
                  )
                );
              } else if (parsed.type === 'location') {
                handleLocationResponse(parsed.data);
              } else if (parsed.type === 'properties') {
                handlePropertiesResponse(parsed.data);
              } else if (parsed.type === 'action') {
                handleActionResponse(parsed.data);
              } else if (parsed.type === 'experience') {
                handleExperienceResponse(parsed.data);
              } else if (parsed.type === 'insight') {
                handleInsightResponse(parsed.data);
              }
            } catch (error) {
              console.error('Failed to parse streaming response:', error);
            }
          }
        }
      }

    } catch (error) {
      console.error('Chat error:', error);
      toast({
        title: 'Error',
        description: 'Failed to send message. Please try again.',
        variant: 'destructive',
      });
      
      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        role: 'assistant',
        content: 'I apologize, but I encountered an error. Please try again.',
        timestamp: new Date(),
        type: 'error'
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      initializationRef.current.messageBeingSent = false;
    }
  }, [context, sessionId, toast, input]);

  const handleLocationResponse = (locationData: any) => {
    // Enhanced conversation context with geographic intelligence
    setConversationContext(prev => ({
      ...prev,
      currentFocus: 'location',
      searchHistory: [...prev.searchHistory, { location: locationData.name, timestamp: new Date() }],
      lastKnownLocation: {
        name: locationData.name,
        coordinates: { lat: locationData.lat, lng: locationData.lng },
        timestamp: new Date()
      }
    }));

    // Generate context-aware content based on location and user context
    const generateContextualContent = () => {
      let content = `🗺️ Perfect! I found **${locationData.name}** for you.`;

      // Add context-specific insights
      if (context?.bedrooms && parseInt(context.bedrooms) > 1) {
        content += ` This is an excellent choice for your ${context.bedrooms}-bedroom group accommodation needs!`;
      }

      if (context?.groupType) {
        const groupMessages = {
          family: "Perfect for your family vacation with plenty of family-friendly options!",
          friends: "Great choice for your group getaway with amazing nightlife and activities!",
          corporate: "Excellent business destination with top-tier conference facilities!",
          wedding: "Romantic and beautiful location perfect for your wedding celebration!",
          reunion: "Wonderful spot for bringing everyone together with spacious accommodations!",
          multi_generational: "Ideal for multi-generational travel with something for everyone!"
        };
        content += ` ${groupMessages[context.groupType] || 'Perfect for your group travel needs!'}`;
      }

      return content;
    };

    // Create an enhanced interactive location message
    const locationMessage: Message = {
      id: `location-${Date.now()}`,
      role: 'assistant',
      content: generateContextualContent(),
      timestamp: new Date(),
      type: 'location',
      data: {
        ...locationData,
        contextualInsights: {
          groupType: context?.groupType,
          bedrooms: context?.bedrooms,
          travelPurpose: context?.travelPurpose,
          hasSpecialNeeds: !!(context?.bedrooms && parseInt(context.bedrooms) > 1)
        }
      },
      metadata: {
        actionable: true,
        priority: 'high',
        geography: {
          coordinates: { lat: locationData.lat, lng: locationData.lng },
          name: locationData.name,
          type: locationData.type || 'destination',
          timezone: getTimezoneFromCoordinates(locationData.lat, locationData.lng),
          region: getRegionFromCoordinates(locationData.lat, locationData.lng)
        }
      }
    };

    setMessages(prev => [...prev, locationMessage]);

    // 🚀 BACKGROUND PROPERTY SEARCH - Search properties automatically but don't navigate
    if (context?.checkIn && context?.checkOut) {
      console.log('🔍 Starting background property search for:', locationData.name);

      // Trigger background property search
      setTimeout(async () => {
        try {
          const searchParams = new URLSearchParams({
            locationName: locationData.name,
            lat: locationData.lat.toString(),
            lng: locationData.lng.toString(),
            checkIn: context.checkIn || new Date(Date.now() + 86400000).toISOString().split('T')[0],
            checkOut: context.checkOut || new Date(Date.now() + 2 * 86400000).toISOString().split('T')[0],
            guests: context.guests || '2',
            rooms: context.rooms || '1',
            pageSize: '5' // Limit for preview
          });

          const response = await fetch(`/api/properties/search?${searchParams.toString()}`);
          if (response.ok) {
            const data = await response.json();
            if (data.properties && data.properties.length > 0) {
              // Add property preview message
              const propertyPreviewMessage: Message = {
                id: `property-preview-${Date.now()}`,
                role: 'assistant',
                content: `🏨 Great news! I found ${data.properties.length} amazing properties in ${locationData.name}:`,
                timestamp: new Date(),
                type: 'properties',
                data: data.properties.slice(0, 3), // Show top 3
                metadata: {
                  confidence: 0.9,
                  actionable: true,
                  priority: 'high',
                  searchContext: {
                    location: locationData.name,
                    coordinates: { lat: locationData.lat, lng: locationData.lng },
                    totalFound: data.properties.length
                  }
                }
              };

              setMessages(prev => [...prev, propertyPreviewMessage]);
            }
          }
        } catch (error) {
          console.error('Background property search failed:', error);
        }
      }, 1500); // Small delay to let location card render first
    }
  };

  const handlePropertiesResponse = (properties: any[]) => {
    const propertyMessage: Message = {
      id: `properties-${Date.now()}`,
      role: 'assistant',
      content: `I found ${properties.length} perfect accommodations for you:`,
      timestamp: new Date(),
      type: 'properties',
      data: properties,
      metadata: {
        confidence: 0.9,
        actionable: true,
        priority: 'high'
      }
    };

    setMessages(prev => [...prev, propertyMessage]);
  };

  const handleActionResponse = (actionData: any) => {
    // Handle different action types
    if (actionData.type === 'location') {
      // Delegate to location handler for proper rendering
      handleLocationResponse(actionData.data);
      return;
    }

    // For other action types, create a generic action message
    const actionMessage: Message = {
      id: `action-${Date.now()}`,
      role: 'assistant',
      content: actionData.label || 'Here\'s what I found:',
      timestamp: new Date(),
      type: 'action',
      data: actionData,
      metadata: {
        actionable: true,
        priority: 'medium'
      }
    };

    setMessages(prev => [...prev, actionMessage]);
  };

  const handleExperienceResponse = (experiences: ExperienceRecommendation[]) => {
    const experienceMessage: Message = {
      id: `experience-${Date.now()}`,
      role: 'assistant',
      content: `Here are some amazing experiences I recommend:`,
      timestamp: new Date(),
      type: 'experience',
      data: experiences,
      metadata: {
        confidence: 0.8,
        actionable: true,
        priority: 'medium'
      }
    };

    setMessages(prev => [...prev, experienceMessage]);
  };

  const handleInsightResponse = (insights: TravelInsight[]) => {
    const insightMessage: Message = {
      id: `insight-${Date.now()}`,
      role: 'assistant',
      content: `Here are some important travel insights:`,
      timestamp: new Date(),
      type: 'insight',
      data: insights,
      metadata: {
        confidence: 0.9,
        actionable: true,
        priority: insights.some(i => i.importance === 'high') ? 'high' : 'medium'
      }
    };

    setMessages(prev => [...prev, insightMessage]);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: input.trim(),
      timestamp: new Date(),
      type: 'text'
    };

    setMessages(prev => [...prev, userMessage]);
    sendMessageToAI(input.trim());
    setInput('');
  };

  const handleQuickAction = (action: string) => {
    console.log('🚀 Quick action triggered:', action);

    // Create user message immediately
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: action,
      timestamp: new Date(),
      type: 'text'
    };

    setMessages(prev => [...prev, userMessage]);

    // Send to AI immediately
    sendMessageToAI(action);

    // Clear input
    setInput('');
  };

  const containerClasses = cn(
    'flex flex-col overflow-hidden',
    {
      'fixed inset-4 z-50 bg-background border rounded-lg shadow-lg max-w-[95vw] max-h-[95vh]': variant === 'modal',
      'w-full h-full': variant === 'embedded',
      'fixed bottom-4 right-4 w-96 h-[500px] z-50 bg-background border rounded-lg shadow-lg': variant === 'floating',
      'fixed inset-2 z-50': variant === 'modal' && isMaximized,
    },
    className
  );

  // 🧠 SMART Context-Aware Quick Actions with Geography Intelligence
  const getQuickActions = () => {
    const baseActions = [];

    // 🗺️ Geography-first actions based on last known location
    if (conversationContext.lastKnownLocation) {
      const location = conversationContext.lastKnownLocation;
      baseActions.push({
        icon: <Navigation className="w-4 h-4" />,
        label: `Around ${location.name}`,
        description: "Discover nearby attractions, restaurants, and local activities",
        action: `Show me nearby attractions, restaurants, and activities within walking distance of ${location.name}. Include local transportation options!`
      });

      baseActions.push({
        icon: <MapPin className="w-4 h-4" />,
        label: `Best Areas in ${location.name}`,
        description: "Find the perfect neighborhood for your stay",
        action: `Which neighborhoods in ${location.name} are best for staying? Include safety, convenience, and local character insights!`
      });
    }

    // 🏠 Context-aware accommodation actions
    if (context?.bedrooms && parseInt(context.bedrooms) > 1) {
      baseActions.unshift({
        icon: <Bed className="w-4 h-4" />,
        label: `${context.bedrooms}-Bedroom Gems`,
        description: `Perfect multi-bedroom accommodations for your group`,
        action: `Find amazing ${context.bedrooms}-bedroom properties with great reviews and perfect locations for my group size`
      });
    }

    // 👥 Group-specific intelligent actions
    if (context?.groupType) {
      const smartGroupActions = {
        family: {
          icon: <Users className="w-4 h-4" />,
          label: 'Family Paradise',
          description: 'Kid-friendly stays with pools and activities',
          action: 'Find family-friendly accommodations with pools, kids activities, and nearby attractions. Include safety ratings and family reviews!'
        },
        friends: {
          icon: <Sparkles className="w-4 h-4" />,
          label: 'Epic Group Trip',
          description: 'Perfect spots for groups with great nightlife',
          action: 'Show me party-friendly accommodations with great nightlife nearby, group activities, and spaces for everyone to hang out!'
        },
        corporate: {
          icon: <TrendingUp className="w-4 h-4" />,
          label: 'Business Excellence',
          description: 'Professional hotels with business amenities',
          action: 'Find business hotels with conference facilities, high-speed internet, and convenient airport/downtown access!'
        },
        wedding: {
          icon: <Heart className="w-4 h-4" />,
          label: 'Wedding Magic',
          description: 'Romantic venues perfect for celebrations',
          action: 'Suggest romantic venues and accommodations perfect for wedding parties, with photo opportunities and celebration spaces!'
        },
        reunion: {
          icon: <Home className="w-4 h-4" />,
          label: 'Reunion Central',
          description: 'Large spaces for family gatherings',
          action: 'Find large vacation rentals perfect for family reunions with common areas, kitchens, and space for everyone to gather!'
        },
        multi_generational: {
          icon: <Users className="w-4 h-4" />,
          label: 'Multi-Gen Perfect',
          description: 'Accessible comfort for all ages',
          action: 'Show me accommodations with accessibility features, varied activity options, and comfort for all ages from kids to grandparents!'
        }
      };

      if (smartGroupActions[context.groupType]) {
        baseActions.unshift(smartGroupActions[context.groupType]);
      }
    }

    // 🌍 Smart destination discovery
    if (!conversationContext.lastKnownLocation) {
      baseActions.push({
        icon: <Globe className="w-4 h-4" />,
        label: 'Discover Destinations',
        description: 'Trending places to visit this season',
        action: 'Show me trending travel destinations for this season with insider tips on the best areas to stay!'
      });
    }

    // 💎 Unique experiences based on context
    baseActions.push({
      icon: <Sparkles className="w-4 h-4" />,
      label: 'Unique Stays',
      description: conversationContext.lastKnownLocation
        ? `Special accommodations in ${conversationContext.lastKnownLocation.name}`
        : 'Extraordinary stays around the world',
      action: conversationContext.lastKnownLocation
        ? `Find unique and memorable accommodations in ${conversationContext.lastKnownLocation.name} - treehouses, historic properties, or local gems!`
        : 'Suggest unique accommodations worldwide - castles, treehouses, houseboats, and other extraordinary stays!'
    });

    // 💰 Smart deals and timing
    baseActions.push({
      icon: <TrendingUp className="w-4 h-4" />,
      label: 'Best Value',
      description: context?.checkIn ? 'Great deals for your travel dates' : 'Current best accommodation deals',
      action: context?.checkIn
        ? `Find the best accommodation deals for my ${context.checkIn} travel dates with insider tips on saving money!`
        : 'Show me the best accommodation deals right now and tips for finding great value!'
    });

    return baseActions.slice(0, 6); // Limit to 6 most relevant actions
  };

  const quickActions = getQuickActions();

  return (
    <Card className={containerClasses}>
      <CardHeader className="border-b bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center">
              <Bot className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold">AI Travel Companion</h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Your intelligent travel planning assistant
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {variant === 'modal' && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsMaximized(!isMaximized)}
              >
                {isMaximized ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
              </Button>
            )}
            {onClose && (
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="flex-1 p-0 overflow-hidden">
        <ScrollArea ref={scrollAreaRef} className="h-full">
          <div className="p-4 space-y-4 max-w-full">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  'flex gap-3',
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                )}
              >
                {message.role !== 'user' && (
                  <Avatar className="w-8 h-8">
                    <AvatarFallback>
                      <Bot className="w-4 h-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
                
                <div
                  className={cn(
                    'max-w-[85%] w-full rounded-lg px-4 py-3 overflow-hidden',
                    message.role === 'user'
                      ? 'bg-primary text-primary-foreground'
                      : message.role === 'system'
                      ? 'bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 text-foreground border'
                      : 'bg-secondary text-secondary-foreground'
                  )}
                >
                  <div className="flex items-start gap-2">
                    {message.metadata?.priority === 'high' && (
                      <Sparkles className="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                    )}
                    <div className="flex-1 min-w-0 overflow-hidden">
                      {/* Enhanced text formatting for better readability */}
                      <div className="text-sm leading-relaxed space-y-3 break-words overflow-wrap-anywhere">
                        {message.content.split('\n\n').map((paragraph, index) => {
                          // Handle different types of content
                          if (paragraph.trim().startsWith('**') && paragraph.trim().endsWith('**')) {
                            // Bold headers
                            return (
                              <h4 key={index} className="font-bold text-base text-gray-900 dark:text-gray-100 mt-4 mb-2">
                                {paragraph.replace(/\*\*/g, '')}
                              </h4>
                            );
                          } else if (paragraph.includes('•') || paragraph.includes('-')) {
                            // Lists
                            return (
                              <div key={index} className="space-y-1">
                                {paragraph.split('\n').map((line, lineIndex) => {
                                  if (line.trim().startsWith('•') || line.trim().startsWith('-')) {
                                    return (
                                      <div key={lineIndex} className="flex items-start gap-2">
                                        <span className="text-blue-500 mt-1 flex-shrink-0">•</span>
                                        <span className="break-words overflow-wrap-anywhere">{line.replace(/^[•-]\s*/, '')}</span>
                                      </div>
                                    );
                                  }
                                  return line.trim() ? <p key={lineIndex}>{line}</p> : null;
                                })}
                              </div>
                            );
                          } else if (paragraph.trim()) {
                            // Regular paragraphs
                            return (
                              <p key={index} className="text-gray-700 dark:text-gray-300 break-words overflow-wrap-anywhere whitespace-pre-wrap">
                                {paragraph.split('**').map((part, partIndex) =>
                                  partIndex % 2 === 1 ?
                                    <strong key={partIndex} className="font-semibold text-gray-900 dark:text-gray-100">{part}</strong> :
                                    part
                                )}
                              </p>
                            );
                          }
                          return null;
                        })}
                      </div>

                      {/* 🌟 AMAZING Interactive Location Display */}
                      {message.type === 'location' && message.data && (
                        <div className="mt-4 p-6 border-2 border-blue-200 dark:border-blue-700 rounded-2xl bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-900/30 dark:via-indigo-900/30 dark:to-purple-900/30 shadow-lg hover:shadow-xl transition-all duration-300">
                          {/* Header with location badge */}
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-2">
                              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                              <span className="text-xs font-medium text-green-700 dark:text-green-400 uppercase tracking-wide">
                                📍 DESTINATION FOUND
                              </span>
                            </div>
                            <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white border-0">
                              ⭐ Perfect Match
                            </Badge>
                          </div>

                          <div className="flex items-start gap-6">
                            {/* Enhanced location icon */}
                            <div className="relative">
                              <div className="w-20 h-20 rounded-2xl bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-700 flex items-center justify-center flex-shrink-0 shadow-lg">
                                <MapPin className="w-10 h-10 text-white" />
                              </div>
                              <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                <span className="text-white text-xs font-bold">✓</span>
                              </div>
                            </div>

                            <div className="flex-1">
                              {/* Location name with enhanced typography */}
                              <h4 className="font-bold text-2xl text-gray-900 dark:text-gray-100 mb-2 leading-tight">
                                {message.data.name}
                              </h4>

                              {/* Coordinates with better formatting */}
                              <div className="flex items-center gap-2 mb-3">
                                <div className="flex items-center gap-1 px-3 py-1 bg-white/60 dark:bg-gray-800/60 rounded-full">
                                  <Globe className="w-3 h-3 text-blue-600" />
                                  <span className="text-xs font-mono text-gray-600 dark:text-gray-400">
                                    {message.data.lat?.toFixed(4)}, {message.data.lng?.toFixed(4)}
                                  </span>
                                </div>
                                {message.data.type && (
                                  <Badge variant="outline" className="text-xs bg-white/60 dark:bg-gray-800/60">
                                    {message.data.type}
                                  </Badge>
                                )}
                              </div>

                              {/* Enhanced Description with Context */}
                              {message.data.description && (
                                <p className="text-sm text-gray-700 dark:text-gray-300 mb-4 italic bg-white/40 dark:bg-gray-800/40 p-3 rounded-lg">
                                  💫 {message.data.description}
                                </p>
                              )}

                              {/* 🌍 Real-Time Context Panel */}
                              <div className="bg-white/40 dark:bg-gray-800/40 rounded-lg p-3 mb-4">
                                <div className="grid grid-cols-2 gap-3 text-xs">
                                  <div className="flex items-center gap-2">
                                    <Clock className="w-3 h-3 text-blue-600" />
                                    <span className="text-gray-600 dark:text-gray-400">
                                      {message.metadata?.geography?.timezone || 'Local Time'}
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <Globe className="w-3 h-3 text-green-600" />
                                    <span className="text-gray-600 dark:text-gray-400">
                                      {message.metadata?.geography?.region || 'Region'}
                                    </span>
                                  </div>
                                  {message.data.contextualInsights?.groupType && (
                                    <div className="flex items-center gap-2 col-span-2">
                                      <Users className="w-3 h-3 text-purple-600" />
                                      <span className="text-gray-600 dark:text-gray-400">
                                        Optimized for {message.data.contextualInsights.groupType} travel
                                      </span>
                                    </div>
                                  )}
                                </div>
                              </div>

                              {/* 🗺️ ENHANCED Geography & Maps Integration */}
                              <div className="space-y-4 mt-4">
                                {/* Interactive Mini Map Preview */}
                                <div className="bg-white/60 dark:bg-gray-800/60 rounded-xl p-4 border border-blue-200/50">
                                  <div className="flex items-center gap-3 mb-3">
                                    <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                      <MapPin className="w-4 h-4 text-white" />
                                    </div>
                                    <div>
                                      <h6 className="font-semibold text-sm">📍 Geographic Context</h6>
                                      <p className="text-xs text-gray-600 dark:text-gray-400">
                                        Lat: {message.data.lat?.toFixed(6)} • Lng: {message.data.lng?.toFixed(6)}
                                      </p>
                                    </div>
                                  </div>

                                  {/* Mini map placeholder with interactive preview */}
                                  <div
                                    className="w-full h-24 bg-gradient-to-br from-blue-100 to-green-100 dark:from-blue-900/30 dark:to-green-900/30 rounded-lg border-2 border-dashed border-blue-300 dark:border-blue-600 flex items-center justify-center cursor-pointer hover:bg-gradient-to-br hover:from-blue-200 hover:to-green-200 transition-all duration-200"
                                    onClick={() => {
                                      const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${message.data.lat},${message.data.lng}&zoom=12`;
                                      window.open(mapsUrl, '_blank');
                                    }}
                                  >
                                    <div className="text-center">
                                      <Globe className="w-6 h-6 mx-auto mb-1 text-blue-600" />
                                      <p className="text-xs font-medium text-blue-700 dark:text-blue-300">
                                        🗺️ Click to view interactive map
                                      </p>
                                    </div>
                                  </div>
                                </div>

                                {/* Enhanced action buttons with geography focus */}
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                  <Button
                                    size="sm"
                                    onClick={() => {
                                      const searchParams = new URLSearchParams({
                                        locationName: message.data.name,
                                        lat: message.data.lat.toString(),
                                        lng: message.data.lng.toString(),
                                        checkIn: context?.checkIn || new Date(Date.now() + 86400000).toISOString().split('T')[0],
                                        checkOut: context?.checkOut || new Date(Date.now() + 2 * 86400000).toISOString().split('T')[0],
                                        guests: context?.guests || '2',
                                        rooms: context?.rooms || '1'
                                      });

                                      if (onNavigate) {
                                        onNavigate(`/results?${searchParams.toString()}`);
                                      } else {
                                        navigate(`/results?${searchParams.toString()}`);
                                      }
                                      onClose?.();
                                    }}
                                    className="bg-gradient-to-r from-blue-500 via-indigo-600 to-purple-700 hover:from-blue-600 hover:via-indigo-700 hover:to-purple-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                                  >
                                    <Search className="w-4 h-4 mr-2" />
                                    🏨 Search Here
                                  </Button>

                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      const mapsUrl = `https://www.google.com/maps/search/?api=1&query=${message.data.lat},${message.data.lng}&zoom=15`;
                                      window.open(mapsUrl, '_blank');
                                    }}
                                    className="border-blue-300 hover:bg-blue-50 dark:border-blue-600 dark:hover:bg-blue-900/30 transition-all duration-200"
                                  >
                                    <Globe className="w-4 h-4 mr-2" />
                                    🗺️ Full Map
                                  </Button>

                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      setInput(`Show me nearby attractions, restaurants, and points of interest around ${message.data.name}. Include walking distances and local transportation options!`);
                                      inputRef.current?.focus();
                                    }}
                                    className="border-green-300 hover:bg-green-50 dark:border-green-600 dark:hover:bg-green-900/30 transition-all duration-200"
                                  >
                                    <Navigation className="w-4 h-4 mr-2" />
                                    🎯 Explore Area
                                  </Button>

                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      setInput(`Tell me about ${message.data.name} - best neighborhoods to stay, local culture, weather patterns, and insider travel tips!`);
                                      inputRef.current?.focus();
                                    }}
                                    className="border-purple-300 hover:bg-purple-50 dark:border-purple-600 dark:hover:bg-purple-900/30 transition-all duration-200"
                                  >
                                    <Sparkles className="w-4 h-4 mr-2" />
                                    ✨ Local Insights
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Bottom action bar */}
                          <div className="mt-6 pt-4 border-t border-blue-200/50 dark:border-blue-700/50">
                            <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
                              <span className="flex items-center gap-1">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                Ready to explore this destination
                              </span>
                              <span className="flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                Just found • {new Date().toLocaleTimeString()}
                              </span>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Enhanced Property Display */}
                      {message.type === 'properties' && message.data && (
                        <div className="mt-4 space-y-4">
                          {message.data.slice(0, 3).map((property: PropertyRecommendation, index: number) => (
                            <div
                              key={index}
                              className="p-4 border rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 hover:shadow-lg transition-all duration-200"
                            >
                              <div className="flex items-start gap-4">
                                <div className="w-20 h-20 rounded-xl bg-gradient-to-br from-purple-500 to-blue-600 flex items-center justify-center flex-shrink-0">
                                  <Hotel className="w-8 h-8 text-white" />
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-start justify-between gap-2 mb-2">
                                    <h4 className="font-bold text-base text-gray-900 dark:text-gray-100 leading-tight">
                                      {property.name}
                                    </h4>
                                    <div className="flex items-center gap-1 flex-shrink-0 bg-yellow-100 dark:bg-yellow-900/30 px-2 py-1 rounded-full">
                                      <Star className="w-3 h-3 fill-yellow-500 text-yellow-500" />
                                      <span className="text-xs font-medium text-yellow-700 dark:text-yellow-300">
                                        {property.rating}
                                      </span>
                                    </div>
                                  </div>

                                  <div className="flex items-center gap-4 mb-3 text-sm text-gray-600 dark:text-gray-400">
                                    {property.bedrooms && (
                                      <div className="flex items-center gap-1">
                                        <Bed className="w-4 h-4" />
                                        <span>{property.bedrooms} bedroom{property.bedrooms > 1 ? 's' : ''}</span>
                                      </div>
                                    )}
                                    <div className="flex items-center gap-1">
                                      <MapPin className="w-4 h-4" />
                                      <span>Prime location</span>
                                    </div>
                                  </div>

                                  <div className="flex items-center justify-between mb-3">
                                    <div className="flex items-baseline gap-1">
                                      <span className="text-2xl font-bold text-green-600 dark:text-green-400">
                                        ${property.price}
                                      </span>
                                      <span className="text-sm text-gray-500">
                                        /{property.currency === 'USD' ? 'night' : property.currency}
                                      </span>
                                    </div>
                                    {property.suitabilityScore && property.suitabilityScore > 80 && (
                                      <Badge className="bg-gradient-to-r from-green-500 to-emerald-600 text-white">
                                        🎯 Perfect Match
                                      </Badge>
                                    )}
                                  </div>

                                  {property.highlights && property.highlights.length > 0 && (
                                    <div className="flex flex-wrap gap-2 mb-3">
                                      {property.highlights.slice(0, 3).map((highlight, idx) => (
                                        <Badge key={idx} variant="outline" className="text-xs bg-blue-50 dark:bg-blue-900/30 border-blue-200 dark:border-blue-700">
                                          ✨ {highlight}
                                        </Badge>
                                      ))}
                                    </div>
                                  )}

                                  {property.reasonForRecommendation && (
                                    <p className="text-sm text-gray-700 dark:text-gray-300 mb-3 italic bg-blue-50 dark:bg-blue-900/20 p-2 rounded-lg">
                                      💡 "{property.reasonForRecommendation}"
                                    </p>
                                  )}

                                  <div className="flex gap-2">
                                    <Button
                                      size="sm"
                                      onClick={() => onPropertySelect?.(property)}
                                      className="bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 flex-1"
                                    >
                                      <Eye className="w-4 h-4 mr-2" />
                                      View Details
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => {
                                        setInput(`Tell me more about ${property.name} and why it's perfect for my trip`);
                                        inputRef.current?.focus();
                                      }}
                                    >
                                      <MessageCircle className="w-4 h-4" />
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}

                          {message.data.length > 3 && (
                            <div className="mt-4 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-xl border border-indigo-200 dark:border-indigo-700">
                              <div className="text-center">
                                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                                  🎉 I found <strong>{message.data.length} amazing properties</strong> that match your needs!
                                </p>
                                <Button
                                  size="lg"
                                  className="w-full bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700"
                                  onClick={() => {
                                    // Navigate to results with context
                                    const searchParams = new URLSearchParams({
                                      ...(context?.location && { locationName: context.location }),
                                      ...(context?.checkIn && { checkIn: context.checkIn }),
                                      ...(context?.checkOut && { checkOut: context.checkOut }),
                                      ...(context?.guests && { guests: context.guests }),
                                      ...(context?.rooms && { rooms: context.rooms }),
                                      aiRecommended: 'true'
                                    });

                                    if (onNavigate) {
                                      onNavigate(`/results?${searchParams.toString()}`);
                                    } else {
                                      navigate(`/results?${searchParams.toString()}`);
                                    }
                                    onClose?.();
                                  }}
                                >
                                  <Search className="w-5 h-5 mr-2" />
                                  Explore All {message.data.length} Properties
                                </Button>
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                      {/* Experience Recommendations */}
                      {message.type === 'experience' && message.data && (
                        <div className="mt-3 space-y-2">
                          {message.data.map((experience: ExperienceRecommendation, index: number) => (
                            <div key={index} className="p-3 border rounded-lg bg-background">
                              <div className="flex items-start gap-3">
                                <div className="w-8 h-8 rounded-full bg-accent flex items-center justify-center flex-shrink-0">
                                  {experience.type === 'restaurant' && <Utensils className="w-4 h-4" />}
                                  {experience.type === 'attraction' && <Camera className="w-4 h-4" />}
                                  {experience.type === 'activity' && <Mountain className="w-4 h-4" />}
                                  {experience.type === 'transportation' && <Car className="w-4 h-4" />}
                                  {experience.type === 'event' && <Calendar className="w-4 h-4" />}
                                </div>
                                <div className="flex-1">
                                  <h5 className="font-medium text-sm">{experience.name}</h5>
                                  <p className="text-xs text-muted-foreground mt-1">{experience.description}</p>
                                  <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                                    {experience.duration && (
                                      <div className="flex items-center gap-1">
                                        <Clock className="w-3 h-3" />
                                        {experience.duration}
                                      </div>
                                    )}
                                    {experience.priceRange && (
                                      <div className="flex items-center gap-1">
                                        <DollarSign className="w-3 h-3" />
                                        {experience.priceRange}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Travel Insights */}
                      {message.type === 'insight' && message.data && (
                        <div className="mt-3 space-y-2">
                          {message.data.map((insight: TravelInsight, index: number) => (
                            <div
                              key={index}
                              className={cn(
                                "p-3 border rounded-lg",
                                insight.importance === 'high' ? 'border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-900/20' :
                                insight.importance === 'medium' ? 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20' :
                                'border-gray-200 bg-gray-50 dark:border-gray-800 dark:bg-gray-900/20'
                              )}
                            >
                              <div className="flex items-start gap-2">
                                <div className={cn(
                                  "w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0",
                                  insight.importance === 'high' ? 'bg-orange-100 dark:bg-orange-900' :
                                  insight.importance === 'medium' ? 'bg-blue-100 dark:bg-blue-900' :
                                  'bg-gray-100 dark:bg-gray-900'
                                )}>
                                  {insight.type === 'weather' && <Waves className="w-3 h-3" />}
                                  {insight.type === 'events' && <Calendar className="w-3 h-3" />}
                                  {insight.type === 'pricing' && <TrendingUp className="w-3 h-3" />}
                                  {insight.type === 'local_tips' && <Navigation className="w-3 h-3" />}
                                  {insight.type === 'safety' && <Heart className="w-3 h-3" />}
                                </div>
                                <div className="flex-1">
                                  <h5 className="font-medium text-sm">{insight.title}</h5>
                                  <p className="text-xs text-muted-foreground mt-1">{insight.content}</p>
                                  {insight.actionable && (
                                    <Badge variant="outline" className="text-xs mt-2">
                                      Actionable Tip
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Action Items */}
                      {message.type === 'action' && message.data && (
                        <div className="mt-3">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              if (message.data.type === 'location') {
                                handleLocationResponse(message.data.data);
                              } else if (message.data.type === 'search') {
                                setInput(message.data.label);
                                inputRef.current?.focus();
                              }
                            }}
                          >
                            {message.data.label}
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {message.role === 'user' && (
                  <Avatar className="w-8 h-8">
                    <AvatarFallback>
                      <User className="w-4 h-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}

            {isLoading && (
              <div className="flex gap-3 justify-start">
                <Avatar className="w-8 h-8">
                  <AvatarFallback>
                    <Bot className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
                <div className="bg-secondary text-secondary-foreground rounded-lg px-4 py-2">
                  <div className="flex items-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span className="text-sm">Thinking...</span>
                  </div>
                </div>
              </div>
            )}

            {messages.length === 1 && (
              <div className="p-6 space-y-6">
                <div className="text-center">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">
                    ✨ What can I help you with today?
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    {context?.location
                      ? `I see you're interested in ${context.location}. Let me help you find the perfect place to stay!`
                      : "I'm your AI travel expert - just tell me where you'd like to go or what you're looking for!"
                    }
                  </p>
                  {/* Debug context - remove in production */}
                  {process.env.NODE_ENV === 'development' && context && (
                    <div className="text-xs text-gray-400 mb-4 p-2 bg-gray-100 dark:bg-gray-800 rounded">
                      Debug Context: {JSON.stringify(context, null, 2)}
                    </div>
                  )}
                </div>
                <div className="space-y-3">
                  {quickActions.slice(0, 4).map((action, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      className="w-full h-auto p-4 text-left hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-blue-900/20 dark:hover:to-purple-900/20 transition-all duration-200 border-2 hover:border-blue-200"
                      onClick={() => {
                        console.log('Quick action clicked:', action.action);
                        handleQuickAction(action.action);
                      }}
                    >
                      <div className="flex items-center gap-4 w-full">
                        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 flex items-center justify-center flex-shrink-0">
                          {action.icon}
                        </div>
                        <div className="flex-1 text-left">
                          <div className="font-semibold text-base text-gray-900 dark:text-gray-100">{action.label}</div>
                          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {action.description || (action.action.length > 80 ? action.action.substring(0, 80) + '...' : action.action)}
                          </div>
                        </div>
                        <div className="text-blue-500">
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                      </div>
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>

      <CardFooter className="border-t p-4 bg-gray-50 dark:bg-gray-900/50">
        <form onSubmit={handleSubmit} className="flex gap-3 w-full">
          <Input
            ref={inputRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask me anything about travel..."
            className="flex-1"
            disabled={isLoading}
          />
          <Button type="submit" disabled={!input.trim() || isLoading}>
            <Send className="w-4 h-4" />
          </Button>
        </form>
      </CardFooter>
    </Card>
  );
}
