import { useF<PERSON>, ControllerRenderProps } from "react-hook-form";
import { useLocation } from "wouter";
import { Button } from "@/components/ui/button.js";
import { Input } from "@/components/ui/input.js";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form.js";
import { Calendar } from "@/components/ui/calendar.js";
import { format, addDays, startOfDay } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils.js";
import { useToast } from "@/hooks/use-toast.js";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover.js";
import { DateRange } from "react-day-picker";
import { useEffect, useRef, useCallback, useState } from "react";
import { DatePickerWithRange } from "@/components/ui/date-range-picker.js";

// Add Google Maps types
type AutocompleteInstance = {
  getPlace: () => {
    geometry?: {
      location: {
        lat: () => number;
        lng: () => number;
      };
    };
    formatted_address?: string;
    name?: string;
    types?: string[];
  };
  addListener: (event: string, handler: () => void) => void;
};

declare global {
  interface Window {
    google: {
      maps: {
        places: {
          Autocomplete: new (
            input: HTMLInputElement, 
            options?: {
              types?: string[];
              fields?: string[];
            }
          ) => AutocompleteInstance;
        };
        event: {
          clearInstanceListeners: (instance: any) => void;
        };
      };
    };
    initGoogleMaps?: () => void;
  }
}

export interface SearchFormValues {
  location: string;
  lat: number | null;
  lng: number | null;
  locationName: string;
  placeType: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  rooms: number;
  bedrooms?: number;
}

export interface SearchFormProps {
  onSubmit: (data: SearchFormValues) => void;
  formRef?: React.RefObject<{ submit: () => void }>;
  onChange?: (data: SearchFormValues) => void;
}

export default function SearchForm({ onSubmit: onSubmitProp, formRef, onChange }: SearchFormProps) {
  const [_, setLocation] = useLocation();
  const { toast } = useToast();
  const autocompleteRef = useRef<AutocompleteInstance | null>(null);
  const inputRef = useRef<HTMLInputElement | null>(null);
  const today = startOfDay(new Date());
  const [isGoogleMapsLoaded, setIsGoogleMapsLoaded] = useState(false);
  const [googleMapsKey, setGoogleMapsKey] = useState<string | null>(null);
  const keyFetchAttempted = useRef(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [dateRange, setDateRange] = useState<DateRange>({
    from: undefined,
    to: undefined
  });

  // Add window resize handler
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const form = useForm<SearchFormValues>({
    defaultValues: {
      location: "",
      lat: null,
      lng: null,
      locationName: "",
      placeType: "",
      checkIn: "",
      checkOut: "",
      guests: 2,
      rooms: 1,
      bedrooms: 1
    },
  });

  // Fetch API key once
  useEffect(() => {
    const fetchApiKey = async () => {
      if (keyFetchAttempted.current) return;
      keyFetchAttempted.current = true;

      try {
        const response = await fetch('/api/config');
        const config = await response.json();
        if (config.googleMapsApiKey) {
          setGoogleMapsKey(config.googleMapsApiKey);
        } else {
          throw new Error('Google Maps API key not found');
        }
      } catch (error) {
        console.error('Failed to fetch API key:', error);
        toast({
          title: "Error",
          description: "Failed to initialize location search.",
          variant: "destructive",
        });
      }
    };

    fetchApiKey();
  }, [toast]);

  // Load Google Maps script
  useEffect(() => {
    if (!googleMapsKey) return;

    const loadGoogleMaps = async () => {
      try {
        // Check if script is already loaded and initialized properly
        if (window.google?.maps?.places?.Autocomplete) {
          setIsGoogleMapsLoaded(true);
          return;
        }

        // Remove any existing scripts and callbacks
        const cleanup = () => {
          const scripts = document.querySelectorAll('script[src*="maps.googleapis.com"]');
          scripts.forEach(script => script.remove());
          // Fix TypeScript error with optional chaining
          if (window.google) {
            window.google = undefined as any;
          }
        };
        cleanup();

        // Create a script element
        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?key=${googleMapsKey}&libraries=places`;
        script.async = true;

        // Create a promise to handle script loading
        await new Promise<void>((resolve, reject) => {
          script.addEventListener('load', () => {
            if (window.google?.maps?.places?.Autocomplete) {
              setIsGoogleMapsLoaded(true);
              resolve();
            } else {
              reject(new Error('Google Maps failed to initialize'));
            }
          });

          script.addEventListener('error', () => {
            cleanup();
            reject(new Error('Failed to load Google Maps script'));
          });

          document.head.appendChild(script);
        });

      } catch (error) {
        console.error('Failed to load Google Maps:', error);
        toast({
          title: "Error",
          description: "Location search is temporarily unavailable.",
          variant: "destructive",
        });
      }
    };

    loadGoogleMaps();

    // Cleanup on unmount
    return () => {
      const scripts = document.querySelectorAll('script[src*="maps.googleapis.com"]');
      scripts.forEach(script => {
        if (!window.google?.maps?.places?.Autocomplete) {
          script.remove();
        }
      });
    };
  }, [googleMapsKey, toast]);

  // Initialize autocomplete when input is ready and Google Maps is loaded
  useEffect(() => {
    if (inputRef.current && isGoogleMapsLoaded && window.google?.maps?.places) {
      // Prevent form submission on enter
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Enter') {
          e.preventDefault();
        }
      };
      inputRef.current.addEventListener('keydown', handleKeyDown);

      const autocomplete = new window.google.maps.places.Autocomplete(inputRef.current, {
        types: ['(cities)'],  // Only use cities type
        fields: ['geometry.location', 'formatted_address', 'name', 'types']
      });

      autocomplete.addListener('place_changed', () => {
        const place = autocomplete.getPlace();
        
        if (!place.geometry?.location) {
          toast({
            title: "Error",
            description: "Please select a location from the suggestions.",
            variant: "destructive",
          });
          return;
        }

        const lat = place.geometry.location.lat();
        const lng = place.geometry.location.lng();
        const locationName = place.formatted_address || place.name || '';
        
        let placeType = 'unknown';
        if (place.types && place.types.length > 0) {
          const specificTypes = place.types.filter(type => 
            !['political', 'geocode'].includes(type)
          );
          placeType = specificTypes[0] || place.types[0];
        }

        form.setValue('location', locationName);
        form.setValue('locationName', locationName);
        form.setValue('lat', lat);
        form.setValue('lng', lng);
        form.setValue('placeType', placeType);
      });

      autocompleteRef.current = autocomplete;

      // Cleanup function
      return () => {
        if (inputRef.current) {
          inputRef.current.removeEventListener('keydown', handleKeyDown);
        }
        if (autocomplete) {
          window.google?.maps?.event?.clearInstanceListeners(autocomplete);
        }
      };
    }
  }, [inputRef.current, isGoogleMapsLoaded, form, toast]);

  const handleSubmit = async (data: SearchFormValues) => {
    if (!data.lat || !data.lng) {
      toast({
        title: "Error",
        description: "Please select a location from the dropdown",
        variant: "destructive",
      });
      return;
    }

    // If dates are not provided, set default dates (tomorrow and day after)
    let checkIn = data.checkIn;
    let checkOut = data.checkOut;
    
    if (!checkIn || !checkOut) {
      const tomorrow = addDays(new Date(), 1);
      const dayAfter = addDays(new Date(), 2);
      checkIn = format(tomorrow, "yyyy-MM-dd");
      checkOut = format(dayAfter, "yyyy-MM-dd");
      
      // Optionally show a toast to inform the user about default dates
      if (!data.checkIn && !data.checkOut) {
        toast({
          title: "Default dates used",
          description: `Using ${format(tomorrow, "MMM d")} - ${format(dayAfter, "MMM d")}. You can change dates on the results page.`,
          variant: "default",
        });
      }
    }

    try {
      // Construct search URL with all required parameters
      const searchParams = new URLSearchParams({
        lat: data.lat.toString(),
        lng: data.lng.toString(),
        locationName: data.locationName || '',
        checkIn: checkIn,
        checkOut: checkOut,
        guests: data.guests.toString(),
        rooms: data.rooms.toString()
      });
      
      // Log the URL parameters for debugging
      console.log('Submitting search with params:', Object.fromEntries(searchParams.entries()));
      
      // Use window.location.href to ensure full URL path
      const searchUrl = `/results?${searchParams.toString()}`;
      console.log('Navigating to:', searchUrl);
      
      // Update URL and trigger navigation
      window.history.pushState({}, '', searchUrl);
      setLocation(searchUrl);
      
      // Call the provided onSubmit prop with the form data (including default dates)
      if (onSubmitProp) {
        onSubmitProp({
          ...data,
          checkIn,
          checkOut
        });
      }
    } catch (error) {
      console.error('Search error:', error);
      toast({
        title: "Error",
        description: "An error occurred while searching. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle input ref assignment
  const handleInputRef = useCallback((element: HTMLInputElement | null) => {
    inputRef.current = element;
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (autocompleteRef.current && window.google?.maps) {
        window.google.maps.event.clearInstanceListeners(autocompleteRef.current);
      }
    };
  }, []);

  // Create a submit function that can be called externally
  useEffect(() => {
    if (formRef) {
      formRef.current = {
        submit: () => form.handleSubmit(handleSubmit)()
      };
    }
  }, [form, handleSubmit, formRef]);

  // Watch form values and call onChange
  useEffect(() => {
    const subscription = form.watch((data) => {
      onChange?.(data as SearchFormValues);
    });
    return () => subscription.unsubscribe();
  }, [form, onChange]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
          <FormField
            control={form.control}
            name="location"
            render={({ field }: { field: ControllerRenderProps<SearchFormValues, 'location'> }) => {
              const { ref: _ref, ...fieldWithoutRef } = field;
              return (
                <FormItem className="md:col-span-5">
                  <FormLabel>Where</FormLabel>
                  <FormControl>
                    <Input
                      ref={handleInputRef}
                      placeholder="Enter destination"
                      {...fieldWithoutRef}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                        field.onChange(e);
                        // Clear location data when user starts typing
                        if (e.target.value === '') {
                          form.setValue('lat', null);
                          form.setValue('lng', null);
                          form.setValue('locationName', '');
                        }
                      }}
                      className="w-full"
                      autoComplete="off" // Prevent browser autocomplete from interfering
                    />
                  </FormControl>
                </FormItem>
              );
            }}
          />

          <div className="md:col-span-5">
            <FormField
              control={form.control}
              name="checkIn"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Dates (optional)</FormLabel>
                  <FormControl>
                    <DatePickerWithRange
                      date={dateRange}
                      onChange={(range: DateRange) => {
                        setDateRange(range);
                        if (range.from) {
                          form.setValue("checkIn", format(range.from, "yyyy-MM-dd"));
                        } else {
                          form.setValue("checkIn", "");
                        }
                        if (range.to) {
                          form.setValue("checkOut", format(range.to, "yyyy-MM-dd"));
                        } else {
                          form.setValue("checkOut", "");
                        }
                      }}
                      className="w-full"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="guests"
            render={({ field }: { field: ControllerRenderProps<SearchFormValues, 'guests'> }) => (
              <FormItem className="md:col-span-2">
                <FormLabel>Guests</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min={1}
                    max={10}
                    {...field}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => field.onChange(parseInt(e.target.value))}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </form>
    </Form>
  );
}