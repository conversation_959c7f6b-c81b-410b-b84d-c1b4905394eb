import { describe, it, expect } from '@jest/globals';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

describe('Critical Functionality Tests', () => {
  describe('File Structure Validation', () => {
    it('should have multi-bedroom service file', () => {
      const servicePath = path.join(__dirname, '../server/services/multiBedroomService.ts');
      expect(fs.existsSync(servicePath)).toBe(true);
    });

    it('should have unified AI chat component', () => {
      const componentPath = path.join(__dirname, '../client/src/components/UnifiedAIChat.tsx');
      expect(fs.existsSync(componentPath)).toBe(true);
    });

    it('should have removed old AI chat components', () => {
      const oldComponents = [
        '../client/src/components/AiChatEnhanced.tsx',
        '../client/src/components/EnhancedAIChat.tsx',
        '../client/src/components/AiChat.tsx.bak'
      ];

      oldComponents.forEach(componentPath => {
        const fullPath = path.join(__dirname, componentPath);
        expect(fs.existsSync(fullPath)).toBe(false);
      });
    });
  });

  describe('Property Caching Fix Validation', () => {
    it('should have fixed property caching implementation', () => {
      const travsrvPath = path.join(__dirname, '../server/services/travsrv.ts');
      const content = fs.readFileSync(travsrvPath, 'utf8');

      // Check that caching is re-enabled
      expect(content).toContain('await Promise.all(');
      expect(content).toContain('cacheProperty(property)');

      // Check that the fix for JSONB fields is present
      expect(content).toContain('amenities: Array.isArray(property.amenities) ? property.amenities : []');
      expect(content).toContain('images: Array.isArray(property.images) ? property.images : []');
    });
  });

  describe('Multi-Bedroom API Route', () => {
    it('should have multi-bedroom API route registered', () => {
      const routesPath = path.join(__dirname, '../server/routes.ts');
      const content = fs.readFileSync(routesPath, 'utf8');

      expect(content).toContain('/api/properties/multi-bedroom');
      expect(content).toContain('searchMultiBedroomAccommodations');
    });
  });

  describe('Component Integration', () => {
    it('should have updated Search page to use UnifiedAIChat', () => {
      const searchPath = path.join(__dirname, '../client/src/pages/Search.tsx');
      const content = fs.readFileSync(searchPath, 'utf8');

      expect(content).toContain('UnifiedAIChat');
      expect(content).not.toContain('AiTravelCompanion');
    });

    it('should have updated Results page to use UnifiedAIChat', () => {
      const resultsPath = path.join(__dirname, '../client/src/pages/Results.tsx');
      const content = fs.readFileSync(resultsPath, 'utf8');

      expect(content).toContain('UnifiedAIChat');
      expect(content).not.toContain('AiTravelCompanion');
    });
  });

  describe('Service Integration', () => {
    it('should have proper imports in multi-bedroom service', () => {
      const servicePath = path.join(__dirname, '../server/services/multiBedroomService.ts');
      const content = fs.readFileSync(servicePath, 'utf8');

      expect(content).toContain("import { searchTravSrvProperties, getPropertyAvailability } from './travsrv.js'");
      expect(content).toContain("import { geocodeLocation } from './geocodingService.js'");
    });
  });

  describe('TypeScript Fixes', () => {
    it('should have fixed critical TypeScript errors', () => {
      const paymentServicePath = path.join(__dirname, '../server/services/paymentService.ts');
      const content = fs.readFileSync(paymentServicePath, 'utf8');

      // Check Stripe API version fix
      expect(content).toContain("apiVersion: '2024-12-18.acacia'");
    });
  });

  describe('Multi-Bedroom Filter Integration', () => {
    it('should have bedroom filter in Results page', () => {
      const resultsPath = path.join(__dirname, '../client/src/pages/Results.tsx');
      const content = fs.readFileSync(resultsPath, 'utf8');

      // Check that bedroom filter state exists
      expect(content).toContain('selectedBedrooms');
      expect(content).toContain('setSelectedBedrooms');

      // Check that bedroom filtering logic exists
      expect(content).toContain('matchesBedrooms');

      // Check that multi-bedroom quick action exists
      expect(content).toContain('Multi-Bedroom');
      expect(content).toContain('3+ bedrooms for groups');
    });

    it('should have multi-bedroom API endpoint', () => {
      const routesPath = path.join(__dirname, '../server/routes.ts');
      const content = fs.readFileSync(routesPath, 'utf8');

      expect(content).toContain('/api/properties/multi-bedroom');
      expect(content).toContain('searchMultiBedroomAccommodations');
    });
  });

  describe('AI Chat Integration', () => {
    it('should have enhanced AI chat with multi-bedroom support', () => {
      const chatPath = path.join(__dirname, '../client/src/components/UnifiedAIChat.tsx');
      const content = fs.readFileSync(chatPath, 'utf8');

      // Check for multi-bedroom quick actions
      expect(content).toContain('Multi-Bedroom Stays');
      expect(content).toContain('bedrooms');

      // Check for enhanced mode
      expect(content).toContain('enhancedMode: true');
    });
  });
});


