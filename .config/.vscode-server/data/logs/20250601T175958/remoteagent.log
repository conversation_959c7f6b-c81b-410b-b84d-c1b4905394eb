2025-06-01 17:59:58.831 [info] 




2025-06-01 17:59:58.831 [info] Extension host agent started.
2025-06-01 17:59:58.837 [info] Started initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
2025-06-01 17:59:58.892 [info] Completed initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
2025-06-01 17:59:59.008 [info] [<unknown>][0362e62b][ManagementConnection] New connection established.
2025-06-01 17:59:59.055 [info] [<unknown>][6614c1c8][ExtensionHostConnection] New connection established.
2025-06-01 17:59:59.132 [info] [<unknown>][6614c1c8][ExtensionHostConnection] <1675> Launched Extension Host Process.
2025-06-01 17:59:59.249 [error] [network] #1: https://az764295.vo.msecnd.net/extensions/marketplace.json - error GET getaddrinfo ENOTFOUND az764295.vo.msecnd.net
2025-06-01 18:00:02.029 [error] CodeExpectedError: Could not find pty 4 on pty host
    at I.W (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:48:12233)
    at I.updateTitle (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:48:7722)
    at N.s.<computed> (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:48:2962)
    at Object.call (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:31:4203)
    at Jc.s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:78968)
    at Jc.q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:78491)
    at hs.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:77893)
    at C.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:746)
    at C.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:964)
    at process.w (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:24:29137)
    at process.emit (node:events:519:28)
    at emit (node:internal/child_process:951:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21)
2025-06-01 18:00:02.030 [error] CodeExpectedError: Could not find pty 4 on pty host
    at I.W (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:48:12233)
    at I.updateIcon (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:48:7772)
    at N.s.<computed> (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:48:2962)
    at Object.call (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:31:4203)
    at Jc.s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:78968)
    at Jc.q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:78491)
    at hs.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:77893)
    at C.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:746)
    at C.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:29:964)
    at process.w (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/vs/platform/terminal/node/ptyHostMain.js:24:29137)
    at process.emit (node:events:519:28)
    at emit (node:internal/child_process:951:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21)
2025-06-01 18:04:58.795 [info] New EH opened, aborting shutdown
2025-06-01 18:44:16.564 [info] [<unknown>][0362e62b][ManagementConnection] The client has reconnected.
2025-06-01 18:44:49.097 [info] [<unknown>][6614c1c8][ExtensionHostConnection] The client has reconnected.
2025-06-01 19:53:35.399 [info] [<unknown>][6614c1c8][ExtensionHostConnection] The client has reconnected.
2025-06-01 19:53:35.400 [info] [<unknown>][0362e62b][ManagementConnection] The client has reconnected.
2025-06-01 19:53:35.403 [error] Error: Unexpected SIGPIPE
    at process.<anonymous> (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-f1a4fb101478ce6ec82fe9627c43efbf9e98c813/server/out/server-main.js:183:1060)
    at process.emit (node:events:531:35)
