2025-06-01 18:00:00.440 [info] Extension host with pid 1675 started
2025-06-01 18:00:00.452 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/vscode.lock': Lock acquired.
2025-06-01 18:00:00.558 [info] Eager extensions activated
2025-06-01 18:02:02.834 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-01 18:02:02.835 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-01 18:02:02.836 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-01 18:02:03.025 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-01 18:02:03.025 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-01 18:02:03.174 [info] ExtensionService#_doActivateExtension vscode.npm, startup: false, activationEvent: 'workspaceContains:package.json'
2025-06-01 18:02:03.590 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-01 18:02:09.033 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-01 18:02:09.033 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-01 18:02:09.034 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:json'
2025-06-01 18:02:14.005 [info] ExtensionService#_doActivateExtension vscode.markdown-language-features, startup: false, activationEvent: 'onLanguage:markdown'
2025-06-01 18:02:14.741 [info] ExtensionService#_doActivateExtension vscode.markdown-math, startup: false, activationEvent: 'api', root cause: vscode.markdown-language-features
