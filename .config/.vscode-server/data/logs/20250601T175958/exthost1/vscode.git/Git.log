2025-06-01 18:02:03.485 [info] [main] Log level: Info
2025-06-01 18:02:03.485 [info] [main] Validating found git in: "git"
2025-06-01 18:02:03.485 [info] [main] Using git "2.47.2" from "git"
2025-06-01 18:02:03.486 [info] [Model][doInitialScan] Initial repository scan started
2025-06-01 18:02:03.486 [info] > git rev-parse --show-toplevel [3ms]
2025-06-01 18:02:03.486 [info] > git rev-parse --git-dir --git-common-dir [15ms]
2025-06-01 18:02:03.486 [info] [Model][openRepository] Opened repository: /home/<USER>/workspace
2025-06-01 18:02:03.486 [info] > git config --get commit.template [2ms]
2025-06-01 18:02:03.486 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-01 18:02:03.500 [info] > git rev-parse --show-toplevel [0ms]
2025-06-01 18:02:03.520 [info] > git status -z -uall [13ms]
2025-06-01 18:02:03.521 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [7ms]
2025-06-01 18:02:03.541 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-01 18:02:03.547 [info] > git rev-parse --show-toplevel [28ms]
2025-06-01 18:02:03.556 [info] > git config --local branch.main.vscode-merge-base [10ms]
2025-06-01 18:02:03.561 [info] > git config --get commit.template [21ms]
2025-06-01 18:02:03.563 [info] > git rev-parse --show-toplevel [10ms]
2025-06-01 18:02:03.573 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [12ms]
2025-06-01 18:02:03.616 [info] > git merge-base refs/heads/main refs/remotes/origin/main [38ms]
2025-06-01 18:02:03.621 [info] > git rev-parse --show-toplevel [48ms]
2025-06-01 18:02:03.621 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [53ms]
2025-06-01 18:02:03.630 [info] > git diff --name-status -z --diff-filter=ADMR 321834a4fd8b2568b5e53a37b787f1381eb5c6c9...refs/remotes/origin/main [9ms]
2025-06-01 18:02:03.631 [info] > git rev-parse --show-toplevel [1ms]
2025-06-01 18:02:03.663 [info] > git status -z -uall [11ms]
2025-06-01 18:02:03.663 [info] > git rev-parse --show-toplevel [19ms]
2025-06-01 18:02:03.674 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [11ms]
2025-06-01 18:02:03.680 [info] > git rev-parse --show-toplevel [7ms]
2025-06-01 18:02:03.689 [info] > git rev-parse --show-toplevel [1ms]
2025-06-01 18:02:03.702 [info] > git rev-parse --show-toplevel [6ms]
2025-06-01 18:02:03.727 [info] > git rev-parse --show-toplevel [21ms]
2025-06-01 18:02:03.734 [info] > git rev-parse --show-toplevel [2ms]
2025-06-01 18:02:03.742 [info] > git rev-parse --show-toplevel [1ms]
2025-06-01 18:02:03.750 [info] > git rev-parse --show-toplevel [1ms]
2025-06-01 18:02:03.756 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-01 18:02:03.998 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-01 18:02:06.638 [info] > git config --get commit.template [6ms]
2025-06-01 18:02:06.640 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-01 18:02:06.649 [info] > git status -z -uall [5ms]
2025-06-01 18:02:06.653 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [4ms]
2025-06-01 18:02:11.666 [info] > git config --get commit.template [5ms]
2025-06-01 18:02:11.666 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-01 18:02:11.675 [info] > git status -z -uall [4ms]
2025-06-01 18:02:11.676 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [2ms]
2025-06-01 18:02:11.914 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-01 18:02:14.321 [info] > git show --textconv :ENHANCED_AI_CHAT_IMPROVEMENTS.md [5ms]
2025-06-01 18:02:14.324 [info] > git ls-files --stage -- /home/<USER>/workspace/ENHANCED_AI_CHAT_IMPROVEMENTS.md [3ms]
2025-06-01 18:02:14.330 [info] > git cat-file -s 8cf6c7c038f4e1a4569a4a837fcc07aafdfd004b [2ms]
2025-06-01 18:02:16.687 [info] > git config --get commit.template [2ms]
2025-06-01 18:02:16.695 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-01 18:02:16.722 [info] > git status -z -uall [7ms]
2025-06-01 18:02:16.723 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [3ms]
2025-06-01 18:03:36.948 [info] > git config --get commit.template [3ms]
2025-06-01 18:03:36.948 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-01 18:03:36.957 [info] > git status -z -uall [5ms]
2025-06-01 18:03:36.958 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [1ms]
2025-06-01 18:03:41.971 [info] > git config --get commit.template [4ms]
2025-06-01 18:03:41.971 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-01 18:03:41.981 [info] > git status -z -uall [6ms]
2025-06-01 18:03:41.981 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [2ms]
2025-06-01 18:05:51.748 [info] > git config --get commit.template [29ms]
2025-06-01 18:05:51.754 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-01 18:05:51.779 [info] > git status -z -uall [18ms]
2025-06-01 18:05:51.782 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [3ms]
2025-06-01 18:05:56.794 [info] > git config --get commit.template [5ms]
2025-06-01 18:05:56.794 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-01 18:05:56.803 [info] > git status -z -uall [4ms]
2025-06-01 18:05:56.804 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [1ms]
2025-06-01 18:06:01.816 [info] > git config --get commit.template [1ms]
2025-06-01 18:06:01.822 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-01 18:06:01.846 [info] > git status -z -uall [12ms]
2025-06-01 18:06:01.846 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [3ms]
2025-06-01 18:06:06.861 [info] > git config --get commit.template [5ms]
2025-06-01 18:06:06.862 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-01 18:06:06.871 [info] > git status -z -uall [5ms]
2025-06-01 18:06:06.872 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [2ms]
2025-06-01 18:10:44.783 [info] > git config --get commit.template [8ms]
2025-06-01 18:10:44.784 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-01 18:10:44.793 [info] > git status -z -uall [5ms]
2025-06-01 18:10:44.794 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [2ms]
2025-06-01 18:42:41.626 [info] > git config --get commit.template [1ms]
2025-06-01 18:42:41.631 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-01 18:42:41.639 [info] > git status -z -uall [4ms]
2025-06-01 18:42:41.640 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [1ms]
2025-06-01 18:43:22.654 [info] > git config --get commit.template [5ms]
2025-06-01 18:43:22.655 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-01 18:43:22.664 [info] > git status -z -uall [5ms]
2025-06-01 18:43:22.665 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [2ms]
2025-06-01 19:30:27.789 [info] > git config --get commit.template [3ms]
2025-06-01 19:30:27.789 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-01 19:30:27.797 [info] > git status -z -uall [4ms]
2025-06-01 19:30:27.798 [info] > git for-each-ref --sort -committerdate --format %(refname) %(objectname) %(*objectname) [1ms]
2025-06-01 19:54:04.661 [info] > git check-ignore -v -z --stdin [1ms]
