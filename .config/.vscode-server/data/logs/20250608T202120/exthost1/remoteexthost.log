2025-06-08 20:21:25.863 [info] Extension host with pid 276 started
2025-06-08 20:21:25.863 [error] Error: EEXIST: file already exists, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/vscode.lock'
2025-06-08 20:21:25.863 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/vscode.lock': Could not acquire lock, checking if the file is stale.
2025-06-08 20:21:25.868 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/vscode.lock': The pid 1167 appears to be gone.
2025-06-08 20:21:25.868 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/vscode.lock': Deleting a stale lock.
2025-06-08 20:21:25.966 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/vscode.lock': Lock acquired.
2025-06-08 20:21:26.542 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-08 20:21:26.543 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-08 20:21:26.694 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-08 20:21:26.694 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-08 20:21:27.346 [info] ExtensionService#_doActivateExtension vscode.npm, startup: true, activationEvent: 'workspaceContains:package.json'
2025-06-08 20:21:27.578 [info] Eager extensions activated
2025-06-08 20:21:27.578 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-08 20:21:27.579 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-08 20:21:27.580 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-06-08 20:21:32.461 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:typescript'
2025-06-08 20:21:32.547 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:markdown'
2025-06-08 20:21:32.547 [info] ExtensionService#_doActivateExtension vscode.markdown-language-features, startup: false, activationEvent: 'onLanguage:markdown'
2025-06-08 20:21:35.056 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-08 20:21:35.056 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:json'
2025-06-08 20:21:35.871 [error] CodeExpectedError: cannot open vscode-userdata:/Users/<USER>/Library/Application%20Support/Code/User/keybindings.json. Detail: Unable to read file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json' (Error: Unable to resolve nonexistent file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json')
    at n_e.$tryOpenDocument (vscode-file://vscode-app/Applications/Visual%20Studio%20Code.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1276:8652)
2025-06-08 20:37:44.861 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:37:44.871 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:37:45.493 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:37:45.499 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:37:56.535 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:37:56.538 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:37:57.210 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:37:57.211 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:38:24.751 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:38:24.763 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:38:25.427 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:38:25.429 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:38:43.604 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:38:43.608 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:38:44.320 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:38:44.321 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:39:26.218 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:39:26.222 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:39:26.818 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:39:26.821 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:39:44.834 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:39:44.836 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:39:45.450 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:39:45.451 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:39:56.078 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:39:56.083 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:39:56.835 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:39:56.837 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:40:14.669 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:40:14.671 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:40:15.305 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:40:15.307 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:41:17.384 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:41:17.387 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:41:18.005 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:41:18.007 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:41:33.905 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:41:33.907 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:41:34.658 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:41:34.659 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:41:46.120 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:41:46.122 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:41:46.788 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:41:46.789 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:42:04.708 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:42:04.710 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:42:05.570 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:42:05.572 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:43:00.733 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:43:00.735 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:43:01.430 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:43:01.431 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:44:22.378 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:44:22.382 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:44:23.151 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:44:23.154 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:44:40.797 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:44:40.800 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:44:41.579 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:44:41.580 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:44:53.175 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:44:53.179 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:44:53.900 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:44:53.906 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:45:06.118 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:45:06.121 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:45:06.854 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:45:06.855 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:45:18.082 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-06-08 20:46:23.903 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:46:23.906 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:46:24.700 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:46:24.701 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:46:39.273 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:46:39.286 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:46:40.051 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:46:40.053 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:47:01.727 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:47:01.728 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:47:02.334 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:47:02.335 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:51:56.744 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:51:56.771 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:51:57.607 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-08 20:51:57.609 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
