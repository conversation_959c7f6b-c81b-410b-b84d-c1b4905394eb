2025-06-08 20:21:28.843 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-08 20:21:28.843 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-08 20:21:28.843 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5}
2025-06-08 20:21:28.879 [info] 'AugmentExtension' Retrieving model config
2025-06-08 20:21:29.286 [info] 'AugmentExtension' Retrieved model config
2025-06-08 20:21:29.286 [info] 'AugmentExtension' Returning model config
2025-06-08 20:21:29.358 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
2025-06-08 20:21:29.358 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/7/2025, 9:50:32 PM
2025-06-08 20:21:29.358 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-08 20:21:29.358 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-08 20:21:29.358 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 6/7/2025, 9:50:32 PM; type = explicit
2025-06-08 20:21:29.358 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-06-08 20:21:29.358 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/7/2025, 9:50:32 PM
2025-06-08 20:21:29.435 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-08 20:21:29.435 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-08 20:21:29.436 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-08 20:21:29.436 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-08 20:21:29.450 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-08 20:21:29.451 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-08 20:21:29.880 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-08 20:21:29.971 [info] 'WorkspaceManager[workspace]' Start tracking
2025-06-08 20:21:29.974 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-06-08 20:21:29.974 [info] 'OpenFileManager' Opened source folder 100
2025-06-08 20:21:29.975 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-08 20:21:29.981 [info] 'MtimeCache[workspace]' read 1379 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-08 20:21:30.153 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-08 20:21:30.153 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-08 20:21:30.280 [info] 'TaskManager' Setting current root task UUID to fdae86cd-91fa-4af5-9a62-343ffb7fad2e
2025-06-08 20:21:30.454 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-08 20:21:30.454 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-08 20:21:30.770 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-08 20:21:30.773 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-08 20:21:30.773 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-08 20:21:30.773 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-08 20:21:31.647 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1368.359599,"timestamp":"2025-06-08T20:21:31.647Z"}]
2025-06-08 20:21:45.754 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-06-08 20:21:45.754 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 2459
  - files emitted: 1833
  - other paths emitted: 4
  - total paths emitted: 4296
  - timing stats:
    - readDir: 26 ms
    - filter: 266 ms
    - yield: 91 ms
    - total: 480 ms
2025-06-08 20:21:45.754 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 1407
  - paths not accessible: 0
  - not plain files: 0
  - large files: 25
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 1368
  - mtime cache misses: 39
  - probe batches: 5
  - blob names probed: 1445
  - files read: 474
  - blobs uploaded: 37
  - timing stats:
    - ingestPath: 5 ms
    - probe: 4428 ms
    - stat: 271 ms
    - read: 7790 ms
    - upload: 1306 ms
2025-06-08 20:21:45.754 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 3 ms
  - read MtimeCache: 7 ms
  - pre-populate PathMap: 159 ms
  - create PathFilter: 1031 ms
  - create PathNotifier: 1 ms
  - enumerate paths: 483 ms
  - purge stale PathMap entries: 1 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 14093 ms
  - enable persist: 4 ms
  - total: 15782 ms
2025-06-08 20:21:45.754 [info] 'WorkspaceManager' Workspace startup complete in 16410 ms
2025-06-08 20:26:36.082 [warning] 'DiffViewSessionReporter' No request id found for diff panel resolution
2025-06-08 20:29:57.171 [info] 'ToolFileUtils' Reading file: tests/multi-bedroom-service.test.ts
2025-06-08 20:29:58.014 [info] 'ToolFileUtils' File not found: tests/multi-bedroom-service.test.ts. No similar files found
2025-06-08 20:29:58.015 [error] 'StrReplaceEditorTool' Error in tool call: File not found: tests/multi-bedroom-service.test.ts
2025-06-08 20:31:42.631 [info] 'WorkspaceManager[workspace]' Directory created: scripts
2025-06-08 20:37:35.511 [warning] 'DiffViewSessionReporter' No request id found for diff panel resolution
2025-06-08 20:37:44.239 [info] 'ToolFileUtils' Reading file: server/services/travsrv.ts
2025-06-08 20:37:44.505 [info] 'ToolFileUtils' Successfully read file: server/services/travsrv.ts (18682 bytes)
2025-06-08 20:37:45.660 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/5488a806
2025-06-08 20:37:46.502 [info] 'ToolFileUtils' Reading file: server/services/travsrv.ts
2025-06-08 20:37:46.502 [info] 'ToolFileUtils' Successfully read file: server/services/travsrv.ts (19869 bytes)
2025-06-08 20:37:56.442 [info] 'ToolFileUtils' Reading file: server/services/travsrv.ts
2025-06-08 20:37:56.442 [info] 'ToolFileUtils' Successfully read file: server/services/travsrv.ts (19869 bytes)
2025-06-08 20:37:56.540 [error] 'FuzzySymbolSearcher' Failed to read file tokens for fa37106aa89399025203a0fa35bae39de53911453fa30f552e13d48de57eedb3: deleted
2025-06-08 20:37:58.212 [info] 'ToolFileUtils' Reading file: server/services/travsrv.ts
2025-06-08 20:37:58.212 [info] 'ToolFileUtils' Successfully read file: server/services/travsrv.ts (19819 bytes)
2025-06-08 20:38:09.258 [warning] 'DiffViewSessionReporter' No request id found for diff panel resolution
2025-06-08 20:38:09.554 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-08 20:38:10.030 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (109332 bytes)
2025-06-08 20:38:15.430 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: [1,30]
2025-06-08 20:38:24.255 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-08 20:38:24.255 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (109332 bytes)
2025-06-08 20:38:25.650 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/5e12e58f
2025-06-08 20:38:26.436 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-08 20:38:26.436 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (109418 bytes)
2025-06-08 20:38:43.413 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-08 20:38:43.414 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (109418 bytes)
2025-06-08 20:38:43.611 [error] 'FuzzySymbolSearcher' Failed to read file tokens for aa2af415836928dd297ede319f0c7f54759fa6daa44f00e7f18d817b920ee0a4: deleted
2025-06-08 20:38:45.323 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-08 20:38:45.323 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (112011 bytes)
2025-06-08 20:38:53.841 [info] 'ViewTool' Tool called with path: client/src/components and view_range: undefined
2025-06-08 20:38:53.921 [info] 'ViewTool' Listing directory: client/src/components (depth: 2, showHidden: false)
2025-06-08 20:39:11.682 [info] 'ViewTool' Tool called with path: client/src/pages/Search.tsx and view_range: undefined
2025-06-08 20:39:18.508 [info] 'ViewTool' Tool called with path: client/src/pages/Results.tsx and view_range: undefined
2025-06-08 20:39:25.799 [info] 'ToolFileUtils' Reading file: client/src/pages/Search.tsx
2025-06-08 20:39:25.800 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Search.tsx (15390 bytes)
2025-06-08 20:39:27.061 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-75c5484b
2025-06-08 20:39:27.824 [info] 'ToolFileUtils' Reading file: client/src/pages/Search.tsx
2025-06-08 20:39:27.824 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Search.tsx (15382 bytes)
2025-06-08 20:39:36.999 [info] 'ViewTool' Tool called with path: client/src/pages/Search.tsx and view_range: undefined
2025-06-08 20:39:44.744 [info] 'ToolFileUtils' Reading file: client/src/pages/Search.tsx
2025-06-08 20:39:44.745 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Search.tsx (15382 bytes)
2025-06-08 20:39:46.452 [info] 'ToolFileUtils' Reading file: client/src/pages/Search.tsx
2025-06-08 20:39:46.453 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Search.tsx (15592 bytes)
2025-06-08 20:39:55.621 [info] 'ToolFileUtils' Reading file: client/src/pages/Results.tsx
2025-06-08 20:39:55.621 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Results.tsx (46865 bytes)
2025-06-08 20:39:57.054 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-3f165b01
2025-06-08 20:39:57.839 [info] 'ToolFileUtils' Reading file: client/src/pages/Results.tsx
2025-06-08 20:39:57.839 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Results.tsx (46857 bytes)
2025-06-08 20:40:06.496 [info] 'ViewTool' Tool called with path: client/src/pages/Results.tsx and view_range: undefined
2025-06-08 20:40:14.523 [info] 'ToolFileUtils' Reading file: client/src/pages/Results.tsx
2025-06-08 20:40:14.523 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Results.tsx (46857 bytes)
2025-06-08 20:40:16.309 [info] 'ToolFileUtils' Reading file: client/src/pages/Results.tsx
2025-06-08 20:40:16.309 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Results.tsx (47511 bytes)
2025-06-08 20:40:26.155 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: undefined
2025-06-08 20:40:31.913 [info] 'ViewTool' Tool called with path: server/routes/enhancedChatRoutes.js and view_range: [1,50]
2025-06-08 20:40:31.995 [info] 'ViewTool' Path does not exist: server/routes/enhancedChatRoutes.js
2025-06-08 20:40:39.989 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-08 20:40:39.990 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (112011 bytes)
2025-06-08 20:40:41.926 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-08 20:40:41.926 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (112067 bytes)
2025-06-08 20:40:53.523 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-08 20:40:53.523 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (112067 bytes)
2025-06-08 20:40:53.718 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 2252472313c7ec3fdeac7c425f80d06c16da9b169edee6930c7d0291141e5d86: deleted
2025-06-08 20:40:55.305 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-08 20:40:55.305 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (112158 bytes)
2025-06-08 20:41:16.701 [info] 'ToolFileUtils' Reading file: client/src/pages/EnhancedAIChatDemo.tsx
2025-06-08 20:41:16.951 [info] 'ToolFileUtils' Successfully read file: client/src/pages/EnhancedAIChatDemo.tsx (8593 bytes)
2025-06-08 20:41:18.239 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/58f9e426
2025-06-08 20:41:19.009 [info] 'ToolFileUtils' Reading file: client/src/pages/EnhancedAIChatDemo.tsx
2025-06-08 20:41:19.010 [info] 'ToolFileUtils' Successfully read file: client/src/pages/EnhancedAIChatDemo.tsx (8591 bytes)
2025-06-08 20:41:27.483 [info] 'ViewTool' Tool called with path: client/src/pages/EnhancedAIChatDemo.tsx and view_range: undefined
2025-06-08 20:41:33.808 [info] 'ToolFileUtils' Reading file: client/src/pages/EnhancedAIChatDemo.tsx
2025-06-08 20:41:33.808 [info] 'ToolFileUtils' Successfully read file: client/src/pages/EnhancedAIChatDemo.tsx (8591 bytes)
2025-06-08 20:41:35.661 [info] 'ToolFileUtils' Reading file: client/src/pages/EnhancedAIChatDemo.tsx
2025-06-08 20:41:35.661 [info] 'ToolFileUtils' Successfully read file: client/src/pages/EnhancedAIChatDemo.tsx (8623 bytes)
2025-06-08 20:41:45.384 [info] 'ToolFileUtils' Reading file: client/src/pages/AIDemo.tsx
2025-06-08 20:41:45.638 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AIDemo.tsx (8015 bytes)
2025-06-08 20:41:47.001 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-3d2b5d68
2025-06-08 20:41:47.791 [info] 'ToolFileUtils' Reading file: client/src/pages/AIDemo.tsx
2025-06-08 20:41:47.791 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AIDemo.tsx (8041 bytes)
2025-06-08 20:41:57.050 [info] 'ViewTool' Tool called with path: client/src/pages/AIDemo.tsx and view_range: [1,20]
2025-06-08 20:42:04.619 [info] 'ToolFileUtils' Reading file: client/src/pages/AIDemo.tsx
2025-06-08 20:42:04.620 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AIDemo.tsx (8041 bytes)
2025-06-08 20:42:06.573 [info] 'ToolFileUtils' Reading file: client/src/pages/AIDemo.tsx
2025-06-08 20:42:06.574 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AIDemo.tsx (8033 bytes)
2025-06-08 20:42:16.766 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-06-08 20:42:17.012 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3749 bytes)
2025-06-08 20:42:46.358 [info] 'ViewTool' Tool called with path: package.json and view_range: undefined
2025-06-08 20:42:52.362 [info] 'ViewTool' Tool called with path: package.json and view_range: [6,20]
2025-06-08 20:43:00.279 [info] 'ToolFileUtils' Reading file: package.json
2025-06-08 20:43:00.280 [info] 'ToolFileUtils' Successfully read file: package.json (4909 bytes)
2025-06-08 20:43:01.666 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-7d8434b8
2025-06-08 20:43:02.439 [info] 'ToolFileUtils' Reading file: package.json
2025-06-08 20:43:02.439 [info] 'ToolFileUtils' Successfully read file: package.json (5126 bytes)
2025-06-08 20:44:21.833 [info] 'ToolFileUtils' Reading file: server/services/paymentService.ts
2025-06-08 20:44:22.121 [info] 'ToolFileUtils' Successfully read file: server/services/paymentService.ts (6278 bytes)
2025-06-08 20:44:23.377 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/738d4b21
2025-06-08 20:44:24.155 [info] 'ToolFileUtils' Reading file: server/services/paymentService.ts
2025-06-08 20:44:24.156 [info] 'ToolFileUtils' Successfully read file: server/services/paymentService.ts (6285 bytes)
2025-06-08 20:44:40.286 [info] 'ToolFileUtils' Reading file: server/services/multiBedroomService.ts
2025-06-08 20:44:40.540 [info] 'ToolFileUtils' Successfully read file: server/services/multiBedroomService.ts (17619 bytes)
2025-06-08 20:44:41.791 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-59b04428
2025-06-08 20:44:42.582 [info] 'ToolFileUtils' Reading file: server/services/multiBedroomService.ts
2025-06-08 20:44:42.582 [info] 'ToolFileUtils' Successfully read file: server/services/multiBedroomService.ts (17621 bytes)
2025-06-08 20:44:52.649 [info] 'ToolFileUtils' Reading file: client/src/components/UnifiedAIChat.tsx
2025-06-08 20:44:52.905 [info] 'ToolFileUtils' Successfully read file: client/src/components/UnifiedAIChat.tsx (33283 bytes)
2025-06-08 20:44:54.917 [info] 'ToolFileUtils' Reading file: client/src/components/UnifiedAIChat.tsx
2025-06-08 20:44:54.918 [info] 'ToolFileUtils' Successfully read file: client/src/components/UnifiedAIChat.tsx (33331 bytes)
2025-06-08 20:45:05.521 [info] 'ToolFileUtils' Reading file: client/src/pages/Results.tsx
2025-06-08 20:45:05.846 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Results.tsx (47511 bytes)
2025-06-08 20:45:07.858 [info] 'ToolFileUtils' Reading file: client/src/pages/Results.tsx
2025-06-08 20:45:07.858 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Results.tsx (47666 bytes)
2025-06-08 20:45:33.194 [info] 'ViewTool' Tool called with path: package.json and view_range: undefined
2025-06-08 20:46:23.259 [info] 'ToolFileUtils' Reading file: server/services/travsrv.ts
2025-06-08 20:46:23.545 [info] 'ToolFileUtils' Successfully read file: server/services/travsrv.ts (19819 bytes)
2025-06-08 20:46:25.703 [info] 'ToolFileUtils' Reading file: server/services/travsrv.ts
2025-06-08 20:46:25.703 [info] 'ToolFileUtils' Successfully read file: server/services/travsrv.ts (19825 bytes)
2025-06-08 20:46:38.770 [info] 'ToolFileUtils' Reading file: tests/critical-functionality.test.ts
2025-06-08 20:46:39.020 [info] 'ToolFileUtils' Successfully read file: tests/critical-functionality.test.ts (4887 bytes)
2025-06-08 20:46:40.263 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-3bc52e10
2025-06-08 20:46:41.055 [info] 'ToolFileUtils' Reading file: tests/critical-functionality.test.ts
2025-06-08 20:46:41.055 [info] 'ToolFileUtils' Successfully read file: tests/critical-functionality.test.ts (5370 bytes)
2025-06-08 20:47:01.639 [info] 'ToolFileUtils' Reading file: tests/critical-functionality.test.ts
2025-06-08 20:47:01.639 [info] 'ToolFileUtils' Successfully read file: tests/critical-functionality.test.ts (5370 bytes)
2025-06-08 20:47:03.337 [info] 'ToolFileUtils' Reading file: tests/critical-functionality.test.ts
2025-06-08 20:47:03.337 [info] 'ToolFileUtils' Successfully read file: tests/critical-functionality.test.ts (630 bytes)
