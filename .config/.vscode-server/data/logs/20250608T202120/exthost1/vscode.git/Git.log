2025-06-08 20:21:27.552 [info] [main] Log level: Info
2025-06-08 20:21:27.552 [info] [main] Validating found git in: "git"
2025-06-08 20:21:27.552 [info] [main] Using git "2.47.2" from "git"
2025-06-08 20:21:27.552 [info] [Model][doInitialScan] Initial repository scan started
2025-06-08 20:21:27.552 [info] > git rev-parse --show-toplevel [96ms]
2025-06-08 20:21:27.552 [info] > git rev-parse --git-dir --git-common-dir [0ms]
2025-06-08 20:21:27.552 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-08 20:21:27.552 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-08 20:21:27.552 [info] > git config --get commit.template [6ms]
2025-06-08 20:21:27.552 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [67ms]
2025-06-08 20:21:27.552 [info] > git rev-parse --show-toplevel [15ms]
2025-06-08 20:21:28.366 [info] > git status -z -uall [801ms]
2025-06-08 20:21:28.366 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [793ms]
2025-06-08 20:21:28.367 [info] > git rev-parse --show-toplevel [810ms]
2025-06-08 20:21:28.440 [info] > git check-ignore -v -z --stdin [68ms]
2025-06-08 20:21:28.441 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [29ms]
2025-06-08 20:21:28.449 [info] > git rev-parse --show-toplevel [72ms]
2025-06-08 20:21:28.453 [info] > git config --get commit.template [14ms]
2025-06-08 20:21:28.454 [info] > git config --get --local branch.main.vscode-merge-base [5ms]
2025-06-08 20:21:28.468 [info] > git rev-parse --show-toplevel [15ms]
2025-06-08 20:21:28.534 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [72ms]
2025-06-08 20:21:28.534 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [76ms]
2025-06-08 20:21:28.644 [info] > git merge-base refs/heads/main refs/remotes/origin/main [100ms]
2025-06-08 20:21:28.649 [info] > git rev-parse --show-toplevel [177ms]
2025-06-08 20:21:28.655 [info] > git diff --name-status -z --diff-filter=ADMR 321834a4fd8b2568b5e53a37b787f1381eb5c6c9...refs/remotes/origin/main [6ms]
2025-06-08 20:21:28.754 [info] > git rev-parse --show-toplevel [100ms]
2025-06-08 20:21:28.763 [info] > git status -z -uall [94ms]
2025-06-08 20:21:28.763 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [15ms]
2025-06-08 20:21:28.770 [info] > git rev-parse --show-toplevel [8ms]
2025-06-08 20:21:28.879 [info] > git rev-parse --show-toplevel [36ms]
2025-06-08 20:21:28.891 [info] > git rev-parse --show-toplevel [1ms]
2025-06-08 20:21:28.899 [info] > git rev-parse --show-toplevel [0ms]
2025-06-08 20:21:28.910 [info] > git rev-parse --show-toplevel [4ms]
2025-06-08 20:21:28.938 [info] > git rev-parse --show-toplevel [18ms]
2025-06-08 20:21:28.944 [info] > git rev-parse --show-toplevel [1ms]
2025-06-08 20:21:28.955 [info] > git rev-parse --show-toplevel [2ms]
2025-06-08 20:21:28.968 [info] > git rev-parse --show-toplevel [2ms]
2025-06-08 20:21:28.971 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-08 20:22:00.284 [info] > git config --get commit.template [18ms]
2025-06-08 20:22:00.299 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-08 20:22:00.315 [info] > git status -z -uall [9ms]
2025-06-08 20:22:00.315 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:22:05.336 [info] > git config --get commit.template [9ms]
2025-06-08 20:22:05.337 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-08 20:22:05.353 [info] > git status -z -uall [9ms]
2025-06-08 20:22:05.356 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-08 20:26:34.327 [info] > git config --get commit.template [10ms]
2025-06-08 20:26:34.329 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:26:34.347 [info] > git status -z -uall [9ms]
2025-06-08 20:26:34.348 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-08 20:26:39.366 [info] > git config --get commit.template [6ms]
2025-06-08 20:26:39.369 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-08 20:26:39.381 [info] > git status -z -uall [5ms]
2025-06-08 20:26:39.382 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:29:37.972 [info] > git config --get commit.template [7ms]
2025-06-08 20:29:37.973 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:29:37.987 [info] > git status -z -uall [6ms]
2025-06-08 20:29:37.989 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:29:43.014 [info] > git config --get commit.template [12ms]
2025-06-08 20:29:43.016 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:29:43.034 [info] > git status -z -uall [10ms]
2025-06-08 20:29:43.035 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:29:48.050 [info] > git config --get commit.template [2ms]
2025-06-08 20:29:48.058 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:29:48.070 [info] > git status -z -uall [6ms]
2025-06-08 20:29:48.070 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:29:53.088 [info] > git config --get commit.template [8ms]
2025-06-08 20:29:53.089 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:29:53.103 [info] > git status -z -uall [6ms]
2025-06-08 20:29:53.105 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:29:58.123 [info] > git config --get commit.template [2ms]
2025-06-08 20:29:58.130 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:29:58.143 [info] > git status -z -uall [8ms]
2025-06-08 20:29:58.145 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:30:03.157 [info] > git config --get commit.template [2ms]
2025-06-08 20:30:03.165 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:30:03.176 [info] > git status -z -uall [5ms]
2025-06-08 20:30:03.176 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:30:08.188 [info] > git config --get commit.template [2ms]
2025-06-08 20:30:08.197 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:30:08.209 [info] > git status -z -uall [6ms]
2025-06-08 20:30:08.211 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:30:13.226 [info] > git config --get commit.template [6ms]
2025-06-08 20:30:13.227 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:30:13.246 [info] > git status -z -uall [10ms]
2025-06-08 20:30:13.246 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-08 20:30:18.262 [info] > git config --get commit.template [7ms]
2025-06-08 20:30:18.263 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:30:18.278 [info] > git status -z -uall [8ms]
2025-06-08 20:30:18.279 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:30:23.295 [info] > git config --get commit.template [7ms]
2025-06-08 20:30:23.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:30:23.310 [info] > git status -z -uall [6ms]
2025-06-08 20:30:23.311 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:30:28.336 [info] > git config --get commit.template [14ms]
2025-06-08 20:30:28.340 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-08 20:30:28.400 [info] > git status -z -uall [42ms]
2025-06-08 20:30:28.402 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-08 20:30:33.416 [info] > git config --get commit.template [1ms]
2025-06-08 20:30:33.425 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:30:33.443 [info] > git status -z -uall [8ms]
2025-06-08 20:30:33.445 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:30:38.468 [info] > git config --get commit.template [8ms]
2025-06-08 20:30:38.470 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:30:38.490 [info] > git status -z -uall [12ms]
2025-06-08 20:30:38.491 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:30:43.504 [info] > git config --get commit.template [0ms]
2025-06-08 20:30:43.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:30:43.527 [info] > git status -z -uall [8ms]
2025-06-08 20:30:43.528 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:30:48.589 [info] > git config --get commit.template [50ms]
2025-06-08 20:30:48.599 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:30:48.624 [info] > git status -z -uall [9ms]
2025-06-08 20:30:48.626 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:30:53.645 [info] > git config --get commit.template [7ms]
2025-06-08 20:30:53.646 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:30:53.660 [info] > git status -z -uall [6ms]
2025-06-08 20:30:53.662 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:30:58.681 [info] > git config --get commit.template [8ms]
2025-06-08 20:30:58.683 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:30:58.700 [info] > git status -z -uall [10ms]
2025-06-08 20:30:58.702 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-08 20:31:03.718 [info] > git config --get commit.template [5ms]
2025-06-08 20:31:03.718 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:31:03.734 [info] > git status -z -uall [8ms]
2025-06-08 20:31:03.735 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:31:08.753 [info] > git config --get commit.template [9ms]
2025-06-08 20:31:08.754 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:31:08.768 [info] > git status -z -uall [6ms]
2025-06-08 20:31:08.770 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:31:13.786 [info] > git config --get commit.template [6ms]
2025-06-08 20:31:13.788 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:31:13.800 [info] > git status -z -uall [7ms]
2025-06-08 20:31:13.801 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:31:18.815 [info] > git config --get commit.template [3ms]
2025-06-08 20:31:18.833 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-08 20:31:18.867 [info] > git status -z -uall [19ms]
2025-06-08 20:31:18.870 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-08 20:31:23.888 [info] > git config --get commit.template [8ms]
2025-06-08 20:31:23.889 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:31:23.903 [info] > git status -z -uall [7ms]
2025-06-08 20:31:23.905 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:31:28.924 [info] > git config --get commit.template [8ms]
2025-06-08 20:31:28.925 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:31:28.941 [info] > git status -z -uall [9ms]
2025-06-08 20:31:28.943 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-08 20:31:33.958 [info] > git config --get commit.template [5ms]
2025-06-08 20:31:33.960 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:31:33.970 [info] > git status -z -uall [5ms]
2025-06-08 20:31:33.971 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:31:38.992 [info] > git config --get commit.template [9ms]
2025-06-08 20:31:38.993 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:31:39.023 [info] > git status -z -uall [9ms]
2025-06-08 20:31:39.024 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:31:44.039 [info] > git config --get commit.template [6ms]
2025-06-08 20:31:44.040 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:31:44.051 [info] > git status -z -uall [6ms]
2025-06-08 20:31:44.052 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:31:49.068 [info] > git config --get commit.template [7ms]
2025-06-08 20:31:49.069 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:31:49.085 [info] > git status -z -uall [8ms]
2025-06-08 20:31:49.086 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:31:54.102 [info] > git config --get commit.template [7ms]
2025-06-08 20:31:54.104 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:31:54.115 [info] > git status -z -uall [6ms]
2025-06-08 20:31:54.115 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:31:59.132 [info] > git config --get commit.template [2ms]
2025-06-08 20:31:59.142 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:31:59.159 [info] > git status -z -uall [7ms]
2025-06-08 20:31:59.162 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:32:04.180 [info] > git config --get commit.template [7ms]
2025-06-08 20:32:04.181 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:32:04.196 [info] > git status -z -uall [7ms]
2025-06-08 20:32:04.198 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:32:09.220 [info] > git config --get commit.template [7ms]
2025-06-08 20:32:09.222 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:32:09.237 [info] > git status -z -uall [7ms]
2025-06-08 20:32:09.238 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:32:14.264 [info] > git config --get commit.template [10ms]
2025-06-08 20:32:14.266 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:32:14.285 [info] > git status -z -uall [11ms]
2025-06-08 20:32:14.286 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:32:19.303 [info] > git config --get commit.template [1ms]
2025-06-08 20:32:19.399 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [86ms]
2025-06-08 20:32:19.427 [info] > git status -z -uall [11ms]
2025-06-08 20:32:19.429 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-08 20:32:26.484 [info] > git config --get commit.template [9ms]
2025-06-08 20:32:26.486 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:32:26.501 [info] > git status -z -uall [8ms]
2025-06-08 20:32:26.502 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:32:31.526 [info] > git config --get commit.template [13ms]
2025-06-08 20:32:31.526 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:32:31.560 [info] > git status -z -uall [16ms]
2025-06-08 20:32:31.561 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-08 20:32:36.616 [info] > git config --get commit.template [7ms]
2025-06-08 20:32:36.618 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:32:36.642 [info] > git status -z -uall [15ms]
2025-06-08 20:32:36.642 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-08 20:32:41.665 [info] > git config --get commit.template [11ms]
2025-06-08 20:32:41.666 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:32:41.683 [info] > git status -z -uall [9ms]
2025-06-08 20:32:41.684 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:32:49.569 [info] > git config --get commit.template [80ms]
2025-06-08 20:32:49.584 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:32:49.605 [info] > git status -z -uall [10ms]
2025-06-08 20:32:49.607 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-08 20:32:54.655 [info] > git config --get commit.template [2ms]
2025-06-08 20:32:54.669 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-08 20:32:54.691 [info] > git status -z -uall [9ms]
2025-06-08 20:32:54.692 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:32:59.713 [info] > git config --get commit.template [8ms]
2025-06-08 20:32:59.715 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:32:59.730 [info] > git status -z -uall [8ms]
2025-06-08 20:32:59.731 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:33:04.748 [info] > git config --get commit.template [8ms]
2025-06-08 20:33:04.749 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:33:04.762 [info] > git status -z -uall [6ms]
2025-06-08 20:33:04.763 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:33:09.779 [info] > git config --get commit.template [6ms]
2025-06-08 20:33:09.780 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:33:09.790 [info] > git status -z -uall [5ms]
2025-06-08 20:33:09.791 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:33:14.806 [info] > git config --get commit.template [6ms]
2025-06-08 20:33:14.807 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:33:14.819 [info] > git status -z -uall [6ms]
2025-06-08 20:33:14.820 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:33:19.835 [info] > git config --get commit.template [5ms]
2025-06-08 20:33:19.836 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:33:19.848 [info] > git status -z -uall [7ms]
2025-06-08 20:33:19.850 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:33:24.951 [info] > git config --get commit.template [87ms]
2025-06-08 20:33:24.953 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:33:24.979 [info] > git status -z -uall [16ms]
2025-06-08 20:33:24.980 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-08 20:33:30.003 [info] > git config --get commit.template [9ms]
2025-06-08 20:33:30.005 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:33:30.029 [info] > git status -z -uall [15ms]
2025-06-08 20:33:30.029 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:33:35.050 [info] > git config --get commit.template [8ms]
2025-06-08 20:33:35.051 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:33:35.070 [info] > git status -z -uall [11ms]
2025-06-08 20:33:35.070 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:33:40.091 [info] > git config --get commit.template [9ms]
2025-06-08 20:33:40.092 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:33:40.108 [info] > git status -z -uall [8ms]
2025-06-08 20:33:40.109 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:33:45.129 [info] > git config --get commit.template [8ms]
2025-06-08 20:33:45.129 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:33:45.143 [info] > git status -z -uall [7ms]
2025-06-08 20:33:45.144 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:33:50.163 [info] > git config --get commit.template [9ms]
2025-06-08 20:33:50.164 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:33:50.180 [info] > git status -z -uall [8ms]
2025-06-08 20:33:50.181 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:33:55.203 [info] > git config --get commit.template [9ms]
2025-06-08 20:33:55.204 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:33:55.223 [info] > git status -z -uall [8ms]
2025-06-08 20:33:55.224 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:34:00.238 [info] > git config --get commit.template [5ms]
2025-06-08 20:34:00.239 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:34:00.252 [info] > git status -z -uall [8ms]
2025-06-08 20:34:00.253 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-08 20:34:05.272 [info] > git config --get commit.template [9ms]
2025-06-08 20:34:05.273 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:34:05.287 [info] > git status -z -uall [7ms]
2025-06-08 20:34:05.289 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:34:10.305 [info] > git config --get commit.template [7ms]
2025-06-08 20:34:10.307 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:34:10.320 [info] > git status -z -uall [7ms]
2025-06-08 20:34:10.322 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:34:15.340 [info] > git config --get commit.template [7ms]
2025-06-08 20:34:15.343 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-08 20:34:15.357 [info] > git status -z -uall [7ms]
2025-06-08 20:34:15.358 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:34:20.373 [info] > git config --get commit.template [7ms]
2025-06-08 20:34:20.375 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:34:20.390 [info] > git status -z -uall [8ms]
2025-06-08 20:34:20.391 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:34:25.412 [info] > git config --get commit.template [9ms]
2025-06-08 20:34:25.414 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:34:25.429 [info] > git status -z -uall [7ms]
2025-06-08 20:34:25.430 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:34:30.443 [info] > git config --get commit.template [5ms]
2025-06-08 20:34:30.444 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:34:30.460 [info] > git status -z -uall [9ms]
2025-06-08 20:34:30.461 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:34:35.478 [info] > git config --get commit.template [8ms]
2025-06-08 20:34:35.480 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:34:35.496 [info] > git status -z -uall [8ms]
2025-06-08 20:34:35.496 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:34:40.514 [info] > git config --get commit.template [7ms]
2025-06-08 20:34:40.516 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:34:40.527 [info] > git status -z -uall [6ms]
2025-06-08 20:34:40.529 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:34:45.546 [info] > git config --get commit.template [7ms]
2025-06-08 20:34:45.547 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:34:45.608 [info] > git status -z -uall [54ms]
2025-06-08 20:34:45.609 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [47ms]
2025-06-08 20:34:54.548 [info] > git config --get commit.template [9ms]
2025-06-08 20:34:54.549 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:34:54.612 [info] > git status -z -uall [56ms]
2025-06-08 20:34:54.612 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [49ms]
2025-06-08 20:34:59.630 [info] > git config --get commit.template [5ms]
2025-06-08 20:34:59.632 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:34:59.646 [info] > git status -z -uall [7ms]
2025-06-08 20:34:59.648 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:35:04.665 [info] > git config --get commit.template [6ms]
2025-06-08 20:35:04.667 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:35:04.684 [info] > git status -z -uall [10ms]
2025-06-08 20:35:04.685 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:35:09.703 [info] > git config --get commit.template [9ms]
2025-06-08 20:35:09.704 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:35:09.720 [info] > git status -z -uall [8ms]
2025-06-08 20:35:09.722 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:35:14.746 [info] > git config --get commit.template [11ms]
2025-06-08 20:35:14.747 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:35:14.762 [info] > git status -z -uall [6ms]
2025-06-08 20:35:14.763 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:35:19.778 [info] > git config --get commit.template [6ms]
2025-06-08 20:35:19.779 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:35:19.793 [info] > git status -z -uall [7ms]
2025-06-08 20:35:19.794 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:35:24.813 [info] > git config --get commit.template [8ms]
2025-06-08 20:35:24.815 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:35:24.830 [info] > git status -z -uall [7ms]
2025-06-08 20:35:24.831 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:35:29.848 [info] > git config --get commit.template [7ms]
2025-06-08 20:35:29.849 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:35:29.863 [info] > git status -z -uall [7ms]
2025-06-08 20:35:29.864 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:35:34.882 [info] > git config --get commit.template [7ms]
2025-06-08 20:35:34.883 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:35:34.899 [info] > git status -z -uall [8ms]
2025-06-08 20:35:34.900 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:35:39.918 [info] > git config --get commit.template [7ms]
2025-06-08 20:35:39.919 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:35:39.935 [info] > git status -z -uall [8ms]
2025-06-08 20:35:39.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:35:44.951 [info] > git config --get commit.template [6ms]
2025-06-08 20:35:44.952 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:35:44.967 [info] > git status -z -uall [7ms]
2025-06-08 20:35:44.968 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:35:49.984 [info] > git config --get commit.template [7ms]
2025-06-08 20:35:49.986 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:35:49.997 [info] > git status -z -uall [6ms]
2025-06-08 20:35:49.998 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:35:55.014 [info] > git config --get commit.template [6ms]
2025-06-08 20:35:55.015 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:35:55.030 [info] > git status -z -uall [8ms]
2025-06-08 20:35:55.031 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:36:00.050 [info] > git config --get commit.template [9ms]
2025-06-08 20:36:00.052 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:36:00.063 [info] > git status -z -uall [6ms]
2025-06-08 20:36:00.064 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:36:05.082 [info] > git config --get commit.template [8ms]
2025-06-08 20:36:05.084 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:36:05.109 [info] > git status -z -uall [12ms]
2025-06-08 20:36:05.112 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-08 20:36:10.130 [info] > git config --get commit.template [8ms]
2025-06-08 20:36:10.131 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:36:10.145 [info] > git status -z -uall [8ms]
2025-06-08 20:36:10.148 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-08 20:36:15.165 [info] > git config --get commit.template [1ms]
2025-06-08 20:36:15.179 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:36:15.195 [info] > git status -z -uall [8ms]
2025-06-08 20:36:15.196 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:36:20.216 [info] > git config --get commit.template [10ms]
2025-06-08 20:36:20.218 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:36:20.235 [info] > git status -z -uall [9ms]
2025-06-08 20:36:20.236 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:36:25.257 [info] > git config --get commit.template [10ms]
2025-06-08 20:36:25.260 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-08 20:36:25.275 [info] > git status -z -uall [8ms]
2025-06-08 20:36:25.276 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:36:30.301 [info] > git config --get commit.template [7ms]
2025-06-08 20:36:30.302 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:36:30.319 [info] > git status -z -uall [7ms]
2025-06-08 20:36:30.320 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:36:35.338 [info] > git config --get commit.template [8ms]
2025-06-08 20:36:35.339 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:36:35.353 [info] > git status -z -uall [6ms]
2025-06-08 20:36:35.354 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:36:40.373 [info] > git config --get commit.template [7ms]
2025-06-08 20:36:40.375 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:36:40.390 [info] > git status -z -uall [7ms]
2025-06-08 20:36:40.391 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:36:45.409 [info] > git config --get commit.template [8ms]
2025-06-08 20:36:45.410 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:36:45.425 [info] > git status -z -uall [8ms]
2025-06-08 20:36:45.427 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:36:50.445 [info] > git config --get commit.template [9ms]
2025-06-08 20:36:50.447 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:36:50.461 [info] > git status -z -uall [7ms]
2025-06-08 20:36:50.462 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:36:55.474 [info] > git config --get commit.template [2ms]
2025-06-08 20:36:55.483 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:36:55.501 [info] > git status -z -uall [11ms]
2025-06-08 20:36:55.501 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-08 20:37:00.518 [info] > git config --get commit.template [6ms]
2025-06-08 20:37:00.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:37:00.536 [info] > git status -z -uall [9ms]
2025-06-08 20:37:00.544 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-08 20:37:05.563 [info] > git config --get commit.template [7ms]
2025-06-08 20:37:05.564 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:37:05.579 [info] > git status -z -uall [9ms]
2025-06-08 20:37:05.582 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-08 20:37:10.621 [info] > git config --get commit.template [9ms]
2025-06-08 20:37:10.635 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:37:10.684 [info] > git status -z -uall [34ms]
2025-06-08 20:37:10.685 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-08 20:37:15.714 [info] > git config --get commit.template [9ms]
2025-06-08 20:37:15.715 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:37:15.734 [info] > git status -z -uall [11ms]
2025-06-08 20:37:15.734 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-08 20:37:20.753 [info] > git config --get commit.template [7ms]
2025-06-08 20:37:20.754 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:37:20.771 [info] > git status -z -uall [10ms]
2025-06-08 20:37:20.771 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:37:25.787 [info] > git config --get commit.template [6ms]
2025-06-08 20:37:25.788 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:37:25.800 [info] > git status -z -uall [7ms]
2025-06-08 20:37:25.801 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:37:30.819 [info] > git config --get commit.template [8ms]
2025-06-08 20:37:30.820 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:37:30.835 [info] > git status -z -uall [8ms]
2025-06-08 20:37:30.836 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:37:35.859 [info] > git config --get commit.template [11ms]
2025-06-08 20:37:35.861 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:37:35.882 [info] > git status -z -uall [11ms]
2025-06-08 20:37:35.883 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-08 20:37:40.900 [info] > git config --get commit.template [6ms]
2025-06-08 20:37:40.901 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:37:40.915 [info] > git status -z -uall [8ms]
2025-06-08 20:37:40.916 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:37:46.006 [info] > git config --get commit.template [76ms]
2025-06-08 20:37:46.008 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [66ms]
2025-06-08 20:37:46.079 [info] > git status -z -uall [12ms]
2025-06-08 20:37:46.080 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-08 20:37:51.096 [info] > git config --get commit.template [7ms]
2025-06-08 20:37:51.097 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:37:51.116 [info] > git status -z -uall [14ms]
2025-06-08 20:37:51.116 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-08 20:37:56.137 [info] > git config --get commit.template [8ms]
2025-06-08 20:37:56.138 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:37:56.159 [info] > git status -z -uall [12ms]
2025-06-08 20:37:56.162 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-08 20:38:01.198 [info] > git config --get commit.template [12ms]
2025-06-08 20:38:01.200 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:38:01.216 [info] > git status -z -uall [7ms]
2025-06-08 20:38:01.217 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:38:06.232 [info] > git config --get commit.template [6ms]
2025-06-08 20:38:06.234 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:38:06.244 [info] > git status -z -uall [5ms]
2025-06-08 20:38:06.245 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:38:11.264 [info] > git config --get commit.template [8ms]
2025-06-08 20:38:11.265 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:38:11.282 [info] > git status -z -uall [9ms]
2025-06-08 20:38:11.285 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-08 20:38:16.596 [info] > git config --get commit.template [2ms]
2025-06-08 20:38:16.603 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:38:16.618 [info] > git status -z -uall [7ms]
2025-06-08 20:38:16.619 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:43:46.118 [info] > git config --get commit.template [9ms]
2025-06-08 20:43:46.119 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:43:46.131 [info] > git status -z -uall [6ms]
2025-06-08 20:43:46.131 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:43:51.211 [info] > git config --get commit.template [26ms]
2025-06-08 20:43:51.212 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:43:51.271 [info] > git status -z -uall [17ms]
2025-06-08 20:43:51.275 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-08 20:43:56.299 [info] > git config --get commit.template [9ms]
2025-06-08 20:43:56.303 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-08 20:43:56.327 [info] > git status -z -uall [11ms]
2025-06-08 20:43:56.329 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:44:01.348 [info] > git config --get commit.template [8ms]
2025-06-08 20:44:01.350 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:44:01.385 [info] > git status -z -uall [23ms]
2025-06-08 20:44:01.389 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-08 20:44:06.421 [info] > git config --get commit.template [9ms]
2025-06-08 20:44:06.422 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:44:06.438 [info] > git status -z -uall [10ms]
2025-06-08 20:44:06.440 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:44:11.465 [info] > git config --get commit.template [8ms]
2025-06-08 20:44:11.466 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:44:11.481 [info] > git status -z -uall [7ms]
2025-06-08 20:44:11.486 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-08 20:44:16.503 [info] > git config --get commit.template [6ms]
2025-06-08 20:44:16.504 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:44:16.514 [info] > git status -z -uall [5ms]
2025-06-08 20:44:16.515 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:44:21.531 [info] > git config --get commit.template [6ms]
2025-06-08 20:44:21.532 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:44:21.545 [info] > git status -z -uall [7ms]
2025-06-08 20:44:21.546 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:44:26.559 [info] > git config --get commit.template [2ms]
2025-06-08 20:44:26.568 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:44:26.587 [info] > git status -z -uall [10ms]
2025-06-08 20:44:26.588 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:44:31.614 [info] > git config --get commit.template [14ms]
2025-06-08 20:44:31.616 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:44:31.632 [info] > git status -z -uall [8ms]
2025-06-08 20:44:31.633 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:44:36.652 [info] > git config --get commit.template [8ms]
2025-06-08 20:44:36.653 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:44:36.664 [info] > git status -z -uall [5ms]
2025-06-08 20:44:36.666 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:44:41.685 [info] > git config --get commit.template [8ms]
2025-06-08 20:44:41.687 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:44:41.710 [info] > git status -z -uall [14ms]
2025-06-08 20:44:41.710 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:44:50.897 [info] > git config --get commit.template [10ms]
2025-06-08 20:44:50.898 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:44:50.918 [info] > git status -z -uall [11ms]
2025-06-08 20:44:50.919 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:44:56.891 [info] > git config --get commit.template [2ms]
2025-06-08 20:44:56.900 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:44:56.917 [info] > git status -z -uall [9ms]
2025-06-08 20:44:56.917 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:45:01.932 [info] > git config --get commit.template [5ms]
2025-06-08 20:45:01.934 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:45:01.944 [info] > git status -z -uall [6ms]
2025-06-08 20:45:01.945 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:45:07.010 [info] > git config --get commit.template [13ms]
2025-06-08 20:45:07.020 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:45:07.033 [info] > git status -z -uall [6ms]
2025-06-08 20:45:07.039 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-08 20:45:12.055 [info] > git config --get commit.template [6ms]
2025-06-08 20:45:12.056 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:45:12.069 [info] > git status -z -uall [8ms]
2025-06-08 20:45:12.071 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:45:17.089 [info] > git config --get commit.template [8ms]
2025-06-08 20:45:17.089 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:45:17.099 [info] > git status -z -uall [5ms]
2025-06-08 20:45:17.101 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:45:22.119 [info] > git config --get commit.template [8ms]
2025-06-08 20:45:22.121 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:45:22.132 [info] > git status -z -uall [5ms]
2025-06-08 20:45:22.133 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:45:27.149 [info] > git config --get commit.template [8ms]
2025-06-08 20:45:27.150 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:45:27.167 [info] > git status -z -uall [9ms]
2025-06-08 20:45:27.168 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:50:36.143 [info] > git config --get commit.template [8ms]
2025-06-08 20:50:36.145 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:50:36.163 [info] > git status -z -uall [10ms]
2025-06-08 20:50:36.163 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:50:41.192 [info] > git config --get commit.template [9ms]
2025-06-08 20:50:41.193 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:50:41.208 [info] > git status -z -uall [9ms]
2025-06-08 20:50:41.209 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:50:46.226 [info] > git config --get commit.template [6ms]
2025-06-08 20:50:46.228 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:50:46.256 [info] > git status -z -uall [19ms]
2025-06-08 20:50:46.264 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-08 20:50:51.289 [info] > git config --get commit.template [12ms]
2025-06-08 20:50:51.291 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:50:51.320 [info] > git status -z -uall [19ms]
2025-06-08 20:50:51.320 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-08 20:50:56.335 [info] > git config --get commit.template [6ms]
2025-06-08 20:50:56.336 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:50:56.347 [info] > git status -z -uall [6ms]
2025-06-08 20:50:56.348 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:51:01.367 [info] > git config --get commit.template [7ms]
2025-06-08 20:51:01.368 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:51:01.386 [info] > git status -z -uall [9ms]
2025-06-08 20:51:01.388 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:51:06.408 [info] > git config --get commit.template [8ms]
2025-06-08 20:51:06.408 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:51:06.421 [info] > git status -z -uall [6ms]
2025-06-08 20:51:06.422 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:51:11.441 [info] > git config --get commit.template [1ms]
2025-06-08 20:51:11.452 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:51:11.473 [info] > git status -z -uall [11ms]
2025-06-08 20:51:11.474 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:52:25.443 [info] > git config --get commit.template [21ms]
2025-06-08 20:52:25.445 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-08 20:52:25.491 [info] > git status -z -uall [16ms]
2025-06-08 20:52:25.496 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-08 20:52:30.518 [info] > git config --get commit.template [10ms]
2025-06-08 20:52:30.520 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:52:30.538 [info] > git status -z -uall [9ms]
2025-06-08 20:52:30.551 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [13ms]
2025-06-08 20:52:35.575 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:52:35.576 [info] > git config --get commit.template [10ms]
2025-06-08 20:52:35.595 [info] > git status -z -uall [12ms]
2025-06-08 20:52:35.596 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-08 20:52:40.620 [info] > git config --get commit.template [10ms]
2025-06-08 20:52:40.621 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:52:40.640 [info] > git status -z -uall [13ms]
2025-06-08 20:52:40.643 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-08 20:52:45.671 [info] > git config --get commit.template [12ms]
2025-06-08 20:52:45.673 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:52:45.703 [info] > git status -z -uall [11ms]
2025-06-08 20:52:45.704 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:52:51.280 [info] > git config --get commit.template [13ms]
2025-06-08 20:52:51.281 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:52:51.304 [info] > git status -z -uall [8ms]
2025-06-08 20:52:51.305 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-08 20:52:56.338 [info] > git config --get commit.template [14ms]
2025-06-08 20:52:56.339 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:52:56.372 [info] > git status -z -uall [14ms]
2025-06-08 20:52:56.376 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-08 20:53:01.396 [info] > git config --get commit.template [7ms]
2025-06-08 20:53:01.399 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:53:01.412 [info] > git status -z -uall [5ms]
2025-06-08 20:53:01.417 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-08 20:53:06.434 [info] > git config --get commit.template [7ms]
2025-06-08 20:53:06.436 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:53:06.447 [info] > git status -z -uall [6ms]
2025-06-08 20:53:06.448 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:53:11.469 [info] > git config --get commit.template [9ms]
2025-06-08 20:53:11.471 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:53:11.493 [info] > git status -z -uall [14ms]
2025-06-08 20:53:11.495 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:53:16.514 [info] > git config --get commit.template [7ms]
2025-06-08 20:53:16.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:53:16.530 [info] > git status -z -uall [9ms]
2025-06-08 20:53:16.531 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:53:21.560 [info] > git config --get commit.template [11ms]
2025-06-08 20:53:21.560 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:53:21.575 [info] > git status -z -uall [8ms]
2025-06-08 20:53:21.576 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:53:26.595 [info] > git config --get commit.template [8ms]
2025-06-08 20:53:26.596 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:53:26.614 [info] > git status -z -uall [9ms]
2025-06-08 20:53:26.615 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:53:31.633 [info] > git config --get commit.template [7ms]
2025-06-08 20:53:31.634 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:53:31.649 [info] > git status -z -uall [8ms]
2025-06-08 20:53:31.652 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-08 20:53:36.677 [info] > git config --get commit.template [16ms]
2025-06-08 20:53:36.679 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:53:36.694 [info] > git status -z -uall [8ms]
2025-06-08 20:53:36.694 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-08 20:53:41.709 [info] > git config --get commit.template [5ms]
2025-06-08 20:53:41.710 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:53:41.724 [info] > git status -z -uall [9ms]
2025-06-08 20:53:41.725 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:53:46.743 [info] > git config --get commit.template [8ms]
2025-06-08 20:53:46.744 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 20:53:46.760 [info] > git status -z -uall [8ms]
2025-06-08 20:53:46.761 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:53:51.780 [info] > git config --get commit.template [9ms]
2025-06-08 20:53:51.781 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 20:53:51.796 [info] > git status -z -uall [8ms]
2025-06-08 20:53:51.797 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 20:53:56.812 [info] > git config --get commit.template [6ms]
2025-06-08 20:53:56.813 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:53:56.831 [info] > git status -z -uall [8ms]
2025-06-08 20:53:56.832 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 20:54:01.848 [info] > git config --get commit.template [7ms]
2025-06-08 20:54:01.849 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 20:54:01.866 [info] > git status -z -uall [8ms]
2025-06-08 20:54:01.867 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 21:04:33.952 [info] > git config --get commit.template [2ms]
2025-06-08 21:04:33.966 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-08 21:04:33.986 [info] > git status -z -uall [9ms]
2025-06-08 21:04:33.987 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 21:04:39.009 [info] > git config --get commit.template [10ms]
2025-06-08 21:04:39.010 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 21:04:39.025 [info] > git status -z -uall [8ms]
2025-06-08 21:04:39.026 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 21:04:44.046 [info] > git config --get commit.template [10ms]
2025-06-08 21:04:44.047 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 21:04:44.081 [info] > git status -z -uall [21ms]
2025-06-08 21:04:44.081 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 21:04:49.100 [info] > git config --get commit.template [8ms]
2025-06-08 21:04:49.101 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 21:04:49.118 [info] > git status -z -uall [9ms]
2025-06-08 21:04:49.120 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 21:04:54.143 [info] > git config --get commit.template [9ms]
2025-06-08 21:04:54.144 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 21:04:54.163 [info] > git status -z -uall [9ms]
2025-06-08 21:04:54.164 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 21:04:59.181 [info] > git config --get commit.template [7ms]
2025-06-08 21:04:59.182 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-08 21:04:59.194 [info] > git status -z -uall [5ms]
2025-06-08 21:04:59.195 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-08 21:07:43.717 [info] > git config --get commit.template [1ms]
2025-06-08 21:07:43.727 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 21:07:43.749 [info] > git status -z -uall [13ms]
2025-06-08 21:07:43.750 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 21:30:22.093 [info] > git config --get commit.template [17ms]
2025-06-08 21:30:22.094 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 21:30:22.122 [info] > git status -z -uall [14ms]
2025-06-08 21:30:22.124 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 21:30:22.140 [info] > git merge-base refs/heads/main refs/remotes/origin/main [2ms]
2025-06-08 21:30:22.153 [info] > git diff --name-status -z --diff-filter=ADMR 321834a4fd8b2568b5e53a37b787f1381eb5c6c9...refs/remotes/origin/main [2ms]
2025-06-08 21:30:27.162 [info] > git config --get commit.template [11ms]
2025-06-08 21:30:27.164 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 21:30:27.184 [info] > git status -z -uall [10ms]
2025-06-08 21:30:27.185 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 21:30:32.204 [info] > git config --get commit.template [8ms]
2025-06-08 21:30:32.205 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 21:30:32.233 [info] > git status -z -uall [22ms]
2025-06-08 21:30:32.233 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-08 21:30:37.307 [info] > git config --get commit.template [11ms]
2025-06-08 21:30:37.308 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 21:30:37.332 [info] > git status -z -uall [15ms]
2025-06-08 21:30:37.332 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-08 21:30:42.361 [info] > git config --get commit.template [14ms]
2025-06-08 21:30:42.363 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-08 21:30:42.390 [info] > git status -z -uall [14ms]
2025-06-08 21:30:42.392 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 21:30:47.425 [info] > git config --get commit.template [21ms]
2025-06-08 21:30:47.425 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 21:30:47.447 [info] > git status -z -uall [8ms]
2025-06-08 21:30:47.449 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 21:30:52.465 [info] > git config --get commit.template [0ms]
2025-06-08 21:30:52.476 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 21:30:52.497 [info] > git status -z -uall [13ms]
2025-06-08 21:30:52.497 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 21:30:57.517 [info] > git config --get commit.template [8ms]
2025-06-08 21:30:57.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 21:30:57.537 [info] > git status -z -uall [10ms]
2025-06-08 21:30:57.538 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 21:31:02.624 [info] > git config --get commit.template [73ms]
2025-06-08 21:31:02.624 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [55ms]
2025-06-08 21:31:02.643 [info] > git status -z -uall [9ms]
2025-06-08 21:31:02.644 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 21:32:37.979 [info] > git config --get commit.template [10ms]
2025-06-08 21:32:37.980 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 21:32:37.998 [info] > git status -z -uall [9ms]
2025-06-08 21:32:37.999 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 21:32:43.028 [info] > git config --get commit.template [9ms]
2025-06-08 21:32:43.029 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 21:32:43.055 [info] > git status -z -uall [17ms]
2025-06-08 21:32:43.056 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-08 21:32:48.082 [info] > git config --get commit.template [11ms]
2025-06-08 21:32:48.083 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-08 21:32:48.108 [info] > git status -z -uall [11ms]
2025-06-08 21:32:48.109 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
