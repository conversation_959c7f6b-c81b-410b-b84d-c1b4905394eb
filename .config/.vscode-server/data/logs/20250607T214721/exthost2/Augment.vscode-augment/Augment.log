2025-06-07 21:50:23.805 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-07 21:50:23.805 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-07 21:50:23.805 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5}
2025-06-07 21:50:23.891 [info] 'AugmentExtension' Retrieving model config
2025-06-07 21:50:24.196 [info] 'AugmentExtension' Retrieved model config
2025-06-07 21:50:24.196 [info] 'AugmentExtension' Returning model config
2025-06-07 21:50:24.219 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
2025-06-07 21:50:24.219 [info] 'SyncingPermissionTracker' Initial syncing permission: undefined
2025-06-07 21:50:24.220 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-07 21:50:24.220 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-07 21:50:24.220 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace unknown: no permission information recorded
2025-06-07 21:50:24.220 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = unknown
2025-06-07 21:50:24.236 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-07 21:50:24.236 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-07 21:50:24.237 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-07 21:50:24.251 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-07 21:50:24.252 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-07 21:50:24.261 [info] 'WorkspaceManager' Beginning full qualification of source folder /home/<USER>/workspace
2025-06-07 21:50:24.985 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-07 21:50:24.985 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-07 21:50:24.986 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-07 21:50:24.986 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-07 21:50:24.998 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-07 21:50:24.999 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-07 21:50:28.363 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-07 21:50:29.093 [info] 'WorkspaceManager' Finished full qualification of source folder /home/<USER>/workspace: trackable files: 1767, uploaded fraction: 0.785, is repo: true
2025-06-07 21:50:29.093 [info] 'WorkspaceManager' Requesting syncing permission because source folder has less than 90% of files uploaded
2025-06-07 21:50:29.095 [info] 'AwaitingSyncingPermissionApp' Registering AwaitingSyncingPermissionApp
2025-06-07 21:50:32.174 [info] 'AwaitingSyncingPermissionApp' User granted syncing permission
2025-06-07 21:50:32.174 [info] 'WorkspaceManager' Enabling syncing for all trackable source folders
2025-06-07 21:50:32.175 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/7/2025, 9:50:32 PM
2025-06-07 21:50:32.392 [info] 'WorkspaceManager[workspace]' Start tracking
2025-06-07 21:50:32.404 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-06-07 21:50:32.404 [info] 'OpenFileManager' Opened source folder 100
2025-06-07 21:50:32.405 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-07 21:50:32.405 [info] 'MtimeCache[workspace]' no blob name cache found at /home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json (probably new source folder); error = ENOENT: no such file or directory, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json'
2025-06-07 21:50:32.778 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-07 21:50:32.778 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-07 21:50:33.070 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-07 21:50:33.070 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-07 21:50:33.070 [info] 'ToolsModel' Saved chat mode: CHAT
2025-06-07 21:50:33.070 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-07 21:50:33.257 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-07 21:50:33.257 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-07 21:50:33.280 [info] 'TaskManager' Setting current root task UUID to e4555c40-1a0b-4630-b594-daf497c34799
2025-06-07 21:50:33.280 [info] 'TaskManager' Setting current root task UUID to e4555c40-1a0b-4630-b594-daf497c34799
2025-06-07 21:50:33.332 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets
2025-06-07 21:50:33.333 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/agent-edits
2025-06-07 21:50:33.334 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/agent-edits/manifest
2025-06-07 21:50:33.334 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/agent-edits/shards
2025-06-07 21:50:33.334 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/task-storage
2025-06-07 21:50:33.335 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/task-storage/manifest
2025-06-07 21:50:33.335 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/task-storage/tasks
2025-06-07 21:50:33.518 [info] 'TaskManager' Setting current root task UUID to e5fb3da8-167a-4fb6-8e8e-0840ecac57c6
2025-06-07 21:50:33.518 [info] 'TaskManager' Setting current root task UUID to e5fb3da8-167a-4fb6-8e8e-0840ecac57c6
2025-06-07 21:50:33.518 [info] 'TaskManager' Setting current root task UUID to 6ec9c24f-dced-4a74-95a5-5835b1fa6490
2025-06-07 21:50:33.518 [info] 'TaskManager' Setting current root task UUID to 6ec9c24f-dced-4a74-95a5-5835b1fa6490
2025-06-07 21:50:38.286 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-292bd64e-85d6-4d8e-af41-5535eb0e09b5.json'
2025-06-07 21:50:45.080 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-06-07 21:50:45.080 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 2441
  - files emitted: 1771
  - other paths emitted: 4
  - total paths emitted: 4216
  - timing stats:
    - readDir: 28 ms
    - filter: 106 ms
    - yield: 24 ms
    - total: 171 ms
2025-06-07 21:50:45.080 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 1385
  - paths not accessible: 0
  - not plain files: 0
  - large files: 25
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 0
  - mtime cache misses: 1385
  - probe batches: 4
  - blob names probed: 1696
  - files read: 2077
  - blobs uploaded: 293
  - timing stats:
    - ingestPath: 7 ms
    - probe: 2727 ms
    - stat: 33 ms
    - read: 3770 ms
    - upload: 3418 ms
2025-06-07 21:50:45.080 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 12 ms
  - read MtimeCache: 1 ms
  - pre-populate PathMap: 0 ms
  - create PathFilter: 171 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 195 ms
  - purge stale PathMap entries: 2 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 12300 ms
  - enable persist: 6 ms
  - total: 12687 ms
2025-06-07 21:50:45.081 [info] 'WorkspaceManager' Workspace startup complete in 20877 ms
2025-06-07 21:50:45.321 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1
2025-06-07 21:54:10.879 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-07 21:54:10.894 [info] 'ToolsModel' Saved chat mode: AGENT
2025-06-07 21:54:10.895 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-07 21:54:10.895 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-07 21:54:11.208 [info] 'TaskManager' Setting current root task UUID to fdae86cd-91fa-4af5-9a62-343ffb7fad2e
2025-06-07 21:54:11.208 [info] 'TaskManager' Setting current root task UUID to fdae86cd-91fa-4af5-9a62-343ffb7fad2e
2025-06-07 21:54:11.514 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-07 21:54:11.514 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-07 21:55:18.655 [info] 'ViewTool' Tool called with path: . and view_range: undefined
2025-06-07 21:55:18.736 [info] 'ViewTool' Listing directory: . (depth: 2, showHidden: false)
2025-06-07 21:55:26.548 [info] 'ViewTool' Tool called with path: package.json and view_range: undefined
2025-06-07 21:55:30.722 [info] 'ViewTool' Tool called with path: server/index.ts and view_range: undefined
2025-06-07 21:55:31.944 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250607T214721/exthost2/vscode.typescript-language-features
2025-06-07 21:55:34.907 [info] 'ViewTool' Tool called with path: db/schema.ts and view_range: undefined
2025-06-07 21:55:39.347 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: undefined
2025-06-07 21:55:53.423 [info] 'ViewTool' Tool called with path: client and view_range: undefined
2025-06-07 21:55:53.503 [info] 'ViewTool' Listing directory: client (depth: 2, showHidden: false)
2025-06-07 21:55:57.823 [info] 'ViewTool' Tool called with path: client/src/App.tsx and view_range: undefined
2025-06-07 21:56:12.409 [info] 'ViewTool' Tool called with path: SOLUTION_SUMMARY.md and view_range: undefined
2025-06-07 21:56:17.712 [info] 'ViewTool' Tool called with path: AI_CONSOLIDATION_PLAN.md and view_range: undefined
2025-06-07 21:56:22.057 [info] 'ViewTool' Tool called with path: ENHANCED_AI_CHAT_IMPROVEMENTS.md and view_range: undefined
2025-06-07 21:56:26.815 [info] 'ViewTool' Tool called with path: tests and view_range: undefined
2025-06-07 21:56:26.896 [info] 'ViewTool' Listing directory: tests (depth: 2, showHidden: false)
2025-06-07 21:56:31.240 [info] 'ViewTool' Tool called with path: TESTING_GUIDE.md and view_range: undefined
2025-06-07 21:57:15.264 [info] 'ToolFileUtils' Reading file: server/services/paymentService.ts
2025-06-07 21:57:15.889 [info] 'ToolFileUtils' File not found: server/services/paymentService.ts. No similar files found
2025-06-07 21:57:15.890 [error] 'StrReplaceEditorTool' Error in tool call: File not found: server/services/paymentService.ts
2025-06-07 21:57:41.053 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-292bd64e-85d6-4d8e-af41-5535eb0e09b5.json'
2025-06-07 21:57:41.212 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/checkpoint-documents
2025-06-07 21:58:08.046 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-292bd64e-85d6-4d8e-af41-5535eb0e09b5.json'
2025-06-07 21:58:51.490 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-292bd64e-85d6-4d8e-af41-5535eb0e09b5.json'
2025-06-07 21:59:22.397 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-292bd64e-85d6-4d8e-af41-5535eb0e09b5.json'
2025-06-07 22:00:04.458 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-292bd64e-85d6-4d8e-af41-5535eb0e09b5.json'
2025-06-07 22:00:14.787 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/47de4c07
