2025-06-07 21:49:52.548 [info] [main] Log level: Info
2025-06-07 21:49:52.548 [info] [main] Validating found git in: "git"
2025-06-07 21:49:52.548 [info] [main] Using git "2.47.2" from "git"
2025-06-07 21:49:52.548 [info] [Model][doInitialScan] Initial repository scan started
2025-06-07 21:49:52.548 [info] > git rev-parse --show-toplevel [47ms]
2025-06-07 21:49:52.548 [info] > git rev-parse --git-dir --git-common-dir [2ms]
2025-06-07 21:49:52.548 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-07 21:49:52.548 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-07 21:49:52.548 [info] > git config --get commit.template [54ms]
2025-06-07 21:49:52.548 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-06-07 21:49:52.620 [info] > git status -z -uall [61ms]
2025-06-07 21:49:52.621 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [51ms]
2025-06-07 21:49:52.679 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [28ms]
2025-06-07 21:49:52.691 [info] > git config --get commit.template [13ms]
2025-06-07 21:49:52.691 [info] > git rev-parse --show-toplevel [28ms]
2025-06-07 21:49:52.707 [info] > git rev-parse --show-toplevel [8ms]
2025-06-07 21:49:52.708 [info] > git config --get --local branch.main.vscode-merge-base [19ms]
2025-06-07 21:49:52.719 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-07 21:49:52.720 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 21:49:52.721 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [6ms]
2025-06-07 21:49:52.740 [info] > git merge-base refs/heads/main refs/remotes/origin/main [8ms]
2025-06-07 21:49:52.774 [info] > git rev-parse --show-toplevel [35ms]
2025-06-07 21:49:52.815 [info] > git diff --name-status -z --diff-filter=ADMR 321834a4fd8b2568b5e53a37b787f1381eb5c6c9...refs/remotes/origin/main [64ms]
2025-06-07 21:49:52.817 [info] > git status -z -uall [37ms]
2025-06-07 21:49:52.817 [info] > git rev-parse --show-toplevel [7ms]
2025-06-07 21:49:52.818 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [29ms]
2025-06-07 21:49:52.836 [info] > git rev-parse --show-toplevel [0ms]
2025-06-07 21:49:52.852 [info] > git rev-parse --show-toplevel [6ms]
2025-06-07 21:49:52.888 [info] > git rev-parse --show-toplevel [11ms]
2025-06-07 21:49:52.902 [info] > git rev-parse --show-toplevel [3ms]
2025-06-07 21:49:52.910 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 21:49:52.917 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 21:49:52.923 [info] > git rev-parse --show-toplevel [3ms]
2025-06-07 21:49:52.932 [info] > git rev-parse --show-toplevel [4ms]
2025-06-07 21:49:52.940 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 21:49:52.956 [info] > git rev-parse --show-toplevel [6ms]
2025-06-07 21:49:52.960 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-07 21:49:53.047 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-07 21:50:08.594 [info] > git config --get commit.template [5ms]
2025-06-07 21:50:08.595 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:50:08.602 [info] > git status -z -uall [4ms]
2025-06-07 21:50:08.603 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 21:50:13.616 [info] > git config --get commit.template [5ms]
2025-06-07 21:50:13.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 21:50:13.629 [info] > git status -z -uall [5ms]
2025-06-07 21:50:13.631 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:50:18.645 [info] > git config --get commit.template [4ms]
2025-06-07 21:50:18.646 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:50:18.654 [info] > git status -z -uall [4ms]
2025-06-07 21:50:18.655 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 21:50:23.794 [info] > git config --get commit.template [45ms]
2025-06-07 21:50:23.809 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 21:50:23.830 [info] > git status -z -uall [6ms]
2025-06-07 21:50:23.831 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:54:03.779 [info] > git config --get commit.template [0ms]
2025-06-07 21:54:03.797 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:54:03.827 [info] > git status -z -uall [11ms]
2025-06-07 21:54:03.828 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 21:54:08.857 [info] > git config --get commit.template [10ms]
2025-06-07 21:54:08.859 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 21:54:08.881 [info] > git status -z -uall [9ms]
2025-06-07 21:54:08.884 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 21:54:13.897 [info] > git config --get commit.template [1ms]
2025-06-07 21:54:13.908 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 21:54:13.922 [info] > git status -z -uall [6ms]
2025-06-07 21:54:13.924 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 21:54:19.427 [info] > git config --get commit.template [6ms]
2025-06-07 21:54:19.428 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:54:19.445 [info] > git status -z -uall [10ms]
2025-06-07 21:54:19.445 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 21:54:24.473 [info] > git config --get commit.template [12ms]
2025-06-07 21:54:24.475 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 21:54:24.491 [info] > git status -z -uall [8ms]
2025-06-07 21:54:24.491 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:54:29.510 [info] > git config --get commit.template [8ms]
2025-06-07 21:54:29.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 21:54:29.524 [info] > git status -z -uall [6ms]
2025-06-07 21:54:29.525 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:54:34.539 [info] > git config --get commit.template [2ms]
2025-06-07 21:54:34.549 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:54:34.568 [info] > git status -z -uall [12ms]
2025-06-07 21:54:34.569 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:54:39.587 [info] > git config --get commit.template [8ms]
2025-06-07 21:54:39.587 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:54:39.601 [info] > git status -z -uall [7ms]
2025-06-07 21:54:39.602 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 21:54:44.619 [info] > git config --get commit.template [6ms]
2025-06-07 21:54:44.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 21:54:44.632 [info] > git status -z -uall [6ms]
2025-06-07 21:54:44.633 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:54:49.652 [info] > git config --get commit.template [7ms]
2025-06-07 21:54:49.653 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:54:49.666 [info] > git status -z -uall [6ms]
2025-06-07 21:54:49.667 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 21:54:54.688 [info] > git config --get commit.template [8ms]
2025-06-07 21:54:54.689 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:54:54.703 [info] > git status -z -uall [7ms]
2025-06-07 21:54:54.705 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:54:59.725 [info] > git config --get commit.template [7ms]
2025-06-07 21:54:59.726 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:54:59.740 [info] > git status -z -uall [9ms]
2025-06-07 21:54:59.741 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 21:55:04.761 [info] > git config --get commit.template [8ms]
2025-06-07 21:55:04.762 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:55:04.776 [info] > git status -z -uall [6ms]
2025-06-07 21:55:04.778 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:55:09.797 [info] > git config --get commit.template [1ms]
2025-06-07 21:55:09.815 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 21:55:09.835 [info] > git status -z -uall [9ms]
2025-06-07 21:55:09.837 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:55:14.860 [info] > git config --get commit.template [10ms]
2025-06-07 21:55:14.861 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:55:14.877 [info] > git status -z -uall [8ms]
2025-06-07 21:55:14.880 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 21:58:08.740 [info] > git config --get commit.template [1ms]
2025-06-07 21:58:08.749 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 21:58:08.762 [info] > git status -z -uall [7ms]
2025-06-07 21:58:08.763 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:59:42.634 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 21:59:42.634 [info] > git config --get commit.template [13ms]
2025-06-07 21:59:42.650 [info] > git status -z -uall [8ms]
2025-06-07 21:59:42.652 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:59:47.668 [info] > git config --get commit.template [1ms]
2025-06-07 21:59:47.746 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [69ms]
2025-06-07 21:59:47.770 [info] > git status -z -uall [14ms]
2025-06-07 21:59:47.770 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 21:59:52.787 [info] > git config --get commit.template [6ms]
2025-06-07 21:59:52.788 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:59:52.801 [info] > git status -z -uall [8ms]
2025-06-07 21:59:52.801 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 21:59:57.859 [info] > git config --get commit.template [7ms]
2025-06-07 21:59:57.860 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:59:57.872 [info] > git status -z -uall [7ms]
2025-06-07 21:59:57.873 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:00:02.894 [info] > git config --get commit.template [9ms]
2025-06-07 22:00:02.895 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:00:02.962 [info] > git status -z -uall [60ms]
2025-06-07 22:00:02.962 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [53ms]
2025-06-07 22:00:07.982 [info] > git config --get commit.template [7ms]
2025-06-07 22:00:07.984 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:00:07.996 [info] > git status -z -uall [6ms]
2025-06-07 22:00:07.998 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:00:13.016 [info] > git config --get commit.template [7ms]
2025-06-07 22:00:13.017 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:00:13.028 [info] > git status -z -uall [6ms]
2025-06-07 22:00:13.029 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:00:18.059 [info] > git config --get commit.template [9ms]
2025-06-07 22:00:18.060 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:00:18.075 [info] > git status -z -uall [9ms]
2025-06-07 22:00:18.076 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 22:00:23.095 [info] > git config --get commit.template [7ms]
2025-06-07 22:00:23.096 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:00:23.107 [info] > git status -z -uall [5ms]
2025-06-07 22:00:23.109 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:00:28.125 [info] > git config --get commit.template [2ms]
2025-06-07 22:00:28.136 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:00:28.151 [info] > git status -z -uall [8ms]
2025-06-07 22:00:28.152 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:00:33.170 [info] > git config --get commit.template [7ms]
2025-06-07 22:00:33.171 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:00:33.183 [info] > git status -z -uall [6ms]
2025-06-07 22:00:33.184 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:00:38.218 [info] > git config --get commit.template [18ms]
2025-06-07 22:00:38.221 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 22:00:38.240 [info] > git status -z -uall [10ms]
2025-06-07 22:00:38.242 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:00:43.259 [info] > git config --get commit.template [7ms]
2025-06-07 22:00:43.260 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 22:00:43.271 [info] > git status -z -uall [6ms]
2025-06-07 22:00:43.272 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:00:48.306 [info] > git config --get commit.template [13ms]
2025-06-07 22:00:48.307 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:00:48.328 [info] > git status -z -uall [10ms]
2025-06-07 22:00:48.331 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 22:00:53.349 [info] > git config --get commit.template [7ms]
2025-06-07 22:00:53.350 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:00:53.362 [info] > git status -z -uall [7ms]
2025-06-07 22:00:53.363 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:00:58.390 [info] > git config --get commit.template [12ms]
2025-06-07 22:00:58.391 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:00:58.410 [info] > git status -z -uall [10ms]
2025-06-07 22:00:58.411 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:01:03.441 [info] > git config --get commit.template [11ms]
2025-06-07 22:01:03.444 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 22:01:03.462 [info] > git status -z -uall [7ms]
2025-06-07 22:01:03.464 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 22:01:08.483 [info] > git config --get commit.template [0ms]
2025-06-07 22:01:08.500 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 22:01:08.549 [info] > git status -z -uall [22ms]
2025-06-07 22:01:08.549 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:01:13.568 [info] > git config --get commit.template [6ms]
2025-06-07 22:01:13.570 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:01:13.582 [info] > git status -z -uall [6ms]
2025-06-07 22:01:13.584 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:01:18.603 [info] > git config --get commit.template [7ms]
2025-06-07 22:01:18.605 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 22:01:18.618 [info] > git status -z -uall [7ms]
2025-06-07 22:01:18.619 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:01:23.637 [info] > git config --get commit.template [7ms]
2025-06-07 22:01:23.638 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:01:23.651 [info] > git status -z -uall [7ms]
2025-06-07 22:01:23.652 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:01:28.683 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 22:01:28.683 [info] > git config --get commit.template [17ms]
2025-06-07 22:01:28.702 [info] > git status -z -uall [9ms]
2025-06-07 22:01:28.704 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:01:33.720 [info] > git config --get commit.template [6ms]
2025-06-07 22:01:33.722 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 22:01:33.733 [info] > git status -z -uall [5ms]
2025-06-07 22:01:33.734 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:01:38.747 [info] > git config --get commit.template [2ms]
2025-06-07 22:01:38.756 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:01:38.769 [info] > git status -z -uall [7ms]
2025-06-07 22:01:38.770 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:01:43.790 [info] > git config --get commit.template [9ms]
2025-06-07 22:01:43.791 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:01:43.808 [info] > git status -z -uall [6ms]
2025-06-07 22:01:43.808 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:01:48.827 [info] > git config --get commit.template [9ms]
2025-06-07 22:01:48.828 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:01:48.847 [info] > git status -z -uall [10ms]
2025-06-07 22:01:48.847 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:01:53.865 [info] > git config --get commit.template [7ms]
2025-06-07 22:01:53.867 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:01:53.879 [info] > git status -z -uall [6ms]
2025-06-07 22:01:53.880 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:01:58.897 [info] > git config --get commit.template [7ms]
2025-06-07 22:01:58.899 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:01:58.910 [info] > git status -z -uall [5ms]
2025-06-07 22:01:58.912 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:02:03.931 [info] > git config --get commit.template [8ms]
2025-06-07 22:02:03.934 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 22:02:03.962 [info] > git status -z -uall [21ms]
2025-06-07 22:02:03.968 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 22:02:08.996 [info] > git config --get commit.template [13ms]
2025-06-07 22:02:09.000 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 22:02:09.025 [info] > git status -z -uall [12ms]
2025-06-07 22:02:09.029 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 22:02:14.063 [info] > git config --get commit.template [13ms]
2025-06-07 22:02:14.065 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 22:02:14.085 [info] > git status -z -uall [9ms]
2025-06-07 22:02:14.088 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 22:02:19.103 [info] > git config --get commit.template [2ms]
2025-06-07 22:02:19.119 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 22:02:19.137 [info] > git status -z -uall [8ms]
2025-06-07 22:02:19.139 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:02:24.164 [info] > git config --get commit.template [12ms]
2025-06-07 22:02:24.166 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:02:24.193 [info] > git status -z -uall [14ms]
2025-06-07 22:02:24.193 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 22:02:29.240 [info] > git config --get commit.template [27ms]
2025-06-07 22:02:29.242 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 22:02:29.273 [info] > git status -z -uall [19ms]
2025-06-07 22:02:29.275 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 22:02:34.301 [info] > git config --get commit.template [12ms]
2025-06-07 22:02:34.304 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 22:02:34.332 [info] > git status -z -uall [13ms]
2025-06-07 22:02:34.332 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:02:39.351 [info] > git config --get commit.template [1ms]
2025-06-07 22:02:39.367 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 22:02:39.390 [info] > git status -z -uall [12ms]
2025-06-07 22:02:39.392 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:02:44.505 [info] > git config --get commit.template [25ms]
2025-06-07 22:02:44.506 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 22:02:44.532 [info] > git status -z -uall [15ms]
2025-06-07 22:02:44.532 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:02:49.568 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 22:02:49.568 [info] > git config --get commit.template [18ms]
2025-06-07 22:02:49.600 [info] > git status -z -uall [12ms]
2025-06-07 22:02:49.603 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 22:02:54.624 [info] > git config --get commit.template [2ms]
2025-06-07 22:02:54.637 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:02:54.656 [info] > git status -z -uall [9ms]
2025-06-07 22:02:54.659 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 22:02:59.679 [info] > git config --get commit.template [4ms]
2025-06-07 22:02:59.771 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 22:02:59.797 [info] > git status -z -uall [9ms]
2025-06-07 22:02:59.799 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 22:03:04.827 [info] > git config --get commit.template [14ms]
2025-06-07 22:03:04.830 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 22:03:04.851 [info] > git status -z -uall [10ms]
2025-06-07 22:03:04.854 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 22:03:09.876 [info] > git config --get commit.template [8ms]
2025-06-07 22:03:09.878 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 22:03:09.893 [info] > git status -z -uall [8ms]
2025-06-07 22:03:09.894 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:03:14.920 [info] > git config --get commit.template [13ms]
2025-06-07 22:03:14.922 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 22:03:14.939 [info] > git status -z -uall [8ms]
2025-06-07 22:03:14.940 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:03:19.965 [info] > git config --get commit.template [11ms]
2025-06-07 22:03:19.966 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:03:19.982 [info] > git status -z -uall [8ms]
2025-06-07 22:03:19.983 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:03:25.004 [info] > git config --get commit.template [8ms]
2025-06-07 22:03:25.005 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:03:25.020 [info] > git status -z -uall [7ms]
2025-06-07 22:03:25.022 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:03:31.839 [info] > git config --get commit.template [17ms]
2025-06-07 22:03:31.841 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 22:03:31.866 [info] > git status -z -uall [13ms]
2025-06-07 22:03:31.869 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 22:03:36.891 [info] > git config --get commit.template [8ms]
2025-06-07 22:03:36.893 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:03:36.906 [info] > git status -z -uall [7ms]
2025-06-07 22:03:36.907 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:03:41.927 [info] > git config --get commit.template [1ms]
2025-06-07 22:03:41.939 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:03:41.953 [info] > git status -z -uall [7ms]
2025-06-07 22:03:41.954 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:03:47.830 [info] > git config --get commit.template [10ms]
2025-06-07 22:03:47.831 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:03:47.845 [info] > git status -z -uall [8ms]
2025-06-07 22:03:47.846 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:03:52.865 [info] > git config --get commit.template [8ms]
2025-06-07 22:03:52.866 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:03:52.879 [info] > git status -z -uall [7ms]
2025-06-07 22:03:52.880 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:03:57.897 [info] > git config --get commit.template [7ms]
2025-06-07 22:03:57.898 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 22:03:57.911 [info] > git status -z -uall [7ms]
2025-06-07 22:03:57.912 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:04:02.930 [info] > git config --get commit.template [6ms]
2025-06-07 22:04:02.932 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:04:02.943 [info] > git status -z -uall [6ms]
2025-06-07 22:04:02.944 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:04:07.962 [info] > git config --get commit.template [7ms]
2025-06-07 22:04:07.963 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:04:07.973 [info] > git status -z -uall [5ms]
2025-06-07 22:04:07.975 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:04:12.993 [info] > git config --get commit.template [7ms]
2025-06-07 22:04:12.994 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:04:13.010 [info] > git status -z -uall [7ms]
2025-06-07 22:04:13.011 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:04:18.029 [info] > git config --get commit.template [7ms]
2025-06-07 22:04:18.030 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 22:04:18.044 [info] > git status -z -uall [6ms]
2025-06-07 22:04:18.046 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:04:23.065 [info] > git config --get commit.template [8ms]
2025-06-07 22:04:23.066 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:04:23.079 [info] > git status -z -uall [6ms]
2025-06-07 22:04:23.080 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:04:28.098 [info] > git config --get commit.template [7ms]
2025-06-07 22:04:28.099 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:04:28.120 [info] > git status -z -uall [12ms]
2025-06-07 22:04:28.122 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 22:04:33.141 [info] > git config --get commit.template [7ms]
2025-06-07 22:04:33.142 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:04:33.155 [info] > git status -z -uall [5ms]
2025-06-07 22:04:33.156 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:04:38.177 [info] > git config --get commit.template [8ms]
2025-06-07 22:04:38.178 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 22:04:38.200 [info] > git status -z -uall [9ms]
2025-06-07 22:04:38.201 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:04:43.226 [info] > git config --get commit.template [8ms]
2025-06-07 22:04:43.227 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 22:04:43.240 [info] > git status -z -uall [7ms]
2025-06-07 22:04:43.241 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:04:48.276 [info] > git config --get commit.template [17ms]
2025-06-07 22:04:48.278 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 22:04:48.330 [info] > git status -z -uall [37ms]
2025-06-07 22:04:48.330 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:04:53.355 [info] > git config --get commit.template [9ms]
2025-06-07 22:04:53.355 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:04:53.367 [info] > git status -z -uall [6ms]
2025-06-07 22:04:53.368 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:04:58.385 [info] > git config --get commit.template [6ms]
2025-06-07 22:04:58.386 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 22:04:58.397 [info] > git status -z -uall [5ms]
2025-06-07 22:04:58.398 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:05:03.416 [info] > git config --get commit.template [6ms]
2025-06-07 22:05:03.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:05:03.429 [info] > git status -z -uall [7ms]
2025-06-07 22:05:03.430 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:05:08.446 [info] > git config --get commit.template [1ms]
2025-06-07 22:05:08.458 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:05:08.474 [info] > git status -z -uall [7ms]
2025-06-07 22:05:08.475 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:05:13.498 [info] > git config --get commit.template [9ms]
2025-06-07 22:05:13.500 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:05:13.514 [info] > git status -z -uall [7ms]
2025-06-07 22:05:13.515 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:05:18.542 [info] > git config --get commit.template [12ms]
2025-06-07 22:05:18.543 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 22:05:18.563 [info] > git status -z -uall [9ms]
2025-06-07 22:05:18.564 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:05:23.586 [info] > git config --get commit.template [10ms]
2025-06-07 22:05:23.587 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 22:05:23.602 [info] > git status -z -uall [8ms]
2025-06-07 22:05:23.603 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:05:28.620 [info] > git config --get commit.template [7ms]
2025-06-07 22:05:28.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 22:05:28.631 [info] > git status -z -uall [6ms]
2025-06-07 22:05:28.632 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:05:33.649 [info] > git config --get commit.template [2ms]
2025-06-07 22:05:33.664 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 22:05:33.687 [info] > git status -z -uall [10ms]
2025-06-07 22:05:33.688 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:05:38.753 [info] > git config --get commit.template [7ms]
2025-06-07 22:05:38.754 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:05:38.768 [info] > git status -z -uall [8ms]
2025-06-07 22:05:38.769 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 22:05:43.789 [info] > git config --get commit.template [8ms]
2025-06-07 22:05:43.789 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 22:05:43.805 [info] > git status -z -uall [8ms]
2025-06-07 22:05:43.806 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:05:48.841 [info] > git config --get commit.template [18ms]
2025-06-07 22:05:48.845 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 22:05:48.868 [info] > git status -z -uall [11ms]
2025-06-07 22:05:48.869 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:05:53.886 [info] > git config --get commit.template [7ms]
2025-06-07 22:05:53.887 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 22:05:53.898 [info] > git status -z -uall [6ms]
2025-06-07 22:05:53.899 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:05:58.926 [info] > git config --get commit.template [10ms]
2025-06-07 22:05:58.927 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 22:05:58.940 [info] > git status -z -uall [6ms]
2025-06-07 22:05:58.942 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:06:03.959 [info] > git config --get commit.template [7ms]
2025-06-07 22:06:03.960 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:06:03.970 [info] > git status -z -uall [5ms]
2025-06-07 22:06:03.971 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:06:08.988 [info] > git config --get commit.template [7ms]
2025-06-07 22:06:08.991 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 22:06:09.004 [info] > git status -z -uall [7ms]
2025-06-07 22:06:09.005 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:06:14.025 [info] > git config --get commit.template [8ms]
2025-06-07 22:06:14.026 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:06:14.041 [info] > git status -z -uall [6ms]
2025-06-07 22:06:14.042 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:06:19.062 [info] > git config --get commit.template [7ms]
2025-06-07 22:06:19.063 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:06:19.076 [info] > git status -z -uall [5ms]
2025-06-07 22:06:19.077 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:06:24.093 [info] > git config --get commit.template [6ms]
2025-06-07 22:06:24.094 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 22:06:24.106 [info] > git status -z -uall [6ms]
2025-06-07 22:06:24.107 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:06:29.125 [info] > git config --get commit.template [2ms]
2025-06-07 22:06:29.144 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 22:06:29.169 [info] > git status -z -uall [11ms]
2025-06-07 22:06:29.172 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 22:06:34.189 [info] > git config --get commit.template [7ms]
2025-06-07 22:06:34.190 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:06:34.202 [info] > git status -z -uall [6ms]
2025-06-07 22:06:34.203 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:06:39.228 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 22:06:39.229 [info] > git config --get commit.template [12ms]
2025-06-07 22:06:39.246 [info] > git status -z -uall [10ms]
2025-06-07 22:06:39.248 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:06:44.264 [info] > git config --get commit.template [2ms]
2025-06-07 22:06:44.280 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 22:06:44.294 [info] > git status -z -uall [6ms]
2025-06-07 22:06:44.296 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:06:49.324 [info] > git config --get commit.template [14ms]
2025-06-07 22:06:49.325 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:06:49.344 [info] > git status -z -uall [10ms]
2025-06-07 22:06:49.345 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:06:54.362 [info] > git config --get commit.template [7ms]
2025-06-07 22:06:54.363 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 22:06:54.377 [info] > git status -z -uall [8ms]
2025-06-07 22:06:54.377 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 22:06:59.394 [info] > git config --get commit.template [6ms]
2025-06-07 22:06:59.395 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:06:59.408 [info] > git status -z -uall [6ms]
2025-06-07 22:06:59.409 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 22:07:04.428 [info] > git config --get commit.template [0ms]
2025-06-07 22:07:04.445 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 22:07:04.460 [info] > git status -z -uall [7ms]
2025-06-07 22:07:04.461 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
