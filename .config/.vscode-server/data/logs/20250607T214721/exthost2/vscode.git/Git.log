2025-06-07 21:49:52.548 [info] [main] Log level: Info
2025-06-07 21:49:52.548 [info] [main] Validating found git in: "git"
2025-06-07 21:49:52.548 [info] [main] Using git "2.47.2" from "git"
2025-06-07 21:49:52.548 [info] [Model][doInitialScan] Initial repository scan started
2025-06-07 21:49:52.548 [info] > git rev-parse --show-toplevel [47ms]
2025-06-07 21:49:52.548 [info] > git rev-parse --git-dir --git-common-dir [2ms]
2025-06-07 21:49:52.548 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-07 21:49:52.548 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-07 21:49:52.548 [info] > git config --get commit.template [54ms]
2025-06-07 21:49:52.548 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-06-07 21:49:52.620 [info] > git status -z -uall [61ms]
2025-06-07 21:49:52.621 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [51ms]
2025-06-07 21:49:52.679 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [28ms]
2025-06-07 21:49:52.691 [info] > git config --get commit.template [13ms]
2025-06-07 21:49:52.691 [info] > git rev-parse --show-toplevel [28ms]
2025-06-07 21:49:52.707 [info] > git rev-parse --show-toplevel [8ms]
2025-06-07 21:49:52.708 [info] > git config --get --local branch.main.vscode-merge-base [19ms]
2025-06-07 21:49:52.719 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-07 21:49:52.720 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 21:49:52.721 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [6ms]
2025-06-07 21:49:52.740 [info] > git merge-base refs/heads/main refs/remotes/origin/main [8ms]
2025-06-07 21:49:52.774 [info] > git rev-parse --show-toplevel [35ms]
2025-06-07 21:49:52.815 [info] > git diff --name-status -z --diff-filter=ADMR 321834a4fd8b2568b5e53a37b787f1381eb5c6c9...refs/remotes/origin/main [64ms]
2025-06-07 21:49:52.817 [info] > git status -z -uall [37ms]
2025-06-07 21:49:52.817 [info] > git rev-parse --show-toplevel [7ms]
2025-06-07 21:49:52.818 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [29ms]
2025-06-07 21:49:52.836 [info] > git rev-parse --show-toplevel [0ms]
2025-06-07 21:49:52.852 [info] > git rev-parse --show-toplevel [6ms]
2025-06-07 21:49:52.888 [info] > git rev-parse --show-toplevel [11ms]
2025-06-07 21:49:52.902 [info] > git rev-parse --show-toplevel [3ms]
2025-06-07 21:49:52.910 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 21:49:52.917 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 21:49:52.923 [info] > git rev-parse --show-toplevel [3ms]
2025-06-07 21:49:52.932 [info] > git rev-parse --show-toplevel [4ms]
2025-06-07 21:49:52.940 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 21:49:52.956 [info] > git rev-parse --show-toplevel [6ms]
2025-06-07 21:49:52.960 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-07 21:49:53.047 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-07 21:50:08.594 [info] > git config --get commit.template [5ms]
2025-06-07 21:50:08.595 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:50:08.602 [info] > git status -z -uall [4ms]
2025-06-07 21:50:08.603 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 21:50:13.616 [info] > git config --get commit.template [5ms]
2025-06-07 21:50:13.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 21:50:13.629 [info] > git status -z -uall [5ms]
2025-06-07 21:50:13.631 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:50:18.645 [info] > git config --get commit.template [4ms]
2025-06-07 21:50:18.646 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:50:18.654 [info] > git status -z -uall [4ms]
2025-06-07 21:50:18.655 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 21:50:23.794 [info] > git config --get commit.template [45ms]
2025-06-07 21:50:23.809 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-07 21:50:23.830 [info] > git status -z -uall [6ms]
2025-06-07 21:50:23.831 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:54:03.779 [info] > git config --get commit.template [0ms]
2025-06-07 21:54:03.797 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:54:03.827 [info] > git status -z -uall [11ms]
2025-06-07 21:54:03.828 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 21:54:08.857 [info] > git config --get commit.template [10ms]
2025-06-07 21:54:08.859 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 21:54:08.881 [info] > git status -z -uall [9ms]
2025-06-07 21:54:08.884 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 21:54:13.897 [info] > git config --get commit.template [1ms]
2025-06-07 21:54:13.908 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 21:54:13.922 [info] > git status -z -uall [6ms]
2025-06-07 21:54:13.924 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 21:54:19.427 [info] > git config --get commit.template [6ms]
2025-06-07 21:54:19.428 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:54:19.445 [info] > git status -z -uall [10ms]
2025-06-07 21:54:19.445 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 21:54:24.473 [info] > git config --get commit.template [12ms]
2025-06-07 21:54:24.475 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 21:54:24.491 [info] > git status -z -uall [8ms]
2025-06-07 21:54:24.491 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:54:29.510 [info] > git config --get commit.template [8ms]
2025-06-07 21:54:29.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 21:54:29.524 [info] > git status -z -uall [6ms]
2025-06-07 21:54:29.525 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:54:34.539 [info] > git config --get commit.template [2ms]
2025-06-07 21:54:34.549 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:54:34.568 [info] > git status -z -uall [12ms]
2025-06-07 21:54:34.569 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:54:39.587 [info] > git config --get commit.template [8ms]
2025-06-07 21:54:39.587 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:54:39.601 [info] > git status -z -uall [7ms]
2025-06-07 21:54:39.602 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 21:54:44.619 [info] > git config --get commit.template [6ms]
2025-06-07 21:54:44.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 21:54:44.632 [info] > git status -z -uall [6ms]
2025-06-07 21:54:44.633 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:54:49.652 [info] > git config --get commit.template [7ms]
2025-06-07 21:54:49.653 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:54:49.666 [info] > git status -z -uall [6ms]
2025-06-07 21:54:49.667 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 21:54:54.688 [info] > git config --get commit.template [8ms]
2025-06-07 21:54:54.689 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:54:54.703 [info] > git status -z -uall [7ms]
2025-06-07 21:54:54.705 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:54:59.725 [info] > git config --get commit.template [7ms]
2025-06-07 21:54:59.726 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:54:59.740 [info] > git status -z -uall [9ms]
2025-06-07 21:54:59.741 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 21:55:04.761 [info] > git config --get commit.template [8ms]
2025-06-07 21:55:04.762 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:55:04.776 [info] > git status -z -uall [6ms]
2025-06-07 21:55:04.778 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 21:55:09.797 [info] > git config --get commit.template [1ms]
2025-06-07 21:55:09.815 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 21:55:09.835 [info] > git status -z -uall [9ms]
2025-06-07 21:55:09.837 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
