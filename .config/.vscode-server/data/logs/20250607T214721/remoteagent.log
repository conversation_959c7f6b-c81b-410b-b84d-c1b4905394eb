2025-06-07 21:47:21.414 [info] 




2025-06-07 21:47:21.424 [info] Extension host agent started.
2025-06-07 21:47:21.638 [info] [<unknown>][7bc5ce9f][ManagementConnection] New connection established.
2025-06-07 21:47:21.639 [info] [<unknown>][f7be278f][ExtensionHostConnection] New connection established.
2025-06-07 21:47:21.711 [info] [<unknown>][f7be278f][ExtensionHostConnection] <447> Launched Extension Host Process.
2025-06-07 21:47:21.909 [info] ComputeTargetPlatform: linux-x64
2025-06-07 21:47:23.411 [info] ComputeTargetPlatform: linux-x64
2025-06-07 21:47:59.196 [info] [<unknown>][7bc5ce9f][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-06-07 21:47:59.239 [info] [<unknown>][f7be278f][ExtensionHostConnection] <447> Extension Host Process exited with code: 0, signal: null.
2025-06-07 21:47:59.239 [info] Cancelling previous shutdown timeout
2025-06-07 21:47:59.239 [info] Last EH closed, waiting before shutting down
2025-06-07 21:49:50.215 [info] [<unknown>][334792a7][ManagementConnection] New connection established.
2025-06-07 21:49:50.216 [info] [<unknown>][f5c6d9f5][ExtensionHostConnection] New connection established.
2025-06-07 21:49:50.220 [info] [<unknown>][f5c6d9f5][ExtensionHostConnection] <1167> Launched Extension Host Process.
2025-06-07 21:50:20.536 [info] Getting Manifest... augment.vscode-augment
2025-06-07 21:50:20.613 [info] Installing extension: augment.vscode-augment {"installPreReleaseVersion":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"darwin-x64"},"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.100.3","date":"2025-06-02T13:30:54.273Z"}}
2025-06-07 21:50:22.222 [info] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 1292ms.
2025-06-07 21:50:22.936 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.472.3: augment.vscode-augment
2025-06-07 21:50:22.971 [info] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.472.3
2025-06-07 21:50:22.982 [info] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-07 21:52:59.241 [info] New EH opened, aborting shutdown
