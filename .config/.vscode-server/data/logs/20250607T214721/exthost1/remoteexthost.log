2025-06-07 21:47:23.254 [info] Extension host with pid 447 started
2025-06-07 21:47:23.254 [error] Error: EEXIST: file already exists, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/vscode.lock'
2025-06-07 21:47:23.254 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/vscode.lock': Could not acquire lock, checking if the file is stale.
2025-06-07 21:47:23.257 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/vscode.lock': The pid 1675 appears to be gone.
2025-06-07 21:47:23.257 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/vscode.lock': Deleting a stale lock.
2025-06-07 21:47:23.269 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/vscode.lock': Lock acquired.
2025-06-07 21:47:23.413 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-07 21:47:23.414 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-07 21:47:23.558 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-07 21:47:23.558 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-07 21:47:23.819 [info] ExtensionService#_doActivateExtension vscode.npm, startup: true, activationEvent: 'workspaceContains:package.json'
2025-06-07 21:47:23.987 [info] Eager extensions activated
2025-06-07 21:47:23.987 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-07 21:47:23.988 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-07 21:47:29.701 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-07 21:47:29.701 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-07 21:47:29.702 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:json'
2025-06-07 21:47:59.199 [info] Extension host terminating: received terminate message from renderer
2025-06-07 21:47:59.200 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/95793ceb282eebc492862cdf341aa813/vscode.lock': Marking the lockfile as scheduled to be released in 6000 ms.
2025-06-07 21:47:59.227 [info] Extension host with pid 447 exiting with code 0
