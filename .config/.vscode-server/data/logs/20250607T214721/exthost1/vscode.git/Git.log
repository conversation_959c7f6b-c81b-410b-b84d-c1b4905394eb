2025-06-07 21:47:23.922 [info] [main] Log level: Info
2025-06-07 21:47:23.922 [info] [main] Validating found git in: "git"
2025-06-07 21:47:23.922 [info] [main] Using git "2.47.2" from "git"
2025-06-07 21:47:23.922 [info] [Model][doInitialScan] Initial repository scan started
2025-06-07 21:47:23.922 [info] > git rev-parse --show-toplevel [37ms]
2025-06-07 21:47:23.922 [info] > git rev-parse --git-dir --git-common-dir [2ms]
2025-06-07 21:47:23.922 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-07 21:47:23.922 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-07 21:47:23.922 [info] > git config --get commit.template [2ms]
2025-06-07 21:47:23.922 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-07 21:47:23.922 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 21:47:23.924 [info] > git status -z -uall [16ms]
2025-06-07 21:47:23.924 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-07 21:47:23.956 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-07 21:47:23.962 [info] > git rev-parse --show-toplevel [40ms]
2025-06-07 21:47:23.966 [info] > git config --get commit.template [17ms]
2025-06-07 21:47:23.967 [info] > git config --get --local branch.main.vscode-merge-base [5ms]
2025-06-07 21:47:23.976 [info] > git rev-parse --show-toplevel [10ms]
2025-06-07 21:47:23.976 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [5ms]
2025-06-07 21:47:23.986 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-07 21:47:24.009 [info] > git rev-parse --show-toplevel [26ms]
2025-06-07 21:47:24.010 [info] > git merge-base refs/heads/main refs/remotes/origin/main [30ms]
2025-06-07 21:47:24.018 [info] > git diff --name-status -z --diff-filter=ADMR 321834a4fd8b2568b5e53a37b787f1381eb5c6c9...refs/remotes/origin/main [4ms]
2025-06-07 21:47:24.026 [info] > git status -z -uall [4ms]
2025-06-07 21:47:24.026 [info] > git rev-parse --show-toplevel [8ms]
2025-06-07 21:47:24.030 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 21:47:24.033 [info] > git rev-parse --show-toplevel [3ms]
2025-06-07 21:47:24.038 [info] > git rev-parse --show-toplevel [2ms]
2025-06-07 21:47:24.042 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 21:47:24.046 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 21:47:24.050 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 21:47:24.055 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 21:47:24.060 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 21:47:24.064 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 21:47:24.068 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 21:47:24.073 [info] > git rev-parse --show-toplevel [1ms]
2025-06-07 21:47:24.076 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-07 21:47:24.461 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-07 21:47:40.611 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-07 21:47:48.448 [info] > git config --get commit.template [5ms]
2025-06-07 21:47:48.449 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 21:47:48.458 [info] > git status -z -uall [5ms]
2025-06-07 21:47:48.459 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 21:47:53.473 [info] > git config --get commit.template [4ms]
2025-06-07 21:47:53.474 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 21:47:53.483 [info] > git status -z -uall [5ms]
2025-06-07 21:47:53.484 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 21:47:58.498 [info] > git config --get commit.template [5ms]
2025-06-07 21:47:58.498 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 21:47:58.506 [info] > git status -z -uall [4ms]
2025-06-07 21:47:58.507 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
