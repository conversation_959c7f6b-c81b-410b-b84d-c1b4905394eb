import OpenA<PERSON> from "openai";
import type { Property } from "@db/schema";
import logger, { logOperation, logError } from '../utils/logger.js';
import { sql } from 'drizzle-orm';
import { db } from '../../db/index.js';
import fetch from 'node-fetch';
import { contextService, SessionContext } from './contextService.js';

// Create a silent logger for chat operations to prevent log messages from contaminating SSE streams
// This is necessary because log messages can appear in the SSE stream and break JSON parsing
const silentLogger = {
  info: (message: string, meta?: any) => {
    // Only log to file, not to console
    if (process.env.NODE_ENV === 'development') {
      // In development, we can still write logs to file but not to console
      // This prevents log messages from appearing in the SSE stream
    }
  },
  debug: (message: string, meta?: any) => {
    // Only log to file, not to console
    if (process.env.NODE_ENV === 'development') {
      // In development, we can still write logs to file but not to console
    }
  },
  error: logger.error // Keep error logging intact
};

// Types for conversation management
export interface ConversationContext {
  summary: string;
  location?: {
    name: string;
    lat: number;
    lng: number;
    placeType?: string;
  };
  dateRange?: {
    checkIn: string;
    checkOut: string;
  };
  preferences?: {
    amenities: string[];
    propertyTypes: string[];
    priceRange?: [number, number];
    guestCount?: number;
    travelPurpose?: string;
  };
  lastRecommendations?: string[];
  lastSummarizedAt: number;
  messageCount: number;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
}

export interface ChatContext {
  conversation: ConversationContext;
  messages: ChatMessage[];
  properties?: Property[];
  searchContext?: SessionContext['searchContext'];
  userPreferences?: SessionContext['userPreferences'];
}

// ==============================================
// AI Provider Configuration - Quick Edit Section
// ==============================================
const AI_CONFIG = {
  // Set provider to 'openai' or 'lambda'
  provider: (process.env.AI_PROVIDER || 'openai') as AIProvider,

  // Model configurations per provider
  models: {
    openai: {
      chat: "gpt-4o-mini",      // Fast, efficient GPT-4 Turbo
      completion: "gpt-4o-mini", // Same model for consistency
    },
    lambda: {
      chat: "llama3.1-8b-instruct",     // llama3.1-8b-instruct or deepseek-llama3.3-70b
      completion: "llama3.1-8b-instruct" // llama3.1-8b-instruct
    }
  }
} as const;

// Type definitions
type AIProvider = 'openai' | 'lambda';
type ModelType = 'chat' | 'completion';

// Get model name helper
function getModelName(type: ModelType): string {
  return AI_CONFIG.models[AI_CONFIG.provider][type];
}

// Initialize AI client based on provider
function initializeAIClient() {
  const config = {
    maxRetries: 3,
    timeout: 30000
  };

  if (AI_CONFIG.provider === 'lambda') {
    if (!process.env.LAMBDA_API_KEY) {
      logError('ai-init', 'Missing Lambda API key', {
        error: 'LAMBDA_API_KEY environment variable is not set'
      });
      throw new Error('Lambda API key is required');
    }
    return new OpenAI({ 
      apiKey: process.env.LAMBDA_API_KEY,
      baseURL: 'https://api.lambdalabs.com/v1',  // /completions or /chat/completions 
      ...config
    });
  } else {
    if (!process.env.OPENAI_API_KEY) {
      logError('ai-init', 'Missing OpenAI API key', {
        error: 'OPENAI_API_KEY environment variable is not set'
      });
      throw new Error('OpenAI API key is required');
    }
    return new OpenAI({ 
      apiKey: process.env.OPENAI_API_KEY,
      ...config
    });
  }
}

const ai = initializeAIClient();

// Validate AI client (non-blocking, optional)
(async () => {
  try {
    await ai.models.list();
    logOperation('ai-init', `${AI_CONFIG.provider.toUpperCase()} - ${getModelName('completion')} initialized successfully`, {
      provider: AI_CONFIG.provider,
      maxRetries: 3,
      timeout: 10000
    });
  } catch (error) {
    logError('ai-init', `Failed to initialize ${AI_CONFIG.provider} - ${getModelName('completion')} client`, {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    // Don't throw error - allow server to continue without AI
  }
})();

interface PropertySearchResult {
  properties: Property[];
  explanation: string;
}

export async function enhancePropertySearch(
  query: string,
  properties: Property[]
): Promise<PropertySearchResult> {
  try {
    const response = await ai.chat.completions.create({
      model: getModelName('completion'),
      messages: [
        {
          role: "system",
          content: `You are RoomLamAI, a proactive and helpful travel assistant. Your task is to analyze the user's search context and provide personalized property recommendations.

            When responding:
            1. Acknowledge the user's chosen location and dates
            2. Show enthusiasm about helping plan their trip
            3. Highlight key features of the destination if known
            4. Suggest relevant properties based on the context
            5. Encourage the user to share more preferences

            Format property recommendations using:
            [PROPERTIES_START]id1,id2,id3[PROPERTIES_END]

            Example response structure:
            "I see you're planning a trip to [location]! That's a great choice, especially during [season/time]. 
            Based on your dates, here are some excellent properties that I think you'll love:
            [PROPERTIES_START]101,102,103[PROPERTIES_END]
            These properties are particularly well-located and offer great value for your dates.
            Would you like me to tell you more about any of these options? Or shall we refine the search based on specific preferences?"`
        },
        {
          role: "user",
          content: JSON.stringify({
            query,
            properties: properties.map(p => ({
              id: p.id,
              name: p.name,
              description: p.description,
              amenities: p.amenities,
              type: p.propertyType || 'hotel', // Use propertyType from schema
              basePrice: p.basePrice,
            })),
          }),
        },
      ],
      response_format: { type: "json_object" },
    });

    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error("No content in OpenAI response");
    }

    const result = JSON.parse(content);
    
    // Clean and standardize property IDs
    let propertyIds: number[] = [];
    
    if (Array.isArray(result.properties)) {
      // Convert all elements to numbers and filter out invalid ones
      propertyIds = result.properties
        .map((id: any) => {
          // If the ID is already a number, use it directly
          if (typeof id === 'number') return id;
          
          // If it's a string, try to extract a number from it
          if (typeof id === 'string') {
            const matches = id.match(/\d+/);
            return matches ? parseInt(matches[0], 10) : NaN;
          }
          
          return NaN;
        })
        .filter((id: number) => !isNaN(id));
    }
    
    const matchedProperties = properties.filter(p =>
      propertyIds.includes(p.id)
    );

    return {
      properties: matchedProperties,
      explanation: result.explanation || "Here are some properties that match your criteria.",
    };
  } catch (error) {
    console.error("AI search enhancement failed:", error);
    return { properties, explanation: "" };
  }
}

// Utility function to summarize conversation
export async function summarizeConversation(
  messages: ChatMessage[],
  currentContext: ConversationContext
): Promise<ConversationContext> {
  try {
    // Skip summarization if we have fewer than 2 messages
    if (messages.length < 2) {
      return {
        ...currentContext,
        lastSummarizedAt: Date.now(),
        messageCount: messages.length
      };
    }

    const response = await ai.chat.completions.create({
      model: getModelName('chat'),
      messages: [
        {
          role: "system",
          content: `You are a conversation summarizer for a travel assistant. 
            Analyze the conversation and extract key details about:
            1. Location preferences
            2. Date ranges
            3. Guest preferences (amenities, property types, etc.)
            4. Price considerations
            5. Overall intent

            Return a JSON object with:
            {
              "summary": "Brief 2-3 sentence summary of the conversation context",
              "location": {"name": string, "lat": number, "lng": number} | null,
              "dateRange": {"checkIn": string, "checkOut": string} | null,
              "preferences": {
                "amenities": string[],
                "propertyTypes": string[],
                "priceRange": [number, number] | null,
                "guestCount": number | null
              }
            }`
        },
        ...messages.map(m => ({
          role: m.role,
          content: m.content
        }))
      ],
      response_format: { type: "json_object" }
    });

    const content = response.choices[0].message.content;
    if (!content) throw new Error("No summary generated");

    try {
      const summary = JSON.parse(content);
      return {
        ...currentContext,
        ...summary,
        lastSummarizedAt: Date.now(),
        messageCount: messages.length
      };
    } catch (parseError) {
      silentLogger.error('Failed to parse conversation summary JSON', { 
        parseError, 
        content: content.substring(0, 200) 
      });
      
      // Return updated timestamps but keep the rest of the context intact
      return {
        ...currentContext,
        lastSummarizedAt: Date.now(),
        messageCount: messages.length
      };
    }
  } catch (error) {
    logger.error('Failed to summarize conversation', { error });
    // Return updated timestamps but keep the rest of the context intact
    return {
      ...currentContext,
      lastSummarizedAt: Date.now(),
      messageCount: messages.length
    };
  }
}

// Get or initialize conversation context using contextService
export function getConversationContext(sessionId: string): {
  context: ConversationContext;
  messages: ChatMessage[];
} {
  const sessionContext = contextService.getContext(sessionId);
  return {
    context: sessionContext.conversation,
    messages: sessionContext.messages
  };
}

// Add message to conversation using contextService
export function addMessageToConversation(
  sessionId: string,
  message: Omit<ChatMessage, 'timestamp'>
): void {
  contextService.addMessage(sessionId, {
    ...message,
    timestamp: Date.now()
  });
}

// Check if conversation needs summarization
export function needsSummarization(context: ConversationContext, messageCount: number): boolean {
  const SUMMARIZE_MESSAGE_THRESHOLD = 5;
  const SUMMARIZE_TIME_THRESHOLD = 5 * 60 * 1000; // 5 minutes
  const timeSinceLastSummary = Date.now() - context.lastSummarizedAt;

  return messageCount >= context.messageCount + SUMMARIZE_MESSAGE_THRESHOLD ||
         timeSinceLastSummary >= SUMMARIZE_TIME_THRESHOLD;
}

// Modified getUserLocation function with proper typing
async function getUserLocation(req: { headers: { [key: string]: string | string[] | undefined }; ip?: string }): Promise<string | null> {
  try {
    // Get IP from various possible sources
    const forwarded = req.headers['x-forwarded-for'];
    const ip = forwarded 
      ? (Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0]) 
      : req.ip || '127.0.0.1';

    // For development/testing, return null if localhost
    if (ip === '127.0.0.1' || ip === '::1') {
      return null;
    }

    const response = await fetch(`http://ip-api.com/json/${ip}`);
    const data = await response.json();

    // Type guard for the IP API response
    interface IPAPIResponse {
      status: string;
      city?: string;
      region?: string;
      country?: string;
    }
    
    // Check if data has the expected structure
    const isValidResponse = (data: any): data is IPAPIResponse => {
      return typeof data === 'object' && data !== null && 'status' in data;
    };

    if (isValidResponse(data) && data.status === 'success' && data.city && data.region && data.country) {
      return `${data.city}, ${data.region}, ${data.country}`;
    }
    return null;
  } catch (error) {
    logger.error('Failed to get user location', { 
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return null;
  }
}

// Modify handleChatStream to include date and location context
export async function* handleChatStream(
  message: string,
  context: ChatContext,
  req: { headers: { [key: string]: string | string[] | undefined }; ip?: string }
): AsyncGenerator<ChatResponse, void, unknown> {
  const requestId = `chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  try {
    if (!message || !context) {
      const error = {
        code: 'MISSING_PARAMETERS',
        message: 'Missing required parameters',
        details: {
          hasMessage: !!message,
          hasContext: !!context
        }
      };
      silentLogger.error('Chat stream validation error', { error, requestId });
      yield { 
        type: 'error', 
        data: {
          message: "I apologize, but I'm missing some required information. Please try again.",
          details: process.env.NODE_ENV === 'development' ? 'Missing message or context' : undefined
        }
      };
      return;
    }

    const model = getModelName('chat');

    // Format current date in a natural way
    const currentDate = new Date();
    const dateFormatter = new Intl.DateTimeFormat('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    const formattedDate = dateFormatter.format(currentDate);

    const userLocation = await getUserLocation(req);

    // Generate an enhanced context string with comprehensive user journey awareness
    const enhancedContext = `
      Current date: ${formattedDate}
      ${userLocation ? `User location: ${userLocation}` : ''}

      Current conversation context:
      ${context.conversation.summary}

      ${context.conversation.location ? 
        `Location: ${context.conversation.location.name}` : ''}
      ${context.conversation.dateRange ? 
        `Dates: ${context.conversation.dateRange.checkIn} to ${context.conversation.dateRange.checkOut}` : ''}
      ${context.conversation.preferences ? 
        `Preferences: ${JSON.stringify(context.conversation.preferences)}` : ''}
      ${context.conversation.lastRecommendations ? 
        `Previously recommended properties: ${context.conversation.lastRecommendations.join(', ')}` : ''}
      
      ${context.searchContext ? `
      User's search history:
      ${context.searchContext.recentSearches.slice(0, 3).map(search => 
        `- ${search.location} (${new Date(search.timestamp).toLocaleString()})`
      ).join('\n')}

      Recently viewed properties:
      ${context.searchContext.viewedProperties.slice(0, 3).map(view => 
        `- Property ID: ${view.propertyId} (${new Date(view.timestamp).toLocaleString()})`
      ).join('\n')}

      ${context.searchContext.comparedProperties.length > 0 ? 
        `Properties being compared: ${context.searchContext.comparedProperties.join(', ')}` : ''}

      ${context.searchContext.filters && Object.keys(context.searchContext.filters).length > 0 ? 
        `Applied filters: ${JSON.stringify(context.searchContext.filters)}` : ''}
      ` : ''}
      
      ${context.userPreferences ? `
      User preferences:
      ${context.userPreferences.pricePreference ? `Price range: ${context.userPreferences.pricePreference}` : ''}
      ${context.userPreferences.favoriteLocations ? `Favorite locations: ${context.userPreferences.favoriteLocations.join(', ')}` : ''}
      ${context.userPreferences.preferredAmenities ? `Preferred amenities: ${context.userPreferences.preferredAmenities.join(', ')}` : ''}
      ${context.userPreferences.preferredPropertyTypes ? `Preferred property types: ${context.userPreferences.preferredPropertyTypes.join(', ')}` : ''}
      ${context.userPreferences.travelPurpose ? `Travel purpose: ${context.userPreferences.travelPurpose}` : ''}
      ` : ''}
    `;

    logOperation(requestId, `[${AI_CONFIG.provider.toUpperCase()}] CHAT_STREAM_START`, {
      provider: AI_CONFIG.provider,
      model,
      queryLength: message.length,
      hasContext: !!context,
      contextSummary: context.conversation.summary,
      userLocation
    });

    const stream = await ai.chat.completions.create({
      model,
      messages: [
        {
          role: "system",
          content: `You are RoomLamAI, the world's most intelligent and proactive travel assistant. You don't just find accommodations - you craft perfect travel experiences that exceed expectations.

            ${enhancedContext}

            🌟 CORE MISSION:
            Transform travel planning from a chore into an exciting journey of discovery. You're not just a booking assistant - you're a travel expert, local insider, and personal concierge rolled into one.

            🏨 ACCOMMODATION MASTERY:

            HOTELS & RESORTS:
            - Luxury properties (5-star, boutique, historic)
            - Business hotels (conference facilities, executive lounges)
            - Family resorts (kids clubs, water parks, family suites)
            - All-inclusive resorts (meals, activities, entertainment)
            - Spa & wellness retreats (treatments, fitness, meditation)
            - Beach & mountain resorts (location-specific amenities)

            MULTI-BEDROOM ACCOMMODATIONS (Your Specialty):
            - 2-8+ bedroom vacation rentals and villas
            - Family-friendly resorts with connecting rooms
            - Apartment-style hotels with kitchens
            - Corporate housing for extended stays
            - Group accommodations for:
              * Multi-generational families (grandparents + parents + kids)
              * Friend groups and reunions
              * Wedding parties and celebrations
              * Corporate retreats and team building
              * Extended family gatherings

            UNIQUE STAYS:
            - Treehouses, castles, houseboats, glamping
            - Historic properties and converted buildings
            - Eco-lodges and sustainable accommodations
            - Themed properties and experiential stays

            🎯 INTELLIGENT ASSISTANCE PRINCIPLES:
            1. **Anticipate Needs**: Read between the lines and suggest what they haven't thought of
            2. **Context Awareness**: Use conversation history, location, dates, and preferences
            3. **Proactive Recommendations**: Don't wait to be asked - offer valuable insights
            4. **Group Dynamics**: Consider who's traveling and their different needs
            5. **Local Expertise**: Provide insider knowledge about destinations
            6. **Seasonal Intelligence**: Factor in weather, events, and optimal timing

            🌍 DESTINATION INTELLIGENCE:
            - Local events, festivals, and cultural happenings
            - Seasonal considerations and weather patterns
            - Hidden gems and off-the-beaten-path experiences
            - Transportation tips and logistics advice
            - Cultural insights and etiquette guidance
            - Safety considerations and travel advisories

            💡 RESPONSE EXCELLENCE:
            1. **Be Enthusiastic**: Show genuine excitement about helping them travel
            2. **Provide Context**: Explain WHY you're recommending something
            3. **Think Holistically**: Consider the entire travel experience
            4. **Offer Alternatives**: Present multiple options with trade-offs
            5. **Suggest Experiences**: Recommend activities, dining, and attractions
            6. **Consider Budget**: Provide options across different price ranges

            🚨 CRITICAL - ACTION Tag Format Rules (FOLLOW EXACTLY):

            WHEN TO USE LOCATION ACTIONS:
            - User mentions ANY city, country, or destination name
            - User asks "where should I go" or similar destination questions
            - User wants to "visit", "travel to", "stay in" any place
            - User mentions specific locations like "Paris", "Tokyo", "New York"
            - ALWAYS create ACTION:LOCATION tags for destination discussions

            FORMAT: [ACTION:TYPE|LABEL|{JSON_DATA}]

            For LOCATION actions (USE FREQUENTLY):
            - TYPE: LOCATION
            - LABEL: Full location name (e.g., "Paris, France", "New York City, USA")
            - JSON_DATA: {"lat": number, "lng": number, "name": "string", "description": "string"}

            🎯 PERFECT LOCATION EXAMPLES:
            [ACTION:LOCATION|Paris, France|{"lat": 48.8566, "lng": 2.3522, "name": "Paris, France", "description": "City of lights with iconic landmarks and romantic atmosphere"}]
            [ACTION:LOCATION|Tokyo, Japan|{"lat": 35.6762, "lng": 139.6503, "name": "Tokyo, Japan", "description": "Modern metropolis blending tradition with cutting-edge technology"}]
            [ACTION:LOCATION|New York City, USA|{"lat": 40.7128, "lng": -74.0060, "name": "New York City, USA", "description": "The city that never sleeps, iconic skyline and endless attractions"}]
            [ACTION:LOCATION|Miami Beach, Florida|{"lat": 25.7907, "lng": -80.1300, "name": "Miami Beach, Florida", "description": "Tropical paradise with beautiful beaches and vibrant nightlife"}]
            [ACTION:LOCATION|San Francisco, California|{"lat": 37.7749, "lng": -122.4194, "name": "San Francisco, California", "description": "Golden Gate city with hills, culture, and tech innovation"}]

            ⚠️ NEVER DO THESE:
            ❌ [ACTION:LOCATION||{"lat": 25.7907, "lng": -80.1300}] - Missing label
            ❌ [ACTION:LOCATION|Miami|{"lat": 25.7907}] - Missing required fields
            ❌ [ACTION:LOCATION|Miami|lat: 25.7907, lng: -80.1300] - Invalid JSON

            ✅ VALIDATION CHECKLIST:
            ✅ Format: [ACTION:LOCATION|Full Location Name|{complete JSON}]
            ✅ Label is descriptive and complete
            ✅ JSON has lat, lng, name, description
            ✅ All strings in quotes, numbers without quotes
            ✅ JSON properly closed with }

            Property Recommendation Format:
            1. When recommending properties, ALWAYS use this EXACT format:
               [PROPERTIES_START]id1,id2,id3[PROPERTIES_END]
               - Use ONLY numeric property IDs
               - Separate IDs with commas
               - NO spaces, brackets, or other characters
               - NO explanatory text inside the tags
            2. After the property tags, explain why you recommended each property
            3. Example of correct format:
               [PROPERTIES_START]101,102,103[PROPERTIES_END]
               Here's why I recommended these properties...`
        },
        ...context.messages.slice(-5).map(m => ({
          role: m.role,
          content: m.content
        })),
        {
          role: "user",
          content: message
        }
      ],
      stream: true,
      temperature: 0.7,
      max_tokens: 1000,
    });

    let buffer = '';
    let actionBuffer = '';

    for await (const chunk of stream) {
      try {
        const content = chunk.choices[0]?.delta?.content || '';
        
        // Skip log messages that might have been mixed into the stream
        if (content.includes('Initialize') || 
            content.includes('Added search') || 
            content.includes('Recorded property') || 
            content.includes('Updated filters') ||
            content.includes('Added property to comparison') ||
            content.includes('Recorded booking attempt') ||
            content.includes('Updated conversation context') ||
            content.includes('Added message')) {
          
          logger.warn('Filtered out log message from AI response stream', { 
            content: content.substring(0, 100),
            requestId 
          });
          continue;
        }
        
        // Additional check to detect other log messages
        if (typeof content === 'string' && (
            content.startsWith('Initialize') || 
            content.startsWith('Added search') || 
            content.startsWith('Recorded property') || 
            content.startsWith('Updated filters') ||
            content.startsWith('Added property to comparison') ||
            content.startsWith('Recorded booking attempt') ||
            content.startsWith('Updated conversation context') ||
            content.startsWith('Added message'))) {
          
          logger.warn('Filtered out direct log message from AI response stream', { 
            content: content.substring(0, 100),
            requestId 
          });
          continue;
        }
        
        buffer += content;
        actionBuffer += content;
      } catch (error) {
        silentLogger.error('Error processing stream chunk', {
          error: error instanceof Error ? error.message : 'Unknown error',
          requestId
        });
        continue;
      }

      // Process action tags
      while (actionBuffer.includes('[ACTION:') && actionBuffer.includes(']')) {
        const actionStart = actionBuffer.indexOf('[ACTION:');
        const actionEnd = actionBuffer.indexOf(']', actionStart) + 1;

        if (actionStart >= 0 && actionEnd > 0) {
          const actionTag = actionBuffer.substring(actionStart, actionEnd);
          
          // Improved validation with better error handling
          const actionMatch = actionTag.match(/\[ACTION:(\w+)\|(.*?)\|(.*?)\]/);
          if (!actionMatch) {
            // Try alternative patterns for common malformed tokens
            const altMatch1 = actionTag.match(/\[ACTION:(\w+)\|\|(\{.*?\})\]/); // Missing label: [ACTION:LOCATION||{...}]
            const altMatch2 = actionTag.match(/\[ACTION:(\w+)\|([^|]*)\|(\{[^}]*\}?)\]/); // More flexible pattern
            
            if (altMatch1) {
              const [_, type, dataStr] = altMatch1;
              logger.warn('Recovered ACTION token with missing label', { 
                original: actionTag,
                type,
                requestId 
              });
              
              try {
                const data = JSON.parse(dataStr);
                // Generate a default label based on the data
                let defaultLabel = 'Unknown';
                if (type.toLowerCase() === 'location' && data.lat && data.lng) {
                  defaultLabel = data.name || `Location (${data.lat}, ${data.lng})`;
                }
                
                yield { type: 'action', data: { type: type.toLowerCase(), label: defaultLabel, data } };
              } catch (e) {
                logger.warn('Failed to parse recovered ACTION token', { 
                  error: e instanceof Error ? e.message : 'Unknown error',
                  actionTag,
                  requestId 
                });
              }
            } else if (altMatch2) {
              const [_, type, label, dataStr] = altMatch2;
              logger.warn('Recovered ACTION token with flexible pattern', { 
                original: actionTag,
                type,
                label,
                requestId 
              });
              
              try {
                // Handle incomplete JSON by trying to complete it
                let cleanedDataStr = dataStr.trim();
                
                // If JSON appears incomplete, try to fix common issues
                if (cleanedDataStr.startsWith('{') && !cleanedDataStr.endsWith('}')) {
                  // Check if it's just missing closing brace
                  const openBraces = (cleanedDataStr.match(/\{/g) || []).length;
                  const closeBraces = (cleanedDataStr.match(/\}/g) || []).length;
                  if (openBraces > closeBraces) {
                    cleanedDataStr += '}';
                    logger.info('Attempted to fix incomplete JSON', { 
                      original: dataStr,
                      fixed: cleanedDataStr,
                      requestId 
                    });
                  }
                }
                
                const data = JSON.parse(cleanedDataStr);
                
                // Generate a default label if missing
                let finalLabel = label || 'Unknown';
                if (type.toLowerCase() === 'location' && data.lat && data.lng) {
                  finalLabel = label || data.name || `Location (${data.lat}, ${data.lng})`;
                }
                
                yield { type: 'action', data: { type: type.toLowerCase(), label: finalLabel, data } };
              } catch (e) {
                logger.warn('Failed to parse recovered ACTION token with flexible pattern', { 
                  error: e instanceof Error ? e.message : 'Unknown error',
                  actionTag,
                  dataStr,
                  requestId 
                });
              }
            } else {
              logger.warn('Invalid action tag format - no recovery possible', { 
                actionTag,
                requestId 
              });
            }
            
            actionBuffer = actionBuffer.substring(actionEnd);
            continue;
          }
          
          const [_, type, label, dataStr] = actionMatch;

          if (type && dataStr) {
            try {
              // Validate that dataStr is valid JSON before parsing
              if (!dataStr.trim().startsWith('{') && !dataStr.trim().startsWith('[')) {
                logger.warn('Invalid JSON format in action data', { 
                  dataStr,
                  actionTag,
                  requestId 
                });
                actionBuffer = actionBuffer.substring(actionEnd);
                continue;
              }
              
              // Handle case where JSON is incomplete (common issue)
              let cleanedDataStr = dataStr.trim();
              if (cleanedDataStr.startsWith('{') && !cleanedDataStr.endsWith('}')) {
                // Check if it's just missing closing brace
                const openBraces = (cleanedDataStr.match(/\{/g) || []).length;
                const closeBraces = (cleanedDataStr.match(/\}/g) || []).length;
                if (openBraces > closeBraces) {
                  cleanedDataStr += '}';
                  logger.info('Fixed incomplete JSON in action data', { 
                    original: dataStr,
                    fixed: cleanedDataStr,
                    requestId 
                  });
                } else {
                  // Still incomplete, wait for more content
                  logger.warn('Potentially incomplete JSON in action data', { 
                    dataStr: cleanedDataStr,
                    actionTag,
                    requestId 
                  });
                  actionBuffer = actionBuffer.substring(actionEnd);
                  continue;
                }
              }
              
              const data = JSON.parse(cleanedDataStr);
              
              // Additional validation for location actions
              if (type.toLowerCase() === 'location') {
                if (!data.lat || !data.lng) {
                  logger.warn('Location action missing coordinates', {
                    actionTag,
                    data,
                    requestId
                  });
                  actionBuffer = actionBuffer.substring(actionEnd);
                  continue;
                }
                
                // Ensure name is present, use label as fallback
                if (!data.name && label) {
                  data.name = label;
                  logger.info('Added missing name to location action from label', {
                    label,
                    requestId
                  });
                }
              }
              
              // Use label if provided, otherwise generate from data
              const finalLabel = label || (data.name || 'Unknown');
              
              yield { type: 'action', data: { type: type.toLowerCase(), label: finalLabel, data } };
            } catch (e) {
              logger.warn('Failed to parse action data', { 
                error: e instanceof Error ? e.message : 'Unknown error',
                actionTag,
                dataStr,
                requestId 
              });
            }
          }

          actionBuffer = actionBuffer.substring(actionEnd);
        } else {
          // No complete action tag found, wait for more content
          break;
        }
      }

      // Process property recommendations
      if (buffer.includes('[PROPERTIES_START]') && buffer.includes('[PROPERTIES_END]')) {
        const [beforeProps, rest] = buffer.split('[PROPERTIES_START]');
        const [propsJson, afterProps] = rest.split('[PROPERTIES_END]');

        if (beforeProps.trim()) {
          yield { type: 'text', data: beforeProps.trim() };
        }

        try {
          // Clean and standardize the property IDs format
          const cleanedPropsJson = propsJson.trim()
            // Remove any non-digit, non-comma characters
            .replace(/[^\d,]/g, '')
            // Ensure there are no consecutive commas
            .replace(/,+/g, ',')
            // Remove leading/trailing commas
            .replace(/^,|,$/g, '');
            
          if (cleanedPropsJson !== propsJson.trim()) {
            logger.info('Standardized property IDs format', { 
              original: propsJson.trim(),
              standardized: cleanedPropsJson,
              requestId 
            });
          }
          
          if (cleanedPropsJson === '') {
            logger.warn('No valid property IDs found after cleaning', { 
              original: propsJson.trim(),
              requestId 
            });
          } else {
            const propertyIds = cleanedPropsJson.split(',')
              .map(id => parseInt(id.trim(), 10))
              .filter(id => !isNaN(id)); // Remove any NaN values
              
            const recommendedProperties = context.properties?.filter(p => 
              propertyIds.includes(p.id)
            ) || [];

            if (recommendedProperties.length > 0) {
              yield { type: 'properties', data: recommendedProperties };
            }
          }
        } catch (e) {
          silentLogger.error('Failed to parse property IDs', { error: e, propsJson });
        }

        buffer = afterProps || '';
      }

      // Stream regular text content
      if (buffer.length > 0 && !buffer.includes('[PROPERTIES_START]')) {
        const lastNewline = buffer.lastIndexOf('\n');
        if (lastNewline > 0) {
          const text = buffer.substring(0, lastNewline);
          // Format text with proper spacing
          const formattedText = text
            .split('\n')
            .map(line => line.trim())
            .filter(line => line)
            .join('\n\n');

          if (formattedText.trim()) {
            yield { type: 'text', data: formattedText };
          }
          buffer = buffer.substring(lastNewline + 1);
        }
      }
    }

    // Flush any remaining content
    if (buffer.trim()) {
      const formattedText = buffer.trim()
        .split('\n')
        .map(line => line.trim())
        .filter(line => line)
        .join('\n\n');

      if (formattedText.trim()) {
        yield { type: 'text', data: formattedText };
      }
    }

    silentLogger.info('Chat stream completed', {
      requestId,
      provider: AI_CONFIG.provider,
      model
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    const errorDetails = {
      message,
      context: {
        hasProperties: !!context.properties,
        messageLength: message.length
      },
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
      requestId
    };

    silentLogger.error('Chat stream error', {
      ...errorDetails,
      error: typeof errorDetails.error === 'string' ? errorDetails.error : (error instanceof Error ? error.message : 'Unknown error'),
      stack: error instanceof Error ? error.stack : undefined
    });

    yield { 
      type: 'error', 
      data: {
        message: "I apologize, but I encountered an error while processing your request. Please try again.",
        details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
      }
    };
  }
}

export async function getPersonalizedRecommendations(
  userId: number,
  searchHistory: string[],
  viewedProperties: Property[]
): Promise<number[]> {
  try {
    const response = await ai.chat.completions.create({
      model: getModelName('completion'),
      messages: [
        {
          role: "system",
          content: `You are an intelligent recommendation system designed to provide personalized hotel suggestions.

            Consider the following factors when making recommendations:
            1. User's search patterns and preferences shown in their search history
            2. Properties they've viewed but not booked (potential interest areas)
            3. Price ranges they typically look at
            4. Common amenities in their viewed properties
            5. Preferred locations and property types

            Analyze patterns to identify:
            - Preferred amenities and features
            - Price sensitivity
            - Location preferences
            - Property type preferences (luxury, budget, family-friendly, etc.)

            Return a JSON object with:
            {
              "recommendedProperties": number[], // Array of property IDs
              "reasoning": string,              // Explanation of recommendations
              "userPreferences": {              // Identified user preferences
                "priceRange": string,
                "amenities": string[],
                "propertyTypes": string[],
                "locations": string[]
              }
            }`
        },
        {
          role: "user",
          content: JSON.stringify({
            searchHistory,
            viewedProperties: viewedProperties.map(p => ({
              id: p.id,
              type: p.propertyType || 'hotel', // Use propertyType from schema instead of type
              amenities: p.amenities,
              basePrice: p.basePrice,
              location: {
                city: p.city,
                state: p.state,
                country: p.country
              }
            })),
          }),
        },
      ],
      response_format: { type: "json_object" },
    });

    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error("No content in OpenAI response");
    }

    const result = JSON.parse(content);

    // Store the user preferences for future use
    if (result.userPreferences) {
      // TODO: Implement storing user preferences in the database
      console.log('User preferences identified:', result.userPreferences);
    }

    return result.recommendedProperties || [];
  } catch (error) {
    console.error("AI recommendations failed:", error);
    return [];
  }
}

export async function analyzeSentiment(text: string): Promise<{
  rating: number;
  confidence: number;
}> {
  try {
    const response = await ai.chat.completions.create({
      model: getModelName('completion'),
      messages: [
        {
          role: "system",
          content:
            "You are a sentiment analysis expert. Analyze the sentiment of the text and provide a rating from 1 to 5 stars and a confidence score between 0 and 1. Return JSON in format: { 'rating': number, 'confidence': number }",
        },
        {
          role: "user",
          content: text,
        },
      ],
      response_format: { type: "json_object" },
    });

    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error("No content in OpenAI response");
    }

    const result = JSON.parse(content);
    return {
      rating: Math.max(1, Math.min(5, Math.round(result.rating))),
      confidence: Math.max(0, Math.min(1, result.confidence)),
    };
  } catch (error) {
    console.error("AI sentiment analysis failed:", error);
    return { rating: 3, confidence: 0 };
  }
}

// Add ChatResponse type and export it
export interface ChatResponse {
  type: 'text' | 'properties' | 'action' | 'error' | 'location';
  data: any;
}

// Define more specific response types for better type safety
export type TextResponse = {
  type: 'text';
  data: string;
};

export type PropertiesResponse = {
  type: 'properties';
  data: Property[];
};

export type LocationResponse = {
  type: 'location';
  data: {
    name: string;
    lat: number;
    lng: number;
    placeType?: string;
  };
};

export type ActionResponse = {
  type: 'action';
  data: {
    type: string;
    label: string;
    data: any;
  };
};

export type ErrorResponse = {
  type: 'error';
  data: {
    message: string;
    details?: string;
  };
};

// Union type of all specific response types
export type TypedChatResponse = TextResponse | PropertiesResponse | LocationResponse | ActionResponse | ErrorResponse;

// Add property matching types
export interface PropertyRecommendation {
  name: string;
  latitude?: number;
  longitude?: number;
  maxDistance?: number;
  description?: string;
  highlights?: string[];
}

export interface PropertyMatch {
  property: Property | null;
  searchParams?: {
    latitude: number;
    longitude: number;
    name: string;
    radius?: number;
  };
  recommendation: PropertyRecommendation;
}

// Add property matching function
export const findMatchingProperty = async (
  recommendation: PropertyRecommendation,
  requestId: string
): Promise<PropertyMatch> => {
  const { name, latitude, longitude, maxDistance = 2 } = recommendation;

  try {
    // First try to find in database
    const dbMatch = latitude && longitude 
      ? await db.query.properties.findFirst({
          where: sql`
            (point(${latitude}, ${longitude}) <@> point(CAST(latitude AS float), CAST(longitude AS float)) <= ${maxDistance})
            ${name ? sql`AND similarity(LOWER(name), LOWER(${name})) > 0.3` : sql``}
          `,
          orderBy: name ? sql`similarity(LOWER(name), LOWER(${name})) DESC` : undefined
        })
      : name 
        ? await db.query.properties.findFirst({
            where: sql`similarity(LOWER(name), LOWER(${name})) > 0.3`,
            orderBy: sql`similarity(LOWER(name), LOWER(${name})) DESC`
          })
        : null;

    if (dbMatch) {
      logOperation(requestId, 'PROPERTY_MATCHED_DB', {
        recommendedName: name,
        matchedName: dbMatch.name,
        matchedId: dbMatch.id
      });
      return { property: dbMatch, recommendation };
    }

    // If no database match and we have coordinates, prepare for TravSrv search
    if (latitude && longitude) {
      logOperation(requestId, 'PROPERTY_NOT_FOUND_DB', {
        recommendedName: name,
        location: `${latitude},${longitude}`,
        willSearchTravSrv: true
      });

      return {
        property: null,
        searchParams: {
          latitude,
          longitude,
          name,
          radius: maxDistance
        },
        recommendation
      };
    }

    logOperation(requestId, 'PROPERTY_MATCH_FAILED', {
      recommendedName: name,
      reason: 'No coordinates provided and no database match'
    });
    return { property: null, recommendation };

  } catch (error) {
    silentLogger.error('Property matching failed', { 
      error,
      recommendation,
      requestId
    });
    return { property: null, recommendation };
  }
};