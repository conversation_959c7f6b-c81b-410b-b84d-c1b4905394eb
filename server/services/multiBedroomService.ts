import { searchTravSrvProperties, getPropertyAvailability } from './travsrv.js';
import { geocodeLocation } from './geocodingService.js';
import { db } from '../../db/index.js';
import { properties } from '../../db/schema.js';
import { eq, and, gte, lte, sql } from 'drizzle-orm';
import logger from '../utils/logger.js';

export interface MultiBedroomSearchParams {
  location: string;
  checkIn: string;
  checkOut: string;
  adults: number;
  children?: number;
  bedrooms: number;
  minBedrooms?: number;
  maxBedrooms?: number;
  propertyTypes?: string[];
  amenities?: string[];
  priceRange?: {
    min?: number;
    max?: number;
  };
  groupType?: 'family' | 'friends' | 'corporate' | 'wedding' | 'reunion' | 'multi_generational';
  specialRequests?: string[];
}

export interface MultiBedroomProperty {
  id: number;
  name: string;
  description: string;
  location: {
    address: string;
    city: string;
    state: string;
    country: string;
    coordinates: { lat: number; lng: number };
  };
  accommodations: {
    bedrooms: number;
    bathrooms: number;
    maxOccupancy: number;
    bedConfiguration: string[];
    livingSpaces: string[];
  };
  amenities: {
    kitchen: boolean;
    laundry: boolean;
    parking: boolean;
    wifi: boolean;
    pool: boolean;
    hotTub: boolean;
    bbq: boolean;
    gameRoom: boolean;
    familyFriendly: string[];
    accessibility: string[];
  };
  pricing: {
    basePrice: number;
    currency: string;
    totalPrice: number;
    pricePerNight: number;
    fees: Array<{
      type: string;
      amount: number;
      description: string;
    }>;
  };
  images: string[];
  rating: number;
  reviewCount: number;
  propertyType: string;
  highlights: string[];
  nearbyAttractions: Array<{
    name: string;
    distance: string;
    type: string;
  }>;
  suitabilityScore: number;
  recommendations: {
    bestFor: string[];
    considerations: string[];
    localTips: string[];
  };
}

/**
 * Search for multi-bedroom accommodations with intelligent filtering
 */
export async function searchMultiBedroomAccommodations(
  params: MultiBedroomSearchParams
): Promise<MultiBedroomProperty[]> {
  try {
    logger.info('Starting multi-bedroom accommodation search', {
      location: params.location,
      bedrooms: params.bedrooms,
      groupType: params.groupType,
      checkIn: params.checkIn,
      checkOut: params.checkOut
    });

    // Geocode the location
    const locationData = await geocodeLocation(params.location);
    if (!locationData) {
      throw new Error(`Unable to find location: ${params.location}`);
    }

    // Search for properties using the travel service
    const baseProperties = await searchTravSrvProperties({
      latitude: locationData.lat,
      longitude: locationData.lng,
      inDate: params.checkIn,
      outDate: params.checkOut,
      adults: params.adults,
      children: params.children || 0,
      rooms: Math.max(params.bedrooms, 1),
      radius: 25 // Wider search for vacation rentals
    });

    // Enhance properties with multi-bedroom specific data
    const enhancedProperties = await Promise.all(
      baseProperties.map(property => enhancePropertyForMultiBedroom(property, params))
    );

    // Filter and score properties based on multi-bedroom criteria
    const filteredProperties = enhancedProperties
      .filter(property => property !== null)
      .filter(property => meetsBedrooomRequirements(property!, params))
      .map(property => calculateSuitabilityScore(property!, params))
      .sort((a, b) => b.suitabilityScore - a.suitabilityScore);

    logger.info('Multi-bedroom search completed', {
      totalFound: baseProperties.length,
      afterFiltering: filteredProperties.length,
      location: params.location
    });

    return filteredProperties;

  } catch (error) {
    logger.error('Multi-bedroom search failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      params
    });
    throw error;
  }
}

/**
 * Enhance a basic property with multi-bedroom specific information
 */
async function enhancePropertyForMultiBedroom(
  property: any,
  params: MultiBedroomSearchParams
): Promise<MultiBedroomProperty | null> {
  try {
    // Get detailed availability and room information
    const availability = await getPropertyAvailability(
      property.externalId || property.id.toString(),
      {
        inDate: params.checkIn,
        outDate: params.checkOut,
        adults: params.adults,
        children: params.children || 0,
        rooms: params.bedrooms
      }
    );

    // Analyze property type and amenities to determine bedroom count
    const bedroomInfo = analyzeBedroomConfiguration(property, availability);
    
    if (!bedroomInfo || bedroomInfo.bedrooms < params.minBedrooms || 0) {
      return null;
    }

    // Generate intelligent recommendations based on group type
    const recommendations = generateGroupRecommendations(property, params.groupType);

    // Find nearby attractions
    const nearbyAttractions = await findNearbyAttractions(
      property.latitude,
      property.longitude,
      params.groupType
    );

    const enhancedProperty: MultiBedroomProperty = {
      id: property.id,
      name: property.name,
      description: property.description,
      location: {
        address: property.address,
        city: property.city,
        state: property.state,
        country: property.country,
        coordinates: {
          lat: property.latitude,
          lng: property.longitude
        }
      },
      accommodations: {
        bedrooms: bedroomInfo.bedrooms,
        bathrooms: bedroomInfo.bathrooms,
        maxOccupancy: bedroomInfo.maxOccupancy,
        bedConfiguration: bedroomInfo.bedConfiguration,
        livingSpaces: bedroomInfo.livingSpaces
      },
      amenities: analyzeAmenities(property),
      pricing: calculatePricing(property, availability, params),
      images: Array.isArray(property.images) ? property.images : [],
      rating: property.rating || 0,
      reviewCount: property.reviewCount || 0,
      propertyType: property.propertyType || 'vacation_rental',
      highlights: generateHighlights(property, bedroomInfo, params),
      nearbyAttractions,
      suitabilityScore: 0, // Will be calculated later
      recommendations
    };

    return enhancedProperty;

  } catch (error) {
    logger.error('Failed to enhance property for multi-bedroom', {
      propertyId: property.id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return null;
  }
}

/**
 * Analyze property to determine bedroom configuration
 */
function analyzeBedroomConfiguration(property: any, availability: any) {
  // Extract bedroom information from property description and amenities
  const description = (property.description || '').toLowerCase();
  const amenities = property.amenities || [];
  
  // Look for bedroom indicators in description
  const bedroomMatches = description.match(/(\d+)\s*bedroom/g);
  const bathroomMatches = description.match(/(\d+)\s*bathroom/g);
  
  let bedrooms = 1;
  let bathrooms = 1;
  
  if (bedroomMatches) {
    bedrooms = Math.max(...bedroomMatches.map(match => 
      parseInt(match.match(/(\d+)/)?.[1] || '1')
    ));
  }
  
  if (bathroomMatches) {
    bathrooms = Math.max(...bathroomMatches.map(match => 
      parseInt(match.match(/(\d+)/)?.[1] || '1')
    ));
  }

  // Analyze room types from availability data
  const bedConfiguration: string[] = [];
  const livingSpaces: string[] = [];
  
  if (availability?.ratePlans) {
    Object.values(availability.ratePlans).forEach((ratePlan: any) => {
      Object.values(ratePlan.rooms || {}).forEach((room: any) => {
        if (room.description) {
          const roomDesc = room.description.toLowerCase();
          if (roomDesc.includes('king')) bedConfiguration.push('King Bed');
          if (roomDesc.includes('queen')) bedConfiguration.push('Queen Bed');
          if (roomDesc.includes('twin')) bedConfiguration.push('Twin Beds');
          if (roomDesc.includes('sofa')) livingSpaces.push('Sofa Bed');
          if (roomDesc.includes('living')) livingSpaces.push('Living Room');
          if (roomDesc.includes('kitchen')) livingSpaces.push('Full Kitchen');
        }
      });
    });
  }

  // Default configurations based on property type
  if (bedConfiguration.length === 0) {
    for (let i = 0; i < bedrooms; i++) {
      bedConfiguration.push(i === 0 ? 'King Bed' : 'Queen Bed');
    }
  }

  if (livingSpaces.length === 0) {
    livingSpaces.push('Living Room');
    if (amenities.some((a: string) => a.toLowerCase().includes('kitchen'))) {
      livingSpaces.push('Full Kitchen');
    }
  }

  return {
    bedrooms,
    bathrooms,
    maxOccupancy: bedrooms * 2 + livingSpaces.filter(s => s.includes('Sofa')).length * 2,
    bedConfiguration,
    livingSpaces
  };
}

/**
 * Analyze property amenities for multi-bedroom suitability
 */
function analyzeAmenities(property: any) {
  const amenities = property.amenities || [];
  const description = (property.description || '').toLowerCase();
  
  return {
    kitchen: amenities.some((a: string) => 
      a.toLowerCase().includes('kitchen') || a.toLowerCase().includes('kitchenette')
    ) || description.includes('kitchen'),
    laundry: amenities.some((a: string) => 
      a.toLowerCase().includes('laundry') || a.toLowerCase().includes('washer')
    ) || description.includes('laundry'),
    parking: amenities.some((a: string) => 
      a.toLowerCase().includes('parking') || a.toLowerCase().includes('garage')
    ) || description.includes('parking'),
    wifi: amenities.some((a: string) => 
      a.toLowerCase().includes('wifi') || a.toLowerCase().includes('internet')
    ) || description.includes('wifi'),
    pool: amenities.some((a: string) => 
      a.toLowerCase().includes('pool')
    ) || description.includes('pool'),
    hotTub: amenities.some((a: string) => 
      a.toLowerCase().includes('hot tub') || a.toLowerCase().includes('jacuzzi')
    ) || description.includes('hot tub'),
    bbq: amenities.some((a: string) => 
      a.toLowerCase().includes('bbq') || a.toLowerCase().includes('grill')
    ) || description.includes('grill'),
    gameRoom: amenities.some((a: string) => 
      a.toLowerCase().includes('game') || a.toLowerCase().includes('recreation')
    ) || description.includes('game room'),
    familyFriendly: amenities.filter((a: string) => 
      a.toLowerCase().includes('family') || 
      a.toLowerCase().includes('child') || 
      a.toLowerCase().includes('kid')
    ),
    accessibility: amenities.filter((a: string) => 
      a.toLowerCase().includes('accessible') || 
      a.toLowerCase().includes('wheelchair') || 
      a.toLowerCase().includes('mobility')
    )
  };
}

/**
 * Calculate pricing for multi-bedroom stay
 */
function calculatePricing(property: any, availability: any, params: MultiBedroomSearchParams) {
  const basePrice = property.basePrice || 0;
  const nights = Math.ceil(
    (new Date(params.checkOut).getTime() - new Date(params.checkIn).getTime()) / (1000 * 60 * 60 * 24)
  );
  
  let totalPrice = basePrice * nights;
  const fees: Array<{ type: string; amount: number; description: string }> = [];
  
  // Add typical vacation rental fees
  if (property.propertyType === 'vacation_rental' || property.propertyType === 'apartment') {
    const cleaningFee = Math.round(basePrice * 0.15);
    fees.push({
      type: 'cleaning',
      amount: cleaningFee,
      description: 'One-time cleaning fee'
    });
    totalPrice += cleaningFee;
    
    const serviceFee = Math.round(totalPrice * 0.12);
    fees.push({
      type: 'service',
      amount: serviceFee,
      description: 'Service fee'
    });
    totalPrice += serviceFee;
  }
  
  return {
    basePrice,
    currency: property.currency || 'USD',
    totalPrice,
    pricePerNight: basePrice,
    fees
  };
}

/**
 * Check if property meets bedroom requirements
 */
function meetsBedrooomRequirements(property: MultiBedroomProperty, params: MultiBedroomSearchParams): boolean {
  const bedrooms = property.accommodations.bedrooms;
  
  if (bedrooms < params.bedrooms) return false;
  if (params.minBedrooms && bedrooms < params.minBedrooms) return false;
  if (params.maxBedrooms && bedrooms > params.maxBedrooms) return false;
  
  // Check occupancy
  const totalGuests = params.adults + (params.children || 0);
  if (property.accommodations.maxOccupancy < totalGuests) return false;
  
  return true;
}

/**
 * Calculate suitability score based on group type and requirements
 */
function calculateSuitabilityScore(property: MultiBedroomProperty, params: MultiBedroomSearchParams): MultiBedroomProperty {
  let score = 50; // Base score
  
  // Bedroom match bonus
  if (property.accommodations.bedrooms === params.bedrooms) {
    score += 20;
  } else if (property.accommodations.bedrooms > params.bedrooms) {
    score += 10;
  }
  
  // Group type specific scoring
  switch (params.groupType) {
    case 'family':
      if (property.amenities.familyFriendly.length > 0) score += 15;
      if (property.amenities.kitchen) score += 10;
      if (property.amenities.laundry) score += 10;
      if (property.amenities.pool) score += 10;
      break;
      
    case 'friends':
      if (property.amenities.gameRoom) score += 15;
      if (property.amenities.pool) score += 10;
      if (property.amenities.bbq) score += 10;
      if (property.amenities.hotTub) score += 10;
      break;
      
    case 'corporate':
      if (property.amenities.wifi) score += 15;
      if (property.amenities.parking) score += 10;
      if (property.location.city.toLowerCase().includes('downtown')) score += 10;
      break;
      
    case 'multi_generational':
      if (property.accommodations.bedrooms >= 3) score += 15;
      if (property.amenities.accessibility.length > 0) score += 15;
      if (property.amenities.kitchen) score += 10;
      if (property.amenities.laundry) score += 10;
      break;
  }
  
  // Rating bonus
  score += (property.rating || 0) * 5;
  
  property.suitabilityScore = Math.min(100, Math.max(0, score));
  return property;
}

/**
 * Generate group-specific recommendations
 */
function generateGroupRecommendations(property: any, groupType?: string) {
  const bestFor: string[] = [];
  const considerations: string[] = [];
  const localTips: string[] = [];
  
  switch (groupType) {
    case 'family':
      bestFor.push('Family vacations with children');
      bestFor.push('Multi-generational trips');
      considerations.push('Check for child-safe amenities');
      considerations.push('Verify kitchen facilities for meal prep');
      localTips.push('Look for nearby family attractions');
      break;
      
    case 'friends':
      bestFor.push('Group getaways and reunions');
      bestFor.push('Celebration trips');
      considerations.push('Ensure adequate common spaces');
      considerations.push('Check noise policies');
      localTips.push('Explore local nightlife and activities');
      break;
      
    case 'corporate':
      bestFor.push('Team retreats and meetings');
      bestFor.push('Extended business stays');
      considerations.push('Verify reliable WiFi and workspace');
      considerations.push('Check proximity to business districts');
      localTips.push('Consider transportation to meeting venues');
      break;
  }
  
  return { bestFor, considerations, localTips };
}

/**
 * Find nearby attractions based on group type
 */
async function findNearbyAttractions(lat: number, lng: number, groupType?: string) {
  // This would integrate with the geocoding service to find nearby places
  // For now, return mock data based on group type
  const attractions = [];
  
  switch (groupType) {
    case 'family':
      attractions.push(
        { name: 'Family Theme Park', distance: '2.5 miles', type: 'entertainment' },
        { name: 'Children\'s Museum', distance: '1.8 miles', type: 'educational' },
        { name: 'Beach Access', distance: '0.5 miles', type: 'recreation' }
      );
      break;
      
    case 'friends':
      attractions.push(
        { name: 'Downtown Entertainment District', distance: '3.2 miles', type: 'nightlife' },
        { name: 'Adventure Sports Center', distance: '4.1 miles', type: 'recreation' },
        { name: 'Local Brewery Tour', distance: '2.8 miles', type: 'dining' }
      );
      break;
      
    default:
      attractions.push(
        { name: 'City Center', distance: '2.0 miles', type: 'shopping' },
        { name: 'Local Restaurant District', distance: '1.5 miles', type: 'dining' },
        { name: 'Cultural Attractions', distance: '3.0 miles', type: 'cultural' }
      );
  }
  
  return attractions;
}

/**
 * Generate property highlights for multi-bedroom stays
 */
function generateHighlights(property: any, bedroomInfo: any, params: MultiBedroomSearchParams): string[] {
  const highlights: string[] = [];
  
  highlights.push(`${bedroomInfo.bedrooms} bedrooms, ${bedroomInfo.bathrooms} bathrooms`);
  highlights.push(`Sleeps up to ${bedroomInfo.maxOccupancy} guests`);
  
  if (bedroomInfo.livingSpaces.includes('Full Kitchen')) {
    highlights.push('Full kitchen for group meals');
  }
  
  if (property.amenities?.some((a: string) => a.toLowerCase().includes('pool'))) {
    highlights.push('Private or shared pool access');
  }
  
  if (property.amenities?.some((a: string) => a.toLowerCase().includes('parking'))) {
    highlights.push('Parking available');
  }
  
  if (property.rating && property.rating >= 4.5) {
    highlights.push('Highly rated by guests');
  }
  
  return highlights;
}
