import Stripe from 'stripe';
import { db } from '../../db/index.js';
import { reservations, users } from '../../db/schema.js';
import { eq } from 'drizzle-orm';
import logger from '../utils/logger.js';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

export interface PaymentIntentData {
  amount: number;
  currency: string;
  reservationId: number;
  userId: number;
  propertyId: number;
  metadata?: Record<string, string>;
}

export interface BookingConfirmation {
  reservationId: number;
  confirmationCode: string;
  paymentIntentId: string;
  status: 'confirmed' | 'pending' | 'failed';
  totalAmount: number;
  currency: string;
}

/**
 * Create a payment intent for a reservation
 */
export async function createPaymentIntent(data: PaymentIntentData): Promise<Stripe.PaymentIntent> {
  try {
    // Get user and reservation details
    const [user, reservation] = await Promise.all([
      db.query.users.findFirst({ where: eq(users.id, data.userId) }),
      db.query.reservations.findFirst({ where: eq(reservations.id, data.reservationId) })
    ]);

    if (!user || !reservation) {
      throw new Error('User or reservation not found');
    }

    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(data.amount * 100), // Convert to cents
      currency: data.currency.toLowerCase(),
      customer: user.email, // You might want to create Stripe customers
      metadata: {
        reservationId: data.reservationId.toString(),
        userId: data.userId.toString(),
        propertyId: data.propertyId.toString(),
        ...data.metadata
      },
      description: `Hotel reservation for ${user.email}`,
    });

    // Update reservation with payment intent ID
    await db
      .update(reservations)
      .set({ 
        paymentIntentId: paymentIntent.id,
        updatedAt: new Date()
      })
      .where(eq(reservations.id, data.reservationId));

    logger.info('Payment intent created', {
      paymentIntentId: paymentIntent.id,
      reservationId: data.reservationId,
      amount: data.amount
    });

    return paymentIntent;
  } catch (error) {
    logger.error('Failed to create payment intent', {
      error: error instanceof Error ? error.message : 'Unknown error',
      reservationId: data.reservationId
    });
    throw error;
  }
}

/**
 * Confirm a booking after successful payment
 */
export async function confirmBooking(paymentIntentId: string): Promise<BookingConfirmation> {
  try {
    // Retrieve payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
    
    if (paymentIntent.status !== 'succeeded') {
      throw new Error(`Payment not successful. Status: ${paymentIntent.status}`);
    }

    const reservationId = parseInt(paymentIntent.metadata.reservationId);
    
    // Generate confirmation code
    const confirmationCode = generateConfirmationCode();
    
    // Update reservation status
    await db
      .update(reservations)
      .set({
        status: 'confirmed',
        confirmationCode,
        updatedAt: new Date()
      })
      .where(eq(reservations.id, reservationId));

    logger.info('Booking confirmed', {
      reservationId,
      confirmationCode,
      paymentIntentId
    });

    return {
      reservationId,
      confirmationCode,
      paymentIntentId,
      status: 'confirmed',
      totalAmount: paymentIntent.amount / 100,
      currency: paymentIntent.currency.toUpperCase()
    };
  } catch (error) {
    logger.error('Failed to confirm booking', {
      error: error instanceof Error ? error.message : 'Unknown error',
      paymentIntentId
    });
    throw error;
  }
}

/**
 * Handle webhook events from Stripe
 */
export async function handleStripeWebhook(event: Stripe.Event): Promise<void> {
  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        await confirmBooking(paymentIntent.id);
        break;
      
      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object as Stripe.PaymentIntent;
        await handleFailedPayment(failedPayment.id);
        break;
      
      default:
        logger.info('Unhandled webhook event', { type: event.type });
    }
  } catch (error) {
    logger.error('Webhook handling failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      eventType: event.type
    });
    throw error;
  }
}

/**
 * Handle failed payment
 */
async function handleFailedPayment(paymentIntentId: string): Promise<void> {
  try {
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
    const reservationId = parseInt(paymentIntent.metadata.reservationId);
    
    await db
      .update(reservations)
      .set({
        status: 'cancelled',
        updatedAt: new Date()
      })
      .where(eq(reservations.id, reservationId));

    logger.info('Reservation cancelled due to failed payment', {
      reservationId,
      paymentIntentId
    });
  } catch (error) {
    logger.error('Failed to handle payment failure', {
      error: error instanceof Error ? error.message : 'Unknown error',
      paymentIntentId
    });
  }
}

/**
 * Generate a unique confirmation code
 */
function generateConfirmationCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Get booking details by confirmation code
 */
export async function getBookingByConfirmation(confirmationCode: string) {
  try {
    const booking = await db.query.reservations.findFirst({
      where: eq(reservations.confirmationCode, confirmationCode),
      with: {
        property: true,
        user: {
          columns: {
            id: true,
            email: true,
            firstName: true,
            lastName: true
          }
        }
      }
    });

    return booking;
  } catch (error) {
    logger.error('Failed to get booking by confirmation', {
      error: error instanceof Error ? error.message : 'Unknown error',
      confirmationCode
    });
    throw error;
  }
}
