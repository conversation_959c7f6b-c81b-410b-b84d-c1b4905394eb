import { Client } from '@googlemaps/google-maps-services-js';
import logger from '../utils/logger.js';

const googleMapsClient = new Client({});

export interface LocationData {
  name: string;
  lat: number;
  lng: number;
  placeType: string;
  placeId?: string;
  formattedAddress?: string;
  components?: {
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
}

export interface NearbyPlace {
  name: string;
  placeId: string;
  lat: number;
  lng: number;
  types: string[];
  rating?: number;
  priceLevel?: number;
  vicinity?: string;
}

/**
 * Geocode a location string to coordinates and place details
 */
export async function geocodeLocation(locationQuery: string): Promise<LocationData | null> {
  try {
    if (!process.env.GOOGLE_MAPS_API_KEY) {
      logger.warn('Google Maps API key not configured, using fallback');
      return getFallbackLocation(locationQuery);
    }

    const response = await googleMapsClient.geocode({
      params: {
        address: locationQuery,
        key: process.env.GOOGLE_MAPS_API_KEY,
      },
    });

    if (response.data.results.length === 0) {
      logger.warn('No geocoding results found', { query: locationQuery });
      return getFallbackLocation(locationQuery);
    }

    const result = response.data.results[0];
    const location = result.geometry.location;
    
    // Extract address components
    const components: LocationData['components'] = {};
    result.address_components.forEach(component => {
      if (component.types.includes('locality')) {
        components.city = component.long_name;
      } else if (component.types.includes('administrative_area_level_1')) {
        components.state = component.short_name;
      } else if (component.types.includes('country')) {
        components.country = component.long_name;
      } else if (component.types.includes('postal_code')) {
        components.postalCode = component.long_name;
      }
    });

    // Determine place type
    const placeType = getPlaceType(result.types);

    return {
      name: result.formatted_address.split(',')[0], // First part is usually the main name
      lat: location.lat,
      lng: location.lng,
      placeType,
      placeId: result.place_id,
      formattedAddress: result.formatted_address,
      components
    };

  } catch (error) {
    logger.error('Geocoding failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      query: locationQuery
    });
    return getFallbackLocation(locationQuery);
  }
}

/**
 * Reverse geocode coordinates to location details
 */
export async function reverseGeocode(lat: number, lng: number): Promise<LocationData | null> {
  try {
    if (!process.env.GOOGLE_MAPS_API_KEY) {
      return null;
    }

    const response = await googleMapsClient.reverseGeocode({
      params: {
        latlng: { lat, lng },
        key: process.env.GOOGLE_MAPS_API_KEY,
      },
    });

    if (response.data.results.length === 0) {
      return null;
    }

    const result = response.data.results[0];
    const location = result.geometry.location;
    
    return {
      name: result.formatted_address.split(',')[0],
      lat: location.lat,
      lng: location.lng,
      placeType: getPlaceType(result.types),
      placeId: result.place_id,
      formattedAddress: result.formatted_address
    };

  } catch (error) {
    logger.error('Reverse geocoding failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      coordinates: `${lat},${lng}`
    });
    return null;
  }
}

/**
 * Find nearby places of interest
 */
export async function findNearbyPlaces(
  lat: number, 
  lng: number, 
  radius: number = 5000,
  type?: string
): Promise<NearbyPlace[]> {
  try {
    if (!process.env.GOOGLE_MAPS_API_KEY) {
      return [];
    }

    const response = await googleMapsClient.placesNearby({
      params: {
        location: { lat, lng },
        radius,
        type: type as any,
        key: process.env.GOOGLE_MAPS_API_KEY,
      },
    });

    return response.data.results.map(place => ({
      name: place.name || 'Unknown',
      placeId: place.place_id || '',
      lat: place.geometry?.location.lat || lat,
      lng: place.geometry?.location.lng || lng,
      types: place.types || [],
      rating: place.rating,
      priceLevel: place.price_level,
      vicinity: place.vicinity
    }));

  } catch (error) {
    logger.error('Nearby places search failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      coordinates: `${lat},${lng}`,
      radius,
      type
    });
    return [];
  }
}

/**
 * Get place details by place ID
 */
export async function getPlaceDetails(placeId: string): Promise<LocationData | null> {
  try {
    if (!process.env.GOOGLE_MAPS_API_KEY) {
      return null;
    }

    const response = await googleMapsClient.placeDetails({
      params: {
        place_id: placeId,
        key: process.env.GOOGLE_MAPS_API_KEY,
      },
    });

    const place = response.data.result;
    if (!place.geometry?.location) {
      return null;
    }

    return {
      name: place.name || 'Unknown',
      lat: place.geometry.location.lat,
      lng: place.geometry.location.lng,
      placeType: getPlaceType(place.types || []),
      placeId: place.place_id,
      formattedAddress: place.formatted_address
    };

  } catch (error) {
    logger.error('Place details fetch failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      placeId
    });
    return null;
  }
}

/**
 * Determine place type from Google Maps types array
 */
function getPlaceType(types: string[]): string {
  const typeHierarchy = [
    'establishment',
    'point_of_interest',
    'lodging',
    'tourist_attraction',
    'locality',
    'sublocality',
    'neighborhood',
    'administrative_area_level_1',
    'administrative_area_level_2',
    'country'
  ];

  for (const type of typeHierarchy) {
    if (types.includes(type)) {
      return type;
    }
  }

  return 'unknown';
}

/**
 * Fallback location data for when geocoding fails
 */
function getFallbackLocation(query: string): LocationData | null {
  const fallbackLocations: Record<string, LocationData> = {
    'new york': { name: 'New York', lat: 40.7128, lng: -74.0060, placeType: 'locality' },
    'los angeles': { name: 'Los Angeles', lat: 34.0522, lng: -118.2437, placeType: 'locality' },
    'chicago': { name: 'Chicago', lat: 41.8781, lng: -87.6298, placeType: 'locality' },
    'miami': { name: 'Miami', lat: 25.7617, lng: -80.1918, placeType: 'locality' },
    'san francisco': { name: 'San Francisco', lat: 37.7749, lng: -122.4194, placeType: 'locality' },
    'las vegas': { name: 'Las Vegas', lat: 36.1699, lng: -115.1398, placeType: 'locality' },
    'orlando': { name: 'Orlando', lat: 28.5383, lng: -81.3792, placeType: 'locality' },
    'seattle': { name: 'Seattle', lat: 47.6062, lng: -122.3321, placeType: 'locality' },
    'boston': { name: 'Boston', lat: 42.3601, lng: -71.0589, placeType: 'locality' },
    'london': { name: 'London', lat: 51.5074, lng: -0.1278, placeType: 'locality' },
    'paris': { name: 'Paris', lat: 48.8566, lng: 2.3522, placeType: 'locality' },
    'tokyo': { name: 'Tokyo', lat: 35.6762, lng: 139.6503, placeType: 'locality' },
    'sydney': { name: 'Sydney', lat: -33.8688, lng: 151.2093, placeType: 'locality' },
    'dubai': { name: 'Dubai', lat: 25.2048, lng: 55.2708, placeType: 'locality' }
  };

  const normalizedQuery = query.toLowerCase().trim();
  
  // Try exact match first
  if (fallbackLocations[normalizedQuery]) {
    return fallbackLocations[normalizedQuery];
  }

  // Try partial match
  for (const [key, location] of Object.entries(fallbackLocations)) {
    if (normalizedQuery.includes(key) || key.includes(normalizedQuery)) {
      return location;
    }
  }

  logger.warn('No fallback location found', { query });
  return null;
}
