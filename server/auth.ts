import passport from "passport";
import { Strategy as LocalStrategy } from "passport-local";
import { Express, Request, Response, NextFunction } from "express";
import session from "express-session";
import connectPg from "connect-pg-simple";
import { scrypt, randomBytes, timingSafeEqual } from "crypto";
import { promisify } from "util";
import { users, promoCodes } from "../db/schema.js";
import { db } from "../db/index.js";
import { eq } from "drizzle-orm";
import logger from "./utils/logger";
import pkg from 'pg';
const { Pool } = pkg;

// Create PostgreSQL pool for sessions
const pool = new Pool({
  connectionString: process.env.DATABASE_URL
});

// Define the user interface based on database schema
interface IUser {
  id: number;
  email: string;
  password: string;
  isAdmin: boolean;
  isVerified: boolean;
  membershipType: string;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date | null;
  usedPromoCode?: string | null;
  preferences?: Record<string, any> | null;
  profileImageUrl?: string | null;
  phoneNumber?: string | null;
}

// Extend Express.User interface
declare global {
  namespace Express {
    interface User extends IUser {}
  }
}

const scryptAsync = promisify(scrypt);
const PostgresSessionStore = connectPg(session);

export async function hashPassword(password: string) {
  const salt = randomBytes(16).toString("hex");
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString("hex")}.${salt}`;
}

export async function comparePasswords(supplied: string, stored: string) {
  // Special case for test users with plaintext passwords
  if (stored === 'plaintext123') {
    return supplied === 'plaintext123';
  }
  
  // For properly hashed passwords
  try {
    const [hashed, salt] = stored.split(".");
    
    // If not in correct format, it might be a plaintext password
    if (!hashed || !salt) {
      return supplied === stored;
    }
    
    const hashedBuf = Buffer.from(hashed, "hex");
    const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer;
    return timingSafeEqual(hashedBuf, suppliedBuf);
  } catch (error) {
    logger.error('Error comparing passwords', { error: error instanceof Error ? error.message : String(error) });
    return false;
  }
}

async function getUserByEmail(email: string) {
  return db.select().from(users).where(eq(users.email, email)).limit(1);
}

export function setupAuth(app: Express) {
  const store = new PostgresSessionStore({ 
    pool,
    tableName: 'session', // Default
    createTableIfMissing: true
  });
  
  const sessionSettings: session.SessionOptions = {
    secret: process.env.SESSION_SECRET || "travel-booking-secret-key",
    resave: false,
    saveUninitialized: false,
    store,
    cookie: {
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      httpOnly: true,
      secure: process.env.NODE_ENV === "production"
    }
  };

  app.set("trust proxy", 1);
  app.use(session(sessionSettings));
  app.use(passport.initialize());
  app.use(passport.session());

  passport.use(
    new LocalStrategy({
      usernameField: 'email'
    }, async (email: string, password: string, done: any) => {
      try {
        const [user] = await getUserByEmail(email);
        if (!user || !(await comparePasswords(password, user.password))) {
          logger.debug('Authentication failed', { email });
          return done(null, false, { message: 'Invalid email or password' });
        } else {
          logger.debug('Authentication successful', { userId: user.id });
          
          // Create properly typed user object
          const safeUser: Express.User = {
            id: user.id,
            email: user.email,
            password: user.password,
            isAdmin: user.isAdmin ?? false,
            isVerified: user.isVerified ?? false,
            membershipType: user.membershipType ?? 'standard',
            createdAt: user.createdAt ?? new Date(),
            updatedAt: user.updatedAt ?? new Date(),
            lastLoginAt: user.lastLoginAt,
            usedPromoCode: user.usedPromoCode,
            preferences: user.preferences ? user.preferences : null,
            profileImageUrl: user.profileImageUrl,
            phoneNumber: user.phoneNumber
          };
          
          return done(null, safeUser);
        }
      } catch (error) {
        logger.error('Authentication error', { 
          error: error instanceof Error ? error.message : String(error)
        });
        return done(error);
      }
    }),
  );

  passport.serializeUser((user: Express.User, done) => {
    logger.debug('Serializing user', { userId: user.id });
    done(null, user.id);
  });
  
  passport.deserializeUser(async (id: number, done) => {
    try {
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1);
  
      if (!user) {
        logger.warn('User not found during deserialization', { userId: id });
        return done(null, false);
      }
      
      // Ensure all required fields are set with defaults if null
      const safeUser: Express.User = {
        id: user.id,
        email: user.email,
        password: user.password,
        isAdmin: user.isAdmin ?? false,
        isVerified: user.isVerified ?? false,
        membershipType: user.membershipType ?? 'standard',
        createdAt: user.createdAt ?? new Date(),
        updatedAt: user.updatedAt ?? new Date(),
        lastLoginAt: user.lastLoginAt,
        usedPromoCode: user.usedPromoCode,
        preferences: user.preferences ? user.preferences as Record<string, any> : null,
        profileImageUrl: user.profileImageUrl,
        phoneNumber: user.phoneNumber
      };
  
      logger.debug('Deserialized user', { userId: id });
      done(null, safeUser);
    } catch (error) {
      logger.error('Error deserializing user', { 
        error: error instanceof Error ? error.message : String(error),
        userId: id 
      });
      done(error);
    }
  });

  app.post("/api/register", async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { email, password, name, promoCode } = req.body;
      
      // Check if user already exists
      const [existingUser] = await getUserByEmail(email);
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: "Email already exists"
        });
      }
      
      // Promo code is now required for registration
      if (!promoCode) {
        return res.status(400).json({
          success: false,
          message: "Promo code is required for registration"
        });
      }
      
      // Validate promo code
      const [codeData] = await db
        .select()
        .from(promoCodes)
        .where(eq(promoCodes.code, promoCode))
        .limit(1);
      
      if (!codeData) {
        return res.status(400).json({
          success: false,
          message: "Invalid promo code"
        });
      }
      
      if (!codeData.isActive) {
        return res.status(400).json({
          success: false,
          message: "This promo code is no longer active"
        });
      }
      
      if (codeData.expiresAt && new Date(codeData.expiresAt) < new Date()) {
        return res.status(400).json({
          success: false,
          message: "This promo code has expired"
        });
      }
      
      // Check usage count
      const currentUsageCount = codeData.usageCount ?? 0;
      const maxUsages = codeData.maxUsages ?? 0;
      
      if (currentUsageCount >= maxUsages) {
        return res.status(400).json({
          success: false,
          message: "This promo code has reached its maximum usage limit"
        });
      }
      
      // Increment promo code usage count
      await db.update(promoCodes)
        .set({ 
          usageCount: currentUsageCount + 1,
          updatedAt: new Date()
        })
        .where(eq(promoCodes.id, codeData.id));

      // Format preferences
      const preferences = { displayName: name || email.split('@')[0] };
      
      // Create user with hashed password
      const [dbUser] = await db
        .insert(users)
        .values({
          email: email.toLowerCase(),
          password: await hashPassword(password),
          isAdmin: false,
          isVerified: true, // Auto-verify users with valid promo codes
          membershipType: codeData.membershipType || 'standard',
          usedPromoCode: promoCode,
          preferences,
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning();
        
      // Create properly typed user object
      const safeUser: Express.User = {
        id: dbUser.id,
        email: dbUser.email,
        password: dbUser.password,
        isAdmin: dbUser.isAdmin ?? false,
        isVerified: dbUser.isVerified ?? false,
        membershipType: dbUser.membershipType ?? 'standard',
        createdAt: dbUser.createdAt ?? new Date(),
        updatedAt: dbUser.updatedAt ?? new Date(),
        lastLoginAt: dbUser.lastLoginAt,
        usedPromoCode: dbUser.usedPromoCode,
        preferences: preferences as Record<string, any>,
        profileImageUrl: dbUser.profileImageUrl,
        phoneNumber: dbUser.phoneNumber
      };

      // Log in the newly registered user
      req.login(safeUser, (err) => {
        if (err) return next(err);
        res.status(201).json({
          success: true,
          user: safeUser,
          message: "Registration successful"
        });
      });
    } catch (error) {
      logger.error('Registration error', { error: error instanceof Error ? error.message : String(error) });
      res.status(500).json({
        success: false,
        message: "Server error during registration"
      });
    }
  });

  app.post("/api/login", (req: Request, res: Response, next: NextFunction) => {
    passport.authenticate("local", (err: Error | null, user: Express.User | false, info: { message: string } | undefined) => {
      if (err) return next(err);
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: info?.message || "Invalid email or password"
        });
      }
      
      req.login(user, (err) => {
        if (err) return next(err);
        return res.status(200).json({
          success: true,
          user,
          message: "Login successful"
        });
      });
    })(req, res, next);
  });

  app.post("/api/logout", (req: Request, res: Response, next: NextFunction) => {
    req.logout((err) => {
      if (err) return next(err);
      res.status(200).json({
        success: true,
        message: "Logout successful"
      });
    });
  });

  app.get("/api/user", (req: Request, res: Response) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({
        success: false,
        message: "Not authenticated"
      });
    }
    
    res.json({
      success: true,
      user: req.user
    });
  });
}