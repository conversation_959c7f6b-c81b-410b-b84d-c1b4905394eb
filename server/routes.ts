import { Express, Request, Response, NextFunction } from 'express';
import { Server } from 'http';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import { Server as SocketIOServer } from 'socket.io';
import { getPropertyAvailability, searchTravSrvProperties } from './services/travsrv';
import logger, { logOperation, logError } from './utils/logger';
import {
  saveTestResult,
  getTestResults,
  getTestResultById,
  getTestMetrics,
  deleteTestResult
} from './controllers/testController';
// Import analytics controller functions
import {
  getOverviewMetrics,
  getUserActivity,
  getBookingActivity,
  getSearchActivity,
  getAllAnalytics
} from './controllers/analyticsController';

// Import profile controller functions
import {
  getProfile,
  updateProfile,
  updatePreferences,
  changeEmail,
  changePassword
} from './controllers/profileController';
import {
  startTestRun,
  getTestRunStatus,
  listTestRuns,
  stopTestRun,
  setSocketIO,
  cleanupTestRuns
} from './controllers/testRunnerController';
import { db } from '../db/index.js';
import { eq, sql, desc } from 'drizzle-orm';
import { properties, reservations, promoCodes } from '../db/schema.js';
import type { Property, Reservation, PromoCode } from '../db/schema.js';
import type { InferModel } from 'drizzle-orm';
import { MySqlDateString } from 'drizzle-orm/mysql-core';
import { 
  handleChatStream, 
  getConversationContext,
  addMessageToConversation,
  needsSummarization,
  summarizeConversation,
  type ChatResponse,
  findMatchingProperty,
  type PropertyRecommendation,
  type PropertyMatch,
  type TypedChatResponse,
  type TextResponse,
  type PropertiesResponse,
  type LocationResponse,
  type ActionResponse,
  type ErrorResponse
} from './services/openai.js';

// Import contextService at the top level to avoid dynamic imports that could interfere with the response stream
import { contextService } from './services/contextService.js';

// Import enhanced AI services
import { getLocationDetails, validateLocationCoordinates } from './services/enhancedLocationService.js';
import { getTravelInsights } from './services/proactiveIntelligence.js';
import { getEnhancedContext, recordUserMessage, recordAiResponse } from './services/enhancedContextService.js';
import { runTestCase, testLocationDetection } from './services/chatTestingService.js';

// Import authentication related modules
import { requireAuth, requireAdmin } from './middleware/auth';
import { 
  loginUser, 
  registerUser, 
  getTestPromoCode, 
  getCurrentUser 
} from './controllers/authController.js';
import {
  getUsers,
  createUser,
  updateUser,
  deleteUser,
  getPromoCodes,
  createPromoCode,
  updatePromoCode,
  togglePromoCodeStatus,
  deletePromoCode,
  generateTestPromoCode,
  validatePromoCode,
  applyPromoCode
} from './controllers/adminController';

import { getSiteId } from './services/authService.js';

// Import enhanced chat routes
import { 
  enhancedChatHandler, 
  chatTestHandler, 
  locationTestHandler, 
  travelInsightsHandler 
} from './routes/enhancedChatRoutes.js';

const AUTH_HEADER = "Basic bHluY2h0ZXN0OmFybjEyMzQ1";

// Utility functions
const createRequestId = (type: string, id: string) => `${type}-${id}-${Date.now()}`;

const fetchFromApi = async (url: string, requestId: string) => {
  const response = await fetch(url, {
    headers: {
      'Authorization': AUTH_HEADER,
      'Accept-version': '2'
    }
  });

  if (!response.ok) {
    logError(requestId, 'API request failed', {
      status: response.status,
      url,
      method: 'GET',
      headers: {
        'Authorization': '[REDACTED]',
        'Accept-version': '2'
      },
      response: {
        status: response.status,
        statusText: response.statusText
      }
    });
    throw new Error(`API request failed with status ${response.status}`);
  }

  return response.json();
};

const handleApiError = (error: unknown, requestId: string, context: Record<string, any>) => {
  const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
  const statusCode = errorMessage.includes('Invalid') ? 400 : 500;
  
  logError(requestId, errorMessage, {
    operation: `${context.operation}_ERROR`,
    ...context,
    error: errorMessage
  });

  return {
    statusCode,
    body: { 
      message: errorMessage,
      requestId,
      timestamp: new Date().toISOString()
    }
  };
};

const enrichPropertyWithContent = (property: any, content: any) => {
  const contentImages = (content.Images || []).map((img: any) => ({
    url: img.ImagePath || img,
    caption: img.ImageCaption || '',
    displayOrder: img.DisplayOrder || 0,
    roomTypeCode: img.RoomTypeCode || null,
    category: img.Category || (img.ImageCaption?.toLowerCase().includes('room') ? 'room' : 'property')
  }));

  return {
    ...property,
    images: contentImages,
    amenities: content.AmenityList || property.amenities || [],
    description: content.Description || property.description,
  };
};

// Use proper Drizzle type inference for different contexts
type NewProperty = InferModel<typeof properties, 'insert'>;
type PropertySelect = typeof properties.$inferSelect;
type PropertyInsert = Omit<typeof properties.$inferInsert, 'id'>;

// Import our new auth setup
import { setupAuth } from './auth';

// Import analytics middleware
import analyticsMiddleware from './middleware/analytics';

export function registerRoutes(app: Express): Server {
  // Set up session-based authentication
  setupAuth(app);
  
  // Apply analytics middleware to track page views for all routes
  app.use(analyticsMiddleware.trackPageView);
  
  // Register enhanced AI chat routes
  app.post("/api/chat/enhanced", enhancedChatHandler);
  app.post("/api/chat/test", requireAdmin, chatTestHandler);
  app.post("/api/chat/test/location", requireAdmin, locationTestHandler);
  app.post("/api/travel/insights", travelInsightsHandler);
  
  logger.info('Enhanced AI chat routes registered successfully');
  
  // The TestHub endpoint should still use the middleware
  app.get('/api/promo-code/test', requireAdmin, getTestPromoCode);

  // Admin routes
  app.get("/api/admin/users", requireAdmin, getUsers);
  app.post("/api/admin/users", requireAdmin, createUser);
  app.put("/api/admin/users/:id", requireAdmin, updateUser);
  app.delete("/api/admin/users/:id", requireAdmin, deleteUser);
  
  app.get("/api/admin/promo-codes", requireAdmin, getPromoCodes);
  app.post("/api/admin/promo-codes", requireAdmin, createPromoCode);
  app.put("/api/admin/promo-codes/:id", requireAdmin, updatePromoCode);
  app.delete("/api/admin/promo-codes/:id", requireAdmin, deletePromoCode);
  app.put("/api/admin/promo-codes/:id/toggle", requireAdmin, togglePromoCodeStatus);
  app.post("/api/admin/promo-codes/generate-test", requireAdmin, generateTestPromoCode);
  
  // Promo code validation endpoints
  app.get("/api/promo-codes/:code/validate", validatePromoCode);
  app.post("/api/promo-codes/:code/apply", requireAuth, applyPromoCode);
  
  // Analytics endpoints
  app.get("/api/admin/analytics", requireAdmin, getAllAnalytics);
  app.get("/api/admin/analytics/overview", requireAdmin, getOverviewMetrics);
  app.get("/api/admin/analytics/user-activity", requireAdmin, getUserActivity);
  app.get("/api/admin/analytics/booking-activity", requireAdmin, getBookingActivity);
  app.get("/api/admin/analytics/search-activity", requireAdmin, getSearchActivity);
  
  // User profile endpoints
  app.get("/api/profile", requireAuth, getProfile);
  app.put("/api/profile", requireAuth, updateProfile);
  app.put("/api/profile/preferences", requireAuth, updatePreferences);
  app.put("/api/profile/email", requireAuth, changeEmail);
  app.put("/api/profile/password", requireAuth, changePassword);
  
  const httpServer = createServer(app);
  
  // Set up Socket.IO for real-time test updates
  const io = new SocketIOServer(httpServer, {
    path: '/socket.io',
    cors: {
      origin: '*',
      methods: ['GET', 'POST']
    }
  });
  
  // Set up the Socket.IO instance in the test runner controller
  setSocketIO(io.of('/test-runner'));
  
  // Set up Socket.IO event handlers
  io.of('/test-runner').on('connection', (socket) => {
    logger.info('New test runner connection', { socketId: socket.id });
    
    socket.on('disconnect', () => {
      logger.info('Test runner disconnected', { socketId: socket.id });
    });
  });

  // Test endpoint for location detection (development only)
  app.get("/api/test/location", (req, res) => {
    try {
      const locationName = req.query.location as string || 'Miami Beach';
      // Clean locationName by removing quotes if present
      const cleanLocationName = locationName.replace(/"/g, '');
      
      // Enhanced location lookup with more accurate city data
      const locationMap: Record<string, {name: string, lat: number, lng: number, placeType: string}> = {
        'new york': { name: 'New York', lat: 40.7128, lng: -74.0060, placeType: 'locality' },
        'nyc': { name: 'New York City', lat: 40.7128, lng: -74.0060, placeType: 'locality' },
        'manhattan': { name: 'Manhattan', lat: 40.7831, lng: -73.9712, placeType: 'sublocality' },
        'los angeles': { name: 'Los Angeles', lat: 34.0522, lng: -118.2437, placeType: 'locality' },
        'la': { name: 'Los Angeles', lat: 34.0522, lng: -118.2437, placeType: 'locality' },
        'chicago': { name: 'Chicago', lat: 41.8781, lng: -87.6298, placeType: 'locality' },
        'miami': { name: 'Miami', lat: 25.7617, lng: -80.1918, placeType: 'locality' },
        'miami beach': { name: 'Miami Beach', lat: 25.7917, lng: -80.1300, placeType: 'locality' },
        'san francisco': { name: 'San Francisco', lat: 37.7749, lng: -122.4194, placeType: 'locality' },
        'sf': { name: 'San Francisco', lat: 37.7749, lng: -122.4194, placeType: 'locality' },
        'las vegas': { name: 'Las Vegas', lat: 36.1699, lng: -115.1398, placeType: 'locality' },
        'orlando': { name: 'Orlando', lat: 28.5383, lng: -81.3792, placeType: 'locality' },
        'seattle': { name: 'Seattle', lat: 47.6062, lng: -122.3321, placeType: 'locality' },
        'boston': { name: 'Boston', lat: 42.3601, lng: -71.0589, placeType: 'locality' },
        'london': { name: 'London', lat: 51.5074, lng: -0.1278, placeType: 'locality' },
        'paris': { name: 'Paris', lat: 48.8566, lng: 2.3522, placeType: 'locality' },
        'tokyo': { name: 'Tokyo', lat: 35.6762, lng: 139.6503, placeType: 'locality' },
        'sydney': { name: 'Sydney', lat: -33.8688, lng: 151.2093, placeType: 'locality' },
        'rome': { name: 'Rome', lat: 41.9028, lng: 12.4964, placeType: 'locality' },
        'barcelona': { name: 'Barcelona', lat: 41.3851, lng: 2.1734, placeType: 'locality' },
        'dubai': { name: 'Dubai', lat: 25.2048, lng: 55.2708, placeType: 'locality' },
        'bangkok': { name: 'Bangkok', lat: 13.7563, lng: 100.5018, placeType: 'locality' },
        'singapore': { name: 'Singapore', lat: 1.3521, lng: 103.8198, placeType: 'locality' },
        'hong kong': { name: 'Hong Kong', lat: 22.3193, lng: 114.1694, placeType: 'locality' },
        'berlin': { name: 'Berlin', lat: 52.5200, lng: 13.4050, placeType: 'locality' },
        'amsterdam': { name: 'Amsterdam', lat: 52.3676, lng: 4.9041, placeType: 'locality' },
        'madrid': { name: 'Madrid', lat: 40.4168, lng: -3.7038, placeType: 'locality' },
        'cancun': { name: 'Cancun', lat: 21.1619, lng: -86.8515, placeType: 'locality' },
        'honolulu': { name: 'Honolulu', lat: 21.3069, lng: -157.8583, placeType: 'locality' },
        'hawaii': { name: 'Hawaii', lat: 19.8968, lng: -155.5828, placeType: 'administrative_area_level_1' },
        'denver': { name: 'Denver', lat: 39.7392, lng: -104.9903, placeType: 'locality' },
        'phoenix': { name: 'Phoenix', lat: 33.4484, lng: -112.0740, placeType: 'locality' }
      };
      
      // Try exact match first
      let locationData = locationMap[cleanLocationName.toLowerCase()];
      
      // If no exact match, try partial match
      if (!locationData) {
        const locationLower = cleanLocationName.toLowerCase();
        // Find first partial match
        for (const [key, data] of Object.entries(locationMap)) {
          if (locationLower.includes(key) || key.includes(locationLower)) {
            locationData = data;
            break;
          }
        }
      }
      
      // If still no match, use default (New York)
      if (!locationData) {
        locationData = { 
          name: cleanLocationName, 
          lat: 40.7128, 
          lng: -74.0060, 
          placeType: 'locality' 
        };
      }
      
      const locationResponse: LocationResponse = {
        type: "location",
        data: {
          name: locationData.name,
          lat: locationData.lat,
          lng: locationData.lng,
          placeType: locationData.placeType
        }
      };
      
      res.json(locationResponse);
    } catch (error) {
      res.status(500).json({
        error: "Failed to generate test location",
        details: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Frontend configuration endpoint
  app.get("/api/config", (req, res) => {
    logOperation('config', 'CONFIG_REQUEST', {
      timestamp: new Date().toISOString()
    });
    
    res.json({
      googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY,
      googleMapsMapId: process.env.GOOGLE_MAPS_MAP_ID,
    });
  });

  // Property search endpoint that returns candidate results
  app.get("/api/properties/search", analyticsMiddleware.trackSearch, async (req, res) => {
    const requestId = `search-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    try {
      const {
        lat,
        lng,
        locationName,
        placeType,
        checkIn,
        checkOut,
        guests = "1",
        rooms = "1",
        page = "1",
        pageSize = "10",
        radius = "10", // default radius in kilometers
        recommended // Add recommended parameter
      } = req.query as Record<string, string>;

      let dbResults: PropertySelect[] = [];

      if (recommended) {
        // If we have recommended IDs, fetch those specific properties
        const recommendedIds = recommended.split(',').map(id => parseInt(id));
        dbResults = await db.query.properties.findMany({
          where: sql`${properties.id} IN (${sql.join(recommendedIds, sql`, `)})`
        });

        logOperation(requestId, 'RECOMMENDED_PROPERTIES_FETCH', {
          count: dbResults.length,
          requestedIds: recommendedIds
        });
      } else {
        // Regular location-based search
        if (!lat || !lng || !checkIn || !checkOut) {
          throw new Error('Missing required parameters');
        }

        // Adjust search parameters based on place type
        let adjustedRadius = parseFloat(radius);
        let searchStrategy = 'default';

        // Handle different place types
        switch (placeType) {
          case 'locality':
          case 'administrative_area_level_1':
            adjustedRadius = Math.max(adjustedRadius, 20);
            searchStrategy = 'broad';
            break;
          
          case 'establishment':
          case 'point_of_interest':
            adjustedRadius = Math.min(adjustedRadius, 5);
            searchStrategy = 'precise';
            break;
          
          case 'sublocality':
          case 'neighborhood':
            adjustedRadius = Math.min(Math.max(adjustedRadius, 5), 10);
            searchStrategy = 'balanced';
            break;
          
          default:
            searchStrategy = 'default';
        }

        logOperation(requestId, 'SEARCH_REQUEST', {
          lat,
          lng,
          locationName,
          placeType,
          checkIn,
          checkOut,
          guests,
          rooms,
          adjustedRadius,
          searchStrategy
        });

        // First, try to get properties from database within radius
        const ONE_DAY = 24 * 60 * 60 * 1000;
        const locationQuery = sql`
          point(${lat}, ${lng}) <@> point(${properties.latitude}::float, ${properties.longitude}::float) <= ${adjustedRadius}
        `;
        
        const dbProperties = await db.query.properties.findMany({
          where: locationQuery
        });

        // Check if properties need refreshing (empty images, empty amenities, or old updated_at)
        const needsRefresh = dbProperties.length === 0 || dbProperties.some(prop => {
          const hasEmptyImages = !prop.images || (Array.isArray(prop.images) && prop.images.length === 0);
          const hasEmptyAmenities = !prop.amenities || (Array.isArray(prop.amenities) && prop.amenities.length === 0);
          const isStale = prop.updatedAt && (Date.now() - prop.updatedAt.getTime()) > (7 * ONE_DAY); // Refresh if older than 7 days
          return hasEmptyImages || hasEmptyAmenities || isStale;
        });

        if (dbProperties.length > 0 && !needsRefresh) {
          logOperation(requestId, 'PROPERTIES_FROM_DB', {
            count: dbProperties.length,
            location: locationName
          });
          dbResults = dbProperties;
        } else {
          // Log why we're refreshing
          if (dbProperties.length > 0 && needsRefresh) {
            logOperation(requestId, 'PROPERTIES_REFRESHING', {
              count: dbProperties.length,
              location: locationName,
              reason: 'Empty images, amenities, or stale data detected'
            });
          }
          
          // Get candidate properties from Travel Supplier API
          const apiProperties = await searchTravSrvProperties({
            latitude: parseFloat(lat),
            longitude: parseFloat(lng),
            inDate: checkIn,
            outDate: checkOut,
            adults: parseInt(guests),
            rooms: parseInt(rooms),
            radius: parseFloat(radius)
          });

          // Update updatedAt for all properties
          if (apiProperties.length > 0) {
            for (const property of apiProperties) {
              // Prepare the data without id for insert
              const propertyData: PropertyInsert = {
                name: property.name,
                description: property.description,
                propertyType: property.propertyType || 'hotel',
                address: property.address,
                city: property.city,
                state: property.state,
                country: property.country,
                latitude: Number(property.latitude),
                longitude: Number(property.longitude),
                basePrice: Number(property.basePrice),
                rating: property.rating ? Number(property.rating) : null,
                reviewCount: property.reviewCount || 0,
                // Fix: Store arrays directly in JSONB fields, don't JSON.stringify them
                images: Array.isArray(property.images) ? property.images : (property.images ? [property.images] : []),
                amenities: Array.isArray(property.amenities) ? property.amenities : (property.amenities ? [property.amenities] : []),
                externalId: property.externalId,
                createdAt: new Date(),
                updatedAt: new Date()
              };

              try {
                // Insert or update using the external ID as the conflict target
                await db
                  .insert(properties)
                  .values(propertyData)
                  .onConflictDoUpdate({
                    target: [properties.externalId],
                    set: {
                      name: propertyData.name,
                      description: propertyData.description,
                      propertyType: propertyData.propertyType,
                      address: propertyData.address,
                      city: propertyData.city,
                      state: propertyData.state,
                      country: propertyData.country,
                      latitude: propertyData.latitude,
                      longitude: propertyData.longitude,
                      basePrice: propertyData.basePrice,
                      rating: propertyData.rating,
                      reviewCount: propertyData.reviewCount,
                      images: propertyData.images,
                      amenities: propertyData.amenities,
                      updatedAt: new Date()
                    }
                  });
              } catch (error) {
                logError(requestId, 'Failed to update property', {
                  propertyId: property.id,
                  error: error instanceof Error ? error.message : 'Unknown error'
                });
              }
            }
          }

          // After updating the database, fetch the latest properties
          const updatedProperties = await db.query.properties.findMany({
            where: locationQuery
          });

          dbResults = updatedProperties;
          logOperation(requestId, 'PROPERTIES_FROM_API', {
            rawSql: locationQuery.toString(),
            count: dbResults.length,
            location: locationName
          });
        }
      }

      // Calculate pagination
      const currentPage = parseInt(page);
      const itemsPerPage = parseInt(pageSize);
      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;

      // Get paginated results
      const paginatedResults = dbResults.slice(startIndex, endIndex);
      const totalTime = Date.now() - startTime;

      res.json({
        properties: paginatedResults,
        total: dbResults.length,
        currentPage,
        totalPages: Math.ceil(dbResults.length / itemsPerPage),
        explanation: recommended 
          ? `Found ${dbResults.length} recommended properties`
          : `Found ${dbResults.length} properties near ${locationName}`,
        searchId: requestId,
        timing: {
          total: totalTime,
          source: recommended ? 'recommendations' : 'search'
        }
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      
      logError(requestId, errorMessage, {
        operation: 'PROPERTY_SEARCH_ERROR',
        query: req.query
      });

      res.status(400).json({ 
        message: errorMessage,
        requestId,
        timestamp: new Date().toISOString()
      });
    }
  });

  // Real-time availability endpoint
  app.get("/api/properties/:id/availability", async (req, res) => {
    const requestId = createRequestId('availability', req.params.id);
    const { id } = req.params;
    const { checkIn, checkOut, guests, rooms = "1", bestRateOnly } = req.query;

    try {
      if (!checkIn || !checkOut) {
        throw new Error('Check-in and check-out dates are required');
      }

      logOperation(requestId, 'AVAILABILITY_REQUEST', {
        propertyId: id,
        params: { checkIn, checkOut, guests, rooms, bestRateOnly }
      });

      const availability = await getPropertyAvailability(id, {
        inDate: checkIn as string,
        outDate: checkOut as string,
        adults: parseInt(guests as string) || 2,
        rooms: parseInt(rooms as string),
      }, bestRateOnly === 'true');

      // Instead of throwing an error, return empty ratePlans with a status indicator
      if (!availability?.ratePlans || Object.keys(availability.ratePlans).length === 0) {
        logOperation(requestId, 'AVAILABILITY_NO_RATES', {
          propertyId: id,
          params: { checkIn, checkOut, guests, rooms }
        });
        
        return res.json({
          ratePlans: {},
          status: "NO_AVAILABILITY",
          message: "No rates available for the selected dates"
        });
      }

      logOperation(requestId, 'AVAILABILITY_SUCCESS', {
        propertyId: id,
        ratePlanCount: Object.keys(availability.ratePlans).length
      });

      res.json(availability);
    } catch (error) {
      const { statusCode, body } = handleApiError(error, requestId, {
        operation: 'AVAILABILITY',
        propertyId: id,
        params: { checkIn, checkOut, guests, rooms, bestRateOnly }
      });
      res.status(statusCode).json(body);
    }
  });

  // Property detail endpoint
  app.get("/api/properties/:id", analyticsMiddleware.trackPropertyView, async (req, res) => {
    const requestId = createRequestId('property', req.params.id);
    const startTime = Date.now();

    try {
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        throw new Error('Invalid property ID');
      }

      logOperation(requestId, 'DETAILS_REQUEST', { propertyId: id });

      // Get property from database
      let property = await db.query.properties.findFirst({
        where: eq(properties.id, id)
      });

      // Get content from Travel Supplier API
      const contentApiUrl = `https://api.travsrv.com/api/content/findpropertyinfo?propertyid=${id}`;
      const content = await fetchFromApi(contentApiUrl, requestId);

      logOperation(requestId, 'CONTENT_RESPONSE', {
        hasImages: !!content.Images,
        imageCount: Array.isArray(content.Images) ? content.Images.length : 0
      });

      // If no cached property exists, create and cache a new one
      if (!property) {
        const now = new Date();
        const newProperty = {
          externalId: id.toString(),
          name: content.PropertyName || '',
          description: content.Description || '',
          propertyType: content.PropertyType || 'hotel',
          address: content.Address || '',
          city: content.City || '',
          state: content.State || null,
          country: content.Country || '',
          latitude: Number(content.Latitude) || 0,
          longitude: Number(content.Longitude) || 0,
          basePrice: Number(content.BasePrice) || 0,
          rating: content.Rating ? Number(content.Rating) : null,
          reviewCount: content.ReviewCount || 0,
          // Fix: Store arrays directly in JSONB fields, don't JSON.stringify them
          images: Array.isArray(content.Images) ? content.Images : (content.Images ? [content.Images] : []),
          amenities: Array.isArray(content.AmenityList) ? content.AmenityList : (content.AmenityList ? [content.AmenityList] : []),
          createdAt: now,
          updatedAt: now
        };

        try {
          // Insert and get back the full record
          const [insertedProperty] = await db
            .insert(properties)
            .values(newProperty)
            .returning();

          logOperation(requestId, 'PROPERTY_CACHED', { 
            propertyId: insertedProperty.id, 
            isNew: true 
          });
          
          property = insertedProperty;
        } catch (error) {
          logError(requestId, 'Cache operation failed', {
            propertyId: id,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      const enrichedProperty = enrichPropertyWithContent(property, content);

      logOperation(requestId, 'DETAILS_SUCCESS', {
        propertyId: id,
        duration: Date.now() - startTime,
        fromCache: !!property,
        imageCount: enrichedProperty.images.length
      });

      res.json(enrichedProperty);
    } catch (error) {
      const { statusCode, body } = handleApiError(error, requestId, {
        operation: 'DETAILS',
        propertyId: req.params.id,
        duration: Date.now() - startTime,
        url: req.url,
        method: req.method,
        params: req.params,
        query: req.query
      });
      res.status(statusCode).json(body);
    }
  });

  // Context tracking endpoints
  app.post("/api/context/property-view", async (req: Request, res: Response) => {
    try {
      const { sessionId, propertyId } = req.body;
      
      if (!sessionId || !propertyId) {
        return res.status(400).json({ error: "SessionId and propertyId are required" });
      }
      
      const requestId = `property-view-${Date.now()}`;
      logOperation(requestId, 'property_view', { sessionId, propertyId });
      
      // Import the contextService if not at the top of the file
      const { contextService } = await import('./services/contextService.js');
      
      // Record the property view
      contextService.recordPropertyView(sessionId, propertyId);
      
      return res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Failed to record property view', error);
      return res.status(500).json({ error: "Failed to record property view" });
    }
  });
  
  app.post("/api/context/comparison", async (req: Request, res: Response) => {
    try {
      const { sessionId, propertyId, action } = req.body;
      
      if (!sessionId || !propertyId) {
        return res.status(400).json({ error: "SessionId and propertyId are required" });
      }
      
      const requestId = `comparison-${Date.now()}`;
      logOperation(requestId, 'property_comparison', { sessionId, propertyId, action });
      
      // Import the contextService if not at the top of the file
      const { contextService } = await import('./services/contextService.js');
      
      // Add or remove from comparison based on action
      if (action === 'add') {
        contextService.addToComparison(sessionId, propertyId);
      } else {
        // Remove from comparison
        const sessionContext = contextService.getContext(sessionId);
        const updatedComparison = sessionContext.searchContext.comparedProperties.filter(id => id !== propertyId);
        sessionContext.searchContext.comparedProperties = updatedComparison;
      }
      
      return res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Failed to update comparison', error);
      return res.status(500).json({ error: "Failed to update comparison" });
    }
  });
  
  app.post("/api/context/booking-attempt", async (req: Request, res: Response) => {
    try {
      const { sessionId, propertyId, completed } = req.body;
      
      if (!sessionId || !propertyId) {
        return res.status(400).json({ error: "SessionId and propertyId are required" });
      }
      
      const requestId = `booking-attempt-${Date.now()}`;
      logOperation(requestId, 'booking_attempt', { sessionId, propertyId, completed });
      
      // Import the contextService if not at the top of the file
      const { contextService } = await import('./services/contextService.js');
      
      // Record the booking attempt
      contextService.recordBookingAttempt(sessionId, propertyId, !!completed);
      
      return res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Failed to record booking attempt', error);
      return res.status(500).json({ error: "Failed to record booking attempt" });
    }
  });

  // AI Chat endpoint
  app.post("/api/chat", analyticsMiddleware.trackSearch, async (req: Request, res: Response) => {
    const requestId = `chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const { message, context, sessionId = `session-${Date.now()}`, debug = false, extractLocation = false } = req.body;
    
    // Check for debug mode header
    const isDebugMode = req.headers['x-debug-mode'] === 'true' || debug === true;
    
    // Generate proactive insights if we have location context
    let proactiveInsights = null;
    let locationDetails = null;
    
    if (context && context.location) {
      try {
        // Get the session context
        const sessionContext = contextService.getContext(sessionId);
        const conversation = getConversationContext(sessionId);
        
        // Get enhanced location details if we have coordinates
        if (context.location.latitude && context.location.longitude) {
          locationDetails = await getLocationDetails(
            context.location.name,
            Number(context.location.latitude),
            Number(context.location.longitude)
          );
          
          // Log location details for debugging
          if (isDebugMode) {
            logger.debug('Enhanced location details', { 
              details: JSON.stringify(locationDetails),
              requestId 
            });
          }
          
          // Generate travel insights
          try {
            proactiveInsights = await getTravelInsights(
              sessionId,
              {
                ...conversation.context,
                location: {
                  name: context.location.name,
                  lat: Number(context.location.latitude),
                  lng: Number(context.location.longitude)
                },
                dateRange: context.dateRange
              },
              sessionContext.searchContext
            );
            
            // Log insights for debugging
            if (isDebugMode) {
              logger.debug('Generated proactive insights', { 
                insights: JSON.stringify(proactiveInsights),
                requestId 
              });
            }
          } catch (insightError) {
            logger.error('Failed to generate travel insights', {
              error: insightError instanceof Error ? insightError.message : 'Unknown error',
              requestId
            });
          }
        }
      } catch (enhancementError) {
        logger.error('Error in enhanced chat preprocessing', {
          error: enhancementError instanceof Error ? enhancementError.message : 'Unknown error',
          requestId
        });
        // Continue with regular chat processing even if enhancement fails
      }
    }

    if (!message) {
      return res.status(400).json({ error: "Message is required" });
    }

    try {
      // Set up SSE headers
      res.setHeader("Content-Type", "text/event-stream");
      res.setHeader("Cache-Control", "no-cache");
      res.setHeader("Connection", "keep-alive");

      // Get conversation context using enhanced context service (now imported at top level)
      const sessionContext = contextService.getContext(sessionId);
      const conversation = getConversationContext(sessionId);
      
      // If we received additional context details, update the session context
      if (context) {
        // Update location context if provided
        if (context.location) {
          contextService.addSearch(
            sessionId,
            context.location.name || 'Unknown location',
            {
              lat: context.location.latitude || 0,
              lng: context.location.longitude || 0
            },
            context.dateRange,
            context.guests || context.guestCount,
            context.rooms
          );
        }
        
        // Update filter preferences if provided
        if (context.filters || context.preferences) {
          const filters = context.filters || context.preferences || {};
          contextService.updateFilters(sessionId, {
            priceRange: filters.priceRange,
            amenities: filters.amenities || [],
            propertyTypes: filters.propertyTypes || [],
            rating: filters.rating
          });
        }
      }

      // Add new message to history
      addMessageToConversation(sessionId, {
        role: 'user',
        content: message
      });

      // Check if we need to summarize with improved error handling
      try {
        if (needsSummarization(conversation.context, conversation.messages.length)) {
          const updatedContext = await summarizeConversation(
            conversation.messages,
            conversation.context
          );
          
          // Update the context in our persistent store
          contextService.updateConversationContext(sessionId, updatedContext);
        }
      } catch (summaryError) {
        // Log but don't fail if summarization fails
        logger.error('Failed to summarize conversation', { 
          error: summaryError instanceof Error ? summaryError.message : 'Unknown error',
          sessionId,
          requestId
        });
        // Continue with existing context
      }

      // Prepare chat context with enhanced session data
      const chatContext = {
        conversation: conversation.context,
        messages: conversation.messages,
        properties: context?.properties,
        searchContext: sessionContext.searchContext,
        userPreferences: sessionContext.userPreferences
      };

      // Process chat stream with request object for location detection
      const chatStream = handleChatStream(message, chatContext, req);
      
      // Variable to track if we've sent any response
      let responseSent = false;
      
      // Enhanced location extraction for both test and production use
      try {
        if (extractLocation) {
          logger.info('Attempting to extract location from message', {
            messageLength: message.length,
            hasPotentialLocation: message.includes("in ") || 
                                  message.includes("at ") || 
                                  message.includes("near ") || 
                                  message.includes("around "),
            requestId
          });
  
          // Enhanced regex to extract location after common prepositions and verbs
          const locationRegexPatterns = [
            // Main city/location patterns
            /in\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i,         // "in Miami", "in San Francisco near"
            /to\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i,         // "to Chicago", "to San Francisco near"
            /at\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i,         // "at Orlando", "at San Francisco near"
            /near\s+([A-Za-z\s]+)(?:$|,|\.|!|\?)/i,            // "near Seattle"
            /around\s+([A-Za-z\s]+)(?:$|,|\.|!|\?)/i,          // "around Boston"
            
            // Show me/find patterns
            /show\s+me\s+(?:[\w\s]+\s+)?(?:in|at|near)?\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i, // "show me hotels in San Francisco"
            /show\s+(?:[\w\s]+\s+)?(?:in|at|near)?\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i,      // "show hotels in San Francisco"
            
            // Action-based patterns
            /visiting\s+([A-Za-z\s]+)(?:$|,|\.|!|\?)/i,        // "visiting New York"
            /traveling\s+to\s+([A-Za-z\s]+)(?:$|,|\.|!|\?)/i,  // "traveling to Paris"
            /going\s+to\s+([A-Za-z\s]+)(?:$|,|\.|!|\?)/i,      // "going to London"
            /visit\s+([A-Za-z\s]+)(?:$|,|\.|!|\?)/i,           // "visit Hawaii"
            
            // Accommodation-specific patterns
            /book\s+(?:a\s+)?(?:hotel\s+)?(?:in|at|near)\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i, // "book a hotel in Paris"
            /find\s+(?:a\s+)?(?:hotel|place|room|accommodation)\s+(?:in|at|near)\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i, // "find a hotel in Dubai"
            /stay\s+(?:in|at|near)\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i, // "stay in Barcelona"
            /hotels?\s+(?:in|at|near)\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i, // "hotels in Tokyo"
            /accommodation\s+(?:in|at|near)\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i, // "accommodation in Sydney"
            /places?\s+(?:to\s+stay\s+)?(?:in|at|near)\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i, // "places to stay in Rome"
            
            // Trip planning patterns
            /planning\s+(?:a\s+)?(?:trip|visit|vacation|holiday)\s+(?:to|in)\s+([A-Za-z\s]+)(?:$|,|\.|!|\?)/i, // "planning a trip to Hawaii"
            /(?:trip|vacation|holiday)\s+(?:to|in)\s+([A-Za-z\s]+)(?:$|,|\.|!|\?)/i, // "vacation in Cancun"
            
            // Fallback pattern for location in sentence
            /\b(?:San Francisco|New York|Los Angeles|Miami Beach|Chicago|Las Vegas|Orlando|Seattle|Boston|London|Paris|Tokyo|Sydney|Rome|Barcelona|Dubai|Bangkok|Singapore|Hong Kong|Berlin|Amsterdam|Madrid|Cancun|Honolulu|Hawaii|Denver|Phoenix)\b/i  // Major cities
          ];
          
          // Try each pattern until we find a match
          let locationName = null;
          for (const pattern of locationRegexPatterns) {
            const match = message.match(pattern);
            
            // Handle both patterns with capture groups and direct matches (for fallback)
            if (match) {
              if (match[1]) {
                // Patterns with capture groups
                locationName = match[1].trim();
                break;
              } else if (pattern.toString().includes('Francisco|New York')) {
                // Fallback pattern (direct match without capture groups)
                locationName = match[0].trim();
                break;
              }
            }
          }
          
          if (locationName) {
            // Enhanced location lookup with more accurate city data
            const locationMap: Record<string, {name: string, lat: number, lng: number, placeType: string}> = {
              'new york': { name: 'New York', lat: 40.7128, lng: -74.0060, placeType: 'locality' },
              'nyc': { name: 'New York City', lat: 40.7128, lng: -74.0060, placeType: 'locality' },
              'manhattan': { name: 'Manhattan', lat: 40.7831, lng: -73.9712, placeType: 'sublocality' },
              'los angeles': { name: 'Los Angeles', lat: 34.0522, lng: -118.2437, placeType: 'locality' },
              'la': { name: 'Los Angeles', lat: 34.0522, lng: -118.2437, placeType: 'locality' },
              'chicago': { name: 'Chicago', lat: 41.8781, lng: -87.6298, placeType: 'locality' },
              'miami': { name: 'Miami', lat: 25.7617, lng: -80.1918, placeType: 'locality' },
              'miami beach': { name: 'Miami Beach', lat: 25.7917, lng: -80.1300, placeType: 'locality' },
              'san francisco': { name: 'San Francisco', lat: 37.7749, lng: -122.4194, placeType: 'locality' },
              'sf': { name: 'San Francisco', lat: 37.7749, lng: -122.4194, placeType: 'locality' },
              'las vegas': { name: 'Las Vegas', lat: 36.1699, lng: -115.1398, placeType: 'locality' },
              'orlando': { name: 'Orlando', lat: 28.5383, lng: -81.3792, placeType: 'locality' },
              'seattle': { name: 'Seattle', lat: 47.6062, lng: -122.3321, placeType: 'locality' },
              'boston': { name: 'Boston', lat: 42.3601, lng: -71.0589, placeType: 'locality' },
              'london': { name: 'London', lat: 51.5074, lng: -0.1278, placeType: 'locality' },
              'paris': { name: 'Paris', lat: 48.8566, lng: 2.3522, placeType: 'locality' },
              'tokyo': { name: 'Tokyo', lat: 35.6762, lng: 139.6503, placeType: 'locality' },
              'sydney': { name: 'Sydney', lat: -33.8688, lng: 151.2093, placeType: 'locality' },
              'rome': { name: 'Rome', lat: 41.9028, lng: 12.4964, placeType: 'locality' },
              'barcelona': { name: 'Barcelona', lat: 41.3851, lng: 2.1734, placeType: 'locality' },
              'dubai': { name: 'Dubai', lat: 25.2048, lng: 55.2708, placeType: 'locality' },
              'bangkok': { name: 'Bangkok', lat: 13.7563, lng: 100.5018, placeType: 'locality' },
              'singapore': { name: 'Singapore', lat: 1.3521, lng: 103.8198, placeType: 'locality' },
              'hong kong': { name: 'Hong Kong', lat: 22.3193, lng: 114.1694, placeType: 'locality' },
              'berlin': { name: 'Berlin', lat: 52.5200, lng: 13.4050, placeType: 'locality' },
              'amsterdam': { name: 'Amsterdam', lat: 52.3676, lng: 4.9041, placeType: 'locality' },
              'madrid': { name: 'Madrid', lat: 40.4168, lng: -3.7038, placeType: 'locality' },
              'cancun': { name: 'Cancun', lat: 21.1619, lng: -86.8515, placeType: 'locality' },
              'honolulu': { name: 'Honolulu', lat: 21.3069, lng: -157.8583, placeType: 'locality' },
              'hawaii': { name: 'Hawaii', lat: 19.8968, lng: -155.5828, placeType: 'administrative_area_level_1' },
              'denver': { name: 'Denver', lat: 39.7392, lng: -104.9903, placeType: 'locality' },
              'phoenix': { name: 'Phoenix', lat: 33.4484, lng: -112.0740, placeType: 'locality' }
            };
            
            const normalizedLocationName = locationName.toLowerCase();
            const locationData = locationMap[normalizedLocationName] || { 
              name: locationName,
              lat: 40.7128, 
              lng: -74.0060, 
              placeType: 'locality' 
            };
            
            const enhancedLocation = {
              type: 'location',
              data: {
                name: locationData.name,
                lat: locationData.lat,
                lng: locationData.lng,
                placeType: locationData.placeType
              }
            };
            
            logger.info('Successfully extracted location', {
              location: locationData.name,
              coordinates: `${locationData.lat},${locationData.lng}`,
              requestId
            });
            
            // Send the location as the first response
            res.write(`data: ${JSON.stringify(enhancedLocation)}\n\n`);
            // Mark that we've sent a response
            responseSent = true;
            
            // Update conversation context with the detected location
            if (conversation && conversation.context) {
              conversation.context.location = {
                name: locationData.name,
                lat: locationData.lat,
                lng: locationData.lng,
                placeType: locationData.placeType
              };
              
              // Save the updated context
              contextService.updateConversationContext(sessionId, conversation.context);
            }
          } else {
            logger.warn('No location found in message', {
              message: message.substring(0, 100),
              requestId
            });
            
            // Send helpful suggestions instead of a dead-end message
            const inspirationalResponse = {
              type: 'text',
              data: `I'd love to help you explore amazing destinations! 🌍✨ Here are some popular options:\n\n**🏖️ Beach Destinations:**\n• Miami Beach - Art Deco & vibrant nightlife [ACTION:LOCATION|Miami Beach|{"lat": 25.7907, "lng": -80.1300}]\n• Malibu - Stunning coastline & luxury resorts [ACTION:LOCATION|Malibu|{"lat": 34.0259, "lng": -118.7798}]\n• Key West - Tropical paradise & sunset views [ACTION:LOCATION|Key West|{"lat": 24.5557, "lng": -81.7826}]\n\n**🏙️ City Adventures:**\n• New York City - Broadway shows & world-class dining [ACTION:LOCATION|New York City|{"lat": 40.7128, "lng": -74.0060}]\n• San Francisco - Golden Gate Bridge & tech culture [ACTION:LOCATION|San Francisco|{"lat": 37.7749, "lng": -122.4194}]\n• Chicago - Architecture & deep-dish pizza [ACTION:LOCATION|Chicago|{"lat": 41.8781, "lng": -87.6298}]\n\n**🏔️ Mountain Escapes:**\n• Aspen - Skiing & mountain luxury [ACTION:LOCATION|Aspen|{"lat": 39.1911, "lng": -106.8175}]\n• Park City - Year-round outdoor adventures [ACTION:LOCATION|Park City|{"lat": 40.6461, "lng": -111.4980}]\n• Gatlinburg - Smoky Mountains & cabins [ACTION:LOCATION|Gatlinburg|{"lat": 35.7143, "lng": -83.5102}]\n\n**✨ Or tell me:**\n• What type of experience are you looking for?\n• Any specific activities you enjoy?\n• Beach, city, mountains, or something else?\n\nI'm here to help you find the perfect getaway! 🎯`
            };
            res.write(`data: ${JSON.stringify(inspirationalResponse)}\n\n`);
          }
        }
      } catch (e) {
        logger.error('Error extracting test location', {
          error: e instanceof Error ? e.message : String(e),
          requestId
        });
        
        // Send an error response
        const errorResponse = {
          type: 'error',
          data: {
            message: "There was an error processing your location request.",
            details: e instanceof Error ? e.message : "Unknown error"
          }
        };
        res.write(`data: ${JSON.stringify(errorResponse)}\n\n`);
        res.write("data: [DONE]\n\n");
        res.end();
        return;
      }

      // Helper function to filter out log messages
      const isLogMessage = (item: any): boolean => {
        if (typeof item === 'string') {
          return item.startsWith('Initialize') || 
                 item.startsWith('Added search') ||
                 item.startsWith('Recorded property') || 
                 item.startsWith('Updated filters') ||
                 item.startsWith('Added property to comparison') ||
                 item.startsWith('Recorded booking attempt') ||
                 item.startsWith('Updated conversation context') ||
                 item.startsWith('Added message');
        } else if (typeof item === 'object' && item !== null && 'data' in item && typeof item.data === 'string') {
          return item.data.startsWith('Initialize') || 
                 item.data.startsWith('Added search') ||
                 item.data.startsWith('Recorded property') || 
                 item.data.startsWith('Updated filters') ||
                 item.data.startsWith('Added property to comparison') ||
                 item.data.startsWith('Recorded booking attempt') ||
                 item.data.startsWith('Updated conversation context') ||
                 item.data.startsWith('Added message');
        }
        return false;
      };

      for await (const chunk of chatStream) {
        if (chunk) {
          // First, check if this chunk is a log message that should be filtered
          if (isLogMessage(chunk)) {
            // Safe substring extraction function
            const safeSubstring = (text: any, length: number): string => {
              if (typeof text === 'string') {
                return text.substring(0, length);
              }
              return String(text).substring(0, length);
            };
            
            logger.debug('Filtering log message from chat stream', {
              message: typeof chunk === 'string' 
                ? safeSubstring(chunk, 50)
                : (typeof chunk === 'object' && chunk !== null && 'data' in chunk)
                  ? safeSubstring(chunk.data, 50)
                  : 'Unknown format',
              requestId
            });
            continue;
          }
          
          // Ensure chunk is a valid ChatResponse object before sending
          if (typeof chunk === 'object' && 'type' in chunk && chunk.type && 'data' in chunk) {
            // Safe type casting for better type checking
            const responseType = chunk.type as 'text' | 'properties' | 'location' | 'action' | 'error';
            
            // Type validation and runtime validation
            const isValidResponseType = ['text', 'properties', 'location', 'action', 'error'].includes(responseType);
            
            if (!isValidResponseType) {
              logger.warn('Invalid response type detected', {
                type: responseType,
                requestId
              });
              continue;
            }
            
            // Create a type-safe version of the response
            const response = chunk as TypedChatResponse;
            
            // Additional validation for location type - use a type guard pattern
            let hasValidLocationData = false;
            let locationName = "";
            let locationLat = 0;
            let locationLng = 0;
            let locationPlaceType = "";
            
            // Handle location responses with strong type safety
            if (responseType === 'location') {
              const locationData = response.data;
              
              if (locationData && typeof locationData === 'object' &&
                  'name' in locationData && typeof locationData.name === 'string' &&
                  'lat' in locationData && typeof locationData.lat === 'number' &&
                  'lng' in locationData && typeof locationData.lng === 'number') {
                
                hasValidLocationData = true;
                locationName = locationData.name;
                locationLat = locationData.lat;
                locationLng = locationData.lng;
                locationPlaceType = locationData.placeType || 'unknown';
              } else {
                logger.error('Invalid location data format for location type', {
                  data: typeof locationData,
                  hasName: locationData && typeof locationData === 'object' ? 'name' in locationData : false,
                  hasLat: locationData && typeof locationData === 'object' ? 'lat' in locationData : false,
                  hasLng: locationData && typeof locationData === 'object' ? 'lng' in locationData : false,
                  requestId
                });
                continue;
              }
            } 
            // Handle location actions with strong type safety
            else if (responseType === 'action') {
              const actionData = response.data;
              
              if (actionData && typeof actionData === 'object' && 
                  'type' in actionData && actionData.type === 'location' &&
                  'data' in actionData && typeof actionData.data === 'object' &&
                  actionData.data !== null) {
                
                const locationData = actionData.data;
                
                if ('name' in locationData && typeof locationData.name === 'string' &&
                    'lat' in locationData && typeof locationData.lat === 'number' &&
                    'lng' in locationData && typeof locationData.lng === 'number') {
                  
                  hasValidLocationData = true;
                  locationName = locationData.name;
                  locationLat = locationData.lat;
                  locationLng = locationData.lng;
                  locationPlaceType = locationData.placeType || 'unknown';
                } else {
                  logger.error('Invalid location data in action', {
                    actionType: actionData.type,
                    hasName: locationData && typeof locationData === 'object' ? 'name' in locationData : false,
                    hasLat: locationData && typeof locationData === 'object' ? 'lat' in locationData : false,
                    hasLng: locationData && typeof locationData === 'object' ? 'lng' in locationData : false,
                    requestId
                  });
                  continue;
                }
              }
            }

            // Add assistant messages to history
            if (response.type === 'text') {
              addMessageToConversation(sessionId, {
                role: 'assistant',
                content: response.data
              });
            }
            
            // Process validated location data
            if (hasValidLocationData) {
              logger.info('Location response detected', {
                location: locationName,
                coordinates: `${locationLat},${locationLng}`,
                placeType: locationPlaceType,
                requestId
              });
              
              // Update session context with the location
              contextService.addSearch(
                sessionId,
                locationName,
                {
                  lat: locationLat,
                  lng: locationLng
                },
                conversation.context.dateRange,
                conversation.context.preferences?.guestCount,
                // Use guest count as default for rooms if not available
                conversation.context.preferences?.guestCount || 1
              );
              
              // Enhance location with additional details if available
              if (locationDetails && locationDetails.neighborhoods) {
                // Add enhanced details to the location response
                (response as any).data.enhancedDetails = {
                  neighborhoods: locationDetails.neighborhoods.map(n => n.name),
                  landmarks: locationDetails.landmarks?.map(l => l.name),
                  popularTimes: locationDetails.popularTimes,
                  weatherPattern: locationDetails.weatherPattern
                };
              }
              
              // Validate location coordinates
              if (locationLat && locationLng) {
                const isValidLocation = validateLocationCoordinates(
                  locationName,
                  locationLat,
                  locationLng
                );
                
                if (!isValidLocation) {
                  logger.warn('Potentially incorrect location coordinates detected', {
                    location: locationName,
                    coordinates: `${locationLat},${locationLng}`,
                    requestId
                  });
                }
              }
            }
            
            // Enhance text responses with proactive insights
            if (responseType === 'text' && proactiveInsights) {
              const textResponse = response as TextResponse;
              
              // Get original content (ensuring it's a string)
              let enhancedContent = typeof textResponse.data === 'string' 
                ? textResponse.data 
                : (textResponse.data as any).content || '';
              
              // Add price insights if available
              if (proactiveInsights.pricingInsights.length > 0) {
                const insight = proactiveInsights.pricingInsights[0];
                enhancedContent += `\n\n💡 ${insight.message}`;
              }
              
              // Add event alerts if available
              if (proactiveInsights.eventAlerts.length > 0) {
                const alert = proactiveInsights.eventAlerts[0];
                enhancedContent += `\n\n⚠️ ${alert.message}`;
              }
              
              // Add travel tips if available
              if (proactiveInsights.tips.length > 0) {
                enhancedContent += `\n\n✨ Tip: ${proactiveInsights.tips[0]}`;
              }
              
              // Update the response content based on data structure
              if (typeof textResponse.data === 'string') {
                textResponse.data = enhancedContent;
              } else if (textResponse.data && typeof textResponse.data === 'object') {
                (textResponse.data as any).content = enhancedContent;
              }
            }
            
            // Enhance property responses with alternatives
            if (responseType === 'properties' && proactiveInsights && proactiveInsights.alternativeSuggestions.length > 0) {
              const propertiesResponse = response as PropertiesResponse;
              
              const locationAlternatives = proactiveInsights.alternativeSuggestions
                .filter(s => s.type === 'location')
                .map(s => ({ 
                  name: s.data.name, 
                  reason: s.reason, 
                  lat: s.data.lat, 
                  lng: s.data.lng 
                }));
                
              if (locationAlternatives.length > 0 && propertiesResponse.data) {
                // Add alternatives to the response
                (propertiesResponse.data as any).alternativeLocations = locationAlternatives;
              }
            }

            // Send chunk to client
            res.write(`data: ${JSON.stringify(response)}\n\n`);
            responseSent = true;
          } else {
            logger.warn('Invalid chunk format received', { 
              chunkType: typeof chunk,
              chunk: typeof chunk === 'object' ? JSON.stringify(chunk) : String(chunk).substring(0, 100),
              sessionId,
              requestId: `chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
            });
          }
        }
      }

      // If no response was sent, send a fallback error
      if (!responseSent) {
        const fallbackResponse: ErrorResponse = {
          type: 'error',
          data: {
            message: "I'm sorry, I couldn't process your request properly. Please try again."
          }
        };
        res.write(`data: ${JSON.stringify(fallbackResponse)}\n\n`);
      }

      // End the stream
      res.write("data: [DONE]\n\n");
      res.end();

    } catch (error) {
      // Ensure we handle any unexpected errors effectively
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      // Create more detailed error info for debugging
      const errorDetails = {
        message: errorMessage || 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace available',
        type: error instanceof Error ? error.constructor.name : typeof error,
        context: {
          messageLength: message?.length || 0,
          sessionId,
          hasContextParam: !!context,
          extractLocation: extractLocation === true,
          requestId,
          params: Object.keys(req.body || {}).join(',')
        }
      };

      // Log the error with detailed information
      // Using console.error instead of logger to prevent potential circular errors
      console.error('Chat API Error:', JSON.stringify(errorDetails, null, 2));

      // Error handling
      if (!res.headersSent) {
        // If headers not sent yet, send a JSON error
        res.status(500).json({ 
          error: "Failed to process chat request",
          details: process.env.NODE_ENV === 'development' ? errorDetails : errorMessage,
          requestId
        });
      } else {
        // If headers already sent, continue the stream with an error message
        try {
          const errorResponse = {
            type: 'error',
            data: {
              message: "There was an error processing your request. Please try again.",
              details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
            }
          };
          res.write(`data: ${JSON.stringify(errorResponse)}\n\n`);
          res.write("data: [DONE]\n\n");
          res.end();
        } catch (streamError) {
          // If we can't even write to the stream, log and give up
          console.error('Failed to send error response:', streamError);
        }
      }
    }
  });

  // ===================================
  // Authentication routes
  // ===================================
  
  // Apply authentication middleware to all routes
  // No authentication token middleware is needed as we use Passport.js
  
  // Login endpoint
  app.post("/api/auth/login", loginUser);
  
  // Register endpoint
  app.post("/api/auth/register", registerUser);
  
  // Get test promo code for registration
  app.get("/api/auth/promo-code", getTestPromoCode);
  
  // Get current user (requires auth)
  app.get("/api/auth/me", requireAuth, getCurrentUser);
  
  // User endpoint for frontend auth check is now handled in auth.ts
  // This endpoint is kept for compatibility with old API clients
  app.get("/api/user", (req: Request, res: Response) => {
    // Use passport's isAuthenticated() method
    if (!req.isAuthenticated()) {
      return res.status(401).json({
        success: false,
        message: 'Not authenticated'
      });
    }
    
    // Return user data in the new format
    return res.status(200).json({
      success: true,
      user: req.user
    });
  });
  
  // Get pricing tier based on membership
  app.get("/api/auth/pricing-tier", (req: Request, res: Response) => {
    const membershipType = req.user?.membershipType;
    const siteId = getSiteId(membershipType);
    
    res.json({
      tier: membershipType || 'guest',
      siteId,
      discounts: membershipType === 'premium' ? true : false,
      // Include some marketing message for non-premium users
      message: !membershipType || membershipType !== 'premium' 
        ? "Sign up for premium membership to unlock exclusive discounts and offers!" 
        : null
    });
  });
  
  // ===================================
  // Reservation routes
  // ===================================
  
  // Get user's reservations
  app.get("/api/reservations", requireAuth, async (req: Request, res: Response) => {
    try {
      // Query reservations for the logged-in user
      const userId = req.user?.id;
      
      if (!userId) {
        return res.status(401).json({ 
          success: false,
          message: "Authentication required" 
        });
      }
      
      // Get all user reservations
      const userReservations = await db
        .select({
          id: reservations.id,
          userId: reservations.userId,
          propertyId: reservations.propertyId,
          checkIn: reservations.checkIn,
          checkOut: reservations.checkOut,
          totalAmount: reservations.totalAmount,
          status: reservations.status,
          createdAt: reservations.createdAt,
          propertyName: properties.name
        })
        .from(reservations)
        .where(eq(reservations.userId, userId))
        .leftJoin(properties, eq(reservations.propertyId, properties.id))
        .orderBy(desc(reservations.createdAt));
      
      // Format the reservations with property names
      const formattedReservations = userReservations.map(reservation => ({
        id: reservation.id,
        userId: reservation.userId,
        propertyId: reservation.propertyId,
        checkIn: reservation.checkIn,
        checkOut: reservation.checkOut,
        totalPrice: reservation.totalAmount.toString(),
        status: reservation.status,
        createdAt: reservation.createdAt,
        propertyName: reservation.propertyName
      }));
      
      return res.status(200).json({
        success: true,
        reservations: formattedReservations
      });
    } catch (error) {
      logger.error('Error fetching reservations', { error: error instanceof Error ? error.message : 'Unknown error' });
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch reservations'
      });
    }
  });
  
  // Get a specific reservation
  app.get("/api/reservations/:id", requireAuth, async (req: Request, res: Response) => {
    try {
      const reservationId = parseInt(req.params.id);
      const userId = req.user?.id;
      
      if (isNaN(reservationId)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid reservation ID'
        });
      }
      
      // Get the reservation, ensuring it belongs to this user
      const reservationResult = await db
        .select({
          id: reservations.id,
          userId: reservations.userId,
          propertyId: reservations.propertyId,
          checkIn: reservations.checkIn,
          checkOut: reservations.checkOut,
          totalAmount: reservations.totalAmount,
          status: reservations.status,
          createdAt: reservations.createdAt,
          notes: reservations.notes,
          specialRequests: reservations.specialRequests,
          confirmationCode: reservations.confirmationCode,
          propertyName: properties.name
        })
        .from(reservations)
        .where(
          sql`${reservations.id} = ${reservationId} AND ${reservations.userId} = ${userId}`
        )
        .leftJoin(properties, eq(reservations.propertyId, properties.id))
        .limit(1);
      
      if (!reservationResult.length) {
        return res.status(404).json({
          success: false,
          message: 'Reservation not found'
        });
      }
      
      const reservation = reservationResult[0];
      
      return res.status(200).json({
        success: true,
        reservation: {
          ...reservation,
          totalPrice: reservation.totalAmount.toString()
        }
      });
    } catch (error) {
      logger.error('Error fetching reservation', { error: error instanceof Error ? error.message : 'Unknown error' });
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch reservation'
      });
    }
  });
  
  // Cancel a reservation
  app.post("/api/reservations/:id/cancel", requireAuth, async (req: Request, res: Response) => {
    try {
      const reservationId = parseInt(req.params.id);
      const userId = req.user?.id;
      
      if (isNaN(reservationId)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid reservation ID'
        });
      }
      
      // Get the reservation, ensuring it belongs to this user
      const reservationResult = await db
        .select({
          id: reservations.id,
          status: reservations.status
        })
        .from(reservations)
        .where(
          sql`${reservations.id} = ${reservationId} AND ${reservations.userId} = ${userId}`
        )
        .limit(1);
      
      if (!reservationResult.length) {
        return res.status(404).json({
          success: false,
          message: 'Reservation not found'
        });
      }
      
      const reservation = reservationResult[0];
      
      // Check if the reservation can be cancelled
      if (reservation.status !== 'confirmed' && reservation.status !== 'pending') {
        return res.status(400).json({
          success: false,
          message: `Cannot cancel a reservation with status: ${reservation.status}`
        });
      }
      
      // Update the reservation status to cancelled
      await db
        .update(reservations)
        .set({
          status: 'cancelled',
          updatedAt: new Date()
        })
        .where(eq(reservations.id, reservationId));
      
      return res.status(200).json({
        success: true,
        message: 'Reservation cancelled successfully'
      });
    } catch (error) {
      logger.error('Error cancelling reservation', { error: error instanceof Error ? error.message : 'Unknown error' });
      return res.status(500).json({
        success: false,
        message: 'Failed to cancel reservation'
      });
    }
  });
  
  // Test result API endpoints
  // Test management routes - Protected with admin-only access
  app.post("/api/tests/results", requireAdmin, saveTestResult);
  
  app.get("/api/tests/results", requireAdmin, getTestResults);
  
  app.get("/api/tests/results/:id", requireAdmin, getTestResultById);
  
  app.get("/api/tests/metrics", requireAdmin, getTestMetrics);
  
  app.delete("/api/tests/results/:id", requireAdmin, deleteTestResult);
  
  // Test Runner API endpoints
  app.post("/api/tests/run", requireAdmin, startTestRun);
  
  app.get("/api/tests/run/:runId", requireAdmin, getTestRunStatus);
  
  app.delete("/api/tests/run/:runId", requireAdmin, stopTestRun);
  
  app.get("/api/tests/runs", requireAdmin, listTestRuns);
  
  // Admin API endpoints
  // User management
  app.get("/api/admin/users", requireAdmin, getUsers);
  app.post("/api/admin/users", requireAdmin, createUser);
  app.put("/api/admin/users/:id", requireAdmin, updateUser);
  app.delete("/api/admin/users/:id", requireAdmin, deleteUser);
  
  // Promo code management
  app.get("/api/admin/promo-codes", requireAdmin, getPromoCodes);
  app.post("/api/admin/promo-codes", requireAdmin, createPromoCode);
  app.put("/api/admin/promo-codes/:id", requireAdmin, updatePromoCode);
  app.put("/api/admin/promo-codes/:id/toggle", requireAdmin, togglePromoCodeStatus);
  app.delete("/api/admin/promo-codes/:id", requireAdmin, deletePromoCode);
  app.post("/api/admin/promo-codes/generate-test", requireAdmin, generateTestPromoCode);
  app.post("/api/admin/promo-codes/validate", requireAdmin, validatePromoCode);
  app.post("/api/admin/promo-codes/apply", requireAdmin, applyPromoCode);
  
  // Development endpoint for generating test promo code
  app.get("/api/dev/generate-test-promo", async (req, res) => {
    if (process.env.NODE_ENV === 'production') {
      return res.status(404).json({ message: 'Not found in production' });
    }
    
    try {
      // Check if we already have a test promo code
      const [existingPromo] = await db.select()
        .from(promoCodes)
        .where(eq(promoCodes.code, 'TEST2025'))
        .limit(1);
      
      if (existingPromo) {
        // If it exists but is not active, reactivate it
        if (!existingPromo.isActive) {
          await db.update(promoCodes)
            .set({ 
              isActive: true,
              updatedAt: new Date() 
            })
            .where(eq(promoCodes.id, existingPromo.id));
          
          return res.status(200).json({
            message: 'Test promo code reactivated',
            promoCode: 'TEST2025'
          });
        }
        
        // Return existing promo code
        return res.status(200).json({
          message: 'Using existing test promo code',
          promoCode: 'TEST2025'
        });
      }
      
      // Create a new test promo code
      const [newPromo] = await db.insert(promoCodes)
        .values({
          code: 'TEST2025',
          description: 'Test promo code for development',
          membershipType: 'premium',
          discountPercent: 100,
          maxUsages: 100,
          usageCount: 0,
          isActive: true,
          createdBy: 1, // System user
          createdAt: new Date(),
          updatedAt: new Date()
        })
        .returning();
      
      return res.status(201).json({
        message: 'Test promo code created',
        promoCode: newPromo.code
      });
    } catch (error) {
      console.error('Error generating test promo code:', error);
      return res.status(500).json({
        message: 'Failed to generate test promo code',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });
  
  // Schedule cleanup of old test runs every hour
  setInterval(cleanupTestRuns, 60 * 60 * 1000);
  
  // Simple test endpoint for validating chat functionality
  app.post("/api/test-chat", async (req: Request, res: Response) => {
    const { message } = req.body;
    
    if (!message) {
      return res.status(400).json({ error: "Message is required" });
    }
    
    try {
      // Test the location extraction logic
      const testResponse = await fetch(`http://localhost:${process.env.PORT || 5000}/api/chat`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message,
          sessionId: `test-${Date.now()}`,
          extractLocation: true
        })
      });
      
      if (!testResponse.ok) {
        throw new Error(`API returned ${testResponse.status}`);
      }
      
      const reader = testResponse.body?.getReader();
      if (!reader) {
        throw new Error('No response stream');
      }
      
      const decoder = new TextDecoder();
      let content = '';
      let hasLocation = false;
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(5).trim();
            if (!data || data === '[DONE]' || data === 'DONE') continue;
            
            try {
              const parsed = JSON.parse(data);
              if (parsed.type === 'text') {
                content += parsed.data;
              } else if (parsed.type === 'location') {
                hasLocation = true;
              }
            } catch (e) {
              // Skip parse errors
            }
          }
        }
      }
      
      return res.json({
        success: true,
        message,
        response: content,
        hasLocation,
        hasHelpfulContent: content.length > 50 && !content.includes("couldn't detect"),
        isDeadEnd: content.includes("couldn't detect") && content.length < 100
      });
      
    } catch (error) {
      return res.status(500).json({
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // AI Chat endpoint
  app.post("/api/chat", analyticsMiddleware.trackSearch, async (req: Request, res: Response) => {
    const requestId = `chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const { message, context, sessionId = `session-${Date.now()}`, debug = false, extractLocation = false } = req.body;
    
    // Check for debug mode header
    const isDebugMode = req.headers['x-debug-mode'] === 'true' || debug === true;
    
    // Generate proactive insights if we have location context
    let proactiveInsights = null;
    let locationDetails = null;
    
    if (context && context.location) {
      try {
        // Get the session context
        const sessionContext = contextService.getContext(sessionId);
        const conversation = getConversationContext(sessionId);
        
        // Get enhanced location details if we have coordinates
        if (context.location.latitude && context.location.longitude) {
          locationDetails = await getLocationDetails(
            context.location.name,
            Number(context.location.latitude),
            Number(context.location.longitude)
          );
          
          // Log location details for debugging
          if (isDebugMode) {
            logger.debug('Enhanced location details', { 
              details: JSON.stringify(locationDetails),
              requestId 
            });
          }
          
          // Generate travel insights
          try {
            proactiveInsights = await getTravelInsights(
              sessionId,
              {
                ...conversation.context,
                location: {
                  name: context.location.name,
                  lat: Number(context.location.latitude),
                  lng: Number(context.location.longitude)
                },
                dateRange: context.dateRange
              },
              sessionContext.searchContext
            );
            
            // Log insights for debugging
            if (isDebugMode) {
              logger.debug('Generated proactive insights', { 
                insights: JSON.stringify(proactiveInsights),
                requestId 
              });
            }
          } catch (insightError) {
            logger.error('Failed to generate travel insights', {
              error: insightError instanceof Error ? insightError.message : 'Unknown error',
              requestId
            });
          }
        }
      } catch (enhancementError) {
        logger.error('Error in enhanced chat preprocessing', {
          error: enhancementError instanceof Error ? enhancementError.message : 'Unknown error',
          requestId
        });
        // Continue with regular chat processing even if enhancement fails
      }
    }

    if (!message) {
      return res.status(400).json({ error: "Message is required" });
    }

    try {
      // Set up SSE headers
      res.setHeader("Content-Type", "text/event-stream");
      res.setHeader("Cache-Control", "no-cache");
      res.setHeader("Connection", "keep-alive");

      // Get conversation context using enhanced context service (now imported at top level)
      const sessionContext = contextService.getContext(sessionId);
      const conversation = getConversationContext(sessionId);
      
      // If we received additional context details, update the session context
      if (context) {
        // Update location context if provided
        if (context.location) {
          contextService.addSearch(
            sessionId,
            context.location.name || 'Unknown location',
            {
              lat: context.location.latitude || 0,
              lng: context.location.longitude || 0
            },
            context.dateRange,
            context.guests || context.guestCount,
            context.rooms
          );
        }
        
        // Update filter preferences if provided
        if (context.filters || context.preferences) {
          const filters = context.filters || context.preferences || {};
          contextService.updateFilters(sessionId, {
            priceRange: filters.priceRange,
            amenities: filters.amenities || [],
            propertyTypes: filters.propertyTypes || [],
            rating: filters.rating
          });
        }
      }

      // Add new message to history
      addMessageToConversation(sessionId, {
        role: 'user',
        content: message
      });

      // Check if we need to summarize with improved error handling
      try {
        if (needsSummarization(conversation.context, conversation.messages.length)) {
          const updatedContext = await summarizeConversation(
            conversation.messages,
            conversation.context
          );
          
          // Update the context in our persistent store
          contextService.updateConversationContext(sessionId, updatedContext);
        }
      } catch (summaryError) {
        // Log but don't fail if summarization fails
        logger.error('Failed to summarize conversation', { 
          error: summaryError instanceof Error ? summaryError.message : 'Unknown error',
          sessionId,
          requestId
        });
        // Continue with existing context
      }

      // Prepare chat context with enhanced session data
      const chatContext = {
        conversation: conversation.context,
        messages: conversation.messages,
        properties: context?.properties,
        searchContext: sessionContext.searchContext,
        userPreferences: sessionContext.userPreferences
      };

      // Process chat stream with request object for location detection
      const chatStream = handleChatStream(message, chatContext, req);
      
      // Variable to track if we've sent any response
      let responseSent = false;
      
      // Enhanced location extraction for both test and production use
      try {
        if (extractLocation) {
          logger.info('Attempting to extract location from message', {
            messageLength: message.length,
            hasPotentialLocation: message.includes("in ") || 
                                  message.includes("at ") || 
                                  message.includes("near ") || 
                                  message.includes("around "),
            requestId
          });
  
          // Enhanced regex to extract location after common prepositions and verbs
          const locationRegexPatterns = [
            // Main city/location patterns
            /in\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i,         // "in Miami", "in San Francisco near"
            /to\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i,         // "to Chicago", "to San Francisco near"
            /at\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i,         // "at Orlando", "at San Francisco near"
            /near\s+([A-Za-z\s]+)(?:$|,|\.|!|\?)/i,            // "near Seattle"
            /around\s+([A-Za-z\s]+)(?:$|,|\.|!|\?)/i,          // "around Boston"
            
            // Show me/find patterns
            /show\s+me\s+(?:[\w\s]+\s+)?(?:in|at|near)?\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i, // "show me hotels in San Francisco"
            /show\s+(?:[\w\s]+\s+)?(?:in|at|near)?\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i,      // "show hotels in San Francisco"
            
            // Action-based patterns
            /visiting\s+([A-Za-z\s]+)(?:$|,|\.|!|\?)/i,        // "visiting New York"
            /traveling\s+to\s+([A-Za-z\s]+)(?:$|,|\.|!|\?)/i,  // "traveling to Paris"
            /going\s+to\s+([A-Za-z\s]+)(?:$|,|\.|!|\?)/i,      // "going to London"
            /visit\s+([A-Za-z\s]+)(?:$|,|\.|!|\?)/i,           // "visit Hawaii"
            
            // Accommodation-specific patterns
            /book\s+(?:a\s+)?(?:hotel\s+)?(?:in|at|near)\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i, // "book a hotel in Paris"
            /find\s+(?:a\s+)?(?:hotel|place|room|accommodation)\s+(?:in|at|near)\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i, // "find a hotel in Dubai"
            /stay\s+(?:in|at|near)\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i, // "stay in Barcelona"
            /hotels?\s+(?:in|at|near)\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i, // "hotels in Tokyo"
            /accommodation\s+(?:in|at|near)\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i, // "accommodation in Sydney"
            /places?\s+(?:to\s+stay\s+)?(?:in|at|near)\s+([A-Za-z\s]+)(?:$|,|\.|!|\?|near)/i, // "places to stay in Rome"
            
            // Trip planning patterns
            /planning\s+(?:a\s+)?(?:trip|visit|vacation|holiday)\s+(?:to|in)\s+([A-Za-z\s]+)(?:$|,|\.|!|\?)/i, // "planning a trip to Hawaii"
            /(?:trip|vacation|holiday)\s+(?:to|in)\s+([A-Za-z\s]+)(?:$|,|\.|!|\?)/i, // "vacation in Cancun"
            
            // Fallback pattern for location in sentence
            /\b(?:San Francisco|New York|Los Angeles|Miami Beach|Chicago|Las Vegas|Orlando|Seattle|Boston|London|Paris|Tokyo|Sydney|Rome|Barcelona|Dubai|Bangkok|Singapore|Hong Kong|Berlin|Amsterdam|Madrid|Cancun|Honolulu|Hawaii|Denver|Phoenix)\b/i  // Major cities
          ];
          
          // Try each pattern until we find a match
          let locationName = null;
          for (const pattern of locationRegexPatterns) {
            const match = message.match(pattern);
            
            // Handle both patterns with capture groups and direct matches (for fallback)
            if (match) {
              if (match[1]) {
                // Patterns with capture groups
                locationName = match[1].trim();
                break;
              } else if (pattern.toString().includes('Francisco|New York')) {
                // Fallback pattern (direct match without capture groups)
                locationName = match[0].trim();
                break;
              }
            }
          }
          
          if (locationName) {
            // Enhanced location lookup with more accurate city data
            const locationMap: Record<string, {name: string, lat: number, lng: number, placeType: string}> = {
              'new york': { name: 'New York', lat: 40.7128, lng: -74.0060, placeType: 'locality' },
              'nyc': { name: 'New York City', lat: 40.7128, lng: -74.0060, placeType: 'locality' },
              'manhattan': { name: 'Manhattan', lat: 40.7831, lng: -73.9712, placeType: 'sublocality' },
              'los angeles': { name: 'Los Angeles', lat: 34.0522, lng: -118.2437, placeType: 'locality' },
              'la': { name: 'Los Angeles', lat: 34.0522, lng: -118.2437, placeType: 'locality' },
              'chicago': { name: 'Chicago', lat: 41.8781, lng: -87.6298, placeType: 'locality' },
              'miami': { name: 'Miami', lat: 25.7617, lng: -80.1918, placeType: 'locality' },
              'miami beach': { name: 'Miami Beach', lat: 25.7917, lng: -80.1300, placeType: 'locality' },
              'san francisco': { name: 'San Francisco', lat: 37.7749, lng: -122.4194, placeType: 'locality' },
              'sf': { name: 'San Francisco', lat: 37.7749, lng: -122.4194, placeType: 'locality' },
              'las vegas': { name: 'Las Vegas', lat: 36.1699, lng: -115.1398, placeType: 'locality' },
              'orlando': { name: 'Orlando', lat: 28.5383, lng: -81.3792, placeType: 'locality' },
              'seattle': { name: 'Seattle', lat: 47.6062, lng: -122.3321, placeType: 'locality' },
              'boston': { name: 'Boston', lat: 42.3601, lng: -71.0589, placeType: 'locality' },
              'london': { name: 'London', lat: 51.5074, lng: -0.1278, placeType: 'locality' },
              'paris': { name: 'Paris', lat: 48.8566, lng: 2.3522, placeType: 'locality' },
              'tokyo': { name: 'Tokyo', lat: 35.6762, lng: 139.6503, placeType: 'locality' },
              'sydney': { name: 'Sydney', lat: -33.8688, lng: 151.2093, placeType: 'locality' },
              'rome': { name: 'Rome', lat: 41.9028, lng: 12.4964, placeType: 'locality' },
              'barcelona': { name: 'Barcelona', lat: 41.3851, lng: 2.1734, placeType: 'locality' },
              'dubai': { name: 'Dubai', lat: 25.2048, lng: 55.2708, placeType: 'locality' },
              'bangkok': { name: 'Bangkok', lat: 13.7563, lng: 100.5018, placeType: 'locality' },
              'singapore': { name: 'Singapore', lat: 1.3521, lng: 103.8198, placeType: 'locality' },
              'hong kong': { name: 'Hong Kong', lat: 22.3193, lng: 114.1694, placeType: 'locality' },
              'berlin': { name: 'Berlin', lat: 52.5200, lng: 13.4050, placeType: 'locality' },
              'amsterdam': { name: 'Amsterdam', lat: 52.3676, lng: 4.9041, placeType: 'locality' },
              'madrid': { name: 'Madrid', lat: 40.4168, lng: -3.7038, placeType: 'locality' },
              'cancun': { name: 'Cancun', lat: 21.1619, lng: -86.8515, placeType: 'locality' },
              'honolulu': { name: 'Honolulu', lat: 21.3069, lng: -157.8583, placeType: 'locality' },
              'hawaii': { name: 'Hawaii', lat: 19.8968, lng: -155.5828, placeType: 'administrative_area_level_1' },
              'denver': { name: 'Denver', lat: 39.7392, lng: -104.9903, placeType: 'locality' },
              'phoenix': { name: 'Phoenix', lat: 33.4484, lng: -112.0740, placeType: 'locality' }
            };
            
            const normalizedLocationName = locationName.toLowerCase();
            const locationData = locationMap[normalizedLocationName] || { 
              name: locationName,
              lat: 40.7128, 
              lng: -74.0060, 
              placeType: 'locality' 
            };
            
            const enhancedLocation = {
              type: 'location',
              data: {
                name: locationData.name,
                lat: locationData.lat,
                lng: locationData.lng,
                placeType: locationData.placeType
              }
            };
            
            logger.info('Successfully extracted location', {
              location: locationData.name,
              coordinates: `${locationData.lat},${locationData.lng}`,
              requestId
            });
            
            // Send the location as the first response
            res.write(`data: ${JSON.stringify(enhancedLocation)}\n\n`);
            // Mark that we've sent a response
            responseSent = true;
            
            // Update conversation context with the detected location
            if (conversation && conversation.context) {
              conversation.context.location = {
                name: locationData.name,
                lat: locationData.lat,
                lng: locationData.lng,
                placeType: locationData.placeType
              };
              
              // Save the updated context
              contextService.updateConversationContext(sessionId, conversation.context);
            }
          } else {
            logger.warn('No location found in message', {
              message: message.substring(0, 100),
              requestId
            });
            
            // Send helpful suggestions instead of a dead-end message
            const inspirationalResponse = {
              type: 'text',
              data: `I'd love to help you explore amazing destinations! 🌍✨ Here are some popular options:\n\n**🏖️ Beach Destinations:**\n• Miami Beach - Art Deco & vibrant nightlife [ACTION:LOCATION|Miami Beach|{"lat": 25.7907, "lng": -80.1300}]\n• Malibu - Stunning coastline & luxury resorts [ACTION:LOCATION|Malibu|{"lat": 34.0259, "lng": -118.7798}]\n• Key West - Tropical paradise & sunset views [ACTION:LOCATION|Key West|{"lat": 24.5557, "lng": -81.7826}]\n\n**🏙️ City Adventures:**\n• New York City - Broadway shows & world-class dining [ACTION:LOCATION|New York City|{"lat": 40.7128, "lng": -74.0060}]\n• San Francisco - Golden Gate Bridge & tech culture [ACTION:LOCATION|San Francisco|{"lat": 37.7749, "lng": -122.4194}]\n• Chicago - Architecture & deep-dish pizza [ACTION:LOCATION|Chicago|{"lat": 41.8781, "lng": -87.6298}]\n\n**🏔️ Mountain Escapes:**\n• Aspen - Skiing & mountain luxury [ACTION:LOCATION|Aspen|{"lat": 39.1911, "lng": -106.8175}]\n• Park City - Year-round outdoor adventures [ACTION:LOCATION|Park City|{"lat": 40.6461, "lng": -111.4980}]\n• Gatlinburg - Smoky Mountains & cabins [ACTION:LOCATION|Gatlinburg|{"lat": 35.7143, "lng": -83.5102}]\n\n**✨ Or tell me:**\n• What type of experience are you looking for?\n• Any specific activities you enjoy?\n• Beach, city, mountains, or something else?\n\nI'm here to help you find the perfect getaway! 🎯`
            };
            res.write(`data: ${JSON.stringify(inspirationalResponse)}\n\n`);
          }
        }
      } catch (e) {
        logger.error('Error extracting test location', {
          error: e instanceof Error ? e.message : String(e),
          requestId
        });
        
        // Send an error response
        const errorResponse = {
          type: 'error',
          data: {
            message: "There was an error processing your location request.",
            details: e instanceof Error ? e.message : "Unknown error"
          }
        };
        res.write(`data: ${JSON.stringify(errorResponse)}\n\n`);
        res.write("data: [DONE]\n\n");
        res.end();
        return;
      }

      // Helper function to filter out log messages
      const isLogMessage = (item: any): boolean => {
        if (typeof item === 'string') {
          return item.startsWith('Initialize') || 
                 item.startsWith('Added search') ||
                 item.startsWith('Recorded property') || 
                 item.startsWith('Updated filters') ||
                 item.startsWith('Added property to comparison') ||
                 item.startsWith('Recorded booking attempt') ||
                 item.startsWith('Updated conversation context') ||
                 item.startsWith('Added message');
        } else if (typeof item === 'object' && item !== null && 'data' in item && typeof item.data === 'string') {
          return item.data.startsWith('Initialize') || 
                 item.data.startsWith('Added search') ||
                 item.data.startsWith('Recorded property') || 
                 item.data.startsWith('Updated filters') ||
                 item.data.startsWith('Added property to comparison') ||
                 item.data.startsWith('Recorded booking attempt') ||
                 item.data.startsWith('Updated conversation context') ||
                 item.data.startsWith('Added message');
        }
        return false;
      };

      for await (const chunk of chatStream) {
        if (chunk) {
          // First, check if this chunk is a log message that should be filtered
          if (isLogMessage(chunk)) {
            // Safe substring extraction function
            const safeSubstring = (text: any, length: number): string => {
              if (typeof text === 'string') {
                return text.substring(0, length);
              }
              return String(text).substring(0, length);
            };
            
            logger.debug('Filtering log message from chat stream', {
              message: typeof chunk === 'string' 
                ? safeSubstring(chunk, 50)
                : (typeof chunk === 'object' && chunk !== null && 'data' in chunk)
                  ? safeSubstring(chunk.data, 50)
                  : 'Unknown format',
              requestId
            });
            continue;
          }
          
          // Ensure chunk is a valid ChatResponse object before sending
          if (typeof chunk === 'object' && 'type' in chunk && chunk.type && 'data' in chunk) {
            // Safe type casting for better type checking
            const responseType = chunk.type as 'text' | 'properties' | 'location' | 'action' | 'error';
            
            // Type validation and runtime validation
            const isValidResponseType = ['text', 'properties', 'location', 'action', 'error'].includes(responseType);
            
            if (!isValidResponseType) {
              logger.warn('Invalid response type detected', {
                type: responseType,
                requestId
              });
              continue;
            }
            
            // Create a type-safe version of the response
            const response = chunk as TypedChatResponse;
            
            // Additional validation for location type - use a type guard pattern
            let hasValidLocationData = false;
            let locationName = "";
            let locationLat = 0;
            let locationLng = 0;
            let locationPlaceType = "";
            
            // Handle location responses with strong type safety
            if (responseType === 'location') {
              const locationData = response.data;
              
              if (locationData && typeof locationData === 'object' &&
                  'name' in locationData && typeof locationData.name === 'string' &&
                  'lat' in locationData && typeof locationData.lat === 'number' &&
                  'lng' in locationData && typeof locationData.lng === 'number') {
                
                hasValidLocationData = true;
                locationName = locationData.name;
                locationLat = locationData.lat;
                locationLng = locationData.lng;
                locationPlaceType = locationData.placeType || 'unknown';
              } else {
                logger.error('Invalid location data format for location type', {
                  data: typeof locationData,
                  hasName: locationData && typeof locationData === 'object' ? 'name' in locationData : false,
                  hasLat: locationData && typeof locationData === 'object' ? 'lat' in locationData : false,
                  hasLng: locationData && typeof locationData === 'object' ? 'lng' in locationData : false,
                  requestId
                });
                continue;
              }
            } 
            // Handle location actions with strong type safety
            else if (responseType === 'action') {
              const actionData = response.data;
              
              if (actionData && typeof actionData === 'object' && 
                  'type' in actionData && actionData.type === 'location' &&
                  'data' in actionData && typeof actionData.data === 'object' &&
                  actionData.data !== null) {
                
                const locationData = actionData.data;
                
                if ('name' in locationData && typeof locationData.name === 'string' &&
                    'lat' in locationData && typeof locationData.lat === 'number' &&
                    'lng' in locationData && typeof locationData.lng === 'number') {
                  
                  hasValidLocationData = true;
                  locationName = locationData.name;
                  locationLat = locationData.lat;
                  locationLng = locationData.lng;
                  locationPlaceType = locationData.placeType || 'unknown';
                } else {
                  logger.error('Invalid location data in action', {
                    actionType: actionData.type,
                    hasName: locationData && typeof locationData === 'object' ? 'name' in locationData : false,
                    hasLat: locationData && typeof locationData === 'object' ? 'lat' in locationData : false,
                    hasLng: locationData && typeof locationData === 'object' ? 'lng' in locationData : false,
                    requestId
                  });
                  continue;
                }
              }
            }

            // Add assistant messages to history
            if (response.type === 'text') {
              addMessageToConversation(sessionId, {
                role: 'assistant',
                content: response.data
              });
            }
            
            // Process validated location data
            if (hasValidLocationData) {
              logger.info('Location response detected', {
                location: locationName,
                coordinates: `${locationLat},${locationLng}`,
                placeType: locationPlaceType,
                requestId
              });
              
              // Update session context with the location
              contextService.addSearch(
                sessionId,
                locationName,
                {
                  lat: locationLat,
                  lng: locationLng
                },
                conversation.context.dateRange,
                conversation.context.preferences?.guestCount,
                // Use guest count as default for rooms if not available
                conversation.context.preferences?.guestCount || 1
              );
              
              // Enhance location with additional details if available
              if (locationDetails && locationDetails.neighborhoods) {
                // Add enhanced details to the location response
                (response as any).data.enhancedDetails = {
                  neighborhoods: locationDetails.neighborhoods.map(n => n.name),
                  landmarks: locationDetails.landmarks?.map(l => l.name),
                  popularTimes: locationDetails.popularTimes,
                  weatherPattern: locationDetails.weatherPattern
                };
              }
              
              // Validate location coordinates
              if (locationLat && locationLng) {
                const isValidLocation = validateLocationCoordinates(
                  locationName,
                  locationLat,
                  locationLng
                );
                
                if (!isValidLocation) {
                  logger.warn('Potentially incorrect location coordinates detected', {
                    location: locationName,
                    coordinates: `${locationLat},${locationLng}`,
                    requestId
                  });
                }
              }
            }
            
            // Enhance text responses with proactive insights
            if (responseType === 'text' && proactiveInsights) {
              const textResponse = response as TextResponse;
              
              // Get original content (ensuring it's a string)
              let enhancedContent = typeof textResponse.data === 'string' 
                ? textResponse.data 
                : (textResponse.data as any).content || '';
              
              // Add price insights if available
              if (proactiveInsights.pricingInsights.length > 0) {
                const insight = proactiveInsights.pricingInsights[0];
                enhancedContent += `\n\n💡 ${insight.message}`;
              }
              
              // Add event alerts if available
              if (proactiveInsights.eventAlerts.length > 0) {
                const alert = proactiveInsights.eventAlerts[0];
                enhancedContent += `\n\n⚠️ ${alert.message}`;
              }
              
              // Add travel tips if available
              if (proactiveInsights.tips.length > 0) {
                enhancedContent += `\n\n✨ Tip: ${proactiveInsights.tips[0]}`;
              }
              
              // Update the response content based on data structure
              if (typeof textResponse.data === 'string') {
                textResponse.data = enhancedContent;
              } else if (textResponse.data && typeof textResponse.data === 'object') {
                (textResponse.data as any).content = enhancedContent;
              }
            }
            
            // Enhance property responses with alternatives
            if (responseType === 'properties' && proactiveInsights && proactiveInsights.alternativeSuggestions.length > 0) {
              const propertiesResponse = response as PropertiesResponse;
              
              const locationAlternatives = proactiveInsights.alternativeSuggestions
                .filter(s => s.type === 'location')
                .map(s => ({ 
                  name: s.data.name, 
                  reason: s.reason, 
                  lat: s.data.lat, 
                  lng: s.data.lng 
                }));
                
              if (locationAlternatives.length > 0 && propertiesResponse.data) {
                // Add alternatives to the response
                (propertiesResponse.data as any).alternativeLocations = locationAlternatives;
              }
            }

            // Send chunk to client
            res.write(`data: ${JSON.stringify(response)}\n\n`);
            responseSent = true;
          } else {
            logger.warn('Invalid chunk format received', { 
              chunkType: typeof chunk,
              chunk: typeof chunk === 'object' ? JSON.stringify(chunk) : String(chunk).substring(0, 100),
              sessionId,
              requestId: `chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
            });
          }
        }
      }

      // If no response was sent, send a fallback error
      if (!responseSent) {
        const fallbackResponse: ErrorResponse = {
          type: 'error',
          data: {
            message: "I'm sorry, I couldn't process your request properly. Please try again."
          }
        };
        res.write(`data: ${JSON.stringify(fallbackResponse)}\n\n`);
      }

      // End the stream
      res.write("data: [DONE]\n\n");
      res.end();

    } catch (error) {
      // Ensure we handle any unexpected errors effectively
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      // Create more detailed error info for debugging
      const errorDetails = {
        message: errorMessage || 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace available',
        type: error instanceof Error ? error.constructor.name : typeof error,
        context: {
          messageLength: message?.length || 0,
          sessionId,
          hasContextParam: !!context,
          extractLocation: extractLocation === true,
          requestId,
          params: Object.keys(req.body || {}).join(',')
        }
      };

      // Log the error with detailed information
      // Using console.error instead of logger to prevent potential circular errors
      console.error('Chat API Error:', JSON.stringify(errorDetails, null, 2));

      // Error handling
      if (!res.headersSent) {
        // If headers not sent yet, send a JSON error
        res.status(500).json({ 
          error: "Failed to process chat request",
          details: process.env.NODE_ENV === 'development' ? errorDetails : errorMessage,
          requestId
        });
      } else {
        // If headers already sent, continue the stream with an error message
        try {
          const errorResponse = {
            type: 'error',
            data: {
              message: "There was an error processing your request. Please try again.",
              details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
            }
          };
          res.write(`data: ${JSON.stringify(errorResponse)}\n\n`);
          res.write("data: [DONE]\n\n");
          res.end();
        } catch (streamError) {
          // If we can't even write to the stream, log and give up
          console.error('Failed to send error response:', streamError);
        }
      }
    }
  });

  app.post("/api/context/map-move", async (req: Request, res: Response) => {
    try {
      const { sessionId, center, radius } = req.body;
      if (!sessionId || !center || typeof center.lat !== 'number' || typeof center.lng !== 'number') {
        return res.status(400).json({ error: "sessionId, center.lat and center.lng are required" });
      }
      const requestId = `map-move-${Date.now()}`;
      logOperation(requestId, 'map_move', { sessionId, center, radius });
      // Update search context – treat as a new search but keep dates unchanged
      contextService.addSearch(
        sessionId,
        'Map moved',
        { lat: center.lat, lng: center.lng },
        undefined,
        undefined,
        undefined
      );
      return res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Failed to record map move', error);
      return res.status(500).json({ error: "Failed to record map move" });
    }
  });

  app.post("/api/context/filters", async (req: Request, res: Response) => {
    try {
      const { sessionId, filters } = req.body;
      if (!sessionId || !filters) {
        return res.status(400).json({ error: "sessionId and filters are required" });
      }
      const requestId = `filters-${Date.now()}`;
      logOperation(requestId, 'update_filters', { sessionId, filters });
      contextService.updateFilters(sessionId, filters);
      return res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Failed to update filters', error);
      return res.status(500).json({ error: "Failed to update filters" });
    }
  });

  return httpServer;
}