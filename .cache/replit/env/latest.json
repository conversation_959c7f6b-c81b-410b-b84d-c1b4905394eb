{"environment": {"AR": "ar", "AS": "as", "CC": "gcc", "COLORTERM": "truecolor", "CONFIG_SHELL": "/nix/store/306znyj77fv49kwnkpxmb0j2znqpa8bj-bash-5.2p26/bin/bash", "CXX": "g++", "DISPLAY": ":0", "DOCKER_CONFIG": "/home/<USER>/workspace/.config/docker", "GIT_ASKPASS": "replit-git-askpass", "GIT_EDITOR": "replit-git-editor", "HOME": "/home/<USER>", "HOSTNAME": "b6a04ff94e66", "HOST_PATH": "/nix/store/07s64wxjzk6z1glwxvl3yq81vdn42k40-postgresql-15.7/bin:/nix/store/ha8rh8jg19r8418baa8ps9b9kvd6szcf-attr-2.5.2-bin/bin:/nix/store/cnnmb3axmv43lj22gny3m4hj06i1nc7c-libcap-2.69/bin:/nix/store/pn9glkalcj7i5p549dpsl1c46pkb13xr-pulseaudio-17.0/bin:/nix/store/php4qidg2bxzmm79vpri025bqi0fa889-coreutils-9.5/bin:/nix/store/jjcsr5gs4qanf7ln5c6wgcq4sn75a978-findutils-4.9.0/bin:/nix/store/i34mknsjgrfyy71k2h79gda0bvagzc2j-diffutils-3.10/bin:/nix/store/5zjms21vpxlkbc0qyl5pmj2sidfmzmd7-gnused-4.9/bin:/nix/store/28gpmx3z6ss3znd7fhmrzmvk3x5lnfbk-gnugrep-3.11/bin:/nix/store/8vvkbgmnin1x2jkp7wcb2zg1p0vc4ks9-gawk-5.2.2/bin:/nix/store/rik7p68cq7yzlj5pmfpf4yv6jnrpvlgf-gnutar-1.35/bin:/nix/store/j5chw7v1x3vlmf3wmdpdb5gwh9hl0b80-gzip-1.13/bin:/nix/store/mxcq77rlan82dzpv3cgj0fh6qvv8ncil-bzip2-1.0.8-bin/bin:/nix/store/cdzpn0rdq810aknww3w9fy3wmw9ixr66-gnumake-4.4.1/bin:/nix/store/306znyj77fv49kwnkpxmb0j2znqpa8bj-bash-5.2p26/bin:/nix/store/0lfxbmchigx9vs9qmrlbahcy6nxwfnj1-patch-2.7.6/bin:/nix/store/6i4xxaa812vsbli9jkq4mksdddrk27lw-xz-5.4.6-bin/bin:/nix/store/xx7x1dwybpssfhq8yikvzz38bh3yrq97-file-5.45/bin", "LANG": "en_US.UTF-8", "LD": "ld", "LIBGL_DRIVERS_PATH": "/nix/store/hggpnywm6l7cfh2ml1ynm50ap9x4f9rn-mesa-24.0.7-drivers/lib/dri", "LOCALE_ARCHIVE": "/usr/lib/locale/locale-archive", "NIXPKGS_ALLOW_UNFREE": "1", "NIX_BINTOOLS": "/nix/store/kln7kinji3b7sz8r50h4gn9yy6k1js9a-binutils-wrapper-2.41", "NIX_BINTOOLS_WRAPPER_TARGET_HOST_x86_64_unknown_linux_gnu": "1", "NIX_BUILD_CORES": "4", "NIX_BUILD_TOP": "/tmp", "NIX_CC": "/nix/store/9bv7dcvmfcjnmg5mnqwqlq2wxfn8d7yi-gcc-wrapper-13.2.0", "NIX_CC_WRAPPER_TARGET_HOST_x86_64_unknown_linux_gnu": "1", "NIX_CFLAGS_COMPILE": " -frandom-seed=yf3p5xmdpd -isystem /nix/store/07s64wxjzk6z1glwxvl3yq81vdn42k40-postgresql-15.7/include -isystem /nix/store/smacbfb82wp7ncz2rwac68jd50qxr70a-libglvnd-1.7.0-dev/include -isystem /nix/store/a83znbrflv30lvhww0d2rljbyd5mw0c0-pulseaudio-17.0-dev/include -isystem /nix/store/xslrgzkvciny0m0cqbgq4bnvydvpdkgx-libcap-2.69-dev/include -isystem /nix/store/nn78wg4rgns62w5sfzyxashxizd0lfva-attr-2.5.2-dev/include -isystem /nix/store/07s64wxjzk6z1glwxvl3yq81vdn42k40-postgresql-15.7/include -isystem /nix/store/smacbfb82wp7ncz2rwac68jd50qxr70a-libglvnd-1.7.0-dev/include -isystem /nix/store/a83znbrflv30lvhww0d2rljbyd5mw0c0-pulseaudio-17.0-dev/include -isystem /nix/store/xslrgzkvciny0m0cqbgq4bnvydvpdkgx-libcap-2.69-dev/include -isystem /nix/store/nn78wg4rgns62w5sfzyxashxizd0lfva-attr-2.5.2-dev/include", "NIX_ENFORCE_NO_NATIVE": "1", "NIX_HARDENING_ENABLE": "bindnow format fortify fortify3 pic relro stackprotector strictoverflow", "NIX_LDFLAGS": "-rpath /nix/store/yf3p5xmdpdvjjsa0c5g63q3jhhgwrryp-nix-shell/lib  -L/nix/store/07s64wxjzk6z1glwxvl3yq81vdn42k40-postgresql-15.7/lib -L/nix/store/xb4h083j02mr2ix7pgj7iawxh2hk100l-postgresql-15.7-lib/lib -L/nix/store/hggpnywm6l7cfh2ml1ynm50ap9x4f9rn-mesa-24.0.7-drivers/lib -L/nix/store/jz3vvf4nsyirb25rh9dbhksm4gq6wybb-libglvnd-1.7.0/lib -L/nix/store/5nk2ga7i2f030am4qpcdsd8qlk6i3z83-attr-2.5.2/lib -L/nix/store/yvhyhcfhc98wm86pw4ygk5jdr804iwrw-libcap-2.69-lib/lib -L/nix/store/pn9glkalcj7i5p549dpsl1c46pkb13xr-pulseaudio-17.0/lib -L/nix/store/07s64wxjzk6z1glwxvl3yq81vdn42k40-postgresql-15.7/lib -L/nix/store/xb4h083j02mr2ix7pgj7iawxh2hk100l-postgresql-15.7-lib/lib -L/nix/store/hggpnywm6l7cfh2ml1ynm50ap9x4f9rn-mesa-24.0.7-drivers/lib -L/nix/store/jz3vvf4nsyirb25rh9dbhksm4gq6wybb-libglvnd-1.7.0/lib -L/nix/store/5nk2ga7i2f030am4qpcdsd8qlk6i3z83-attr-2.5.2/lib -L/nix/store/yvhyhcfhc98wm86pw4ygk5jdr804iwrw-libcap-2.69-lib/lib -L/nix/store/pn9glkalcj7i5p549dpsl1c46pkb13xr-pulseaudio-17.0/lib", "NIX_PATH": "nixpkgs=/home/<USER>/.nix-defexpr/channels/nixpkgs-stable-24_05:/home/<USER>/.nix-defexpr/channels", "NIX_PROFILES": "/nix/var/nix/profiles/default /home/<USER>/.nix-profile", "NIX_PS1": "\\[\\033[01;34m\\]\\w\\[\\033[00m\\]\\$ ", "NIX_STORE": "/nix/store", "NM": "nm", "OBJCOPY": "objcopy", "OBJDUMP": "objdu<PERSON>", "PATH": "/nix/store/r2wrscmjzn4f2f7wk7q2ms2h96mjwzv7-npx/bin:/home/<USER>/workspace/.config/npm/node_global/bin:/home/<USER>/workspace/node_modules/.bin:/nix/store/rrz8cqhldyl17bbs60g7d8vbaadkxc40-nodejs-20.18.1-wrapped/bin:/nix/store/5q4dz85wgqhifng1fk2xy85pslkmiqvs-bun-1.2.12/bin:/nix/store/z8s3r4vwf4r26g2d7shnw5lva6ihim8f-pnpm-9.15.0/bin:/nix/store/jcgdksj946l5l42c2y9ks2l4g6n74h3f-yarn-1.22.22/bin:/nix/store/2s17mrby0ph00z22rkabfs9vzpzx1r70-prettier-3.3.3/bin:/nix/store/0z5iwcvalafm3j2c5pfhllsfbxrbyzf4-postgresql-16.5/bin:/nix/store/rdd4pnr4x9rqc9wgbibhngv217w2xvxl-bash-interactive-5.2p26/bin:/nix/store/nbad47q0m0m9c5xid7zh05hiknwircbp-patchelf-0.15.0/bin:/nix/store/9bv7dcvmfcjnmg5mnqwqlq2wxfn8d7yi-gcc-wrapper-13.2.0/bin:/nix/store/14c6s4xzhy14i2b05s00rjns2j93gzz4-gcc-13.2.0/bin:/nix/store/c2i631h8i5vcs1sqifwxfsazhwrg6wr5-glibc-2.39-52-bin/bin:/nix/store/php4qidg2bxzmm79vpri025bqi0fa889-coreutils-9.5/bin:/nix/store/kln7kinji3b7sz8r50h4gn9yy6k1js9a-binutils-wrapper-2.41/bin:/nix/store/bgcaxhhxswzvmxjbbgvvaximm5hwghz1-binutils-2.41/bin:/nix/store/07s64wxjzk6z1glwxvl3yq81vdn42k40-postgresql-15.7/bin:/nix/store/ha8rh8jg19r8418baa8ps9b9kvd6szcf-attr-2.5.2-bin/bin:/nix/store/cnnmb3axmv43lj22gny3m4hj06i1nc7c-libcap-2.69/bin:/nix/store/pn9glkalcj7i5p549dpsl1c46pkb13xr-pulseaudio-17.0/bin:/nix/store/jjcsr5gs4qanf7ln5c6wgcq4sn75a978-findutils-4.9.0/bin:/nix/store/i34mknsjgrfyy71k2h79gda0bvagzc2j-diffutils-3.10/bin:/nix/store/5zjms21vpxlkbc0qyl5pmj2sidfmzmd7-gnused-4.9/bin:/nix/store/28gpmx3z6ss3znd7fhmrzmvk3x5lnfbk-gnugrep-3.11/bin:/nix/store/8vvkbgmnin1x2jkp7wcb2zg1p0vc4ks9-gawk-5.2.2/bin:/nix/store/rik7p68cq7yzlj5pmfpf4yv6jnrpvlgf-gnutar-1.35/bin:/nix/store/j5chw7v1x3vlmf3wmdpdb5gwh9hl0b80-gzip-1.13/bin:/nix/store/mxcq77rlan82dzpv3cgj0fh6qvv8ncil-bzip2-1.0.8-bin/bin:/nix/store/cdzpn0rdq810aknww3w9fy3wmw9ixr66-gnumake-4.4.1/bin:/nix/store/306znyj77fv49kwnkpxmb0j2znqpa8bj-bash-5.2p26/bin:/nix/store/0lfxbmchigx9vs9qmrlbahcy6nxwfnj1-patch-2.7.6/bin:/nix/store/6i4xxaa812vsbli9jkq4mksdddrk27lw-xz-5.4.6-bin/bin:/nix/store/xx7x1dwybpssfhq8yikvzz38bh3yrq97-file-5.45/bin:/nix/store/5lkb22xbsxbzih80f4pk1jyb9sp97aj5-pid1/bin:/nix/store/a3a2dskycxvn7cbrfb2nnska6a8xq1b8-replit-runtime-path/bin:/home/<USER>/.nix-profile/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin", "PROMPT_DIRTRIM": "2", "RANLIB": "ranlib", "READELF": "readelf", "REPLIT_BASHRC": "/nix/store/jww70qbp1jafzdjawqy5sj0hpsp9xc03-replit-bashrc/bashrc", "REPLIT_CLI": "/nix/store/x20rg378hii3h2j9bn0516vkpphnr5cx-pid1-0.0.1/bin/replit", "REPLIT_CLUSTER": "kirk", "REPLIT_CONTAINER": "gcr.io/marine-cycle-160323/nix:bf8590a3e2f0a8b70b7ca175eeed9074dffbfca9", "REPLIT_DEV_DOMAIN": "246cb3e6-5959-45f5-b6ae-e2f71deb8208-00-34vbb8ky6c619.kirk.replit.dev", "REPLIT_DOMAINS": "246cb3e6-5959-45f5-b6ae-e2f71deb8208-00-34vbb8ky6c619.kirk.replit.dev", "REPLIT_ENVIRONMENT": "production", "REPLIT_LD_AUDIT": "/nix/store/n5x1kgbz8zjh63ymsijbislyi1n1hir6-replit_rtld_loader-1/rtld_loader.so", "REPLIT_LD_LIBRARY_PATH": "/nix/store/xb4h083j02mr2ix7pgj7iawxh2hk100l-postgresql-15.7-lib/lib:/nix/store/hggpnywm6l7cfh2ml1ynm50ap9x4f9rn-mesa-24.0.7-drivers/lib:/nix/store/jz3vvf4nsyirb25rh9dbhksm4gq6wybb-libglvnd-1.7.0/lib:/nix/store/pn9glkalcj7i5p549dpsl1c46pkb13xr-pulseaudio-17.0/lib", "REPLIT_NIX_CHANNEL": "stable-24_05", "REPLIT_PID1_FLAG_REPLIT_RTLD_LOADER": "1", "REPLIT_PID1_VERSION": "0.0.0-2282521", "REPLIT_RIPPKGS_INDICES": "/nix/store/l5gcmdp908sji4wchfp8csflhjcgnmm3-rippkgs-indices", "REPLIT_RTLD_LOADER": "1", "REPLIT_SUBCLUSTER": "interactive", "REPL_HOME": "/home/<USER>/workspace", "REPL_ID": "246cb3e6-5959-45f5-b6ae-e2f71deb8208", "REPL_IMAGE": "gcr.io/marine-cycle-160323/nix:bf8590a3e2f0a8b70b7ca175eeed9074dffbfca9", "REPL_LANGUAGE": "nix", "REPL_OWNER": "clynch51", "REPL_OWNER_ID": "36523258", "REPL_PUBKEYS": "{\"crosis-ci\":\"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=\",\"crosis-ci:1\":\"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=\",\"crosis-ci:latest\":\"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=\",\"prod\":\"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=\",\"prod:1\":\"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=\",\"prod:2\":\"8uGN+vfszlnV93/HCSHlVLG0xddMlPkir1Ni4JKT4+w=\",\"prod:3\":\"9+MCOSHQSQlcodXoot8dC8NLhc862nLkx1/VMsbY2h8=\",\"prod:4\":\"8uGN+vfszlnV93/HCSHlVLG0xddMlPkir1Ni4JKT4+w=\",\"prod:5\":\"9+MCOSHQSQlcodXoot8dC8NLhc862nLkx1/VMsbY2h8=\",\"prod:latest\":\"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=\",\"vault-goval-token\":\"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E=\",\"vault-goval-token:1\":\"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E=\",\"vault-goval-token:latest\":\"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E=\"}", "REPL_SLUG": "workspace", "SIZE": "size", "SOURCE_DATE_EPOCH": "315532800", "STRINGS": "strings", "STRIP": "strip", "USER": "runner", "XDG_CACHE_HOME": "/home/<USER>/workspace/.cache", "XDG_CONFIG_HOME": "/home/<USER>/workspace/.config", "XDG_DATA_DIRS": "/nix/store/nbad47q0m0m9c5xid7zh05hiknwircbp-patchelf-0.15.0/share:/nix/store/a3a2dskycxvn7cbrfb2nnska6a8xq1b8-replit-runtime-path/share", "XDG_DATA_HOME": "/home/<USER>/workspace/.local/share", "_": "/nix/store/php4qidg2bxzmm79vpri025bqi0fa889-coreutils-9.5/bin/env", "__EGL_VENDOR_LIBRARY_FILENAMES": "/nix/store/hggpnywm6l7cfh2ml1ynm50ap9x4f9rn-mesa-24.0.7-drivers/share/glvnd/egl_vendor.d/50_mesa.json", "__ETC_PROFILE_SOURCED": "1", "__structuredAttrs": "", "buildInputs": "/nix/store/07s64wxjzk6z1glwxvl3yq81vdn42k40-postgresql-15.7 /nix/store/hggpnywm6l7cfh2ml1ynm50ap9x4f9rn-mesa-24.0.7-drivers /nix/store/smacbfb82wp7ncz2rwac68jd50qxr70a-libglvnd-1.7.0-dev /nix/store/a83znbrflv30lvhww0d2rljbyd5mw0c0-pulseaudio-17.0-dev", "buildPhase": "{ echo \"------------------------------------------------------------\";\n  echo \" WARNING: the existence of this path is not guaranteed.\";\n  echo \" It is an internal implementation detail for pkgs.mkShell.\";\n  echo \"------------------------------------------------------------\";\n  echo;\n  # Record all build inputs as runtime dependencies\n  export;\n} >> \"$out\"\n", "builder": "/nix/store/306znyj77fv49kwnkpxmb0j2znqpa8bj-bash-5.2p26/bin/bash", "cmakeFlags": "", "configureFlags": "", "depsBuildBuild": "", "depsBuildBuildPropagated": "", "depsBuildTarget": "", "depsBuildTargetPropagated": "", "depsHostHost": "", "depsHostHostPropagated": "", "depsTargetTarget": "", "depsTargetTargetPropagated": "", "doCheck": "", "doInstallCheck": "", "mesonFlags": "", "nativeBuildInputs": "", "npm_config_prefix": "/home/<USER>/workspace/.config/npm/node_global", "out": "/nix/store/yf3p5xmdpdvjjsa0c5g63q3jhhgwrryp-nix-shell", "outputs": "out", "patches": "", "phases": "buildPhase", "preferLocalBuild": "1", "propagatedBuildInputs": "", "propagatedNativeBuildInputs": "", "shell": "/nix/store/306znyj77fv49kwnkpxmb0j2znqpa8bj-bash-5.2p26/bin/bash", "shellHook": "", "stdenv": "/nix/store/xfhkjnpqjwlf6hlk1ysmq3aaq80f3bjj-stdenv-linux", "strictDeps": "", "system": "x86_64-linux"}}