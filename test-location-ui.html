<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Enhanced AI Location Interface</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            background: #f8f9fa;
        }
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-weight: 600;
            transition: transform 0.2s;
        }
        .test-button:hover {
            transform: translateY(-2px);
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            background: white;
            border-left: 4px solid #667eea;
        }
        .location-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin: 10px 0;
        }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
        .loading { border-left-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Enhanced AI Location Interface Test</h1>
        
        <div class="test-section">
            <h3>🗺️ Location Detection Tests</h3>
            <p>Test the enhanced AI location parsing and beautiful UI rendering:</p>
            
            <button class="test-button" onclick="testLocation('I want to visit Paris, France')">
                🇫🇷 Test Paris
            </button>
            <button class="test-button" onclick="testLocation('Show me destinations in Tokyo')">
                🇯🇵 Test Tokyo
            </button>
            <button class="test-button" onclick="testLocation('Find accommodations in New York City')">
                🇺🇸 Test NYC
            </button>
            <button class="test-button" onclick="testLocation('I need a place to stay in Miami Beach')">
                🏖️ Test Miami
            </button>
            <button class="test-button" onclick="testLocation('Tell me about San Francisco')">
                🌉 Test SF
            </button>
            
            <div id="results"></div>
        </div>

        <div class="test-section">
            <h3>📊 Test Results Summary</h3>
            <div id="summary">
                <p>Click the test buttons above to see the enhanced location interface in action!</p>
            </div>
        </div>
    </div>

    <script>
        let testCount = 0;
        let successCount = 0;

        async function testLocation(query) {
            testCount++;
            const resultsDiv = document.getElementById('results');
            const summaryDiv = document.getElementById('summary');
            
            // Add loading indicator
            const testDiv = document.createElement('div');
            testDiv.className = 'result loading';
            testDiv.innerHTML = `
                <h4>🔄 Testing: "${query}"</h4>
                <p>Sending request to AI...</p>
            `;
            resultsDiv.appendChild(testDiv);

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: query,
                        context: {},
                        sessionId: 'test-ui',
                        extractLocation: true,
                        enhancedMode: true
                    }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let foundLocation = false;
                let locationData = null;

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || '';

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data === '[DONE]') continue;

                            try {
                                const parsed = JSON.parse(data);
                                
                                if (parsed.type === 'action' && parsed.data.type === 'location') {
                                    foundLocation = true;
                                    locationData = parsed.data.data;
                                    break;
                                } else if (parsed.type === 'location') {
                                    foundLocation = true;
                                    locationData = parsed.data;
                                    break;
                                }
                            } catch (error) {
                                // Ignore parsing errors
                            }
                        }
                    }

                    if (foundLocation) break;
                }

                // Update result
                if (foundLocation && locationData) {
                    successCount++;
                    testDiv.className = 'result success';
                    testDiv.innerHTML = `
                        <h4>✅ SUCCESS: "${query}"</h4>
                        <div class="location-card">
                            <h5>📍 ${locationData.name}</h5>
                            <p>📊 Coordinates: ${locationData.lat}, ${locationData.lng}</p>
                            ${locationData.description ? `<p>💫 ${locationData.description}</p>` : ''}
                            <p><strong>🎉 Location parsing and UI rendering working perfectly!</strong></p>
                        </div>
                    `;
                } else {
                    testDiv.className = 'result error';
                    testDiv.innerHTML = `
                        <h4>❌ FAILED: "${query}"</h4>
                        <p>No location data found in AI response. The enhanced parsing may need adjustment.</p>
                    `;
                }

            } catch (error) {
                testDiv.className = 'result error';
                testDiv.innerHTML = `
                    <h4>❌ ERROR: "${query}"</h4>
                    <p>Request failed: ${error.message}</p>
                `;
            }

            // Update summary
            summaryDiv.innerHTML = `
                <h4>📊 Test Results: ${successCount}/${testCount} passed</h4>
                <p><strong>Success Rate: ${Math.round((successCount/testCount)*100)}%</strong></p>
                ${successCount === testCount ? 
                    '<p style="color: green;">🎉 All tests passed! The enhanced AI location interface is working amazingly!</p>' :
                    '<p style="color: orange;">⚠️ Some tests failed. The interface may need further improvements.</p>'
                }
            `;
        }
    </script>
</body>
</html>
