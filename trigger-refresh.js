// Simple script to trigger a property search and see the refresh logic in action

const fetch = (() => {
  try {
    return require('node-fetch');
  } catch (e) {
    // Fallback using built-in fetch for newer Node versions
    return globalThis.fetch;
  }
})();

async function triggerRefresh() {
  try {
    const url = 'http://localhost:5000/api/search?lat=40.7128&lng=-74.0060&checkIn=2024-07-15&checkOut=2024-07-16&guests=2&rooms=1&pageSize=1';
    
    console.log('Making API request to trigger property refresh...');
    console.log('URL:', url);
    
    const response = await fetch(url, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const text = await response.text();
    console.log('Response (first 500 chars):', text.substring(0, 500));
    
    // Try to parse as JSON
    try {
      const data = JSON.parse(text);
      if (data.properties && data.properties.length > 0) {
        const firstProperty = data.properties[0];
        console.log('\nFirst property images:', firstProperty.images);
      }
    } catch (parseError) {
      console.log('Could not parse as JSON:', parseError.message);
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

triggerRefresh(); 