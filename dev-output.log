
> rest-express@1.0.0 dev
> tsx --inspect=0.0.0.0:9229 server/index.ts

Starting inspector on 0.0.0.0:9229 failed: address already in use
[32minfo[39m: lambda - llama3.1-8b-instruct initialized successfully {"maxRetries":3,"operation":"lambda - llama3.1-8b-instruct initialized successfully","provider":"lambda","requestId":"ai-init","service":"travel-booking-service","timeout":10000,"timestamp":"2025-06-01T15:53:32.588Z"}
3:53:32 PM [express] Running database migrations...
[32minfo[39m: Starting database migrations... {"service":"travel-booking-service","timestamp":"2025-06-01T15:53:32.609Z"}
[32minfo[39m: Found 6 previously applied migrations {"service":"travel-booking-service","timestamp":"2025-06-01T15:53:32.785Z"}
[32minfo[39m: Found 6 migration files {"service":"travel-booking-service","timestamp":"2025-06-01T15:53:32.787Z"}
[32minfo[39m: No pending migrations to apply {"service":"travel-booking-service","timestamp":"2025-06-01T15:53:32.788Z"}
3:53:32 PM [express] Database migrations completed successfully
[32minfo[39m: Enhanced AI chat routes registered successfully {"service":"travel-booking-service","timestamp":"2025-06-01T15:53:32.796Z"}
3:53:32 PM [express] Server started successfully on port 5001
3:53:32 PM [express] Inspector available on port 9230
3:53:32 PM [express] Property refresh scheduled for 2025-06-02T03:00:00.000Z
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   [34mdebug[39m: Page view tracked {"deviceType":"desktop","pagePath":"/health","pageTitle":"Page: /health","service":"travel-booking-service","sessionId":"vrQyGP90i4r7xWqATIFMEtMhFm_jknYo","timestamp":"2025-06-01T15:53:35.451Z"}
[34mdebug[39m: Page view tracked {"deviceType":"desktop","pagePath":"/","pageTitle":"Page: /","service":"travel-booking-service","sessionId":"K1jIMPnzkDWNdcre5685eeB9lTVFkrY0","timestamp":"2025-06-01T15:57:20.931Z"}
[34mdebug[39m: Page view tracked {"deviceType":"desktop","pagePath":"/@vite/client","pageTitle":"Page: /@vite/client","service":"travel-booking-service","sessionId":"hrg3pQ_wcP6s2cRUh-RUESO3ns_tobTA","timestamp":"2025-06-01T15:57:21.040Z"}
[34mdebug[39m: Page view tracked {"deviceType":"desktop","pagePath":"/@react-refresh","pageTitle":"Page: /@react-refresh","service":"travel-booking-service","sessionId":"zhEIMVrp_zCfnbomW7GQR61A017mOLTo","timestamp":"2025-06-01T15:57:21.180Z"}
[32minfo[39m: CONFIG_REQUEST {"operation":"CONFIG_REQUEST","requestId":"config","service":"travel-booking-service","timestamp":"2025-06-01T15:57:25.708Z"}
3:57:25 PM [express] GET /api/config 200 in 1ms :: {"googleMapsApiKey":"AIzaSyAiidegmTCISCJSft6seYr4…
3:57:25 PM [express] GET /api/user 401 in 0ms :: {"success":false,"message":"Not authenticated"}
3:57:26 PM [express] GET /api/user 401 in 1ms :: {"success":false,"message":"Not authenticated"}
[32minfo[39m: Attempting to extract location from message {"hasPotentialLocation":false,"messageLength":37,"requestId":"chat-1748793452679-7wwbo0jsz","service":"travel-booking-service","timestamp":"2025-06-01T15:57:32.680Z"}
[33mwarn[39m: No location found in message Surprise me with amazing destinations {"requestId":"chat-1748793452679-7wwbo0jsz","service":"travel-booking-service","timestamp":"2025-06-01T15:57:32.682Z"}
3:57:32 PM [express] POST /api/chat 200 in 3ms
[32minfo[39m: Attempting to extract location from message {"hasPotentialLocation":false,"messageLength":35,"requestId":"chat-1748793482489-oeivcbmd0","service":"travel-booking-service","timestamp":"2025-06-01T15:58:02.489Z"}
[33mwarn[39m: No location found in message Show me popular travel destinations {"requestId":"chat-1748793482489-oeivcbmd0","service":"travel-booking-service","timestamp":"2025-06-01T15:58:02.492Z"}
3:58:02 PM [express] POST /api/chat 200 in 4ms
3:59:46 PM [express] POST /api/test-chat 200 in 4ms
4:00:02 PM [express] POST /api/test-chat 200 in 3ms
[32minfo[39m: CONFIG_REQUEST {"operation":"CONFIG_REQUEST","requestId":"config","service":"travel-booking-service","timestamp":"2025-06-01T16:00:05.224Z"}
4:00:05 PM [express] GET /api/config 200 in 1ms :: {"googleMapsApiKey":"AIzaSyAiidegmTCISCJSft6seYr4…
4:00:08 PM [express] POST /api/test-chat 200 in 3ms
[32minfo[39m: CONFIG_REQUEST {"operation":"CONFIG_REQUEST","requestId":"config","service":"travel-booking-service","timestamp":"2025-06-01T16:01:04.594Z"}
4:01:04 PM [express] GET /api/config 200 in 1ms :: {"googleMapsApiKey":"AIzaSyAiidegmTCISCJSft6seYr4…
4:01:10 PM [express] POST /api/test-chat 200 in 3ms
