// Debug script to see what the AI is actually returning
import fetch from 'node-fetch';

async function debugAIResponse() {
  console.log('🔍 Debugging AI Response Generation...\n');

  try {
    const response = await fetch('http://localhost:5000/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: 'I want to visit Paris, France',
        context: {},
        sessionId: 'debug-session',
        extractLocation: true,
        enhancedMode: true
      }),
    });

    if (!response.ok) {
      console.log(`❌ HTTP Error: ${response.status}`);
      return;
    }

    console.log('📡 Raw AI Response Stream:');
    console.log('=' .repeat(50));

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let chunkCount = 0;

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data === '[DONE]') {
            console.log('🏁 Stream ended');
            continue;
          }

          chunkCount++;
          console.log(`\n📦 Chunk ${chunkCount}:`);
          console.log(`Raw: ${data}`);

          try {
            const parsed = JSON.parse(data);
            console.log(`Type: ${parsed.type}`);
            
            if (parsed.type === 'text') {
              console.log(`Text: "${parsed.data}"`);
            } else if (parsed.type === 'action') {
              console.log(`Action Type: ${parsed.data.type}`);
              console.log(`Action Label: ${parsed.data.label}`);
              console.log(`Action Data:`, JSON.stringify(parsed.data.data, null, 2));
            } else if (parsed.type === 'location') {
              console.log(`Location:`, JSON.stringify(parsed.data, null, 2));
            } else {
              console.log(`Other Data:`, JSON.stringify(parsed.data, null, 2));
            }
          } catch (error) {
            console.log(`❌ Parse Error: ${error.message}`);
          }
        }
      }
    }

    console.log('\n' + '=' .repeat(50));
    console.log(`✅ Debug complete. Total chunks: ${chunkCount}`);

  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
}

debugAIResponse().catch(console.error);
