-- 004_fix_amenities_jsonb.sql
-- Ensure amenities and images columns are proper JSONB arrays and remove corrupted data.
-- This is destructive (existing data is discarded) but required to stop malformed array literal errors.

BEGIN;

-- Drop existing columns if they exist (regardless of type)
ALTER TABLE properties
  DROP COLUMN IF EXISTS amenities,
  DROP COLUMN IF EXISTS images;

-- Recreate columns as JSONB with default empty array
ALTER TABLE properties
  ADD COLUMN amenities jsonb DEFAULT '[]'::jsonb,
  ADD COLUMN images jsonb DEFAULT '[]'::jsonb;

COMMIT; 