-- 007_repopulate_images_amenities.sql
-- This migration addresses the issue where migration 004 wiped out all existing image and amenity data
-- We'll mark properties for refresh so the API will repopulate them with fresh data

BEGIN;

-- Update all properties to have an older updatedAt timestamp
-- This will force the search API to refresh them with fresh data from the external API
UPDATE properties 
SET updated_at = '2020-01-01 00:00:00'::timestamp
WHERE (images = '[]'::jsonb OR amenities = '[]'::jsonb OR images IS NULL OR amenities IS NULL);

COMMIT; 