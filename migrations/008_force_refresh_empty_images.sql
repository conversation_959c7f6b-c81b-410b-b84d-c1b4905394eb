-- 008_force_refresh_empty_images.sql
-- Force refresh of all properties with empty images by setting old updated_at timestamp

BEGIN;

-- Update all properties with empty images or amenities to have an older updatedAt timestamp
-- This will force the search API to refresh them with fresh data from the external API
UPDATE properties 
SET updated_at = '2020-01-01 00:00:00'::timestamp
WHERE 
  images = '[]'::jsonb 
  OR amenities = '[]'::jsonb 
  OR images IS NULL 
  OR amenities IS NULL
  OR jsonb_array_length(images) = 0
  OR jsonb_array_length(amenities) = 0;

-- Log the number of properties that will be refreshed
DO $$
DECLARE
    refresh_count integer;
BEGIN
    SELECT COUNT(*) INTO refresh_count
    FROM properties 
    WHERE updated_at = '2020-01-01 00:00:00'::timestamp;
    
    RAISE NOTICE 'Marked % properties for refresh due to empty images/amenities', refresh_count;
END $$;

COMMIT; 