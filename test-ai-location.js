// Test script to verify enhanced AI location parsing
import fetch from 'node-fetch';

async function testLocationParsing() {
  console.log('🧪 Testing Enhanced AI Location Parsing...\n');

  const testQueries = [
    'I want to visit Paris, France',
    'Show me destinations in Tokyo',
    'Find me places to stay in New York City',
    'I need accommodations in Miami Beach',
    'Tell me about travel to San Francisco'
  ];

  for (const query of testQueries) {
    console.log(`📝 Testing: "${query}"`);
    
    try {
      const response = await fetch('http://localhost:5000/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: query,
          context: {},
          sessionId: 'test-session',
          extractLocation: true,
          enhancedMode: true
        }),
      });

      if (!response.ok) {
        console.log(`❌ HTTP Error: ${response.status}`);
        continue;
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let foundLocation = false;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') continue;

            try {
              const parsed = JSON.parse(data);
              
              if (parsed.type === 'action' && parsed.data.type === 'location') {
                console.log(`✅ Found ACTION:LOCATION - ${parsed.data.data.name} (${parsed.data.data.lat}, ${parsed.data.data.lng})`);
                foundLocation = true;
              } else if (parsed.type === 'location') {
                console.log(`✅ Found LOCATION - ${parsed.data.name} (${parsed.data.lat}, ${parsed.data.lng})`);
                foundLocation = true;
              }
            } catch (error) {
              // Ignore parsing errors for this test
            }
          }
        }
      }

      if (!foundLocation) {
        console.log('⚠️  No location data found in response');
      }

    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }

    console.log('---\n');
  }

  console.log('🎉 Test completed!');
}

testLocationParsing().catch(console.error);
